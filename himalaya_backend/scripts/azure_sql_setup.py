#!/usr/bin/env python3
"""
Azure SQL Database Setup Script for SQL Module
==============================================

This script sets up additional tables and updates existing tables for the SQL module
on Azure PostgreSQL database. It handles:

1. SQL Module specific tables (database_connections, database_schemas, etc.)
2. Schema updates and migrations
3. Indexes and constraints
4. Initial data seeding

Usage:
    python azure_sql_setup.py --action create_tables
    python azure_sql_setup.py --action update_tables
    python azure_sql_setup.py --action create_indexes
    python azure_sql_setup.py --action seed_data
    python azure_sql_setup.py --action full_setup
"""

import os
import sys
import argparse
import logging
import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT
from urllib.parse import quote
from datetime import datetime
import json

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('azure_sql_setup.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class AzureSQLSetup:
    """Azure SQL Database Setup Manager for SQL Module"""
    
    def __init__(self, connection_string=None):
        """
        Initialize the setup manager
        
        Args:
            connection_string: PostgreSQL connection string. If None, will use environment variables.
        """
        self.connection_string = connection_string or self._get_connection_string()
        self.connection = None
        
    def _get_connection_string(self):
        """Get connection string from environment variables"""
        # Try to get from environment first
        db_url = os.getenv('DATABASE_URL')
        if db_url:
            return db_url
            
        # Build from individual components
        db_password = os.getenv('DB_PASSWORD', 'Himalaya@1930')
        db_password = quote(db_password)
        db_host = os.getenv('DB_HOST', 'higpt-dbp2.postgres.database.azure.com')
        db_user = os.getenv('DB_USER', 'aivideoconferenceAdmin')
        db_name = os.getenv('DB_NAME', 'HiGPT_Agents_UAT')
        db_port = os.getenv('DB_PORT', '5432')
        
        return f'postgresql://{db_user}:{db_password}@{db_host}:{db_port}/{db_name}'
    
    def connect(self):
        """Establish database connection"""
        try:
            self.connection = psycopg2.connect(self.connection_string)
            self.connection.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
            logger.info("✅ Successfully connected to Azure PostgreSQL database")
            return True
        except Exception as e:
            logger.error(f"❌ Failed to connect to database: {e}")
            return False
    
    def disconnect(self):
        """Close database connection"""
        if self.connection:
            self.connection.close()
            logger.info("🔌 Database connection closed")
    
    def execute_sql(self, sql, description="SQL execution"):
        """Execute SQL statement with error handling"""
        try:
            cursor = self.connection.cursor()
            cursor.execute(sql)
            logger.info(f"✅ {description} completed successfully")
            return True
        except Exception as e:
            logger.error(f"❌ {description} failed: {e}")
            return False
    
    def table_exists(self, table_name):
        """Check if table exists"""
        try:
            cursor = self.connection.cursor()
            cursor.execute("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_name = %s
                );
            """, (table_name,))
            return cursor.fetchone()[0]
        except Exception as e:
            logger.error(f"Error checking table existence: {e}")
            return False
    
    def create_sql_module_tables(self):
        """Create SQL module specific tables"""
        logger.info("🏗️  Creating SQL module tables...")
        
        # SQL for creating SQL module tables
        sql_tables = [
            # Database Connections table
            """
            CREATE TABLE IF NOT EXISTS database_connections (
                id SERIAL PRIMARY KEY,
                name VARCHAR(100) NOT NULL UNIQUE,
                description TEXT,
                connection_type VARCHAR(50) NOT NULL,
                host VARCHAR(255),
                port INTEGER,
                database_name VARCHAR(100),
                username VARCHAR(100),
                password_encrypted TEXT,
                connection_string TEXT,
                is_active BOOLEAN DEFAULT TRUE,
                created_by INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
                created_at TIMESTAMP DEFAULT NOW(),
                updated_at TIMESTAMP DEFAULT NOW()
            );
            """,
            
            # Database Schemas table
            """
            CREATE TABLE IF NOT EXISTS database_schemas (
                id SERIAL PRIMARY KEY,
                database_id INTEGER NOT NULL REFERENCES database_connections(id) ON DELETE CASCADE,
                schema_name VARCHAR(100),
                table_name VARCHAR(100) NOT NULL,
                table_type VARCHAR(20) DEFAULT 'TABLE',
                table_description TEXT,
                llm_generated_description TEXT,
                row_count BIGINT,
                created_at TIMESTAMP DEFAULT NOW(),
                updated_at TIMESTAMP DEFAULT NOW()
            );
            """,
            
            # Database Columns table
            """
            CREATE TABLE IF NOT EXISTS database_columns (
                id SERIAL PRIMARY KEY,
                schema_id INTEGER NOT NULL REFERENCES database_schemas(id) ON DELETE CASCADE,
                column_name VARCHAR(100) NOT NULL,
                data_type VARCHAR(50) NOT NULL,
                is_nullable BOOLEAN DEFAULT TRUE,
                column_default TEXT,
                character_maximum_length INTEGER,
                numeric_precision INTEGER,
                numeric_scale INTEGER,
                is_primary_key BOOLEAN DEFAULT FALSE,
                is_foreign_key BOOLEAN DEFAULT FALSE,
                foreign_key_table VARCHAR(100),
                foreign_key_column VARCHAR(100),
                column_description TEXT,
                llm_generated_description TEXT,
                sample_values JSONB,
                value_distribution JSONB,
                created_at TIMESTAMP DEFAULT NOW(),
                updated_at TIMESTAMP DEFAULT NOW()
            );
            """,
            
            # Database Roles table
            """
            CREATE TABLE IF NOT EXISTS database_roles (
                id SERIAL PRIMARY KEY,
                name VARCHAR(50) NOT NULL UNIQUE,
                description TEXT,
                permissions JSONB NOT NULL,
                can_read BOOLEAN DEFAULT TRUE,
                can_write BOOLEAN DEFAULT FALSE,
                can_delete BOOLEAN DEFAULT FALSE,
                can_create_tables BOOLEAN DEFAULT FALSE,
                can_drop_tables BOOLEAN DEFAULT FALSE,
                row_level_security JSONB,
                created_at TIMESTAMP DEFAULT NOW(),
                updated_at TIMESTAMP DEFAULT NOW()
            );
            """
        ]
        
        for i, sql in enumerate(sql_tables, 1):
            table_name = sql.split('CREATE TABLE IF NOT EXISTS ')[1].split(' ')[0]
            if self.execute_sql(sql, f"Creating table {table_name} ({i}/{len(sql_tables)})"):
                logger.info(f"📋 Table {table_name} created/verified")
        
        return True

    def create_additional_sql_tables(self):
        """Create additional SQL module tables"""
        logger.info("🏗️  Creating additional SQL module tables...")

        additional_tables = [
            # User Database Access table
            """
            CREATE TABLE IF NOT EXISTS user_database_access (
                id SERIAL PRIMARY KEY,
                user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
                database_id INTEGER NOT NULL REFERENCES database_connections(id) ON DELETE CASCADE,
                role_id INTEGER NOT NULL REFERENCES database_roles(id) ON DELETE CASCADE,
                granted_by INTEGER NOT NULL REFERENCES users(id),
                granted_at TIMESTAMP DEFAULT NOW(),
                expires_at TIMESTAMP,
                is_active BOOLEAN DEFAULT TRUE,
                access_conditions JSONB,
                UNIQUE(user_id, database_id, role_id)
            );
            """,

            # SQL Chat Sessions table
            """
            CREATE TABLE IF NOT EXISTS sql_chat_sessions (
                id SERIAL PRIMARY KEY,
                user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
                database_id INTEGER NOT NULL REFERENCES database_connections(id) ON DELETE CASCADE,
                session_name VARCHAR(255),
                session_description TEXT,
                created_at TIMESTAMP DEFAULT NOW(),
                updated_at TIMESTAMP DEFAULT NOW(),
                is_active BOOLEAN DEFAULT TRUE,
                total_queries INTEGER DEFAULT 0,
                successful_queries INTEGER DEFAULT 0,
                last_activity_at TIMESTAMP DEFAULT NOW()
            );
            """,

            # Query History table
            """
            CREATE TABLE IF NOT EXISTS query_history (
                id SERIAL PRIMARY KEY,
                session_id INTEGER NOT NULL REFERENCES sql_chat_sessions(id) ON DELETE CASCADE,
                user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
                database_id INTEGER NOT NULL REFERENCES database_connections(id) ON DELETE CASCADE,
                query_text TEXT NOT NULL,
                query_type VARCHAR(20) DEFAULT 'SELECT',
                execution_status VARCHAR(20) DEFAULT 'pending',
                result_count INTEGER,
                execution_time_ms INTEGER,
                error_message TEXT,
                result_preview JSONB,
                created_at TIMESTAMP DEFAULT NOW()
            );
            """,

            # Semantic Mappings table
            """
            CREATE TABLE IF NOT EXISTS semantic_mappings (
                id SERIAL PRIMARY KEY,
                database_id INTEGER NOT NULL REFERENCES database_connections(id) ON DELETE CASCADE,
                business_term VARCHAR(100) NOT NULL,
                technical_mapping TEXT NOT NULL,
                description TEXT,
                synonyms JSONB,
                example_queries JSONB,
                created_by INTEGER NOT NULL REFERENCES users(id),
                created_at TIMESTAMP DEFAULT NOW(),
                updated_at TIMESTAMP DEFAULT NOW(),
                is_active BOOLEAN DEFAULT TRUE
            );
            """
        ]

        for i, sql in enumerate(additional_tables, 1):
            table_name = sql.split('CREATE TABLE IF NOT EXISTS ')[1].split(' ')[0]
            if self.execute_sql(sql, f"Creating additional table {table_name} ({i}/{len(additional_tables)})"):
                logger.info(f"📋 Additional table {table_name} created/verified")

        return True

    def create_remaining_sql_tables(self):
        """Create remaining SQL module tables"""
        logger.info("🏗️  Creating remaining SQL module tables...")

        remaining_tables = [
            # Training Feedback table
            """
            CREATE TABLE IF NOT EXISTS training_feedback (
                id SERIAL PRIMARY KEY,
                session_id INTEGER REFERENCES sql_chat_sessions(id) ON DELETE CASCADE,
                query_id INTEGER REFERENCES query_history(id) ON DELETE CASCADE,
                user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
                feedback_type VARCHAR(20) NOT NULL,
                rating INTEGER CHECK (rating >= 1 AND rating <= 5),
                feedback_text TEXT,
                suggested_improvement TEXT,
                created_at TIMESTAMP DEFAULT NOW()
            );
            """,

            # User Preferences table
            """
            CREATE TABLE IF NOT EXISTS user_preferences (
                id SERIAL PRIMARY KEY,
                user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE UNIQUE,
                default_database_id INTEGER REFERENCES database_connections(id),
                query_timeout_seconds INTEGER DEFAULT 30,
                max_result_rows INTEGER DEFAULT 1000,
                preferred_date_format VARCHAR(20) DEFAULT 'YYYY-MM-DD',
                auto_save_queries BOOLEAN DEFAULT TRUE,
                show_query_suggestions BOOLEAN DEFAULT TRUE,
                preferences_json JSONB,
                created_at TIMESTAMP DEFAULT NOW(),
                updated_at TIMESTAMP DEFAULT NOW()
            );
            """,

            # Database Ingestion Logs table
            """
            CREATE TABLE IF NOT EXISTS database_ingestion_logs (
                id SERIAL PRIMARY KEY,
                database_id INTEGER NOT NULL REFERENCES database_connections(id) ON DELETE CASCADE,
                ingestion_type VARCHAR(20) NOT NULL,
                status VARCHAR(20) DEFAULT 'pending',
                started_by INTEGER NOT NULL REFERENCES users(id),
                started_at TIMESTAMP DEFAULT NOW(),
                completed_at TIMESTAMP,
                tables_processed INTEGER DEFAULT 0,
                columns_processed INTEGER DEFAULT 0,
                errors_encountered INTEGER DEFAULT 0,
                log_details JSONB,
                error_message TEXT
            );
            """
        ]

        for i, sql in enumerate(remaining_tables, 1):
            table_name = sql.split('CREATE TABLE IF NOT EXISTS ')[1].split(' ')[0]
            if self.execute_sql(sql, f"Creating remaining table {table_name} ({i}/{len(remaining_tables)})"):
                logger.info(f"📋 Remaining table {table_name} created/verified")

        return True

    def create_indexes_and_constraints(self):
        """Create indexes and constraints for SQL module tables"""
        logger.info("🔗 Creating indexes and constraints...")

        indexes_and_constraints = [
            # Indexes for database_connections
            "CREATE INDEX IF NOT EXISTS idx_database_connections_created_by ON database_connections(created_by);",
            "CREATE INDEX IF NOT EXISTS idx_database_connections_active ON database_connections(is_active);",
            "CREATE INDEX IF NOT EXISTS idx_database_connections_type ON database_connections(connection_type);",

            # Indexes for database_schemas
            "CREATE INDEX IF NOT EXISTS idx_database_schemas_database_id ON database_schemas(database_id);",
            "CREATE INDEX IF NOT EXISTS idx_database_schemas_table_name ON database_schemas(table_name);",
            "CREATE INDEX IF NOT EXISTS idx_database_schemas_table_type ON database_schemas(table_type);",

            # Indexes for database_columns
            "CREATE INDEX IF NOT EXISTS idx_database_columns_schema_id ON database_columns(schema_id);",
            "CREATE INDEX IF NOT EXISTS idx_database_columns_column_name ON database_columns(column_name);",
            "CREATE INDEX IF NOT EXISTS idx_database_columns_data_type ON database_columns(data_type);",
            "CREATE INDEX IF NOT EXISTS idx_database_columns_primary_key ON database_columns(is_primary_key);",

            # Indexes for user_database_access
            "CREATE INDEX IF NOT EXISTS idx_user_database_access_user_id ON user_database_access(user_id);",
            "CREATE INDEX IF NOT EXISTS idx_user_database_access_database_id ON user_database_access(database_id);",
            "CREATE INDEX IF NOT EXISTS idx_user_database_access_role_id ON user_database_access(role_id);",
            "CREATE INDEX IF NOT EXISTS idx_user_database_access_active ON user_database_access(is_active);",

            # Indexes for sql_chat_sessions
            "CREATE INDEX IF NOT EXISTS idx_sql_chat_sessions_user_id ON sql_chat_sessions(user_id);",
            "CREATE INDEX IF NOT EXISTS idx_sql_chat_sessions_database_id ON sql_chat_sessions(database_id);",
            "CREATE INDEX IF NOT EXISTS idx_sql_chat_sessions_active ON sql_chat_sessions(is_active);",
            "CREATE INDEX IF NOT EXISTS idx_sql_chat_sessions_last_activity ON sql_chat_sessions(last_activity_at);",

            # Indexes for query_history
            "CREATE INDEX IF NOT EXISTS idx_query_history_session_id ON query_history(session_id);",
            "CREATE INDEX IF NOT EXISTS idx_query_history_user_id ON query_history(user_id);",
            "CREATE INDEX IF NOT EXISTS idx_query_history_database_id ON query_history(database_id);",
            "CREATE INDEX IF NOT EXISTS idx_query_history_status ON query_history(execution_status);",
            "CREATE INDEX IF NOT EXISTS idx_query_history_created_at ON query_history(created_at);",

            # Indexes for semantic_mappings
            "CREATE INDEX IF NOT EXISTS idx_semantic_mappings_database_id ON semantic_mappings(database_id);",
            "CREATE INDEX IF NOT EXISTS idx_semantic_mappings_business_term ON semantic_mappings(business_term);",
            "CREATE INDEX IF NOT EXISTS idx_semantic_mappings_active ON semantic_mappings(is_active);",

            # Indexes for training_feedback
            "CREATE INDEX IF NOT EXISTS idx_training_feedback_session_id ON training_feedback(session_id);",
            "CREATE INDEX IF NOT EXISTS idx_training_feedback_user_id ON training_feedback(user_id);",
            "CREATE INDEX IF NOT EXISTS idx_training_feedback_type ON training_feedback(feedback_type);",

            # Indexes for database_ingestion_logs
            "CREATE INDEX IF NOT EXISTS idx_database_ingestion_logs_database_id ON database_ingestion_logs(database_id);",
            "CREATE INDEX IF NOT EXISTS idx_database_ingestion_logs_status ON database_ingestion_logs(status);",
            "CREATE INDEX IF NOT EXISTS idx_database_ingestion_logs_started_at ON database_ingestion_logs(started_at);"
        ]

        for i, sql in enumerate(indexes_and_constraints, 1):
            index_name = sql.split('idx_')[1].split(' ')[0] if 'idx_' in sql else f"constraint_{i}"
            if self.execute_sql(sql, f"Creating index/constraint {index_name} ({i}/{len(indexes_and_constraints)})"):
                logger.info(f"🔗 Index/constraint {index_name} created")

        return True

    def seed_initial_data(self):
        """Seed initial data for SQL module"""
        logger.info("🌱 Seeding initial data...")

        # Default database roles
        default_roles = [
            {
                'name': 'read_only',
                'description': 'Read-only access to database',
                'permissions': json.dumps({
                    'select': True,
                    'insert': False,
                    'update': False,
                    'delete': False,
                    'create': False,
                    'drop': False
                }),
                'can_read': True,
                'can_write': False,
                'can_delete': False,
                'can_create_tables': False,
                'can_drop_tables': False
            },
            {
                'name': 'analyst',
                'description': 'Analyst role with read access and limited write for temp tables',
                'permissions': json.dumps({
                    'select': True,
                    'insert': True,
                    'update': False,
                    'delete': False,
                    'create': True,
                    'drop': True,
                    'temp_tables_only': True
                }),
                'can_read': True,
                'can_write': True,
                'can_delete': False,
                'can_create_tables': True,
                'can_drop_tables': True
            },
            {
                'name': 'admin',
                'description': 'Full administrative access',
                'permissions': json.dumps({
                    'select': True,
                    'insert': True,
                    'update': True,
                    'delete': True,
                    'create': True,
                    'drop': True,
                    'admin': True
                }),
                'can_read': True,
                'can_write': True,
                'can_delete': True,
                'can_create_tables': True,
                'can_drop_tables': True
            }
        ]

        # Insert default roles
        for role in default_roles:
            sql = """
                INSERT INTO database_roles (name, description, permissions, can_read, can_write,
                                          can_delete, can_create_tables, can_drop_tables)
                VALUES (%(name)s, %(description)s, %(permissions)s, %(can_read)s, %(can_write)s,
                        %(can_delete)s, %(can_create_tables)s, %(can_drop_tables)s)
                ON CONFLICT (name) DO NOTHING;
            """
            try:
                cursor = self.connection.cursor()
                cursor.execute(sql, role)
                logger.info(f"✅ Seeded role: {role['name']}")
            except Exception as e:
                logger.error(f"❌ Failed to seed role {role['name']}: {e}")

        return True

    def update_existing_tables(self):
        """Update existing tables with new columns or constraints"""
        logger.info("🔄 Updating existing tables...")

        updates = [
            # Add columns to existing chat_sessions if they don't exist
            """
            DO $$
            BEGIN
                IF NOT EXISTS (
                    SELECT 1 FROM information_schema.columns
                    WHERE table_name = 'chat_sessions' AND column_name = 'vertical_id'
                ) THEN
                    ALTER TABLE chat_sessions ADD COLUMN vertical_id INTEGER;
                    ALTER TABLE chat_sessions ADD CONSTRAINT fk_chat_sessions_vertical_id
                        FOREIGN KEY (vertical_id) REFERENCES verticals(id) ON DELETE SET NULL;
                END IF;
            END $$;
            """,

            """
            DO $$
            BEGIN
                IF NOT EXISTS (
                    SELECT 1 FROM information_schema.columns
                    WHERE table_name = 'chat_sessions' AND column_name = 'department_id'
                ) THEN
                    ALTER TABLE chat_sessions ADD COLUMN department_id INTEGER;
                    ALTER TABLE chat_sessions ADD CONSTRAINT fk_chat_sessions_department_id
                        FOREIGN KEY (department_id) REFERENCES departments(id) ON DELETE SET NULL;
                END IF;
            END $$;
            """,

            # Add archived_uploader_name to files table if it doesn't exist
            """
            DO $$
            BEGIN
                IF NOT EXISTS (
                    SELECT 1 FROM information_schema.columns
                    WHERE table_name = 'files' AND column_name = 'archived_uploader_name'
                ) THEN
                    ALTER TABLE files ADD COLUMN archived_uploader_name VARCHAR(100);
                END IF;
            END $$;
            """,

            # Update files uploaded_by to allow NULL
            """
            ALTER TABLE files ALTER COLUMN uploaded_by DROP NOT NULL;
            """,

            # Add indexes for chat_sessions if they don't exist
            "CREATE INDEX IF NOT EXISTS idx_chat_sessions_vertical_id ON chat_sessions(vertical_id);",
            "CREATE INDEX IF NOT EXISTS idx_chat_sessions_department_id ON chat_sessions(department_id);",
            "CREATE INDEX IF NOT EXISTS idx_files_uploaded_by ON files(uploaded_by);"
        ]

        for i, sql in enumerate(updates, 1):
            description = f"Table update {i}/{len(updates)}"
            if self.execute_sql(sql, description):
                logger.info(f"✅ {description} completed")

        return True

    def run_full_setup(self):
        """Run complete setup process"""
        logger.info("🚀 Starting full SQL module setup...")

        if not self.connect():
            return False

        try:
            # Create all tables
            logger.info("📋 Creating SQL module tables...")
            self.create_sql_module_tables()
            self.create_additional_sql_tables()
            self.create_remaining_sql_tables()

            # Create indexes and constraints
            logger.info("🔗 Creating indexes and constraints...")
            self.create_indexes_and_constraints()

            # Update existing tables
            logger.info("🔄 Updating existing tables...")
            self.update_existing_tables()

            # Seed initial data
            logger.info("🌱 Seeding initial data...")
            self.seed_initial_data()

            logger.info("✅ Full SQL module setup completed successfully!")
            return True

        except Exception as e:
            logger.error(f"❌ Setup failed: {e}")
            return False
        finally:
            self.disconnect()


def main():
    """Main execution function"""
    parser = argparse.ArgumentParser(description='Azure SQL Database Setup for SQL Module')
    parser.add_argument('--action',
                       choices=['create_tables', 'update_tables', 'create_indexes', 'seed_data', 'full_setup'],
                       default='full_setup',
                       help='Action to perform (default: full_setup)')
    parser.add_argument('--connection-string',
                       help='PostgreSQL connection string (optional, uses env vars if not provided)')
    parser.add_argument('--verbose', '-v',
                       action='store_true',
                       help='Enable verbose logging')

    args = parser.parse_args()

    # Set logging level
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    # Initialize setup manager
    setup_manager = AzureSQLSetup(args.connection_string)

    logger.info(f"🎯 Starting action: {args.action}")

    # Connect to database
    if not setup_manager.connect():
        logger.error("❌ Failed to connect to database. Exiting.")
        sys.exit(1)

    try:
        success = True

        if args.action == 'create_tables':
            logger.info("📋 Creating SQL module tables...")
            success = (setup_manager.create_sql_module_tables() and
                      setup_manager.create_additional_sql_tables() and
                      setup_manager.create_remaining_sql_tables())

        elif args.action == 'update_tables':
            logger.info("🔄 Updating existing tables...")
            success = setup_manager.update_existing_tables()

        elif args.action == 'create_indexes':
            logger.info("🔗 Creating indexes and constraints...")
            success = setup_manager.create_indexes_and_constraints()

        elif args.action == 'seed_data':
            logger.info("🌱 Seeding initial data...")
            success = setup_manager.seed_initial_data()

        elif args.action == 'full_setup':
            logger.info("🚀 Running full setup...")
            success = setup_manager.run_full_setup()

        if success:
            logger.info(f"✅ Action '{args.action}' completed successfully!")
            print(f"\n🎉 SQL Module setup completed successfully!")
            print(f"📊 Action performed: {args.action}")
            print(f"📝 Check azure_sql_setup.log for detailed logs")
        else:
            logger.error(f"❌ Action '{args.action}' failed!")
            sys.exit(1)

    except Exception as e:
        logger.error(f"❌ Unexpected error during {args.action}: {e}")
        sys.exit(1)
    finally:
        setup_manager.disconnect()


if __name__ == "__main__":
    main()
