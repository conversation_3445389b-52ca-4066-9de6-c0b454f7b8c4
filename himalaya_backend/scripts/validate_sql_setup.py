#!/usr/bin/env python3
"""
SQL Module Setup Validation Script
=================================

This script validates that the SQL module database setup was successful.
It checks for table existence, indexes, constraints, and initial data.

Usage:
    python validate_sql_setup.py
    python validate_sql_setup.py --connection-string "postgresql://..."
"""

import os
import sys
import argparse
import logging
import psycopg2
from urllib.parse import quote

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SQLSetupValidator:
    """Validates SQL module database setup"""
    
    def __init__(self, connection_string=None):
        self.connection_string = connection_string or self._get_connection_string()
        self.connection = None
        
    def _get_connection_string(self):
        """Get connection string from environment variables"""
        db_url = os.getenv('DATABASE_URL')
        if db_url:
            return db_url
            
        db_password = os.getenv('DB_PASSWORD', 'Himalaya@1930')
        db_password = quote(db_password)
        db_host = os.getenv('DB_HOST', 'higpt-dbp2.postgres.database.azure.com')
        db_user = os.getenv('DB_USER', 'aivideoconferenceAdmin')
        db_name = os.getenv('DB_NAME', 'HiGPT_Agents_UAT')
        db_port = os.getenv('DB_PORT', '5432')
        
        return f'postgresql://{db_user}:{db_password}@{db_host}:{db_port}/{db_name}'
    
    def connect(self):
        """Establish database connection"""
        try:
            self.connection = psycopg2.connect(self.connection_string)
            logger.info("✅ Database connection successful")
            return True
        except Exception as e:
            logger.error(f"❌ Database connection failed: {e}")
            return False
    
    def validate_tables(self):
        """Validate that all required tables exist"""
        logger.info("🔍 Validating table existence...")
        
        required_tables = [
            'database_connections',
            'database_schemas', 
            'database_columns',
            'database_roles',
            'user_database_access',
            'sql_chat_sessions',
            'query_history',
            'semantic_mappings',
            'training_feedback',
            'user_preferences',
            'database_ingestion_logs'
        ]
        
        cursor = self.connection.cursor()
        missing_tables = []
        
        for table in required_tables:
            cursor.execute("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_name = %s
                );
            """, (table,))
            
            if cursor.fetchone()[0]:
                logger.info(f"✅ Table {table} exists")
            else:
                logger.error(f"❌ Table {table} missing")
                missing_tables.append(table)
        
        return len(missing_tables) == 0, missing_tables
    
    def validate_indexes(self):
        """Validate that key indexes exist"""
        logger.info("🔍 Validating index existence...")
        
        key_indexes = [
            'idx_database_connections_created_by',
            'idx_sql_chat_sessions_user_id',
            'idx_query_history_session_id',
            'idx_user_database_access_user_id'
        ]
        
        cursor = self.connection.cursor()
        missing_indexes = []
        
        for index in key_indexes:
            cursor.execute("""
                SELECT EXISTS (
                    SELECT FROM pg_indexes 
                    WHERE indexname = %s
                );
            """, (index,))
            
            if cursor.fetchone()[0]:
                logger.info(f"✅ Index {index} exists")
            else:
                logger.error(f"❌ Index {index} missing")
                missing_indexes.append(index)
        
        return len(missing_indexes) == 0, missing_indexes
    
    def validate_initial_data(self):
        """Validate that initial data was seeded"""
        logger.info("🔍 Validating initial data...")
        
        cursor = self.connection.cursor()
        
        # Check for default roles
        cursor.execute("SELECT COUNT(*) FROM database_roles;")
        role_count = cursor.fetchone()[0]
        
        if role_count >= 3:
            logger.info(f"✅ Found {role_count} database roles")
            return True
        else:
            logger.error(f"❌ Expected at least 3 roles, found {role_count}")
            return False
    
    def validate_constraints(self):
        """Validate foreign key constraints"""
        logger.info("🔍 Validating constraints...")
        
        cursor = self.connection.cursor()
        
        # Check for key foreign key constraints
        cursor.execute("""
            SELECT COUNT(*) FROM information_schema.table_constraints 
            WHERE constraint_type = 'FOREIGN KEY' 
            AND table_name IN ('database_schemas', 'database_columns', 'user_database_access', 'sql_chat_sessions');
        """)
        
        fk_count = cursor.fetchone()[0]
        
        if fk_count >= 10:
            logger.info(f"✅ Found {fk_count} foreign key constraints")
            return True
        else:
            logger.error(f"❌ Expected at least 10 FK constraints, found {fk_count}")
            return False
    
    def run_validation(self):
        """Run complete validation"""
        logger.info("🚀 Starting SQL module setup validation...")
        
        if not self.connect():
            return False
        
        try:
            all_passed = True
            
            # Validate tables
            tables_ok, missing_tables = self.validate_tables()
            if not tables_ok:
                logger.error(f"❌ Missing tables: {missing_tables}")
                all_passed = False
            
            # Validate indexes
            indexes_ok, missing_indexes = self.validate_indexes()
            if not indexes_ok:
                logger.error(f"❌ Missing indexes: {missing_indexes}")
                all_passed = False
            
            # Validate initial data
            if not self.validate_initial_data():
                all_passed = False
            
            # Validate constraints
            if not self.validate_constraints():
                all_passed = False
            
            if all_passed:
                logger.info("✅ All validations passed! SQL module setup is complete.")
                print("\n🎉 SQL Module Setup Validation: PASSED")
                print("📊 All required tables, indexes, and data are present")
                print("🔗 Foreign key constraints are properly configured")
                print("🌱 Initial data has been seeded successfully")
            else:
                logger.error("❌ Some validations failed. Check the logs above.")
                print("\n❌ SQL Module Setup Validation: FAILED")
                print("📋 Some components are missing or misconfigured")
                
            return all_passed
            
        except Exception as e:
            logger.error(f"❌ Validation failed with error: {e}")
            return False
        finally:
            if self.connection:
                self.connection.close()


def main():
    """Main execution function"""
    parser = argparse.ArgumentParser(description='Validate SQL Module Database Setup')
    parser.add_argument('--connection-string', 
                       help='PostgreSQL connection string (optional)')
    
    args = parser.parse_args()
    
    validator = SQLSetupValidator(args.connection_string)
    success = validator.run_validation()
    
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
