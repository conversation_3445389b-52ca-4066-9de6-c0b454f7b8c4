#!/bin/bash

# Azure SQL Module Setup Runner
# ============================
# 
# This script provides an easy way to run the SQL module setup
# with proper error handling and logging.
#
# Usage:
#   ./run_sql_setup.sh                    # Full setup
#   ./run_sql_setup.sh create_tables      # Create tables only
#   ./run_sql_setup.sh update_tables      # Update existing tables
#   ./run_sql_setup.sh validate           # Validate setup

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Get the directory of this script
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"

# Default action
ACTION=${1:-full_setup}

print_status "Starting SQL Module Setup..."
print_status "Action: $ACTION"
print_status "Script directory: $SCRIPT_DIR"

# Check if Python is available
if ! command -v python3 &> /dev/null; then
    print_error "Python 3 is not installed or not in PATH"
    exit 1
fi

# Check if required files exist
if [ ! -f "$SCRIPT_DIR/azure_sql_setup.py" ]; then
    print_error "azure_sql_setup.py not found in $SCRIPT_DIR"
    exit 1
fi

# Install requirements if needed
if [ -f "$SCRIPT_DIR/setup_requirements.txt" ]; then
    print_status "Installing/checking requirements..."
    pip3 install -r "$SCRIPT_DIR/setup_requirements.txt" --quiet
    print_success "Requirements satisfied"
fi

# Set up logging
LOG_FILE="$SCRIPT_DIR/sql_setup_$(date +%Y%m%d_%H%M%S).log"
print_status "Logging to: $LOG_FILE"

# Run the appropriate action
case $ACTION in
    "validate")
        print_status "Running validation..."
        if python3 "$SCRIPT_DIR/validate_sql_setup.py" 2>&1 | tee "$LOG_FILE"; then
            print_success "Validation completed successfully!"
        else
            print_error "Validation failed. Check $LOG_FILE for details."
            exit 1
        fi
        ;;
    "full_setup"|"create_tables"|"update_tables"|"create_indexes"|"seed_data")
        print_status "Running SQL setup with action: $ACTION"
        if python3 "$SCRIPT_DIR/azure_sql_setup.py" --action "$ACTION" --verbose 2>&1 | tee "$LOG_FILE"; then
            print_success "Setup completed successfully!"
            
            # Run validation after setup
            print_status "Running post-setup validation..."
            if python3 "$SCRIPT_DIR/validate_sql_setup.py" 2>&1 | tee -a "$LOG_FILE"; then
                print_success "Validation passed! Setup is complete."
            else
                print_warning "Setup completed but validation failed. Check $LOG_FILE for details."
            fi
        else
            print_error "Setup failed. Check $LOG_FILE for details."
            exit 1
        fi
        ;;
    *)
        print_error "Unknown action: $ACTION"
        echo "Available actions:"
        echo "  full_setup     - Complete setup (default)"
        echo "  create_tables  - Create tables only"
        echo "  update_tables  - Update existing tables"
        echo "  create_indexes - Create indexes only"
        echo "  seed_data      - Seed initial data only"
        echo "  validate       - Validate existing setup"
        exit 1
        ;;
esac

print_success "All operations completed!"
print_status "Log file: $LOG_FILE"
