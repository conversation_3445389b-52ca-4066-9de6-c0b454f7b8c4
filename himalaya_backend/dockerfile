FROM python:3.12-slim

# Set Debian repositories to ensure compatibility
RUN echo "deb https://deb.debian.org/debian bookworm main" > /etc/apt/sources.list && \
    echo "deb https://deb.debian.org/debian-security bookworm-security main" >> /etc/apt/sources.list && \
    echo "deb https://deb.debian.org/debian bookworm-updates main" >> /etc/apt/sources.list

# Install required system dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    unoconv \
    libreoffice-writer \
    libreoffice-common \
    fonts-liberation \
    ffmpeg \
    build-essential \
    && rm -rf /var/lib/apt/lists/*

# Set the working directory
WORKDIR /app/src

# Copy the requirements file
COPY src/requirements.txt .

# Install Python dependencies
RUN pip install --upgrade pip && \
    pip install --no-cache-dir --force-reinstall -r requirements.txt

# Copy the .env file
COPY src/.env .env

# Copy the rest of the application code
COPY . ..

# Create a temp directory for intermediate processing
RUN mkdir -p /app/src/temp && \
    chmod 777 /app/src/temp

# Configure LibreOffice's headless mode for background processing
RUN mkdir -p /var/run/soffice && \
    chmod 777 /var/run/soffice

# Expose the Flask application port
EXPOSE 5001

# Set the FLASK_APP environment variable
ENV FLASK_APP=app.py

# Start the services: LibreOffice in headless mode and Flask app
CMD ["sh", "-c", "soffice --headless --accept='socket,host=127.0.0.1,port=8100;urp;' & flask run --host=0.0.0.0 --port=5001"]
