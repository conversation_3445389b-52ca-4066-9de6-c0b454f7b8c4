#!/usr/bin/env python3
"""
Vector Index Cleanup Script for Himalaya Backend

This script provides various options to clean the vector-1742884738327 index.
Use with caution as this will delete search data.

Usage:
    python clean_vector_index.py --help
    python clean_vector_index.py --delete-all --confirm
    python clean_vector_index.py --delete-by-file-ids 123,456,789
    python clean_vector_index.py --delete-older-than 2024-01-01
    python clean_vector_index.py --count-documents
    python clean_vector_index.py --delete-all --confirm --include-database
"""

import os
import sys
import argparse
import logging
from datetime import datetime, timezone
from typing import List, Optional
import traceback

# Add the src directory to the path
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), 'src'))

from azure.search.documents import SearchClient
from azure.core.credentials import AzureKeyCredential
from azure.core.exceptions import ResourceNotFoundError

from config.settings import (
    AZURE_SEARCH_SERVICE_ENDPOINT,
    AZURE_SEARCH_ADMIN_KEY,
    AZURE_SEARCH_ENHANCED_INDEX_NAME
)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class VectorIndexCleaner:
    """
    A utility class to clean the vector search index.
    """
    
    def __init__(self, include_database: bool = False):
        """Initialize the vector index cleaner."""
        try:
            self.include_database = include_database
            self.index_name = AZURE_SEARCH_ENHANCED_INDEX_NAME
            credential = AzureKeyCredential(AZURE_SEARCH_ADMIN_KEY)
            self.search_client = SearchClient(
                endpoint=AZURE_SEARCH_SERVICE_ENDPOINT,
                index_name=self.index_name,
                credential=credential
            )
            logger.info(f"✅ Connected to Azure Search index: {self.index_name}")
            
            # Initialize database connection if needed
            if self.include_database:
                self._init_database()
                
        except Exception as e:
            logger.error(f"❌ Failed to connect to Azure Search: {str(e)}")
            raise
    
    def _init_database(self):
        """Initialize database connection for agentic table operations."""
        try:
            from sqlalchemy import create_engine, text
            from config.settings import DATABASE_URL
            
            self.db_engine = create_engine(DATABASE_URL)
            logger.info(f"✅ Connected to database for agentic table operations")
        except Exception as e:
            logger.error(f"❌ Failed to connect to database: {str(e)}")
            raise
    
    def count_documents(self) -> int:
        """
        Count total documents in the index.
        
        Returns:
            Number of documents
        """
        try:
            results = self.search_client.search(
                search_text="*",
                include_total_count=True,
                top=0  # We only want the count
            )
            count = results.get_count()
            logger.info(f"📊 Total documents in index '{self.index_name}': {count}")
            return count
        except Exception as e:
            logger.error(f"❌ Error counting documents: {str(e)}")
            return 0
    
    def count_database_records(self) -> dict:
        """
        Count records in agentic database tables.
        
        Returns:
            Dictionary with table names and record counts
        """
        if not self.include_database:
            return {}
            
        try:
            from sqlalchemy import text
            
            tables = ['excel_files', 'csv_files', 'csv_embeddings']
            counts = {}
            
            with self.db_engine.connect() as conn:
                for table in tables:
                    result = conn.execute(text(f"SELECT COUNT(*) FROM {table}"))
                    count = result.scalar()
                    counts[table] = count
                    logger.info(f"📊 Records in {table}: {count}")
            
            return counts
        except Exception as e:
            logger.error(f"❌ Error counting database records: {str(e)}")
            return {}
    
    def get_document_sample(self, count: int = 5) -> List[dict]:
        """
        Get a sample of documents from the index.
        
        Args:
            count: Number of sample documents to retrieve
            
        Returns:
            List of sample documents
        """
        try:
            results = self.search_client.search(
                search_text="*",
                top=count,
                select=['chunk_id', 'parent_id', 'title', 'metadata_storage_name']
            )
            
            samples = []
            for result in results:
                samples.append({
                    'chunk_id': result.get('chunk_id'),
                    'parent_id': result.get('parent_id'),
                    'title': result.get('title', '')[:100] + '...' if len(result.get('title', '')) > 100 else result.get('title', ''),
                    'metadata_storage_name': result.get('metadata_storage_name')
                })
            
            logger.info(f"📋 Retrieved {len(samples)} sample documents")
            return samples
            
        except Exception as e:
            logger.error(f"❌ Error retrieving sample documents: {str(e)}")
            return []
    
    def delete_all_documents(self, confirm: bool = False) -> bool:
        """
        Delete ALL documents from the index and optionally from database tables.
        
        Args:
            confirm: Must be True to actually perform deletion
            
        Returns:
            True if successful
        """
        if not confirm:
            logger.warning("⚠️  Deletion not confirmed. Use --confirm flag to actually delete.")
            return False
        
        try:
            # Delete from Azure Search index
            index_success = self._delete_all_from_index()
            
            # Delete from database if enabled
            database_success = True
            if self.include_database:
                database_success = self._delete_all_from_database()
            
            return index_success and database_success
            
        except Exception as e:
            logger.error(f"❌ Error deleting all documents: {str(e)}")
            logger.error(traceback.format_exc())
            return False
    
    def _delete_all_from_index(self) -> bool:
        """Delete all documents from Azure Search index."""
        try:
            logger.warning(f"🚨 DELETING ALL DOCUMENTS from index '{self.index_name}'")
            
            # Get all document IDs in batches
            all_doc_ids = []
            skip = 0
            batch_size = 1000
            
            while True:
                results = self.search_client.search(
                    search_text="*",
                    select=['chunk_id'],
                    top=batch_size,
                    skip=skip
                )
                
                batch_ids = [result['chunk_id'] for result in results]
                if not batch_ids:
                    break
                
                all_doc_ids.extend(batch_ids)
                skip += batch_size
                
                logger.info(f"📥 Collected {len(all_doc_ids)} document IDs...")
            
            if not all_doc_ids:
                logger.info("✅ No documents found to delete from index")
                return True
            
            # Delete in batches (Azure Search has batch size limits)
            deleted_count = 0
            batch_size = 1000  # Azure Search batch limit
            
            for i in range(0, len(all_doc_ids), batch_size):
                batch = all_doc_ids[i:i + batch_size]
                delete_docs = [{'chunk_id': doc_id, '@search.action': 'delete'} for doc_id in batch]
                
                try:
                    result = self.search_client.upload_documents(delete_docs)
                    successful_deletes = sum(1 for r in result if r.succeeded)
                    deleted_count += successful_deletes
                    
                    logger.info(f"🗑️  Deleted batch {i//batch_size + 1}: {successful_deletes}/{len(batch)} documents")
                    
                except Exception as batch_error:
                    logger.error(f"❌ Error deleting batch {i//batch_size + 1}: {str(batch_error)}")
            
            logger.info(f"✅ Total documents deleted from index: {deleted_count}/{len(all_doc_ids)}")
            return deleted_count == len(all_doc_ids)
            
        except Exception as e:
            logger.error(f"❌ Error deleting from index: {str(e)}")
            return False
    
    def _delete_all_from_database(self) -> bool:
        """Delete all records from agentic database tables."""
        try:
            from sqlalchemy import text
            
            logger.warning(f"🚨 DELETING ALL RECORDS from agentic database tables")
            
            # Tables to clean (in order due to foreign key constraints)
            tables_to_clean = [
                'csv_embeddings',  # Delete first (has foreign keys)
                'csv_files',       # Delete second (has foreign keys)
                'excel_files'      # Delete last (referenced by others)
            ]
            
            total_deleted = 0
            
            with self.db_engine.connect() as conn:
                # Start transaction
                trans = conn.begin()
                
                try:
                    for table in tables_to_clean:
                        # Count records before deletion
                        count_result = conn.execute(text(f"SELECT COUNT(*) FROM {table}"))
                        record_count = count_result.scalar()
                        
                        if record_count > 0:
                            # Delete all records
                            delete_result = conn.execute(text(f"DELETE FROM {table}"))
                            deleted_count = delete_result.rowcount
                            total_deleted += deleted_count
                            
                            logger.info(f"🗑️  Deleted {deleted_count} records from {table}")
                        else:
                            logger.info(f"ℹ️  No records found in {table}")
                    
                    # Commit transaction
                    trans.commit()
                    logger.info(f"✅ Total database records deleted: {total_deleted}")
                    return True
                    
                except Exception as e:
                    # Rollback on error
                    trans.rollback()
                    logger.error(f"❌ Database deletion failed, rolled back: {str(e)}")
                    return False
            
        except Exception as e:
            logger.error(f"❌ Error deleting from database: {str(e)}")
            return False
    
    def delete_by_file_ids(self, file_ids: List[int]) -> bool:
        """
        Delete documents for specific file IDs from index and database.
        
        Args:
            file_ids: List of file IDs to delete
            
        Returns:
            True if successful
        """
        try:
            # Delete from Azure Search index
            index_success = self._delete_file_from_index(file_ids)
            
            # Delete from database if enabled
            database_success = True
            if self.include_database:
                database_success = self._delete_file_from_database(file_ids)
            
            return index_success and database_success
            
        except Exception as e:
            logger.error(f"❌ Error deleting by file IDs: {str(e)}")
            return False
    
    def _delete_file_from_index(self, file_ids: List[int]) -> bool:
        """Delete documents for specific file IDs from Azure Search index."""
        try:
            total_deleted = 0
            
            for file_id in file_ids:
                logger.info(f"🗑️  Deleting index documents for file ID: {file_id}")
                
                # Search for documents with this parent_id
                results = self.search_client.search(
                    search_text="*",
                    filter=f"parent_id eq 'file_{file_id}'",
                    select=['chunk_id'],
                    top=1000  # Adjust if you have files with more chunks
                )
                
                doc_ids = [result['chunk_id'] for result in results]
                
                if doc_ids:
                    delete_docs = [{'chunk_id': doc_id, '@search.action': 'delete'} for doc_id in doc_ids]
                    result = self.search_client.upload_documents(delete_docs)
                    
                    successful_deletes = sum(1 for r in result if r.succeeded)
                    total_deleted += successful_deletes
                    
                    logger.info(f"✅ Deleted {successful_deletes}/{len(doc_ids)} index documents for file {file_id}")
                else:
                    logger.info(f"ℹ️  No index documents found for file {file_id}")
            
            logger.info(f"🎯 Total index documents deleted: {total_deleted}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Error deleting from index by file IDs: {str(e)}")
            return False
    
    def _delete_file_from_database(self, file_ids: List[int]) -> bool:
        """Delete database records for specific file IDs."""
        try:
            from sqlalchemy import text
            
            total_deleted = 0
            
            with self.db_engine.connect() as conn:
                trans = conn.begin()
                
                try:
                    for file_id in file_ids:
                        logger.info(f"🗑️  Deleting database records for file ID: {file_id}")
                        
                        # Delete from csv_embeddings first (foreign key constraints)
                        csv_embeddings_deleted = conn.execute(text("""
                            DELETE FROM csv_embeddings 
                            WHERE csv_id IN (
                                SELECT csv_id FROM csv_files WHERE original_file_id = :file_id
                            )
                        """), {"file_id": file_id}).rowcount
                        
                        # Delete from csv_files
                        csv_files_deleted = conn.execute(text("""
                            DELETE FROM csv_files WHERE original_file_id = :file_id
                        """), {"file_id": file_id}).rowcount
                        
                        # Delete from excel_files
                        excel_files_deleted = conn.execute(text("""
                            DELETE FROM excel_files WHERE original_file_id = :file_id
                        """), {"file_id": file_id}).rowcount
                        
                        file_total = csv_embeddings_deleted + csv_files_deleted + excel_files_deleted
                        total_deleted += file_total
                        
                        if file_total > 0:
                            logger.info(f"✅ Deleted {file_total} database records for file {file_id} "
                                      f"(embeddings: {csv_embeddings_deleted}, csv: {csv_files_deleted}, excel: {excel_files_deleted})")
                        else:
                            logger.info(f"ℹ️  No database records found for file {file_id}")
                    
                    trans.commit()
                    logger.info(f"🎯 Total database records deleted: {total_deleted}")
                    return True
                    
                except Exception as e:
                    trans.rollback()
                    logger.error(f"❌ Database deletion failed, rolled back: {str(e)}")
                    return False
            
        except Exception as e:
            logger.error(f"❌ Error deleting from database by file IDs: {str(e)}")
            return False
    
    def delete_by_metadata_pattern(self, pattern: str) -> bool:
        """
        Delete documents matching a metadata pattern.
        
        Args:
            pattern: Pattern to match in metadata_storage_name
            
        Returns:
            True if successful
        """
        try:
            logger.info(f"🔍 Searching for documents with metadata pattern: {pattern}")
            
            # Use search.ismatch for pattern matching
            filter_expression = f"search.ismatch('{pattern}', 'metadata_storage_name')"
            
            results = self.search_client.search(
                search_text="*",
                filter=filter_expression,
                select=['chunk_id', 'metadata_storage_name'],
                top=1000
            )
            
            doc_ids = []
            for result in results:
                doc_ids.append(result['chunk_id'])
                logger.info(f"📄 Found: {result.get('metadata_storage_name')}")
            
            if doc_ids:
                delete_docs = [{'chunk_id': doc_id, '@search.action': 'delete'} for doc_id in doc_ids]
                result = self.search_client.upload_documents(delete_docs)
                
                successful_deletes = sum(1 for r in result if r.succeeded)
                logger.info(f"✅ Deleted {successful_deletes}/{len(doc_ids)} documents matching pattern")
                return successful_deletes == len(doc_ids)
            else:
                logger.info(f"ℹ️  No documents found matching pattern: {pattern}")
                return True
                
        except Exception as e:
            logger.error(f"❌ Error deleting by pattern: {str(e)}")
            return False
    
    def cleanup(self):
        """Clean up database connections."""
        if hasattr(self, 'db_engine'):
            self.db_engine.dispose()

def main():
    """Main function to handle command line arguments."""
    parser = argparse.ArgumentParser(
        description='Clean Vector Index - Himalaya Backend',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Count documents (index only)
  python clean_vector_index.py --count-documents
  
  # Count documents and database records
  python clean_vector_index.py --count-documents --include-database
  
  # Show sample documents
  python clean_vector_index.py --show-sample
  
  # Delete specific files (index only)
  python clean_vector_index.py --delete-by-file-ids 123,456,789
  
  # Delete specific files (index + database)
  python clean_vector_index.py --delete-by-file-ids 123,456,789 --include-database
  
  # Delete all documents (index only - DANGEROUS!)
  python clean_vector_index.py --delete-all --confirm
  
  # Delete all documents and database records (VERY DANGEROUS!)
  python clean_vector_index.py --delete-all --confirm --include-database
  
  # Delete by file name pattern
  python clean_vector_index.py --delete-by-pattern "test_document"
        """
    )
    
    parser.add_argument('--count-documents', action='store_true',
                        help='Count total documents in the index')
    
    parser.add_argument('--show-sample', action='store_true',
                        help='Show sample documents from the index')
    
    parser.add_argument('--delete-all', action='store_true',
                        help='Delete ALL documents from the index (requires --confirm)')
    
    parser.add_argument('--delete-by-file-ids', type=str,
                        help='Delete documents for specific file IDs (comma-separated)')
    
    parser.add_argument('--delete-by-pattern', type=str,
                        help='Delete documents matching metadata pattern')
    
    parser.add_argument('--confirm', action='store_true',
                        help='Confirm destructive operations')
    
    parser.add_argument('--include-database', action='store_true',
                        help='Include agentic database tables in operations (csv_files, excel_files, csv_embeddings)')
    
    args = parser.parse_args()
    
    if not any([args.count_documents, args.show_sample, args.delete_all, 
                args.delete_by_file_ids, args.delete_by_pattern]):
        parser.print_help()
        return
    
    try:
        cleaner = VectorIndexCleaner(include_database=args.include_database)
        
        if args.count_documents:
            count = cleaner.count_documents()
            print(f"\n📊 Index '{cleaner.index_name}' contains {count} documents")
            
            if args.include_database:
                db_counts = cleaner.count_database_records()
                if db_counts:
                    print(f"\n📊 Database table counts:")
                    for table, count in db_counts.items():
                        print(f"   {table}: {count} records")
        
        if args.show_sample:
            samples = cleaner.get_document_sample(10)
            print(f"\n📋 Sample documents from index '{cleaner.index_name}':")
            for i, doc in enumerate(samples, 1):
                print(f"{i:2d}. ID: {doc['chunk_id']}")
                print(f"    Parent: {doc['parent_id']}")
                print(f"    Title: {doc['title']}")
                print(f"    File: {doc['metadata_storage_name']}")
                print()
        
        if args.delete_by_file_ids:
            file_ids = [int(fid.strip()) for fid in args.delete_by_file_ids.split(',')]
            print(f"\n🗑️  Deleting documents for file IDs: {file_ids}")
            success = cleaner.delete_by_file_ids(file_ids)
            if success:
                print("✅ Deletion completed successfully")
            else:
                print("❌ Deletion failed")
        
        if args.delete_by_pattern:
            print(f"\n🔍 Deleting documents matching pattern: {args.delete_by_pattern}")
            success = cleaner.delete_by_metadata_pattern(args.delete_by_pattern)
            if success:
                print("✅ Pattern deletion completed successfully")
            else:
                print("❌ Pattern deletion failed")
        
        if args.delete_all:
            if not args.confirm:
                warning_msg = "This will delete ALL documents from the index"
                if args.include_database:
                    warning_msg += " AND all records from agentic database tables"
                print(f"\n⚠️  WARNING: {warning_msg}!")
                print("Use --confirm flag to proceed with deletion.")
                return
            
            danger_msg = f"Deleting ALL documents from index '{cleaner.index_name}'"
            if args.include_database:
                danger_msg += " and ALL records from agentic database tables"
            print(f"\n🚨 DANGER: {danger_msg}")
            print("This action cannot be undone!")
            
            confirm_input = input("Type 'DELETE ALL' to confirm: ")
            if confirm_input == 'DELETE ALL':
                success = cleaner.delete_all_documents(confirm=True)
                if success:
                    success_msg = "All documents deleted successfully"
                    if args.include_database:
                        success_msg += " (index + database)"
                    print(f"✅ {success_msg}")
                else:
                    print("❌ Deletion failed")
            else:
                print("❌ Deletion cancelled")
    
    except Exception as e:
        logger.error(f"❌ Script failed: {str(e)}")
        logger.error(traceback.format_exc())
        sys.exit(1)
    finally:
        if 'cleaner' in locals():
            cleaner.cleanup()

if __name__ == "__main__":
    main() 