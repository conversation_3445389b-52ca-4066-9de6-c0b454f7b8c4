#!/usr/bin/env python3
"""
Emergency Temporary File Cleanup Script for Himalaya Backend

This script identifies and removes orphaned temporary files that may be causing disk space issues.
It specifically targets files created by the enhanced processing service and CSV agents.

Usage:
    python cleanup_temp_files.py --dry-run    # Preview what would be deleted
    python cleanup_temp_files.py --execute    # Actually delete the files
    python cleanup_temp_files.py --force      # Force delete all temp files (use with caution)
"""

import os
import tempfile
import time
import argparse
import glob
import shutil
import logging
from pathlib import Path
from typing import List, Tuple, Dict
from datetime import datetime, timedelta

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class TempFileCleanup:
    def __init__(self):
        self.temp_dir = tempfile.gettempdir()
        self.cleanup_stats = {
            'files_found': 0,
            'files_deleted': 0,
            'space_freed_mb': 0,
            'errors': []
        }
        
    def get_disk_usage(self) -> Dict[str, float]:
        """Get current disk usage statistics."""
        try:
            total, used, free = shutil.disk_usage(self.temp_dir)
            return {
                'total_gb': total / (1024**3),
                'used_gb': used / (1024**3),
                'free_gb': free / (1024**3),
                'used_percent': (used / total) * 100
            }
        except Exception as e:
            logger.error(f"Error getting disk usage: {e}")
            return {}
    
    def find_orphaned_temp_files(self, older_than_hours: int = 1) -> List[Tuple[str, float]]:
        """
        Find orphaned temporary files that are likely safe to delete.
        
        Args:
            older_than_hours: Only consider files older than this many hours
            
        Returns:
            List of (file_path, size_mb) tuples
        """
        cutoff_time = time.time() - (older_than_hours * 3600)
        temp_files = []
        
        # Common patterns for temporary files created by the system
        patterns = [
            f"{self.temp_dir}/tmp*",  # Standard temp files
            f"{self.temp_dir}/temp*", # Temp files
            f"{self.temp_dir}/*temp*", # Files with temp in name
            # Windows temp files
            "C:/Users/<USER>/AppData/Local/Temp/tmp*",
            "C:/Users/<USER>/AppData/Local/Temp/temp*",
            # Specific patterns from our system
            f"{self.temp_dir}/*.xlsx",
            f"{self.temp_dir}/*.csv", 
            f"{self.temp_dir}/*.pdf",
            f"{self.temp_dir}/*.xls",
        ]
        
        for pattern in patterns:
            try:
                for file_path in glob.glob(pattern):
                    if os.path.isfile(file_path):
                        try:
                            # Check if file is old enough
                            file_mtime = os.path.getmtime(file_path)
                            if file_mtime < cutoff_time:
                                file_size = os.path.getsize(file_path) / (1024 * 1024)  # MB
                                temp_files.append((file_path, file_size))
                                logger.debug(f"Found temp file: {file_path} ({file_size:.2f} MB)")
                        except (OSError, IOError) as e:
                            logger.warning(f"Could not check file {file_path}: {e}")
                            continue
            except Exception as e:
                logger.warning(f"Error processing pattern {pattern}: {e}")
                continue
        
        # Sort by size (largest first)
        temp_files.sort(key=lambda x: x[1], reverse=True)
        self.cleanup_stats['files_found'] = len(temp_files)
        
        return temp_files
    
    def find_large_temp_files(self, min_size_mb: float = 10) -> List[Tuple[str, float]]:
        """Find large temporary files that might be consuming significant space."""
        large_files = []
        
        try:
            for root, dirs, files in os.walk(self.temp_dir):
                for file in files:
                    file_path = os.path.join(root, file)
                    try:
                        size_mb = os.path.getsize(file_path) / (1024 * 1024)
                        if size_mb >= min_size_mb:
                            large_files.append((file_path, size_mb))
                    except (OSError, IOError):
                        continue
        except Exception as e:
            logger.error(f"Error scanning for large files: {e}")
        
        # Sort by size (largest first)
        large_files.sort(key=lambda x: x[1], reverse=True)
        return large_files
    
    def is_safe_to_delete(self, file_path: str) -> bool:
        """
        Check if a file is safe to delete (basic safety checks).
        
        Args:
            file_path: Path to the file
            
        Returns:
            True if safe to delete, False otherwise
        """
        try:
            # Don't delete if file is very new (less than 10 minutes old)
            if time.time() - os.path.getmtime(file_path) < 600:
                return False
            
            # Don't delete system files or important directories
            dangerous_paths = [
                '/etc/', '/usr/', '/bin/', '/sbin/', '/lib/', '/var/log/',
                'C:\\Windows\\', 'C:\\Program Files\\', 'C:\\System32\\'
            ]
            
            for dangerous in dangerous_paths:
                if file_path.startswith(dangerous):
                    return False
            
            # Don't delete files that are currently open (basic check)
            try:
                with open(file_path, 'r+b') as f:
                    pass  # If we can open for writing, it's probably not in use
            except (IOError, OSError, PermissionError):
                # File might be in use, don't delete
                return False
            
            return True
            
        except Exception as e:
            logger.warning(f"Error checking if {file_path} is safe to delete: {e}")
            return False
    
    def delete_file(self, file_path: str, file_size_mb: float) -> bool:
        """
        Safely delete a single file.
        
        Args:
            file_path: Path to the file to delete
            file_size_mb: Size of the file in MB
            
        Returns:
            True if deleted successfully, False otherwise
        """
        try:
            if self.is_safe_to_delete(file_path):
                os.unlink(file_path)
                self.cleanup_stats['files_deleted'] += 1
                self.cleanup_stats['space_freed_mb'] += file_size_mb
                logger.info(f"✅ Deleted: {file_path} ({file_size_mb:.2f} MB)")
                return True
            else:
                logger.warning(f"⚠️ Skipped (safety check failed): {file_path}")
                return False
        except Exception as e:
            error_msg = f"❌ Failed to delete {file_path}: {e}"
            logger.error(error_msg)
            self.cleanup_stats['errors'].append(error_msg)
            return False
    
    def cleanup_temp_files(self, dry_run: bool = True, older_than_hours: int = 1) -> Dict[str, any]:
        """
        Main cleanup function.
        
        Args:
            dry_run: If True, only show what would be deleted
            older_than_hours: Only delete files older than this many hours
            
        Returns:
            Cleanup statistics
        """
        logger.info(f"🧹 TEMP FILE CLEANUP: Starting cleanup (dry_run={dry_run})")
        
        # Get initial disk usage
        initial_usage = self.get_disk_usage()
        logger.info(f"💾 INITIAL DISK USAGE: {initial_usage.get('used_percent', 0):.1f}% used, {initial_usage.get('free_gb', 0):.2f} GB free")
        
        # Find orphaned temp files
        logger.info(f"🔍 Searching for temporary files older than {older_than_hours} hours...")
        temp_files = self.find_orphaned_temp_files(older_than_hours)
        
        if not temp_files:
            logger.info("✅ No orphaned temporary files found")
            return self.cleanup_stats
        
        logger.info(f"📋 Found {len(temp_files)} temporary files")
        
        # Show summary of what will be deleted
        total_size_mb = sum(size for _, size in temp_files)
        logger.info(f"📊 Total size to clean: {total_size_mb:.2f} MB ({total_size_mb/1024:.2f} GB)")
        
        # Show top 10 largest files
        logger.info("📋 Top 10 largest temporary files:")
        for i, (file_path, size_mb) in enumerate(temp_files[:10]):
            logger.info(f"   {i+1:2d}. {file_path} ({size_mb:.2f} MB)")
        
        if dry_run:
            logger.info("🔍 DRY RUN: No files will be deleted. Use --execute to actually delete files.")
            return self.cleanup_stats
        
        # Actually delete files
        logger.info("🗑️  Starting file deletion...")
        for file_path, size_mb in temp_files:
            self.delete_file(file_path, size_mb)
        
        # Get final disk usage
        final_usage = self.get_disk_usage()
        logger.info(f"💾 FINAL DISK USAGE: {final_usage.get('used_percent', 0):.1f}% used, {final_usage.get('free_gb', 0):.2f} GB free")
        
        # Print summary
        logger.info(f"✅ CLEANUP COMPLETE:")
        logger.info(f"   Files found: {self.cleanup_stats['files_found']}")
        logger.info(f"   Files deleted: {self.cleanup_stats['files_deleted']}")
        logger.info(f"   Space freed: {self.cleanup_stats['space_freed_mb']:.2f} MB ({self.cleanup_stats['space_freed_mb']/1024:.2f} GB)")
        logger.info(f"   Errors: {len(self.cleanup_stats['errors'])}")
        
        if self.cleanup_stats['errors']:
            logger.warning("⚠️ Errors encountered:")
            for error in self.cleanup_stats['errors']:
                logger.warning(f"   {error}")
        
        return self.cleanup_stats

def main():
    parser = argparse.ArgumentParser(description="Clean up orphaned temporary files")
    parser.add_argument('--dry-run', action='store_true', 
                        help="Preview what would be deleted without actually deleting")
    parser.add_argument('--execute', action='store_true',
                        help="Actually delete the identified temporary files")
    parser.add_argument('--force', action='store_true',
                        help="Force delete all temp files (use with extreme caution)")
    parser.add_argument('--older-than', type=int, default=1,
                        help="Only delete files older than this many hours (default: 1)")
    parser.add_argument('--min-size', type=float, default=0,
                        help="Only delete files larger than this many MB (default: 0)")
    
    args = parser.parse_args()
    
    # Validate arguments
    if not any([args.dry_run, args.execute, args.force]):
        logger.error("❌ Must specify one of: --dry-run, --execute, or --force")
        parser.print_help()
        return 1
    
    if args.force and not args.execute:
        logger.error("❌ --force requires --execute")
        return 1
    
    # Create cleanup instance
    cleanup = TempFileCleanup()
    
    # Run cleanup
    try:
        if args.force:
            logger.warning("⚠️ FORCE MODE: Will attempt to delete ALL temporary files")
            logger.warning("⚠️ This is potentially dangerous - press Ctrl+C in next 5 seconds to cancel")
            time.sleep(5)
            older_than_hours = 0  # Delete all temp files regardless of age
        else:
            older_than_hours = args.older_than
        
        stats = cleanup.cleanup_temp_files(
            dry_run=args.dry_run,
            older_than_hours=older_than_hours
        )
        
        # Show large files that weren't cleaned up
        if args.min_size > 0:
            logger.info(f"🔍 Checking for large files (>{args.min_size} MB)...")
            large_files = cleanup.find_large_temp_files(args.min_size)
            if large_files:
                logger.info(f"📋 Found {len(large_files)} large temporary files:")
                for file_path, size_mb in large_files[:20]:  # Show top 20
                    logger.info(f"   {file_path} ({size_mb:.2f} MB)")
        
        return 0
        
    except KeyboardInterrupt:
        logger.info("❌ Cleanup cancelled by user")
        return 1
    except Exception as e:
        logger.error(f"❌ Cleanup failed: {e}")
        return 1

if __name__ == "__main__":
    exit(main()) 