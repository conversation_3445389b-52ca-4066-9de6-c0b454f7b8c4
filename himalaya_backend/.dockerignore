# Git files
.git
.gitignore
.gitattributes

# Documentation
README.md
*.md
docs/

# Development files
.vscode/
.idea/
*.swp
*.swo
*~

# Python cache
__pycache__/
*.pyc
*.pyo
*.pyd
.Python
*.py[cod]
*$py.class
.pytest_cache/

# Virtual environments
venv/
env/
ENV/
env.bak/
venv.bak/

# IDE files
.vscode/
.idea/
*.swp
*.swo

# OS files
.DS_Store
Thumbs.db
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db

# Logs
*.log
logs/
src/logs/

# Test files
test_*.py
*_test.py
tests/

# Temporary files
tmp/
temp/
*.tmp
*.temp
src/temp/

# Build artifacts
build/
dist/
*.egg-info/

# Database files
*.db
*.sqlite
*.sqlite3

# Cache directories
.cache/
.npm/
.yarn/

# Media files (if large)
*.mp4
*.avi
*.mov
*.wmv
*.flv
*.webm

# Large data files
*.xlsx
*.xls
*.csv
*.json
finance_data.xlsx
postman_json.json
src/ai_response.json
src/container_detailes.json
src/scraped_content/

# Environment files (except the one we need)
.env.local
.env.development
.env.test
.env.production

# Node modules (if any)
node_modules/

# Backup files
*.bak
*.backup

# System files
.fuse_hidden*
.directory
.Trash-*
.nfs*

# Jupyter notebooks
*.ipynb
.ipynb_checkpoints/

# Coverage reports
htmlcov/
.coverage
.coverage.*
coverage.xml
*.cover
.hypothesis/

# mypy
.mypy_cache/
.dmypy.json
dmypy.json 