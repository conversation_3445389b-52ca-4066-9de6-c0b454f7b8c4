{"id": "/subscriptions/8aea070e-f263-46f0-a0c6-55167624e5b9/resourceGroups/AI_Video_Conference_RG_phase2/providers/Microsoft.App/containerapps/aivccontainerbakendp2", "name": "aivccontainerbakendp2", "type": "Microsoft.App/containerApps", "location": "South India", "tags": {"CreatedBy": "<PERSON><PERSON><PERSON><PERSON>", "CreatedFor": "Higptp2"}, "systemData": {"createdBy": "<EMAIL>", "createdByType": "User", "createdAt": "2025-03-24T16:16:22.8286076", "lastModifiedBy": "<EMAIL>", "lastModifiedByType": "User", "lastModifiedAt": "2025-05-14T07:54:57.8698826"}, "properties": {"provisioningState": "Succeeded", "runningStatus": "Running", "managedEnvironmentId": "/subscriptions/8aea070e-f263-46f0-a0c6-55167624e5b9/resourceGroups/AI_Video_Conference_RG_phase2/providers/Microsoft.App/managedEnvironments/containerenvp2", "environmentId": "/subscriptions/8aea070e-f263-46f0-a0c6-55167624e5b9/resourceGroups/AI_Video_Conference_RG_phase2/providers/Microsoft.App/managedEnvironments/containerenvp2", "workloadProfileName": "Consumption", "patchingMode": "Automatic", "outboundIpAddresses": ["*************", "***********", "************", "*************", "*************", "*************", "*************", "*************", "************", "************", "************", "*************", "*************", "**********", "*************", "*************", "*************", "************", "*************", "***********", "*************"], "latestRevisionName": "aivccontainerbakendp2--7ryk56o", "latestReadyRevisionName": "aivccontainerbakendp2--7ryk56o", "latestRevisionFqdn": "aivccontainerbakendp2--7ryk56o.salmonbay-6d5d2866.southindia.azurecontainerapps.io", "customDomainVerificationId": "B0D45386EA43A0271EDA56314917CD6D15C16EE503971FCC62821A83A859CF0E", "configuration": {"secrets": [{"name": "reg-pswd-7fe9efe1-8768"}], "activeRevisionsMode": "Single", "targetLabel": "", "ingress": {"fqdn": "aivccontainerbakendp2.salmonbay-6d5d2866.southindia.azurecontainerapps.io", "external": true, "targetPort": 5001, "exposedPort": 0, "transport": "Auto", "traffic": [{"weight": 100, "latestRevision": true}], "customDomains": null, "allowInsecure": true, "ipSecurityRestrictions": null, "corsPolicy": {"allowedOrigins": ["*"], "allowedMethods": null, "allowedHeaders": ["*"], "exposeHeaders": null, "maxAge": 0, "allowCredentials": false}, "clientCertificateMode": "Ignore", "stickySessions": {"affinity": "sticky"}, "additionalPortMappings": null, "targetPortHttpScheme": null}, "registries": [{"server": "aivcregistry01p2.azurecr.io", "username": "aivcregistry01p2", "passwordSecretRef": "reg-pswd-7fe9efe1-8768", "identity": ""}], "identitySettings": [], "dapr": null, "runtime": null, "maxInactiveRevisions": 100, "service": null}, "template": {"revisionSuffix": "", "terminationGracePeriodSeconds": null, "containers": [{"image": "aivcregistry01p2.azurecr.io/higpt-flask:latest", "imageType": "ContainerImage", "name": "aivccontainerbakendp2", "resources": {"cpu": 0.5, "memory": "1Gi", "ephemeralStorage": "2Gi"}}], "initContainers": null, "scale": {"minReplicas": 1, "maxReplicas": 2, "cooldownPeriod": 300, "pollingInterval": 30, "rules": [{"name": "http-scaler", "http": {"metadata": {"concurrentRequests": "10"}}}]}, "volumes": null, "serviceBinds": null}, "eventStreamEndpoint": "https://southindia.azurecontainerapps.dev/subscriptions/8aea070e-f263-46f0-a0c6-55167624e5b9/resourceGroups/AI_Video_Conference_RG_phase2/containerApps/aivccontainerbakendp2/eventstream", "delegatedIdentities": []}, "identity": {"type": "None"}, "kind": "containerapps", "apiVersion": "2024-08-02-preview"}