{"id": "/subscriptions/8aea070e-f263-46f0-a0c6-55167624e5b9/resourceGroups/AI_Video_Conference_RG/providers/Microsoft.App/containerapps/aivccontainerbakend", "name": "aivccontainerbakend", "type": "Microsoft.App/containerApps", "location": "South India", "systemData": {"createdBy": "<EMAIL>", "createdByType": "User", "createdAt": "2024-12-16T08:03:17.5055707", "lastModifiedBy": "<EMAIL>", "lastModifiedByType": "User", "lastModifiedAt": "2025-05-14T09:03:30.0060692"}, "properties": {"provisioningState": "Succeeded", "runningStatus": "Running", "managedEnvironmentId": "/subscriptions/8aea070e-f263-46f0-a0c6-55167624e5b9/resourceGroups/AI_Video_Conference_RG/providers/Microsoft.App/managedEnvironments/envvccontainerapp", "environmentId": "/subscriptions/8aea070e-f263-46f0-a0c6-55167624e5b9/resourceGroups/AI_Video_Conference_RG/providers/Microsoft.App/managedEnvironments/envvccontainerapp", "workloadProfileName": "Consumption", "patchingMode": "Automatic", "outboundIpAddresses": ["*************", "***********", "************", "*************", "*************", "*************", "*************", "*************", "************", "************", "************", "*************", "*************", "**********", "*************", "*************", "*************", "************", "*************", "***********", "************"], "latestRevisionName": "aivccontainerbakend--kfcfe2v", "latestReadyRevisionName": "aivccontainerbakend--kfcfe2v", "latestRevisionFqdn": "aivccontainerbakend--kfcfe2v.mangodesert-321e63c0.southindia.azurecontainerapps.io", "customDomainVerificationId": "B0D45386EA43A0271EDA56314917CD6D15C16EE503971FCC62821A83A859CF0E", "configuration": {"secrets": [{"name": "reg-pswd-d2239eb9-aed6"}], "activeRevisionsMode": "Single", "targetLabel": "", "ingress": {"fqdn": "aivccontainerbakend.mangodesert-321e63c0.southindia.azurecontainerapps.io", "external": true, "targetPort": 5001, "exposedPort": 0, "transport": "Auto", "traffic": [{"weight": 100, "latestRevision": true}], "customDomains": null, "allowInsecure": false, "ipSecurityRestrictions": null, "corsPolicy": {"allowedOrigins": ["*"], "allowedMethods": ["*"], "allowedHeaders": ["*"], "exposeHeaders": null, "maxAge": 0, "allowCredentials": false}, "clientCertificateMode": null, "stickySessions": {"affinity": "none"}, "additionalPortMappings": null, "targetPortHttpScheme": null}, "registries": [{"server": "aivcregistry01.azurecr.io", "username": "aivcregistry01", "passwordSecretRef": "reg-pswd-d2239eb9-aed6", "identity": ""}], "identitySettings": [], "dapr": null, "runtime": null, "maxInactiveRevisions": 100, "service": null}, "template": {"revisionSuffix": "", "terminationGracePeriodSeconds": null, "containers": [{"image": "aivcregistry01.azurecr.io/higpt-flask:latest", "imageType": "ContainerImage", "name": "aivccontainerbakend", "resources": {"cpu": 4, "memory": "8Gi", "ephemeralStorage": "8Gi"}, "probes": []}], "initContainers": null, "scale": {"minReplicas": 1, "maxReplicas": 2, "cooldownPeriod": 300, "pollingInterval": 30, "rules": null}, "volumes": [], "serviceBinds": null}, "eventStreamEndpoint": "https://southindia.azurecontainerapps.dev/subscriptions/8aea070e-f263-46f0-a0c6-55167624e5b9/resourceGroups/AI_Video_Conference_RG/containerApps/aivccontainerbakend/eventstream", "delegatedIdentities": []}, "identity": {"type": "None"}, "kind": "containerapps", "apiVersion": "2024-08-02-preview"}