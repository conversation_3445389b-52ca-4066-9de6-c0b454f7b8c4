# Himalaya Enhanced Processing - Phase 2 Technical Specifications

## Document Information
- **Version**: 1.0
- **Date**: January 6, 2025
- **Status**: Implementation Complete
- **Phase**: 2 - Core Infrastructure

## Table of Contents
1. [System Architecture](#system-architecture)
2. [API Specifications](#api-specifications)
3. [Database Schema](#database-schema)
4. [Configuration Management](#configuration-management)
5. [Service Interfaces](#service-interfaces)
6. [Error Handling](#error-handling)
7. [Security Specifications](#security-specifications)
8. [Performance Requirements](#performance-requirements)

## System Architecture

### High-Level Component Diagram
```
┌─────────────────────────────────────────────────────────────────┐
│                    Himalaya Enhanced Processing                 │
├─────────────────────────────────────────────────────────────────┤
│  Frontend (React)                                               │
│  ├── Document Upload Interface                                  │
│  ├── Processing Status Dashboard                                │
│  └── Search Interface                                           │
├─────────────────────────────────────────────────────────────────┤
│  API Layer (Flask)                                              │
│  ├── Document Routes v2 (/api/v2/documents/*)                  │
│  ├── Authentication & Authorization                             │
│  └── Health Check Endpoints                                     │
├─────────────────────────────────────────────────────────────────┤
│  Service Layer                                                  │
│  ├── Enhanced Processing Service                                │
│  ├── Document Type Detector                                     │
│  ├── Azure Search Service                                       │
│  └── Text Chunking Service                                      │
├─────────────────────────────────────────────────────────────────┤
│  Data Layer                                                     │
│  ├── PostgreSQL Database                                        │
│  ├── Azure AI Search (Vector Index)                            │
│  └── File Storage                                               │
└─────────────────────────────────────────────────────────────────┘
```

### Service Dependencies
```mermaid
graph TD
    A[Enhanced Processing Service] --> B[Document Type Detector]
    A --> C[Azure Search Service]
    A --> D[Database Models]
    B --> E[File Extension Analysis]
    B --> F[MIME Type Detection]
    C --> G[Azure OpenAI Embeddings]
    C --> H[Azure AI Search Index]
    D --> I[PostgreSQL Database]
```

## API Specifications

### Base Configuration
- **Base URL**: `http://localhost:5001/api/v2`
- **Authentication**: Bearer Token (JWT)
- **Content-Type**: `application/json`
- **API Version**: v2

### Endpoint Specifications

#### 1. Document Processing

**POST /documents/process**
```http
POST /api/v2/documents/process
Content-Type: multipart/form-data
Authorization: Bearer {token}

Parameters:
- file: File (required) - Document file to process
- enhanced_processing: boolean (optional, default: true)
- chunk_size: integer (optional, default: 1000)
- chunk_overlap: integer (optional, default: 200)
```

**Response Schema**:
```json
{
  "success": true,
  "data": {
    "file_id": "integer",
    "filename": "string",
    "document_type": "string",
    "processing_status": "string",
    "metadata": {
      "page_count": "integer",
      "word_count": "integer",
      "char_count": "integer",
      "chunk_count": "integer",
      "table_count": "integer"
    },
    "azure_search_doc_ids": ["string"],
    "processing_time": "float"
  },
  "message": "string"
}
```

#### 2. Document Status

**GET /documents/{id}/status**
```http
GET /api/v2/documents/{id}/status
Authorization: Bearer {token}
```

**Response Schema**:
```json
{
  "success": true,
  "data": {
    "file_id": "integer",
    "filename": "string",
    "document_type": "string",
    "enhanced_processing_status": "string",
    "processing_metadata": {
      "page_count": "integer",
      "table_count": "integer",
      "chunk_count": "integer",
      "word_count": "integer",
      "char_count": "integer",
      "processing_time": "float",
      "created_at": "datetime",
      "updated_at": "datetime"
    }
  }
}
```

#### 3. Document Chunks

**GET /documents/{id}/chunks**
```http
GET /api/v2/documents/{id}/chunks?page=1&per_page=10&search=query
Authorization: Bearer {token}

Query Parameters:
- page: integer (default: 1)
- per_page: integer (default: 10, max: 100)
- search: string (optional) - Filter chunks by content
```

**Response Schema**:
```json
{
  "success": true,
  "data": {
    "chunks": [
      {
        "id": "integer",
        "chunk_index": "integer",
        "content": "string",
        "word_count": "integer",
        "char_count": "integer",
        "chunk_metadata": "object",
        "azure_search_doc_id": "string"
      }
    ],
    "pagination": {
      "page": "integer",
      "per_page": "integer",
      "total": "integer",
      "pages": "integer"
    }
  }
}
```

#### 4. Document Tables

**GET /documents/{id}/tables**
```http
GET /api/v2/documents/{id}/tables
Authorization: Bearer {token}
```

**Response Schema**:
```json
{
  "success": true,
  "data": {
    "tables": [
      {
        "id": "integer",
        "table_index": "integer",
        "page_number": "integer",
        "table_data": "object",
        "table_headers": ["string"],
        "table_summary": "string",
        "row_count": "integer",
        "column_count": "integer"
      }
    ]
  }
}
```

#### 5. Vector Search

**POST /documents/search**
```http
POST /api/v2/documents/search
Content-Type: application/json
Authorization: Bearer {token}

{
  "query": "string",
  "top_k": "integer (default: 5, max: 20)",
  "filters": {
    "document_type": "string",
    "file_id": "integer",
    "date_range": {
      "start": "datetime",
      "end": "datetime"
    }
  },
  "include_metadata": "boolean (default: true)"
}
```

**Response Schema**:
```json
{
  "success": true,
  "data": {
    "results": [
      {
        "chunk_id": "string",
        "content": "string",
        "score": "float",
        "metadata": {
          "file_id": "integer",
          "filename": "string",
          "document_type": "string",
          "chunk_index": "integer",
          "page_number": "integer"
        }
      }
    ],
    "query": "string",
    "total_results": "integer",
    "search_time": "float"
  }
}
```

#### 6. Batch Processing

**POST /documents/batch-process**
```http
POST /api/v2/documents/batch-process
Content-Type: multipart/form-data
Authorization: Bearer {token}

Parameters:
- files: File[] (required, max: 10) - Multiple document files
- enhanced_processing: boolean (optional, default: true)
```

**Response Schema**:
```json
{
  "success": true,
  "data": {
    "batch_id": "string",
    "total_files": "integer",
    "processed_files": [
      {
        "file_id": "integer",
        "filename": "string",
        "status": "string",
        "document_type": "string",
        "processing_time": "float"
      }
    ],
    "failed_files": [
      {
        "filename": "string",
        "error": "string"
      }
    ],
    "total_processing_time": "float"
  }
}
```

### Error Response Schema
```json
{
  "success": false,
  "error": {
    "code": "string",
    "message": "string",
    "details": "object (optional)"
  },
  "timestamp": "datetime"
}
```

## Database Schema

### Enhanced Processing Tables

#### FileProcessingMetadata
```sql
CREATE TABLE file_processing_metadata (
    id SERIAL PRIMARY KEY,
    file_id INTEGER NOT NULL REFERENCES files(id) ON DELETE CASCADE,
    document_type VARCHAR(50),
    enhanced_processing_status VARCHAR(50) DEFAULT 'pending',
    page_count INTEGER DEFAULT 0,
    table_count INTEGER DEFAULT 0,
    chunk_count INTEGER DEFAULT 0,
    word_count INTEGER DEFAULT 0,
    char_count INTEGER DEFAULT 0,
    processing_time FLOAT DEFAULT 0.0,
    processing_metadata JSONB,
    error_details TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE(file_id)
);

CREATE INDEX idx_file_processing_metadata_file_id ON file_processing_metadata(file_id);
CREATE INDEX idx_file_processing_metadata_document_type ON file_processing_metadata(document_type);
CREATE INDEX idx_file_processing_metadata_status ON file_processing_metadata(enhanced_processing_status);
```

#### DocumentChunk
```sql
CREATE TABLE document_chunks (
    id SERIAL PRIMARY KEY,
    file_id INTEGER NOT NULL REFERENCES files(id) ON DELETE CASCADE,
    chunk_index INTEGER NOT NULL,
    content TEXT NOT NULL,
    word_count INTEGER DEFAULT 0,
    char_count INTEGER DEFAULT 0,
    chunk_metadata JSONB,
    azure_search_doc_id VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE(file_id, chunk_index)
);

CREATE INDEX idx_document_chunks_file_id ON document_chunks(file_id);
CREATE INDEX idx_document_chunks_azure_search_doc_id ON document_chunks(azure_search_doc_id);
CREATE INDEX idx_document_chunks_content_gin ON document_chunks USING gin(to_tsvector('english', content));
```

#### DocumentTable
```sql
CREATE TABLE document_tables (
    id SERIAL PRIMARY KEY,
    file_id INTEGER NOT NULL REFERENCES files(id) ON DELETE CASCADE,
    table_index INTEGER NOT NULL,
    page_number INTEGER,
    table_data JSONB NOT NULL,
    table_headers JSONB,
    table_summary TEXT,
    row_count INTEGER DEFAULT 0,
    column_count INTEGER DEFAULT 0,
    azure_search_doc_id VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE(file_id, table_index)
);

CREATE INDEX idx_document_tables_file_id ON document_tables(file_id);
CREATE INDEX idx_document_tables_azure_search_doc_id ON document_tables(azure_search_doc_id);
```

### Database Relationships
```sql
-- Foreign key constraints
ALTER TABLE file_processing_metadata 
ADD CONSTRAINT fk_file_processing_metadata_file_id 
FOREIGN KEY (file_id) REFERENCES files(id) ON DELETE CASCADE;

ALTER TABLE document_chunks 
ADD CONSTRAINT fk_document_chunks_file_id 
FOREIGN KEY (file_id) REFERENCES files(id) ON DELETE CASCADE;

ALTER TABLE document_tables 
ADD CONSTRAINT fk_document_tables_file_id 
FOREIGN KEY (file_id) REFERENCES files(id) ON DELETE CASCADE;
```

## Configuration Management

### Environment Variables
```bash
# Enhanced Processing Configuration
ENHANCED_PROCESSING_ENABLED=true
INTELLIGENT_CHUNKING_ENABLED=true

# Azure AI Search Configuration
AZURE_SEARCH_SERVICE_NAME=videoconference-aisearch2
AZURE_SEARCH_API_KEY=your_api_key
AZURE_SEARCH_ENHANCED_INDEX_NAME=vector-1742884738327
AZURE_SEARCH_PRODUCTION_INDEX_NAME=vector-1742884805411

# Azure OpenAI Configuration
AZURE_OPENAI_API_KEY=your_api_key
AZURE_OPENAI_ENDPOINT=https://videoconference-openai-p2.openai.azure.com/
AZURE_OPENAI_EMBEDDING_DEPLOYMENT=text-embedding-3-large
AZURE_OPENAI_EMBEDDING_MODEL=text-embedding-3-large
AZURE_OPENAI_API_VERSION=2025-01-01-preview

# Document Processing Settings
CHUNK_SIZE=1000
CHUNK_OVERLAP=200
MIN_CHUNK_SIZE=100
MAX_CHUNK_SIZE=2000

# File Processing Limits
MAX_FILE_SIZE_MB=100
MAX_BATCH_FILES=10
SUPPORTED_FILE_EXTENSIONS=.pdf,.docx,.doc,.xlsx,.xls,.csv,.txt,.md,.html,.htm,.jpg,.jpeg,.png,.gif,.bmp,.tiff,.webp,.pptx,.ppt

# Database Configuration
DATABASE_URL=postgresql://user:password@localhost:5432/himalaya_db
```

### Configuration Classes
```python
# config/settings.py
class EnhancedProcessingConfig:
    # Core Settings
    ENHANCED_PROCESSING_ENABLED = os.getenv('ENHANCED_PROCESSING_ENABLED', 'true').lower() == 'true'
    INTELLIGENT_CHUNKING_ENABLED = os.getenv('INTELLIGENT_CHUNKING_ENABLED', 'true').lower() == 'true'
    
    # Azure Search Settings
    AZURE_SEARCH_SERVICE_NAME = os.getenv('AZURE_SEARCH_SERVICE_NAME')
    AZURE_SEARCH_API_KEY = os.getenv('AZURE_SEARCH_API_KEY')
    AZURE_SEARCH_ENHANCED_INDEX_NAME = os.getenv('AZURE_SEARCH_ENHANCED_INDEX_NAME', 'vector-1742884738327')
    AZURE_SEARCH_PRODUCTION_INDEX_NAME = os.getenv('AZURE_SEARCH_PRODUCTION_INDEX_NAME', 'vector-1742884805411')
    
    # Chunking Settings
    CHUNK_SIZE = int(os.getenv('CHUNK_SIZE', 1000))
    CHUNK_OVERLAP = int(os.getenv('CHUNK_OVERLAP', 200))
    MIN_CHUNK_SIZE = int(os.getenv('MIN_CHUNK_SIZE', 100))
    MAX_CHUNK_SIZE = int(os.getenv('MAX_CHUNK_SIZE', 2000))
    
    # Document Type Configuration
    DOCUMENT_TYPES = {
        'pdf': ['.pdf'],
        'excel': ['.xlsx', '.xls', '.csv'],
        'word': ['.docx', '.doc'],
        'powerpoint': ['.pptx', '.ppt'],
        'text': ['.txt', '.md', '.html', '.htm'],
        'image': ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff', '.webp']
    }
    
    # Processing Agent Mapping
    AGENT_MAPPING = {
        'pdf': 'pdf_processor',
        'excel': 'excel_processor',
        'word': 'text_processor',
        'powerpoint': 'text_processor',
        'text': 'text_processor',
        'image': 'image_processor',
        'unknown': 'basic_processor'
    }
```

## Service Interfaces

### Document Type Detector Interface
```python
class DocumentTypeDetector:
    def __init__(self):
        """Initialize the document type detector."""
        
    def detect_document_type(self, filename: str, mime_type: str = None) -> str:
        """
        Detect document type based on filename and MIME type.
        
        Args:
            filename: Name of the file
            mime_type: MIME type of the file (optional)
            
        Returns:
            Document type string ('pdf', 'excel', 'word', etc.)
        """
        
    def get_processing_agent(self, document_type: str) -> str:
        """
        Get the appropriate processing agent for a document type.
        
        Args:
            document_type: Type of document
            
        Returns:
            Agent name string
        """
        
    def get_supported_extensions(self) -> List[str]:
        """
        Get list of all supported file extensions.
        
        Returns:
            List of file extensions
        """
```

### Azure Search Service Interface
```python
class AzureSearchService:
    def __init__(self, index_name: str):
        """Initialize Azure Search service with specified index."""
        
    def upload_document_chunk(self, chunk_data: Dict[str, Any]) -> str:
        """
        Upload a document chunk to Azure Search.
        
        Args:
            chunk_data: Dictionary containing chunk information
            
        Returns:
            Azure Search document ID
        """
        
    def upload_table_content(self, table_data: Dict[str, Any]) -> str:
        """
        Upload table content to Azure Search.
        
        Args:
            table_data: Dictionary containing table information
            
        Returns:
            Azure Search document ID
        """
        
    def search_documents(self, query: str, filters: Dict = None, top_k: int = 5) -> List[Dict]:
        """
        Perform vector similarity search.
        
        Args:
            query: Search query string
            filters: Optional filters to apply
            top_k: Number of results to return
            
        Returns:
            List of search results
        """
        
    def delete_document(self, doc_id: str) -> bool:
        """
        Delete a document from Azure Search.
        
        Args:
            doc_id: Azure Search document ID
            
        Returns:
            Success status
        """
```

### Enhanced Processing Service Interface
```python
class EnhancedProcessingService:
    def __init__(self):
        """Initialize the enhanced processing service."""
        
    def process_file(self, file_path: str, filename: str, file_id: int) -> Dict[str, Any]:
        """
        Process a file with enhanced capabilities.
        
        Args:
            file_path: Path to the file
            filename: Original filename
            file_id: Database file ID
            
        Returns:
            Processing results dictionary
        """
        
    def chunk_text(self, text: str, chunk_size: int = None, overlap: int = None) -> List[str]:
        """
        Split text into intelligent chunks.
        
        Args:
            text: Text to chunk
            chunk_size: Size of each chunk
            overlap: Overlap between chunks
            
        Returns:
            List of text chunks
        """
        
    def get_processing_status(self, file_id: int) -> Dict[str, Any]:
        """
        Get processing status for a file.
        
        Args:
            file_id: Database file ID
            
        Returns:
            Status information dictionary
        """
```

## Error Handling

### Error Codes and Messages
```python
class ErrorCodes:
    # Document Processing Errors
    DOCUMENT_TYPE_UNSUPPORTED = "DOC_001"
    DOCUMENT_PROCESSING_FAILED = "DOC_002"
    DOCUMENT_TOO_LARGE = "DOC_003"
    DOCUMENT_CORRUPTED = "DOC_004"
    
    # Azure Search Errors
    AZURE_SEARCH_CONNECTION_FAILED = "AZ_001"
    AZURE_SEARCH_INDEX_NOT_FOUND = "AZ_002"
    AZURE_SEARCH_UPLOAD_FAILED = "AZ_003"
    AZURE_SEARCH_QUERY_FAILED = "AZ_004"
    
    # Database Errors
    DATABASE_CONNECTION_FAILED = "DB_001"
    DATABASE_QUERY_FAILED = "DB_002"
    DATABASE_CONSTRAINT_VIOLATION = "DB_003"
    
    # Authentication Errors
    AUTHENTICATION_FAILED = "AUTH_001"
    AUTHORIZATION_FAILED = "AUTH_002"
    TOKEN_EXPIRED = "AUTH_003"
    
    # Validation Errors
    INVALID_REQUEST_FORMAT = "VAL_001"
    MISSING_REQUIRED_FIELD = "VAL_002"
    INVALID_FIELD_VALUE = "VAL_003"
```

### Exception Handling Strategy
```python
class EnhancedProcessingException(Exception):
    def __init__(self, code: str, message: str, details: Dict = None):
        self.code = code
        self.message = message
        self.details = details or {}
        super().__init__(self.message)

def handle_processing_error(func):
    """Decorator for handling processing errors."""
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except EnhancedProcessingException:
            raise
        except Exception as e:
            logger.error(f"Unexpected error in {func.__name__}: {str(e)}")
            raise EnhancedProcessingException(
                code="PROC_999",
                message="Unexpected processing error",
                details={"original_error": str(e)}
            )
    return wrapper
```

## Security Specifications

### Authentication Requirements
- **JWT Token**: Required for all API endpoints
- **Token Expiration**: 24 hours
- **Refresh Token**: Available for token renewal
- **Scope-based Access**: Fine-grained permissions

### Authorization Scopes
```python
REQUIRED_SCOPES = {
    'documents:read': ['GET /documents/*'],
    'documents:write': ['POST /documents/*', 'PUT /documents/*'],
    'documents:delete': ['DELETE /documents/*'],
    'documents:search': ['POST /documents/search'],
    'documents:batch': ['POST /documents/batch-process']
}
```

### Data Protection
- **File Encryption**: Files encrypted at rest
- **Transmission Security**: HTTPS required
- **Input Validation**: Comprehensive request validation
- **SQL Injection Protection**: Parameterized queries
- **Path Traversal Protection**: Safe file operations

## Performance Requirements

### Response Time Targets
- **Document Upload**: < 5 seconds for files up to 10MB
- **Document Processing**: < 30 seconds for typical documents
- **Search Queries**: < 2 seconds for vector similarity search
- **Status Queries**: < 500ms
- **Batch Processing**: < 5 minutes for 10 documents

### Throughput Requirements
- **Concurrent Users**: Support 50 concurrent users
- **Document Processing**: 100 documents per hour
- **Search Queries**: 1000 queries per minute
- **API Requests**: 10,000 requests per hour

### Resource Limits
- **Memory Usage**: < 2GB per processing worker
- **CPU Usage**: < 80% during peak processing
- **Disk Space**: Automatic cleanup of temporary files
- **Database Connections**: Connection pooling with max 20 connections

### Scalability Considerations
- **Horizontal Scaling**: Stateless service design
- **Load Balancing**: Support for multiple service instances
- **Caching**: Redis for frequently accessed data
- **Queue Management**: Async processing for large documents

---

**Document Version**: 1.0  
**Last Updated**: January 6, 2025  
**Review Status**: ✅ Complete  
**Implementation Status**: ✅ Deployed 