# Himalaya Enhanced Processing - Phase 2 API Documentation

## Overview

Phase 2 introduces comprehensive enhanced document processing capabilities with **automatic integration** into the existing file upload workflow. When files are uploaded, they now automatically undergo enhanced processing including document type detection, intelligent chunking, and Azure AI Search integration.

## 🚀 Automatic Integration Features

### **Automatic Processing on Upload**
- **Standard Upload Endpoint** (`POST /api/files`) now automatically triggers enhanced processing
- **New Enhanced Upload Endpoint** (`POST /api/v2/documents/upload-and-process`) provides dedicated enhanced upload with detailed processing feedback
- **Seamless Integration** - No changes required to existing frontend code
- **Backward Compatibility** - All existing functionality preserved

### **Processing Flow**
```
File Upload → Azure Blob Storage → Database Record → Enhanced Processing → Azure AI Search → Complete
```

### **Enhanced Processing Includes**
- ✅ **Document Type Detection** - Automatic identification of PDF, Excel, Word, PowerPoint, Text, Images
- ✅ **Intelligent Text Chunking** - 1000 characters with 200 overlap for optimal search
- ✅ **Vector Embeddings** - Azure OpenAI text-embedding-3-large (3072 dimensions)
- ✅ **Azure AI Search Integration** - Upload to vector index `vector-1742884738327`
- ✅ **Metadata Extraction** - Comprehensive document analysis and storage
- ✅ **Error Handling** - Graceful fallbacks with detailed error reporting

---

## 📋 API Endpoints Overview

### **Upload Endpoints (with Automatic Processing)**

#### 1. **Enhanced Upload and Process** (New)
```http
POST /api/v2/documents/upload-and-process
```
**Purpose**: Upload files with dedicated enhanced processing and detailed feedback

#### 2. **Standard Upload** (Modified)
```http
POST /api/files
```
**Purpose**: Standard file upload now with automatic enhanced processing

### **Enhanced Processing Management**

#### 3. **Process Existing Document**
```http
POST /api/v2/documents/process
```

#### 4. **Force Reprocess Document**
```http
POST /api/v2/documents/{id}/reprocess
```

#### 5. **Get Processing Status**
```http
GET /api/v2/documents/{id}/status
```

#### 6. **Get Document Chunks**
```http
GET /api/v2/documents/{id}/chunks
```

#### 7. **Get Extracted Tables**
```http
GET /api/v2/documents/{id}/tables
```

#### 8. **Vector Search**
```http
POST /api/v2/documents/search
```

#### 9. **Get Document Types**
```http
GET /api/v2/documents/types
```

#### 10. **Batch Process Documents**
```http
POST /api/v2/documents/batch-process
```

#### 11. **Get Processing Statistics**
```http
GET /api/v2/documents/stats
```

---

## 🔧 Detailed API Reference

### **1. Enhanced Upload and Process** (New Endpoint)

**Endpoint**: `POST /api/v2/documents/upload-and-process`

**Description**: Upload files and automatically trigger enhanced document processing with detailed feedback.

**Headers**:
```http
Authorization: Bearer <token>
Content-Type: multipart/form-data
```

**Form Data**:
```javascript
{
  "files[]": [File objects],
  "file_format_id": "1",
  "file_type_id": "1", 
  "department_id": "1",
  "vertical_id": "1",
  "comments": "Document description",
  "sensitivity_id": "4",
  "tags": "{\"category\": \"research\", \"priority\": \"high\"}"
}
```

**Response** (200 OK):
```json
{
  "success": true,
  "message": "Successfully uploaded and processed 2/2 files",
  "summary": {
    "total_files": 2,
    "successful_uploads": 2,
    "successful_processing": 2,
    "failed_processing": 0,
    "enhanced_processing_enabled": true
  },
  "files": [
    {
      "id": 123,
      "original_name": "document.pdf",
      "blob_name": "uuid_timestamp_document.pdf",
      "blob_url": "https://storage.blob.core.windows.net/..."
    }
  ],
  "processing_results": [
    {
      "file_id": 123,
      "file_name": "document.pdf",
      "status": "completed",
      "message": "Document processed successfully",
      "metadata": {
        "document_type": "pdf",
        "page_count": 5,
        "word_count": 1250
      },
      "processing_time": 3.45,
      "document_type": "pdf",
      "chunk_count": 8,
      "azure_search_doc_ids": ["doc_123_chunk_0", "doc_123_chunk_1", ...]
    }
  ]
}
```

**Example Usage**:
```bash
curl -X POST http://127.0.0.1:5001/api/v2/documents/upload-and-process \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -F "files[]=@document.pdf" \
  -F "file_format_id=1" \
  -F "file_type_id=1" \
  -F "department_id=1" \
  -F "vertical_id=1" \
  -F "comments=Test document for enhanced processing"
```

### **2. Standard Upload with Automatic Processing** (Modified)

**Endpoint**: `POST /api/files`

**Description**: Standard file upload endpoint now automatically triggers enhanced processing.

**Headers**:
```http
Authorization: Bearer <token>
Content-Type: multipart/form-data
```

**Form Data**: Same as enhanced upload endpoint

**Response** (200 OK):
```json
{
  "message": "Successfully uploaded 1 files",
  "files": [
    {
      "id": 124,
      "original_name": "spreadsheet.xlsx",
      "blob_name": "uuid_timestamp_spreadsheet.xlsx",
      "blob_url": "https://storage.blob.core.windows.net/...",
      "enhanced_processing": {
        "enabled": true,
        "status": "completed",
        "message": "Enhanced processing completed successfully"
      }
    }
  ],
  "enhanced_processing": {
    "enabled": true,
    "processed_files": 1,
    "failed_files": 0,
    "skipped_files": 0,
    "results": [
      {
        "file_id": 124,
        "file_name": "spreadsheet.xlsx",
        "processing_result": {
          "status": "completed",
          "document_type": "excel",
          "chunk_count": 5,
          "processing_time": 2.1
        }
      }
    ]
  }
}
```

## Base Information

- **API Version**: v2
- **Base URL**: `http://localhost:5001/api/v2`
- **Authentication**: Bearer Token (JWT)
- **Content-Type**: `application/json` (unless specified otherwise)
- **Rate Limiting**: 1000 requests per hour per user

## Authentication

All API endpoints require authentication using JWT tokens in the Authorization header:

```http
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

### Required Scopes

| Scope | Description | Endpoints |
|-------|-------------|-----------|
| `documents:read` | Read document information | GET endpoints |
| `documents:write` | Upload and process documents | POST/PUT endpoints |
| `documents:delete` | Delete documents | DELETE endpoints |
| `documents:search` | Search documents | POST /search |
| `documents:batch` | Batch operations | POST /batch-process |

## Endpoints

### 1. Process Document

Upload and process a document with enhanced capabilities.

**Endpoint**: `POST /documents/process`

**Content-Type**: `multipart/form-data`

**Parameters**:
- `file` (required): Document file to process
- `enhanced_processing` (optional): Enable enhanced processing (default: true)
- `chunk_size` (optional): Text chunk size in characters (default: 1000)
- `chunk_overlap` (optional): Overlap between chunks (default: 200)

**Example Request**:
```bash
curl -X POST http://localhost:5001/api/v2/documents/process \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -F "file=@document.pdf" \
  -F "enhanced_processing=true" \
  -F "chunk_size=1000" \
  -F "chunk_overlap=200"
```

**Success Response** (200):
```json
{
  "success": true,
  "data": {
    "file_id": 123,
    "filename": "document.pdf",
    "document_type": "pdf",
    "processing_status": "completed",
    "metadata": {
      "page_count": 15,
      "word_count": 2500,
      "char_count": 15000,
      "chunk_count": 18,
      "table_count": 3
    },
    "azure_search_doc_ids": [
      "doc_123_chunk_0",
      "doc_123_chunk_1",
      "doc_123_chunk_2"
    ],
    "processing_time": 12.5
  },
  "message": "Document processed successfully"
}
```

**Error Response** (400):
```json
{
  "success": false,
  "error": {
    "code": "DOC_001",
    "message": "Unsupported document type",
    "details": {
      "supported_types": ["pdf", "excel", "word", "powerpoint", "text", "image"]
    }
  },
  "timestamp": "2025-01-06T10:30:00Z"
}
```

### 2. Reprocess Document

Force reprocessing of an existing document with updated settings.

**Endpoint**: `POST /documents/{id}/reprocess`

**Parameters**:
- `enhanced_processing` (optional): Enable enhanced processing
- `chunk_size` (optional): New chunk size
- `chunk_overlap` (optional): New chunk overlap

**Example Request**:
```bash
curl -X POST http://localhost:5001/api/v2/documents/123/reprocess \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "enhanced_processing": true,
    "chunk_size": 1200,
    "chunk_overlap": 250
  }'
```

**Success Response** (200):
```json
{
  "success": true,
  "data": {
    "file_id": 123,
    "processing_status": "reprocessing",
    "message": "Document reprocessing initiated"
  }
}
```

### 3. Get Document Status

Retrieve detailed processing status and metadata for a document.

**Endpoint**: `GET /documents/{id}/status`

**Example Request**:
```bash
curl -X GET http://localhost:5001/api/v2/documents/123/status \
  -H "Authorization: Bearer YOUR_TOKEN"
```

**Success Response** (200):
```json
{
  "success": true,
  "data": {
    "file_id": 123,
    "filename": "document.pdf",
    "document_type": "pdf",
    "enhanced_processing_status": "completed",
    "processing_metadata": {
      "page_count": 15,
      "table_count": 3,
      "chunk_count": 18,
      "word_count": 2500,
      "char_count": 15000,
      "processing_time": 12.5,
      "created_at": "2025-01-06T10:15:00Z",
      "updated_at": "2025-01-06T10:15:12Z"
    }
  }
}
```

### 4. Get Document Chunks

Retrieve text chunks from a processed document with pagination.

**Endpoint**: `GET /documents/{id}/chunks`

**Query Parameters**:
- `page` (optional): Page number (default: 1)
- `per_page` (optional): Items per page (default: 10, max: 100)
- `search` (optional): Filter chunks by content

**Example Request**:
```bash
curl -X GET "http://localhost:5001/api/v2/documents/123/chunks?page=1&per_page=5&search=analysis" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

**Success Response** (200):
```json
{
  "success": true,
  "data": {
    "chunks": [
      {
        "id": 1,
        "chunk_index": 0,
        "content": "This document provides a comprehensive analysis of market trends...",
        "word_count": 142,
        "char_count": 856,
        "chunk_metadata": {
          "page_number": 1,
          "section": "introduction"
        },
        "azure_search_doc_id": "doc_123_chunk_0"
      },
      {
        "id": 2,
        "chunk_index": 1,
        "content": "The analysis reveals significant growth patterns in the technology sector...",
        "word_count": 138,
        "char_count": 832,
        "chunk_metadata": {
          "page_number": 2,
          "section": "analysis"
        },
        "azure_search_doc_id": "doc_123_chunk_1"
      }
    ],
    "pagination": {
      "page": 1,
      "per_page": 5,
      "total": 18,
      "pages": 4
    }
  }
}
```

### 5. Get Document Tables

Retrieve extracted tables from a document.

**Endpoint**: `GET /documents/{id}/tables`

**Example Request**:
```bash
curl -X GET http://localhost:5001/api/v2/documents/123/tables \
  -H "Authorization: Bearer YOUR_TOKEN"
```

**Success Response** (200):
```json
{
  "success": true,
  "data": {
    "tables": [
      {
        "id": 1,
        "table_index": 0,
        "page_number": 5,
        "table_data": {
          "headers": ["Product", "Q1 Sales", "Q2 Sales", "Growth %"],
          "rows": [
            ["Product A", "100,000", "120,000", "20%"],
            ["Product B", "85,000", "95,000", "11.8%"],
            ["Product C", "150,000", "180,000", "20%"]
          ]
        },
        "table_headers": ["Product", "Q1 Sales", "Q2 Sales", "Growth %"],
        "table_summary": "Quarterly sales performance showing growth across all products",
        "row_count": 3,
        "column_count": 4
      }
    ]
  }
}
```

### 6. Search Documents

Perform vector similarity search across processed documents.

**Endpoint**: `POST /documents/search`

**Request Body**:
```json
{
  "query": "market analysis and growth trends",
  "top_k": 5,
  "filters": {
    "document_type": "pdf",
    "file_id": 123,
    "date_range": {
      "start": "2025-01-01T00:00:00Z",
      "end": "2025-01-06T23:59:59Z"
    }
  },
  "include_metadata": true
}
```

**Example Request**:
```bash
curl -X POST http://localhost:5001/api/v2/documents/search \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "market analysis and growth trends",
    "top_k": 5,
    "filters": {
      "document_type": "pdf"
    },
    "include_metadata": true
  }'
```

**Success Response** (200):
```json
{
  "success": true,
  "data": {
    "results": [
      {
        "chunk_id": "doc_123_chunk_5",
        "content": "The market analysis reveals significant growth trends in the technology sector, with particular strength in cloud computing and AI services...",
        "score": 0.92,
        "metadata": {
          "file_id": 123,
          "filename": "market_report.pdf",
          "document_type": "pdf",
          "chunk_index": 5,
          "page_number": 8
        }
      },
      {
        "chunk_id": "doc_124_chunk_2",
        "content": "Growth trends indicate a 25% increase in market demand for sustainable technology solutions...",
        "score": 0.87,
        "metadata": {
          "file_id": 124,
          "filename": "sustainability_report.pdf",
          "document_type": "pdf",
          "chunk_index": 2,
          "page_number": 3
        }
      }
    ],
    "query": "market analysis and growth trends",
    "total_results": 2,
    "search_time": 0.15
  }
}
```

### 7. Get Supported Document Types

Retrieve information about supported document types and file extensions.

**Endpoint**: `GET /documents/types`

**Example Request**:
```bash
curl -X GET http://localhost:5001/api/v2/documents/types \
  -H "Authorization: Bearer YOUR_TOKEN"
```

**Success Response** (200):
```json
{
  "success": true,
  "data": {
    "document_types": {
      "pdf": {
        "extensions": [".pdf"],
        "description": "Portable Document Format",
        "processing_agent": "pdf_processor"
      },
      "excel": {
        "extensions": [".xlsx", ".xls", ".csv"],
        "description": "Spreadsheet documents",
        "processing_agent": "excel_processor"
      },
      "word": {
        "extensions": [".docx", ".doc"],
        "description": "Word documents",
        "processing_agent": "text_processor"
      },
      "powerpoint": {
        "extensions": [".pptx", ".ppt"],
        "description": "Presentation documents",
        "processing_agent": "text_processor"
      },
      "text": {
        "extensions": [".txt", ".md", ".html", ".htm"],
        "description": "Plain text and markup documents",
        "processing_agent": "text_processor"
      },
      "image": {
        "extensions": [".jpg", ".jpeg", ".png", ".gif", ".bmp", ".tiff", ".webp"],
        "description": "Image files with OCR processing",
        "processing_agent": "image_processor"
      }
    },
    "total_extensions": 19,
    "max_file_size_mb": 100
  }
}
```

### 8. Batch Process Documents

Process multiple documents in a single request.

**Endpoint**: `POST /documents/batch-process`

**Content-Type**: `multipart/form-data`

**Parameters**:
- `files` (required): Array of files (max: 10)
- `enhanced_processing` (optional): Enable enhanced processing (default: true)

**Example Request**:
```bash
curl -X POST http://localhost:5001/api/v2/documents/batch-process \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -F "files=@document1.pdf" \
  -F "files=@document2.docx" \
  -F "files=@spreadsheet.xlsx" \
  -F "enhanced_processing=true"
```

**Success Response** (200):
```json
{
  "success": true,
  "data": {
    "batch_id": "batch_20250106_103000",
    "total_files": 3,
    "processed_files": [
      {
        "file_id": 125,
        "filename": "document1.pdf",
        "status": "completed",
        "document_type": "pdf",
        "processing_time": 8.2
      },
      {
        "file_id": 126,
        "filename": "document2.docx",
        "status": "completed",
        "document_type": "word",
        "processing_time": 5.1
      },
      {
        "file_id": 127,
        "filename": "spreadsheet.xlsx",
        "status": "completed",
        "document_type": "excel",
        "processing_time": 3.8
      }
    ],
    "failed_files": [],
    "total_processing_time": 17.1
  }
}
```

### 9. Get Processing Statistics

Retrieve enhanced processing statistics and metrics.

**Endpoint**: `GET /documents/stats`

**Query Parameters**:
- `period` (optional): Time period ('day', 'week', 'month') (default: 'day')
- `document_type` (optional): Filter by document type

**Example Request**:
```bash
curl -X GET "http://localhost:5001/api/v2/documents/stats?period=week&document_type=pdf" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

**Success Response** (200):
```json
{
  "success": true,
  "data": {
    "period": "week",
    "document_type": "pdf",
    "statistics": {
      "total_documents": 45,
      "total_chunks": 892,
      "total_tables": 67,
      "average_processing_time": 8.5,
      "total_processing_time": 382.5,
      "document_types": {
        "pdf": 45,
        "excel": 0,
        "word": 0,
        "powerpoint": 0,
        "text": 0,
        "image": 0
      },
      "processing_status": {
        "completed": 43,
        "failed": 2,
        "pending": 0
      },
      "average_metrics": {
        "pages_per_document": 12.3,
        "words_per_document": 2150,
        "chunks_per_document": 19.8,
        "tables_per_document": 1.5
      }
    },
    "generated_at": "2025-01-06T10:30:00Z"
  }
}
```

## Error Handling

### Error Response Format

All error responses follow a consistent format:

```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "Human-readable error message",
    "details": {
      "additional": "error details"
    }
  },
  "timestamp": "2025-01-06T10:30:00Z"
}
```

### Error Codes

#### Document Processing Errors
- `DOC_001`: Unsupported document type
- `DOC_002`: Document processing failed
- `DOC_003`: Document too large
- `DOC_004`: Document corrupted or unreadable

#### Azure Search Errors
- `AZ_001`: Azure Search connection failed
- `AZ_002`: Azure Search index not found
- `AZ_003`: Azure Search upload failed
- `AZ_004`: Azure Search query failed

#### Database Errors
- `DB_001`: Database connection failed
- `DB_002`: Database query failed
- `DB_003`: Database constraint violation

#### Authentication Errors
- `AUTH_001`: Authentication failed
- `AUTH_002`: Authorization failed (insufficient permissions)
- `AUTH_003`: Token expired

#### Validation Errors
- `VAL_001`: Invalid request format
- `VAL_002`: Missing required field
- `VAL_003`: Invalid field value

### HTTP Status Codes

| Status Code | Description | Usage |
|-------------|-------------|-------|
| 200 | OK | Successful request |
| 201 | Created | Resource created successfully |
| 400 | Bad Request | Invalid request format or parameters |
| 401 | Unauthorized | Authentication required |
| 403 | Forbidden | Insufficient permissions |
| 404 | Not Found | Resource not found |
| 413 | Payload Too Large | File size exceeds limit |
| 422 | Unprocessable Entity | Validation errors |
| 429 | Too Many Requests | Rate limit exceeded |
| 500 | Internal Server Error | Server error |
| 503 | Service Unavailable | Service temporarily unavailable |

## Rate Limiting

The API implements rate limiting to ensure fair usage:

- **Default Limit**: 1000 requests per hour per user
- **Burst Limit**: 100 requests per minute
- **File Upload Limit**: 50 files per hour per user

Rate limit headers are included in responses:

```http
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 995
X-RateLimit-Reset: 1641472800
```

## File Size Limits

- **Maximum file size**: 100 MB per file
- **Batch processing**: Maximum 10 files per batch
- **Total batch size**: Maximum 500 MB per batch

## Supported File Types

| Document Type | Extensions | Max Size | Processing Features |
|---------------|------------|----------|-------------------|
| PDF | .pdf | 100 MB | Text extraction, table detection, OCR |
| Excel | .xlsx, .xls, .csv | 50 MB | Data analysis, table processing |
| Word | .docx, .doc | 50 MB | Text extraction, structure preservation |
| PowerPoint | .pptx, .ppt | 50 MB | Slide content extraction |
| Text | .txt, .md, .html, .htm | 10 MB | Plain text processing |
| Images | .jpg, .jpeg, .png, .gif, .bmp, .tiff, .webp | 25 MB | OCR text extraction |

## Usage Examples

### Python Example

```python
import requests
import json

# Configuration
BASE_URL = "http://localhost:5001/api/v2"
TOKEN = "your_jwt_token_here"
HEADERS = {
    "Authorization": f"Bearer {TOKEN}",
    "Content-Type": "application/json"
}

# Upload and process a document
def upload_document(file_path):
    url = f"{BASE_URL}/documents/process"
    files = {"file": open(file_path, "rb")}
    data = {"enhanced_processing": "true"}
    
    response = requests.post(url, files=files, data=data, 
                           headers={"Authorization": f"Bearer {TOKEN}"})
    return response.json()

# Search documents
def search_documents(query, filters=None):
    url = f"{BASE_URL}/documents/search"
    payload = {
        "query": query,
        "top_k": 5,
        "filters": filters or {},
        "include_metadata": True
    }
    
    response = requests.post(url, json=payload, headers=HEADERS)
    return response.json()

# Get document status
def get_document_status(file_id):
    url = f"{BASE_URL}/documents/{file_id}/status"
    response = requests.get(url, headers=HEADERS)
    return response.json()

# Example usage
if __name__ == "__main__":
    # Upload document
    result = upload_document("document.pdf")
    file_id = result["data"]["file_id"]
    
    # Check status
    status = get_document_status(file_id)
    print(f"Processing status: {status['data']['enhanced_processing_status']}")
    
    # Search documents
    search_results = search_documents("market analysis", {"document_type": "pdf"})
    print(f"Found {len(search_results['data']['results'])} results")
```

### JavaScript Example

```javascript
const BASE_URL = "http://localhost:5001/api/v2";
const TOKEN = "your_jwt_token_here";

// Upload and process document
async function uploadDocument(file) {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('enhanced_processing', 'true');
    
    const response = await fetch(`${BASE_URL}/documents/process`, {
        method: 'POST',
        headers: {
            'Authorization': `Bearer ${TOKEN}`
        },
        body: formData
    });
    
    return await response.json();
}

// Search documents
async function searchDocuments(query, filters = {}) {
    const response = await fetch(`${BASE_URL}/documents/search`, {
        method: 'POST',
        headers: {
            'Authorization': `Bearer ${TOKEN}`,
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            query: query,
            top_k: 5,
            filters: filters,
            include_metadata: true
        })
    });
    
    return await response.json();
}

// Get document chunks
async function getDocumentChunks(fileId, page = 1, perPage = 10) {
    const response = await fetch(
        `${BASE_URL}/documents/${fileId}/chunks?page=${page}&per_page=${perPage}`,
        {
            headers: {
                'Authorization': `Bearer ${TOKEN}`
            }
        }
    );
    
    return await response.json();
}
```

## Changelog

### Version 2.0.0 (January 6, 2025)
- Initial release of Enhanced Processing API v2
- Added document type detection and intelligent routing
- Implemented Azure AI Search integration
- Added comprehensive metadata tracking
- Introduced batch processing capabilities
- Added vector similarity search
- Implemented enhanced error handling and validation

---

**API Version**: v2.0.0  
**Last Updated**: January 6, 2025  
**Status**: ✅ Production Ready  
**Support**: Contact development team for assistance 