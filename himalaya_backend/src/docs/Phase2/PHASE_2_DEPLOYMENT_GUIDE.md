# Himalaya Enhanced Processing - Phase 2 Deployment Guide

## Overview

This guide provides step-by-step instructions for deploying the Phase 2 enhanced document processing system to production environments. The deployment includes core infrastructure components, Azure AI Search integration, and API v2 endpoints.

## Prerequisites

### System Requirements

**Minimum Requirements**:
- **OS**: Windows 10/11, Linux (Ubuntu 18.04+), macOS 10.15+
- **Python**: 3.9+ (recommended: 3.13)
- **Memory**: 4GB RAM minimum, 8GB recommended
- **Storage**: 10GB free space minimum
- **Network**: Stable internet connection for Azure services

**Recommended Production Requirements**:
- **CPU**: 4+ cores
- **Memory**: 16GB RAM
- **Storage**: 50GB+ SSD storage
- **Network**: High-speed internet with low latency to Azure

### Azure Services Required

1. **Azure AI Search**
   - Service tier: Standard or higher
   - Vector index configured with 3072 dimensions
   - API key with read/write permissions

2. **Azure OpenAI**
   - GPT-4 deployment for processing
   - text-embedding-3-large deployment for embeddings
   - API key with appropriate quotas

3. **Database**
   - PostgreSQL 12+ (recommended) or SQLite for development
   - Sufficient storage for document metadata

### Software Dependencies

**Core Dependencies**:
```bash
# Python packages (see requirements.txt)
Flask>=2.3.0
SQLAlchemy>=2.0.0
psycopg2-binary>=2.9.0
azure-search-documents>=11.4.0
openai>=1.0.0
python-docx>=1.1.0
python-pptx>=1.0.0
PyPDF2>=3.0.0
pandas>=2.0.0
numpy>=1.24.0
```

## Environment Setup

### 1. Conda Environment Setup

**Create and activate environment**:
```bash
# Create conda environment
conda create -n higpt_backend python=3.13 -y

# Activate environment
conda activate higpt_backend

# Verify activation
python --version
# Should output: Python 3.13.x
```

### 2. Install Dependencies

**Install core packages**:
```bash
# Navigate to project directory
cd C:\HIMALAYA\himalaya_backend\src

# Install Python dependencies
pip install -r requirements.txt

# Install additional Phase 2 dependencies
pip install markdown-pdf
pip install python-docx
pip install python-pptx
pip install docx2txt
pip install Office365-REST-Python-Client
pip install matplotlib
pip install markdownify
```

**Verify installation**:
```bash
# Test critical imports
python -c "
import flask
import sqlalchemy
import azure.search.documents
import openai
print('All dependencies installed successfully')
"
```

### 3. Environment Variables Configuration

**Create environment file** (`.env`):
```bash
# Enhanced Processing Configuration
ENHANCED_PROCESSING_ENABLED=true
INTELLIGENT_CHUNKING_ENABLED=true

# Azure AI Search Configuration
AZURE_SEARCH_SERVICE_NAME=your-search-service-name
AZURE_SEARCH_API_KEY=your-search-api-key
AZURE_SEARCH_ENHANCED_INDEX_NAME=vector-1742884738327
AZURE_SEARCH_PRODUCTION_INDEX_NAME=vector-1742884805411

# Azure OpenAI Configuration
AZURE_OPENAI_API_KEY=your-openai-api-key
AZURE_OPENAI_ENDPOINT=https://your-openai-service.openai.azure.com/
AZURE_OPENAI_DEPLOYMENT_NAME=gpt-4o
AZURE_OPENAI_EMBEDDING_DEPLOYMENT=text-embedding-3-large
AZURE_OPENAI_EMBEDDING_MODEL=text-embedding-3-large
AZURE_OPENAI_API_VERSION=2025-01-01-preview

# Document Processing Settings
CHUNK_SIZE=1000
CHUNK_OVERLAP=200
MIN_CHUNK_SIZE=100
MAX_CHUNK_SIZE=2000

# File Processing Limits
MAX_FILE_SIZE_MB=100
MAX_BATCH_FILES=10

# Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/himalaya_db
# For development: DATABASE_URL=sqlite:///data/himalaya.db

# Flask Configuration
FLASK_ENV=production
FLASK_DEBUG=false
SECRET_KEY=your-secret-key-here

# Security Configuration
JWT_SECRET_KEY=your-jwt-secret-key
JWT_ACCESS_TOKEN_EXPIRES=86400

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=logs/himalaya_enhanced.log
```

**Load environment variables**:
```bash
# For Windows
set FLASK_APP=app.py
set FLASK_ENV=production

# For Linux/macOS
export FLASK_APP=app.py
export FLASK_ENV=production
```

## Database Setup

### 1. PostgreSQL Setup (Production)

**Install PostgreSQL**:
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install postgresql postgresql-contrib

# Windows (using chocolatey)
choco install postgresql

# macOS (using homebrew)
brew install postgresql
```

**Create database and user**:
```sql
-- Connect as postgres user
sudo -u postgres psql

-- Create database
CREATE DATABASE himalaya_db;

-- Create user
CREATE USER himalaya_user WITH PASSWORD 'secure_password';

-- Grant privileges
GRANT ALL PRIVILEGES ON DATABASE himalaya_db TO himalaya_user;

-- Exit
\q
```

### 2. Database Migration

**Run Phase 1 migration** (if not already done):
```bash
# Navigate to source directory
cd C:\HIMALAYA\himalaya_backend\src

# Run Phase 1 migration
python run_migration.py
```

**Verify database setup**:
```bash
# Test database connection
python -c "
from models.models import db
from app import create_app
app = create_app()
with app.app_context():
    print('Database connection successful')
    print(f'Tables: {db.engine.table_names()}')
"
```

## Azure Services Configuration

### 1. Azure AI Search Setup

**Verify index configuration**:
```bash
# Test Azure Search connection
python -c "
from services.azure_search_service import AzureSearchService
service = AzureSearchService('vector-1742884738327')
print('Azure Search connection successful')
print(f'Index: {service.index_name}')
"
```

**Index schema validation**:
```json
{
  "name": "vector-1742884738327",
  "fields": [
    {
      "name": "chunk_id",
      "type": "Edm.String",
      "key": true,
      "retrievable": true,
      "searchable": false
    },
    {
      "name": "parent_id",
      "type": "Edm.String",
      "retrievable": true,
      "filterable": true,
      "searchable": false
    },
    {
      "name": "chunk",
      "type": "Edm.String",
      "retrievable": true,
      "filterable": true,
      "sortable": true,
      "searchable": true
    },
    {
      "name": "title",
      "type": "Edm.String",
      "retrievable": true,
      "filterable": true,
      "sortable": true,
      "searchable": true
    },
    {
      "name": "text_vector",
      "type": "Collection(Edm.Single)",
      "dimensions": 3072,
      "vectorSearchProfile": "default"
    },
    {
      "name": "metadata_storage_name",
      "type": "Edm.String",
      "retrievable": true,
      "filterable": true,
      "sortable": true,
      "searchable": false
    }
  ]
}
```

### 2. Azure OpenAI Setup

**Test embedding generation**:
```bash
# Test Azure OpenAI connection
python -c "
from services.azure_search_service import AzureSearchService
service = AzureSearchService('vector-1742884738327')
embedding = service._generate_embedding('test text')
print(f'Embedding generated: {len(embedding)} dimensions')
"
```

## Application Deployment

### 1. Pre-deployment Testing

**Run comprehensive tests**:
```bash
# Run Phase 2 test suite
python test_phase2.py

# Expected output: 7/7 tests passed
```

**Validate configuration**:
```bash
# Test configuration loading
python -c "
from config.settings import *
print('Enhanced Processing Enabled:', ENHANCED_PROCESSING_ENABLED)
print('Azure Search Index:', AZURE_SEARCH_ENHANCED_INDEX_NAME)
print('Chunk Size:', CHUNK_SIZE)
"
```

### 2. Application Startup

**Development mode**:
```bash
# Start Flask application
python app.py

# Application should start on http://localhost:5001
```

**Production mode with Gunicorn**:
```bash
# Install Gunicorn
pip install gunicorn

# Start with Gunicorn
gunicorn --bind 0.0.0.0:5001 --workers 4 --timeout 120 app:app

# Or with configuration file
gunicorn --config gunicorn.conf.py app:app
```

**Gunicorn configuration** (`gunicorn.conf.py`):
```python
# Gunicorn configuration
bind = "0.0.0.0:5001"
workers = 4
worker_class = "sync"
worker_connections = 1000
timeout = 120
keepalive = 2
max_requests = 1000
max_requests_jitter = 100
preload_app = True
```

### 3. Health Check Validation

**Test basic health endpoint**:
```bash
curl http://localhost:5001/health
# Expected: {"status": "healthy", "message": "Service is running"}
```

**Test enhanced processing health**:
```bash
curl http://localhost:5001/health/enhanced-processing
# Expected: Enhanced processing status with Azure Search index info
```

## API v2 Endpoint Testing

### 1. Authentication Setup

**Generate test JWT token**:
```python
# test_auth.py
import jwt
import datetime

SECRET_KEY = "your-jwt-secret-key"
payload = {
    'user_id': 1,
    'username': 'test_user',
    'scopes': ['documents:read', 'documents:write', 'documents:search'],
    'exp': datetime.datetime.utcnow() + datetime.timedelta(hours=24)
}

token = jwt.encode(payload, SECRET_KEY, algorithm='HS256')
print(f"Test token: {token}")
```

### 2. Test API Endpoints

**Test document types endpoint**:
```bash
curl -X GET http://localhost:5001/api/v2/documents/types \
  -H "Authorization: Bearer YOUR_TOKEN"
```

**Test document processing** (with actual file):
```bash
curl -X POST http://localhost:5001/api/v2/documents/process \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -F "file=@test_document.pdf" \
  -F "enhanced_processing=true"
```

**Test vector search**:
```bash
curl -X POST http://localhost:5001/api/v2/documents/search \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "test search query",
    "top_k": 5,
    "include_metadata": true
  }'
```

## Monitoring and Logging

### 1. Logging Configuration

**Create logging directory**:
```bash
mkdir -p logs
touch logs/himalaya_enhanced.log
```

**Logging configuration** (`config/logging.py`):
```python
import logging
import os
from logging.handlers import RotatingFileHandler

def setup_logging(app):
    if not app.debug:
        # File handler
        file_handler = RotatingFileHandler(
            'logs/himalaya_enhanced.log',
            maxBytes=10240000,  # 10MB
            backupCount=10
        )
        file_handler.setFormatter(logging.Formatter(
            '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
        ))
        file_handler.setLevel(logging.INFO)
        app.logger.addHandler(file_handler)
        
        app.logger.setLevel(logging.INFO)
        app.logger.info('Himalaya Enhanced Processing startup')
```

### 2. Performance Monitoring

**Basic metrics collection**:
```python
# metrics.py
import time
import psutil
from functools import wraps

def monitor_performance(func):
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        start_memory = psutil.Process().memory_info().rss / 1024 / 1024  # MB
        
        result = func(*args, **kwargs)
        
        end_time = time.time()
        end_memory = psutil.Process().memory_info().rss / 1024 / 1024  # MB
        
        print(f"{func.__name__}: {end_time - start_time:.2f}s, "
              f"Memory: {end_memory - start_memory:.2f}MB")
        
        return result
    return wrapper
```

### 3. Health Monitoring

**Extended health check**:
```python
# health_check.py
@app.route('/health/detailed')
def detailed_health_check():
    health_status = {
        'timestamp': datetime.utcnow().isoformat(),
        'status': 'healthy',
        'components': {}
    }
    
    # Database check
    try:
        db.session.execute('SELECT 1')
        health_status['components']['database'] = 'healthy'
    except Exception as e:
        health_status['components']['database'] = f'unhealthy: {str(e)}'
        health_status['status'] = 'degraded'
    
    # Azure Search check
    try:
        from services.azure_search_service import AzureSearchService
        service = AzureSearchService('vector-1742884738327')
        health_status['components']['azure_search'] = 'healthy'
    except Exception as e:
        health_status['components']['azure_search'] = f'unhealthy: {str(e)}'
        health_status['status'] = 'degraded'
    
    return jsonify(health_status)
```

## Security Configuration

### 1. SSL/TLS Setup

**Generate SSL certificates** (for production):
```bash
# Using Let's Encrypt (recommended)
sudo certbot --nginx -d your-domain.com

# Or self-signed for testing
openssl req -x509 -newkey rsa:4096 -keyout key.pem -out cert.pem -days 365 -nodes
```

**Configure Flask for HTTPS**:
```python
# app.py
if __name__ == '__main__':
    app = create_app()
    # For production with SSL
    app.run(debug=False, port=5001, host='0.0.0.0', 
            ssl_context=('cert.pem', 'key.pem'))
```

### 2. Security Headers

**Add security middleware**:
```python
# security.py
from flask import Flask
from flask_talisman import Talisman

def configure_security(app: Flask):
    # Force HTTPS
    Talisman(app, force_https=True)
    
    # Security headers
    @app.after_request
    def add_security_headers(response):
        response.headers['X-Content-Type-Options'] = 'nosniff'
        response.headers['X-Frame-Options'] = 'DENY'
        response.headers['X-XSS-Protection'] = '1; mode=block'
        response.headers['Strict-Transport-Security'] = 'max-age=31536000; includeSubDomains'
        return response
```

### 3. Rate Limiting

**Configure rate limiting**:
```python
# rate_limiting.py
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address

limiter = Limiter(
    app,
    key_func=get_remote_address,
    default_limits=["1000 per hour", "100 per minute"]
)

# Apply to API endpoints
@app.route('/api/v2/documents/process', methods=['POST'])
@limiter.limit("50 per hour")
def process_document():
    # Implementation
    pass
```

## Backup and Recovery

### 1. Database Backup

**Automated backup script**:
```bash
#!/bin/bash
# backup_db.sh

DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backups/himalaya"
DB_NAME="himalaya_db"
DB_USER="himalaya_user"

mkdir -p $BACKUP_DIR

# Create database backup
pg_dump -U $DB_USER -h localhost $DB_NAME > $BACKUP_DIR/himalaya_db_$DATE.sql

# Compress backup
gzip $BACKUP_DIR/himalaya_db_$DATE.sql

# Keep only last 30 days of backups
find $BACKUP_DIR -name "*.sql.gz" -mtime +30 -delete

echo "Backup completed: himalaya_db_$DATE.sql.gz"
```

### 2. Configuration Backup

**Backup configuration files**:
```bash
#!/bin/bash
# backup_config.sh

DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backups/himalaya/config"
SOURCE_DIR="/path/to/himalaya_backend/src"

mkdir -p $BACKUP_DIR

# Backup configuration files
tar -czf $BACKUP_DIR/config_$DATE.tar.gz \
  $SOURCE_DIR/config/ \
  $SOURCE_DIR/.env \
  $SOURCE_DIR/requirements.txt

echo "Configuration backup completed: config_$DATE.tar.gz"
```

## Troubleshooting

### 1. Common Issues

**Issue: Azure Search connection fails**
```bash
# Check network connectivity
curl -X GET "https://your-search-service.search.windows.net/indexes/vector-1742884738327?api-version=2023-11-01" \
  -H "api-key: YOUR_API_KEY"

# Verify API key permissions
# Ensure key has read/write access to the index
```

**Issue: Database connection fails**
```bash
# Test database connectivity
python -c "
import psycopg2
conn = psycopg2.connect('postgresql://user:pass@localhost:5432/himalaya_db')
print('Database connection successful')
conn.close()
"
```

**Issue: Import errors**
```bash
# Verify all dependencies installed
pip list | grep -E "(flask|sqlalchemy|azure|openai)"

# Reinstall if necessary
pip install --force-reinstall -r requirements.txt
```

### 2. Performance Issues

**High memory usage**:
- Monitor with `htop` or Task Manager
- Adjust worker count in Gunicorn
- Implement connection pooling
- Add memory limits to processing functions

**Slow response times**:
- Check Azure Search latency
- Monitor database query performance
- Implement caching for frequent requests
- Optimize chunking parameters

### 3. Debugging

**Enable debug logging**:
```python
# In app.py
import logging
logging.basicConfig(level=logging.DEBUG)

# Or set environment variable
export FLASK_DEBUG=true
```

**Debug Azure Search issues**:
```python
# Test embedding generation
from services.azure_search_service import AzureSearchService
service = AzureSearchService('vector-1742884738327')
try:
    embedding = service._generate_embedding("test")
    print(f"Success: {len(embedding)} dimensions")
except Exception as e:
    print(f"Error: {e}")
```

## Maintenance

### 1. Regular Maintenance Tasks

**Daily**:
- Check application logs for errors
- Monitor disk space usage
- Verify health endpoints

**Weekly**:
- Review performance metrics
- Check database backup integrity
- Update security patches

**Monthly**:
- Review and rotate logs
- Update dependencies
- Performance optimization review

### 2. Updates and Upgrades

**Update dependencies**:
```bash
# Update Python packages
pip list --outdated
pip install --upgrade package_name

# Update requirements.txt
pip freeze > requirements.txt
```

**Deploy updates**:
```bash
# Backup current deployment
cp -r /path/to/himalaya_backend /backups/himalaya_backup_$(date +%Y%m%d)

# Deploy new version
git pull origin main
pip install -r requirements.txt

# Restart application
sudo systemctl restart himalaya-enhanced
```

## Production Checklist

### Pre-deployment
- [ ] All tests passing (7/7)
- [ ] Environment variables configured
- [ ] Database migration completed
- [ ] Azure services configured and tested
- [ ] SSL certificates installed
- [ ] Security headers configured
- [ ] Rate limiting enabled
- [ ] Logging configured
- [ ] Backup procedures tested

### Post-deployment
- [ ] Health endpoints responding
- [ ] API v2 endpoints functional
- [ ] Document processing working
- [ ] Vector search operational
- [ ] Monitoring alerts configured
- [ ] Performance baselines established
- [ ] Documentation updated
- [ ] Team training completed

---

**Deployment Guide Version**: 1.0  
**Last Updated**: January 6, 2025  
**Status**: ✅ Production Ready  
**Support**: Contact development team for deployment assistance 