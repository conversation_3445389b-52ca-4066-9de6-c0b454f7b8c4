import os
from datetime import timed<PERSON><PERSON>
from urllib.parse import quote

# # Microsoft OAuth configuration
# MS_CONFIG = {
#     "auth_uri": "https://login.microsoftonline.com/cdf30318-22d7-461b-a9f9-88765d14e181",
#     "client_id": "ae625876-85e6-4f8c-a628-31b828c54c56",
#     "client_secret": "****************************************",
#     # "redirect_uri": "https://aivccontainerfrontend.mangodesert-321e63c0.southindia.azurecontainerapps.io/login",
#     "redirect_uri": "https://higpt.himalayawellness.com/login",
#     "scope": ["User.Read"]
# }


# db_password = "Himalaya@1930"
# db_password = quote(db_password)

# # Flask configuration
# FLASK_CONFIG = {
#     'SECRET_KEY': 'secret',
#     'SQLALCHEMY_DATABASE_URI': f'postgresql://aivideoconferenceAdmin:{db_password}@aivideoconference-db.postgres.database.azure.com:5432/HiGPT',
#     'SQLALCHEMY_TRACK_MODIFICATIONS': False,
#     'SESSION_COOKIE_NAME': 'session',
#     'SESSION_COOKIE_SECURE': False,  # Set to True in production with HTTPS
#     'SESSION_COOKIE_HTTPONLY': True,
#     'SESSION_COOKIE_SAMESITE': 'Lax',
#     'PERMANENT_SESSION_LIFETIME': timedelta(hours=24),
#     'SESSION_REFRESH_EACH_REQUEST': True,
#     'SESSION_TYPE': 'filesystem'
# }

# # Azure Settings
# AZURE_STORAGE_CONNECTION_STRING = 'DefaultEndpointsProtocol=https;AccountName=aivcstorage01;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net'
# AZURE_STORAGE_CONTAINER_NAME = 'generalaisearch'
# AZURE_OPENAI_KEY = '********************************'
# AZURE_OPENAI_ENDPOINT = 'https://videoconference-openai.openai.azure.com'
# AZURE_OPENAI_API_VERSION = '2024-02-15-preview'
# AZURE_OPENAI_DEPLOYMENT_NAME = 'gpt-4o'
# AZURE_OPENAI_EMBEDDING_DEPLOYMENT = 'text-embedding-3-large'
# AZURE_SEARCH_SERVICE_ENDPOINT = 'https://videoconference-aisearch.search.windows.net'
# AZURE_SEARCH_INDEX_NAME = 'vector-*************'
# AZURE_SEARCH_ADMIN_KEY = '****************************************************'
# AZURE_SPEECH_KEY = 'd7fef72b83144baaa731cd27d8aad4a4'
# AZURE_SPEECH_REGION = 'centralindia'

# # SERPER_API_KEY 
# SERPER_API_KEY = '4477a4b3ad7806b348a4015d18e0f6f993962229'

# # SharePoint Settings
# SHAREPOINT_SITE_URL = 'https://thdc.sharepoint.com/sites/VideoConference2'
# SHAREPOINT_USERNAME = "<EMAIL>"
# SHAREPOINT_PASSWORD = "Prasanna1$"

# # FGD Video Settings
# FGD_VIDEO_VECTOR_BLOB = 'fgd-vector-files'
# AZURE_SEARCH_FGD_VIDEO_INDEX_NAME = "vector-1731989828839"
# MIN_VECTOR_SCORE_THRESHOLD = 0.2

# # JWT Settings
# JWT_SECRET_KEY = 'your-secret-key-here'  # Make sure to change this in production
# JWT_EXPIRATION_HOURS = 24

# # Add this with your other Azure settings
# FGD_VIDEOS_CONTAINER = 'fgd-videos'

# BACKEND_URL = "https://aivccontainerbakend.mangodesert-321e63c0.southindia.azurecontainerapps.io"
# # Add this with your other settings
# GENERAL_MEDIA_CONTAINER = 'generalaimedia'


# Local Test

# # Microsoft OAuth configuration
# MS_CONFIG = {
#     "auth_uri": "https://login.microsoftonline.com/cdf30318-22d7-461b-a9f9-88765d14e181",
#     "client_id": "ae625876-85e6-4f8c-a628-31b828c54c56",
#     "client_secret": "****************************************",
#     "redirect_uri": "http://localhost:3000/login",
#     "scope": ["User.Read"],
#     "authority": "https://login.microsoftonline.com/cdf30318-22d7-461b-a9f9-88765d14e181"
# }

# # Flask configuration
# db_password = "Himalaya@1930"
# db_password = quote(db_password)

# FLASK_CONFIG = {
#     'SECRET_KEY': 'secret',
#     # 'SQLALCHEMY_DATABASE_URI': 'postgresql://prasanna:password@localhost:5432/higpt_dev',
#     'SQLALCHEMY_DATABASE_URI':f'postgresql://aivideoconferenceAdmin:{db_password}@higpt-dbp2.postgres.database.azure.com:5432/HiGPT',
#     'SQLALCHEMY_TRACK_MODIFICATIONS': False,
#     'SESSION_COOKIE_NAME': 'session',
#     'SESSION_COOKIE_SECURE': False,  # Set to True in production with HTTPS
#     'SESSION_COOKIE_HTTPONLY': True,
#     'SESSION_COOKIE_SAMESITE': 'Lax',
#     'PERMANENT_SESSION_LIFETIME': timedelta(hours=24),
#     'SESSION_REFRESH_EACH_REQUEST': True,
#     'SESSION_TYPE': 'filesystem'
# }

# # Azure Settings
# AZURE_STORAGE_CONNECTION_STRING = 'DefaultEndpointsProtocol=https;AccountName=aivcstorage01;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net'
# AZURE_STORAGE_CONTAINER_NAME = 'generalaisearch'
# AZURE_OPENAI_KEY = '********************************'
# AZURE_OPENAI_ENDPOINT = 'https://videoconference-openai.openai.azure.com'
# AZURE_OPENAI_API_VERSION = '2024-02-15-preview'
# AZURE_OPENAI_DEPLOYMENT_NAME = 'gpt-4o'
# AZURE_OPENAI_EMBEDDING_DEPLOYMENT = 'text-embedding-3-large'
# AZURE_SEARCH_SERVICE_ENDPOINT = 'https://videoconference-aisearch.search.windows.net'
# AZURE_SEARCH_INDEX_NAME = 'vector-*************'
# AZURE_SEARCH_ADMIN_KEY = '****************************************************'
# AZURE_SPEECH_KEY = 'd7fef72b83144baaa731cd27d8aad4a4'
# AZURE_SPEECH_REGION = 'centralindia'

# # SharePoint Settings
# SHAREPOINT_SITE_URL = 'https://thdc.sharepoint.com/sites/VideoConference2'
# SHAREPOINT_USERNAME = "<EMAIL>"
# SHAREPOINT_PASSWORD = "Prasanna1$"

# # FGD Video Settings
# FGD_VIDEO_VECTOR_BLOB = 'fgd-vector-files'
# AZURE_SEARCH_FGD_VIDEO_INDEX_NAME = "vector-1731989828839"
# MIN_VECTOR_SCORE_THRESHOLD = 0.2

# # JWT Settings
# JWT_SECRET_KEY = 'your-secret-key-here'  # Make sure to change this in production
# JWT_EXPIRATION_HOURS = 24

# # Add this with your other Azure settings
# FGD_VIDEOS_CONTAINER = 'fgd-videos'

# BACKEND_URL = "http://localhost:5001"

# # Add this with your other settings
# GENERAL_MEDIA_CONTAINER = 'generalaimedia'


########## himalaya Phase 2 ##########

# UAT
 
# Microsoft OAuth configuration
MS_CONFIG = {
    "auth_uri": "https://login.microsoftonline.com/cdf30318-22d7-461b-a9f9-88765d14e181",
    "client_id": "ae625876-85e6-4f8c-a628-31b828c54c56",
    "client_secret": "****************************************",
    # 'redirect_uri': "https://aivccontainerfrontendp2.salmonbay-6d5d2866.southindia.azurecontainerapps.io/login",
    "redirect_uri": "http://localhost:3000/login",
    "scope": ["User.Read"],
    "authority": "https://login.microsoftonline.com/cdf30318-22d7-461b-a9f9-88765d14e181"
}
 
# Flask configuration
db_password = "Himalaya@1930"
db_password = quote(db_password)

FLASK_CONFIG = {
    'SECRET_KEY': 'secret',
    # 'SQLALCHEMY_DATABASE_URI': 'postgresql://prasanna:password@localhost:5432/higpt_dev',
    # 'SQLALCHEMY_DATABASE_URI':f'postgresql://aivideoconferenceAdmin:{db_password}@higpt-dbp2.postgres.database.azure.com:5432/HiGPT_Agents',
    'SQLALCHEMY_DATABASE_URI':f'postgresql://aivideoconferenceAdmin:{db_password}@higpt-dbp2.postgres.database.azure.com:5432/HiGPT_Agents_UAT',  #switch_local
    'SQLALCHEMY_TRACK_MODIFICATIONS': False,
    'SESSION_COOKIE_NAME': 'session',
    'SESSION_COOKIE_SECURE': False,  # Set to True in production with HTTPS
    'SESSION_COOKIE_HTTPONLY': True,
    'SESSION_COOKIE_SAMESITE': 'Lax',
    'PERMANENT_SESSION_LIFETIME': timedelta(hours=24),
    'SESSION_REFRESH_EACH_REQUEST': True,
    'SESSION_TYPE': 'filesystem'
}
 
# Azure Settings
AZURE_STORAGE_CONNECTION_STRING = 'DefaultEndpointsProtocol=https;AccountName=aivcstorage01p2;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net'
AZURE_STORAGE_CONTAINER_NAME = 'uat-generalaisearch' #switch_local
# AZURE_STORAGE_CONTAINER_NAME = 'generalaisearch'

# Extract account name from connection string for easier reference
import re
match = re.search(r'AccountName=([^;]+)', AZURE_STORAGE_CONNECTION_STRING)
AZURE_STORAGE_ACCOUNT_NAME = match.group(1) if match else 'aivcstorage01p2'

AZURE_OPENAI_KEY = 'FYtOZsQS43uwEosS4Bp7oIt7hF4n0S9yqIkdcBhyLEuyqublTFOTJQQJ99BCAC77bzfXJ3w3AAABACOGG5R9'
AZURE_OPENAI_ENDPOINT = 'https://videoconference-openai-p2.openai.azure.com'
AZURE_OPENAI_API_VERSION = '2025-01-01-preview'
AZURE_OPENAI_DEPLOYMENT_NAME = 'gpt-4o'
AZURE_OPENAI_EMBEDDING_DEPLOYMENT = 'text-embedding-3-large'
AZURE_SEARCH_SERVICE_ENDPOINT = 'https://videoconference-aisearchp2.search.windows.net'
AZURE_SEARCH_INDEX_NAME = 'vector-*************'
AZURE_SEARCH_ADMIN_KEY = '****************************************************'
AZURE_SPEECH_KEY = 'BH8kqBwQFgYIJicvfh3ktlvrfiafEdFiQ1PsIdgIPcLaI7CtNSy1JQQJ99BCACGhslBXJ3w3AAAYACOG7Pzs'
AZURE_SPEECH_REGION = 'centralindia'
SERPER_API_KEY = '4477a4b3ad7806b348a4015d18e0f6f993962229'

# Azure Document Intelligence Settings
DOCUMENT_INTELLIGENCE_ENDPOINT = "https://documentintelligence-p2.cognitiveservices.azure.com/"
DOCUMENT_INTELLIGENCE_KEY = "BCdbdF3eNpoveqP7mxy01fcS5PoydTVy3BXOk4CPilG0KDUxGbtBJQQJ99BEACYeBjFXJ3w3AAALACOGEQxo"
 
# SharePoint Settings
SHAREPOINT_SITE_URL = 'https://thdc.sharepoint.com/sites/VideoConference2'
SHAREPOINT_USERNAME = "<EMAIL>"
SHAREPOINT_PASSWORD = "Prasanna1$"
 
# FGD Video Settings
FGD_VIDEO_VECTOR_BLOB = 'fgd-vector-files'
AZURE_SEARCH_FGD_VIDEO_INDEX_NAME = "vector-1742884738327"
MIN_VECTOR_SCORE_THRESHOLD = 0.2

# JWT Settings
JWT_SECRET_KEY = 'your-secret-key-here'  # Make sure to change this in production
JWT_EXPIRATION_HOURS = 24

# Add this with your other Azure settings
FGD_VIDEOS_CONTAINER = 'fgd-videos'
 
# BACKEND_URL = "http://localhost:5001"
BACKEND_URL = "https://aivccontainerbakendp2.salmonbay-6d5d2866.southindia.azurecontainerapps.io"
 
# Add this with your other settings
GENERAL_MEDIA_CONTAINER = 'generalaimedia'

# =====================================================
# ENHANCED DOCUMENT PROCESSING CONFIGURATION
# =====================================================

# Enhanced Processing Feature Flags
ENHANCED_PROCESSING_ENABLED = True
INTELLIGENT_CHUNKING_ENABLED = True
CUSTOM_EMBEDDINGS_ENABLED = True
AZURE_AI_SEARCH_FALLBACK = True  # Fallback to existing Azure AI Search if enhanced processing fails

# Azure AI Search Enhanced Integration - USE SPECIFIED VECTOR INDEX
# AZURE_SEARCH_ENHANCED_INDEX_NAME = "vector-1742884738327"   #switch_uat
AZURE_SEARCH_ENHANCED_INDEX_NAME = "uat-vector-index"  #switch_local
AZURE_SEARCH_CHUNK_FIELD_NAME = 'enhanced_chunk'
AZURE_SEARCH_METADATA_FIELD_NAME = 'enhanced_metadata'
AZURE_SEARCH_TABLE_FIELD_NAME = 'table_content'

# Document Processing Parameters
CHUNK_SIZE = 5000  # Reduced from 1000 for better semantic coherence
CHUNK_OVERLAP = 200  # Increased from 200 for better context preservation
MIN_CHUNK_SIZE = 500  # Reduced from 100 to capture more content
MAX_CHUNK_SIZE = 8000  # Reduced from 2000 for better focus
EMBEDDING_BATCH_SIZE = 50  # Number of chunks to process in one batch

# Vector Search Configuration
VECTOR_SEARCH_TOP_K = 20  # Increased from 10 for better recall
MIN_VECTOR_SCORE_THRESHOLD = 0.5  # Lowered from 0.65 to allow more results through
VECTOR_SEARCH_RERANK_TOP_K = 10  # Re-rank top results for final selection
SEMANTIC_SIMILARITY_THRESHOLD = 0.7  # Threshold for semantic similarity

# Deep Vector Search Configuration (Per-File Re-ranking Strategy)
DEEP_VECTOR_SEARCH_TOP_K = 100  # Increased from 80 for better initial coverage
DEEP_MIN_VECTOR_SCORE_THRESHOLD = 0.45  # Lowered slightly from 0.5 to capture more diverse content
DEEP_VECTOR_SEARCH_RERANK_TOP_K = 60  # Increased from 50 for more comprehensive final results
DEEP_SEMANTIC_SIMILARITY_THRESHOLD = 0.6
AZURE_FARIC_CONNECTION_STRING = 'dab7htoxeinunkpzrb3f2fhbqe-mcsckz4njp7e3i7qhat4uoms4q.datawarehouse.fabric.microsoft.com'

# Deep Search Agent Configuration
DEEP_SEARCH_COVERAGE_THRESHOLD = 0.9  # 90% coverage target
DEEP_SEARCH_MAX_K = 128  # Maximum k value for retrieval
DEEP_SEARCH_MARGINAL_GAIN_THRESHOLD = 0.02  # 2% marginal gain cutoff
DEEP_SEARCH_SIMILARITY_THRESHOLD = 0.95  # Similarity threshold for answer refinement
DEEP_SEARCH_MAX_ITERATIONS = 4  # Maximum refinement iterations
DEEP_SEARCH_MAX_SUB_QUESTIONS = 5  # Maximum number of sub-questions for decomposition
DEEP_SEARCH_ENABLE_PARALLEL_PROCESSING = True  # Enable parallel processing of documents
DEEP_SEARCH_K_INCREASE_FACTOR = 1.5  # Factor to increase k by when expanding search
DEEP_SEARCH_FILTER_CITATIONS = True  # Whether to filter citations to only include relevant ones

# Advanced Chunking Configuration
SEMANTIC_CHUNKING_ENABLED = True  # Enable semantic boundary detection
SENTENCE_BOUNDARY_PRESERVATION = True  # Preserve sentence boundaries
PARAGRAPH_BOUNDARY_PRESERVATION = True  # Preserve paragraph boundaries
OVERLAP_STRATEGY = 'sliding_window'  # 'sliding_window' or 'sentence_overlap'

# Document Type Detection
SUPPORTED_DOCUMENT_TYPES = {
    'pdf': ['.pdf'],
    'excel': ['.xlsx', '.xls', '.csv'],
    'word': ['.docx', '.doc'],
    'powerpoint': ['.pptx', '.ppt'],
    'text': ['.txt', '.md', '.html', '.htm'],
    'image': ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff', '.webp']
}

# Table Processing Configuration
TABLE_DETECTION_ENABLED = False  # DISABLED - Skip all table processing
SPLIT_TABLE_DETECTION_ENABLED = False  # DISABLED - Skip split table detection
TABLE_MERGE_CONFIDENCE_THRESHOLD = 0.8
MAX_TABLE_ROWS_PER_CHUNK = 50

# OCR Configuration (for image processing)
OCR_ENABLED = True
OCR_LANGUAGE = 'eng'  # Tesseract language code
OCR_CONFIDENCE_THRESHOLD = 60  # Minimum confidence for OCR text

# Content Analysis Configuration
CONTENT_SUMMARY_ENABLED = True
ENTITY_EXTRACTION_ENABLED = True
CONTENT_SUMMARY_MAX_LENGTH = 500  # Maximum characters for AI-generated summary

# Processing Queue Configuration
ASYNC_PROCESSING_ENABLED = True
MAX_CONCURRENT_PROCESSING = 3  # Maximum number of files to process simultaneously
PROCESSING_TIMEOUT_MINUTES = 30  # Timeout for document processing

# Error Handling and Retry Configuration
MAX_PROCESSING_RETRIES = 3
RETRY_DELAY_SECONDS = 5
FALLBACK_TO_BASIC_PROCESSING = True  # Fall back to basic processing if enhanced fails

# Monitoring and Logging
PROCESSING_METRICS_ENABLED = True
DETAILED_LOGGING_ENABLED = True
PERFORMANCE_MONITORING_ENABLED = True

# Storage Configuration for Enhanced Processing
ENHANCED_BLOB_CONTAINER = AZURE_STORAGE_CONTAINER_NAME  # Use same container
CHUNK_STORAGE_PREFIX = 'chunks/'
TABLE_STORAGE_PREFIX = 'tables/'
METADATA_STORAGE_PREFIX = 'metadata/'

# AI Model Configuration for Enhanced Processing
CONTENT_ANALYSIS_MODEL = AZURE_OPENAI_DEPLOYMENT_NAME  # Use same GPT model for content analysis
EMBEDDING_MODEL = AZURE_OPENAI_EMBEDDING_DEPLOYMENT  # Use same embedding model
EMBEDDING_DIMENSIONS = 3072  # text-embedding-3-large dimensions

# Document Processing Limits
MAX_FILE_SIZE_MB = 100  # Maximum file size for enhanced processing
MAX_PAGES_PER_DOCUMENT = 1000  # Maximum pages to process
MAX_TABLES_PER_DOCUMENT = 100  # Maximum tables to extract

# Feature Rollout Configuration (for gradual deployment)
ENHANCED_PROCESSING_ROLLOUT_PERCENTAGE = 100  # Percentage of users to enable enhanced processing
BETA_USER_ENHANCED_PROCESSING = True  # Always enable for beta users
ADMIN_USER_ENHANCED_PROCESSING = True  # Always enable for admin users

# Cleanup Configuration
AUTO_CLEANUP_ENABLED = True
CLEANUP_FAILED_PROCESSING_AFTER_DAYS = 7
CLEANUP_OLD_CHUNKS_AFTER_DAYS = 365

# Performance Optimization
CACHE_EMBEDDINGS = True
CACHE_PROCESSING_RESULTS = True
CACHE_EXPIRY_HOURS = 24

# Conversation Context Configuration
CONVERSATION_HISTORY_LIMIT = 5  # Optimized to 5 messages for better context focus and performance

# Development and Testing
DEVELOPMENT_MODE = False  # Set to True for development
SKIP_HEAVY_PROCESSING_IN_DEV = False  # Skip time-consuming operations in development
TEST_MODE = False  # Set to True for testing

 