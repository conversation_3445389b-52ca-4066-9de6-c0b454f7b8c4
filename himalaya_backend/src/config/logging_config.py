"""
Enhanced Logging Configuration for Himalaya Backend

This module configures logging to reduce Azure SDK verbosity and provide
more useful developer information focused on application logic and performance.
"""

import logging
import sys
import os
from datetime import datetime

def setup_logging():
    """
    Setup enhanced logging configuration with reduced Azure SDK verbosity
    and improved application-level logging.
    """
    
    # Configure root logger
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # ============ REDUCE AZURE SDK VERBOSITY ============
    
    # Reduce Azure HTTP request/response logging to WARNING level
    # This eliminates the verbose "Request method", "Request headers", "Response headers" logs
    azure_loggers_to_quiet = [
        'azure.core.pipeline.policies.http_logging_policy',
        'azure.storage.blob',
        'azure.search.documents',
        'azure.ai.documentintelligence',
        'azure.identity',
        'azure.core',
        'azure.storage',
        'azure.search',
        'urllib3.connectionpool',
        'httpx'
    ]
    
    for logger_name in azure_loggers_to_quiet:
        logger = logging.getLogger(logger_name)
        logger.setLevel(logging.WARNING)  # Only show warnings and errors
        
    # ============ ENHANCE APPLICATION LOGGING ============
    
    # Create application-specific loggers with useful information
    app_loggers = {
        'services.enhanced_processing_service': logging.INFO,
        'services.azure_search_service': logging.INFO,
        'services.excel_processing_service': logging.INFO,
        'services.document_processing_service': logging.INFO,
        'api.chat_routes': logging.INFO,
        'api.agentic_chat_handler': logging.INFO,
        'agents': logging.INFO,
        'utils': logging.INFO
    }
    
    for logger_name, level in app_loggers.items():
        logger = logging.getLogger(logger_name)
        logger.setLevel(level)
    
    # ============ CREATE PERFORMANCE LOGGER ============
    
    # Create a dedicated performance logger
    perf_logger = logging.getLogger('performance')
    perf_logger.setLevel(logging.INFO)
    
    # Add console handler for performance logs
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(logging.INFO)
    
    # Enhanced formatter for performance logs
    perf_formatter = logging.Formatter(
        '⚡ %(asctime)s - PERF - %(message)s',
        datefmt='%H:%M:%S'
    )
    console_handler.setFormatter(perf_formatter)
    
    if not perf_logger.handlers:
        perf_logger.addHandler(console_handler)
    
    # ============ CREATE AGENTIC WORKFLOW LOGGER ============
    
    # Create a dedicated agentic workflow logger
    agentic_logger = logging.getLogger('agentic')
    agentic_logger.setLevel(logging.INFO)
    
    # Add console handler for agentic logs
    agentic_handler = logging.StreamHandler(sys.stdout)
    agentic_handler.setLevel(logging.INFO)
    
    # Enhanced formatter for agentic logs
    agentic_formatter = logging.Formatter(
        '🤖 %(asctime)s - AGENTIC - %(message)s',
        datefmt='%H:%M:%S'
    )
    agentic_handler.setFormatter(agentic_formatter)
    
    if not agentic_logger.handlers:
        agentic_logger.addHandler(agentic_handler)
    
    # ============ SUPPRESS NOISY THIRD-PARTY LOGS ============
    
    # Reduce verbosity of other noisy third-party libraries
    noisy_loggers = [
        'werkzeug',
        'requests.packages.urllib3',
        'urllib3',
        'PIL',
        'matplotlib',
        'pandas'
    ]
    
    for logger_name in noisy_loggers:
        logger = logging.getLogger(logger_name)
        logger.setLevel(logging.WARNING)
    
    # ============ PRINT CONFIGURATION SUMMARY ============
    
    print("🔧 LOGGING CONFIGURATION:")
    print("   ✅ Azure SDK HTTP verbosity: REDUCED (WARNING level)")
    print("   ✅ Application logic logging: ENHANCED (INFO level)")
    print("   ✅ Performance logging: ACTIVE (⚡ prefix)")
    print("   ✅ Agentic workflow logging: ACTIVE (🤖 prefix)")
    print("   ✅ Third-party noise: SUPPRESSED")
    print("   📊 Focus: Application logic, performance, and developer insights")
    print("")


class PerformanceLogger:
    """
    Enhanced performance logger with useful metrics for developers.
    """
    
    def __init__(self):
        self.logger = logging.getLogger('performance')
    
    def log_api_call(self, endpoint: str, method: str, duration: float, status_code: int = None):
        """Log API call performance"""
        status_info = f" ({status_code})" if status_code else ""
        self.logger.info(f"API {method} {endpoint} - {duration:.2f}s{status_info}")
    
    def log_azure_operation(self, operation: str, resource: str, duration: float, success: bool = True):
        """Log Azure operation performance"""
        status = "✅" if success else "❌"
        self.logger.info(f"{status} {operation} {resource} - {duration:.2f}s")
    
    def log_document_processing(self, filename: str, pages: int, chunks: int, tables: int, duration: float):
        """Log document processing performance"""
        self.logger.info(f"📄 {filename} - {pages}p, {chunks}c, {tables}t - {duration:.2f}s")
    
    def log_vector_search(self, query: str, results_count: int, duration: float):
        """Log vector search performance"""
        query_preview = query[:50] + "..." if len(query) > 50 else query
        self.logger.info(f"🔍 '{query_preview}' - {results_count} results - {duration:.2f}s")
    
    def log_agent_execution(self, agent_type: str, query: str, duration: float, success: bool = True):
        """Log agent execution performance"""
        status = "✅" if success else "❌"
        query_preview = query[:30] + "..." if len(query) > 30 else query
        self.logger.info(f"{status} {agent_type} '{query_preview}' - {duration:.2f}s")


class AgenticLogger:
    """
    Enhanced agentic workflow logger with detailed insights.
    """
    
    def __init__(self):
        self.logger = logging.getLogger('agentic')
    
    def log_planner_decision(self, query: str, agent_type: str, confidence: float = None):
        """Log planner agent decision"""
        query_preview = query[:40] + "..." if len(query) > 40 else query
        confidence_info = f" (confidence: {confidence:.2f})" if confidence else ""
        self.logger.info(f"🎯 PLANNER: '{query_preview}' → {agent_type}{confidence_info}")
    
    def log_agent_start(self, agent_type: str, query: str):
        """Log agent start"""
        query_preview = query[:40] + "..." if len(query) > 40 else query
        self.logger.info(f"🚀 {agent_type}: Starting - '{query_preview}'")
    
    def log_agent_complete(self, agent_type: str, success: bool, duration: float):
        """Log agent completion"""
        status = "✅" if success else "❌"
        self.logger.info(f"{status} {agent_type}: Complete - {duration:.2f}s")
    
    def log_retrieval_stats(self, csv_docs: int, text_docs: int, total_chunks: int):
        """Log retrieval statistics"""
        self.logger.info(f"📊 RETRIEVAL: {csv_docs} CSV docs, {text_docs} text docs, {total_chunks} total chunks")
    
    def log_improvement_loop(self, iteration: int, max_iterations: int, improvement_needed: bool):
        """Log improvement loop status"""
        status = "🔄" if improvement_needed else "✅"
        self.logger.info(f"{status} QA LOOP: {iteration}/{max_iterations} - {'Needs improvement' if improvement_needed else 'Approved'}")
    
    def log_multi_agent_coordination(self, agents_used: list, total_duration: float):
        """Log multi-agent coordination summary"""
        agents_str = " → ".join(agents_used)
        self.logger.info(f"🎭 COORDINATION: {agents_str} - {total_duration:.2f}s total")
    
    def log_qa_evaluation(self, agent_type: str, needs_improvement: bool, evaluation_score: float = None, improvement_instructions: str = None):
        """Log QA evaluation results"""
        status = "🔍" if needs_improvement else "✅"
        score_info = f" (score: {evaluation_score:.2f})" if evaluation_score else ""
        self.logger.info(f"{status} QA_EVAL: {agent_type} - {'Needs improvement' if needs_improvement else 'Approved'}{score_info}")
        if improvement_instructions and needs_improvement:
            self.logger.info(f"💡 QA_SUGGESTION: {improvement_instructions[:100]}{'...' if len(improvement_instructions) > 100 else ''}")


# Global instances for easy access
performance = PerformanceLogger()
agentic = AgenticLogger()


def get_logger(name: str) -> logging.Logger:
    """Get a properly configured logger for the given name"""
    return logging.getLogger(name)


# Initialize logging when module is imported
setup_logging() 