import os
from openai import AzureOpenAI
import pdfplumber
import markdownify
from io import BytesIO
from markdown_pdf import MarkdownPdf, Section
from docx import Document
import tempfile
from azure.storage.blob import BlobServiceClient
import subprocess
import shutil
from concurrent.futures import ThreadPoolExecutor, wait, FIRST_COMPLETED
import logging
from typing import List, Tuple, Dict, Any
from dotenv import load_dotenv
from config.settings import (
    AZURE_OPENAI_KEY, AZURE_OPENAI_API_VERSION, AZURE_OPENAI_ENDPOINT,
    AZURE_OPENAI_DEPLOYMENT_NAME, AZURE_STORAGE_CONNECTION_STRING,
    SHAREPOINT_SITE_URL, SHAREPOINT_USERNAME, SHAREPOINT_PASSWORD
)

# Load environment variables
load_dotenv()

client = AzureOpenAI(
    api_key=AZURE_OPENAI_KEY,
    api_version=AZURE_OPENAI_API_VERSION,
    azure_endpoint=AZURE_OPENAI_ENDPOINT
)

# Updated function to extract text from PDF and convert to markdown
def extract_markdown_from_pdf(pdf_buffer):
    with pdfplumber.open(pdf_buffer) as pdf:
        all_text = ""
        for page in pdf.pages:
            text = page.extract_text()
            if text:
                all_text += text + "\n"
    # Convert plain text to markdown format using markdownify
    markdown_text = markdownify.markdownify(all_text, heading_style="ATX")
    return markdown_text

# Updated function to extract text from DOCX
def extract_markdown_from_docx(docx_buffer):
    """Extract markdown from DOCX using LibreOffice conversion"""
    # Create temporary directory
    temp_dir = tempfile.mkdtemp()
    
    try:
        # Save DOCX to temporary file
        temp_docx = os.path.join(temp_dir, 'temp.docx')
        with open(temp_docx, 'wb') as f:
            f.write(docx_buffer.getvalue())
        
        # Convert DOCX to PDF
        pdf_path = convert_docx_to_pdf_using_libreoffice(temp_docx, temp_dir)
        
        # Extract markdown from the PDF
        with open(pdf_path, 'rb') as pdf_file:
            markdown_text = extract_markdown_from_pdf(pdf_file)
        
        return markdown_text
    finally:
        # Clean up temporary directory
        shutil.rmtree(temp_dir, ignore_errors=True)

# Function to translate the PDF content using Azure OpenAI API
def translate_pdf_content(markdown_text, target_language):
 # Debug log
    prompt = f"""Translate the following markdown text to {target_language}. 
    Preserve the overall structure and formatting of the original markdown meticulously.
    If the original text is not in markdown, convert it to markdown, then translate and return the markdown text. 
    Adhere to these guidelines for the translation:

    1. Markdown Formatting:
       - Maintain all heading levels (# for h1, ## for h2, etc.)
       - Preserve bullet points, numbered lists, and their hierarchy
       - Keep code blocks intact (``` for multi-line, ` for inline)
       - Retain emphasis (*italic*, **bold**, ***bold italic***)
       - Preserve links [text](URL) and images ![alt text](image_url)
       - Maintain table structures and alignment

    2. Document Structure:
       - Keep the hierarchy and flow of content intact
       - Preserve paragraph breaks and line spacing
       - Maintain list structures and indentation
       - Recreate tables using proper markdown syntax

    3. Readability and Formatting:
       - Use appropriate indentation for nested elements
       - Preserve horizontal rules (---)
       - Maintain block quotes (> for single level, >> for nested)
       - Ensure consistent spacing between sections

    4. Special Content:
       - Preserve LaTeX or mathematical formulas between $$ delimiters
       - Maintain HTML elements if present
       - Keep file paths, variable names, and code snippets unchanged

    5. Context-Specific Translation:
       - Keep the following in English:
         * Product names
         * Company names
         * Brand names
         * Trademarks
         * Software names
         * Technical terms
         * Proper nouns
         * Acronyms
       - For untranslated words, provide a translation in parentheses if helpful
       - Use discretion to determine which other terms should remain in English
       - Maintain industry-specific jargon or abbreviations as-is

    6. Cultural Sensitivity:
       - Adapt idioms and cultural references appropriately for the target language
       - Provide explanations for culture-specific concepts if necessary

    7. Consistency:
       - Maintain consistent terminology throughout the document
       - Use the same translation for recurring terms or phrases

    8. Quality Assurance:
       - Double-check numerical data, dates, and units of measurement
       - Ensure that hyperlinks and cross-references remain functional
       - Verify that the overall meaning and tone of the original text are preserved

    Original markdown text:
    {markdown_text}
    """

    try:
        response = client.chat.completions.create(
            model=AZURE_OPENAI_DEPLOYMENT_NAME,
            temperature=0.3,
            seed=619,
            messages=[
                {"role": "system", "content": "You are an expert translation assistant with deep knowledge of markdown formatting and technical content."},
                {"role": "user", "content": prompt}
            ]
        )

        translated_markdown = response.choices[0].message.content
        return {
            "translated_markdown": translated_markdown
        }
    except Exception as e:
        raise Exception(f"Azure OpenAI API Error: {e}")

def markdown_to_pdf(markdown_content, title='Translated PDF'):
    pdf = MarkdownPdf()
    pdf.meta["title"] = title
    pdf.add_section(Section(markdown_content, toc=False))
    
    pdf_buffer = BytesIO()
    pdf.save(pdf_buffer)
    pdf_buffer.seek(0)
    
    return pdf_buffer

# New function to convert markdown to DOCX
def markdown_to_docx(markdown_content, title='Translated Document'):
    doc = Document()
    doc.add_heading(title, 0)
    
    for line in markdown_content.split('\n'):
        if line.startswith('#'):
            level = line.count('#')
            text = line.strip('#').strip()
            doc.add_heading(text, level)
        else:
            doc.add_paragraph(line)
    
    docx_buffer = BytesIO()
    doc.save(docx_buffer)
    docx_buffer.seek(0)
    
    return docx_buffer

# Add this function to upload a file to Azure Blob Storage
def upload_to_blob_storage(file_buffer, file_name, container_name):
    blob_service_client = BlobServiceClient.from_connection_string(AZURE_STORAGE_CONNECTION_STRING)
    
    # Create container if it doesn't exist
    try:
        container_client = blob_service_client.create_container(container_name)
    except Exception:
        # Container already exists, get the container client
        container_client = blob_service_client.get_container_client(container_name)
    
    blob_client = container_client.get_blob_client(file_name)
    blob_client.upload_blob(file_buffer.getvalue(), overwrite=True)
    
    return blob_client.url

def convert_docx_to_pdf_using_libreoffice(docx_path, output_dir):
    """Convert DOCX to PDF using LibreOffice"""
    try:
        cmd = f'soffice --headless --convert-to pdf --outdir "{output_dir}" "{docx_path}"'
        process = subprocess.Popen(cmd, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        output, error = process.communicate()
        
        if process.returncode != 0:
            raise Exception(f"Conversion failed: {error.decode()}")
        
        # Get the output PDF path
        pdf_path = os.path.join(output_dir, os.path.splitext(os.path.basename(docx_path))[0] + '.pdf')
        return pdf_path
    except Exception as e:
        raise Exception(f"LibreOffice conversion failed: {str(e)}")

def process_same_language(file_buffer, file_name, lang):
    file_extension = os.path.splitext(file_name)[1].lower()
    
    if file_extension == '.docx':
        # Create temporary directory
        temp_dir = tempfile.mkdtemp()
        try:
            # Save DOCX to temporary file
            temp_docx = os.path.join(temp_dir, 'temp.docx')
            with open(temp_docx, 'wb') as f:
                f.write(file_buffer.getvalue())
            
            # Convert DOCX to PDF
            pdf_path = convert_docx_to_pdf_using_libreoffice(temp_docx, temp_dir)
            
            # Read the PDF into a buffer
            with open(pdf_path, 'rb') as pdf_file:
                pdf_buffer = BytesIO(pdf_file.read())
            
            # Update file name to .pdf
            file_name = file_name.rsplit('.', 1)[0] + '.pdf'
            
            return {
                "translated_language": lang.lower(),
                "file_name": file_name,
                "blob_storage_link": upload_to_blob_storage(pdf_buffer, file_name, "translated-documents")
            }
        finally:
            # Clean up temporary directory
            shutil.rmtree(temp_dir, ignore_errors=True)
    else:
        pdf_buffer = file_buffer
        return {
            "translated_language": lang.lower(),
            "file_name": file_name,
            "blob_storage_link": upload_to_blob_storage(pdf_buffer, file_name, "translated-documents")
        }

def process_different_language(file_buffer, file_extension, original_file_name, lang_name, lang_code):
    # Create temporary directory
    temp_dir = tempfile.mkdtemp()
    
    try:
        # Extract markdown based on file extension
        if file_extension == '.pdf':
            markdown_text = extract_markdown_from_pdf(file_buffer)
        elif file_extension == '.docx':
            # Save DOCX to temporary file
            temp_docx = os.path.join(temp_dir, 'temp.docx')
            with open(temp_docx, 'wb') as f:
                f.write(file_buffer.getvalue())
            
            # Convert DOCX to PDF first
            pdf_path = convert_docx_to_pdf_using_libreoffice(temp_docx, temp_dir)
            
            # Extract markdown from the PDF
            with open(pdf_path, 'rb') as pdf_file:
                markdown_text = extract_markdown_from_pdf(pdf_file)
        else:
            raise ValueError("Unsupported file format")

        # Translate the markdown using Azure OpenAI API
        try:
            translated_response = translate_pdf_content(markdown_text, lang_name)
            translated_markdown = translated_response['translated_markdown']
            pdf_buffer = markdown_to_pdf(translated_markdown[11:-3])
            
            # Generate new file name using language code
            new_file_name = original_file_name.rsplit('_', 1)[0] + f"_{lang_code.lower()}.pdf"
            
            return {
                "translated_language": lang_code.lower(),
                "file_name": new_file_name,
                "blob_storage_link": upload_to_blob_storage(pdf_buffer, new_file_name, "translated-documents")
            }
        except Exception as e:
            raise Exception(f"Translation error for {lang_name}: {str(e)}")
    finally:
        # Clean up temporary directory
        shutil.rmtree(temp_dir, ignore_errors=True)

def process_language(args):
    file_buffer, file_extension, filename, lang_name, base_language, lang_code = args
    try:
        if lang_code == base_language.lower():
            return process_same_language(file_buffer, filename, lang_code)
        else:
            return process_different_language(file_buffer, file_extension, filename, lang_name, lang_code)
    except Exception as e:
        return None
    
def process_translation_batch(process_args: List[Tuple], max_workers: int = 5, 
                            retry_count: int = 3, timeout_seconds: int = 300) -> Tuple[List, List]:
    """
    Process a batch of translations concurrently with retry mechanism
    
    Args:
        process_args: List of argument tuples for translation
        max_workers: Maximum number of concurrent workers
        retry_count: Maximum number of retry attempts
        timeout_seconds: Timeout duration for each translation
        
    Returns:
        Tuple containing lists of translated files and failed languages
    """
    translated_files = []
    failed_languages = []
    pending_languages = set([args[3] for args in process_args])  # Track languages to be processed

    with ThreadPoolExecutor(max_workers=min(len(process_args), max_workers)) as executor:
        future_to_lang = {
            executor.submit(process_language, args): (args[3], 0)  # (language, retry_count)
            for args in process_args
        }
        
        while future_to_lang:
            done, _ = wait(
                future_to_lang.keys(), 
                timeout=timeout_seconds,
                return_when=FIRST_COMPLETED
            )
            
            for future in done:
                lang, attempts = future_to_lang.pop(future)
                try:
                    result = future.result()
                    if result is not None:
                        translated_files.append(result)
                        pending_languages.remove(lang)
                        logging.info(f"Translation completed for {lang}")
                    elif attempts < retry_count:
                        # Retry the translation
                        logging.warning(f"Retrying translation for {lang}, attempt {attempts + 1}")
                        new_future = executor.submit(process_language, next(
                            args for args in process_args if args[3] == lang
                        ))
                        future_to_lang[new_future] = (lang, attempts + 1)
                    else:
                        # Max retries reached
                        logging.error(f"Translation failed for {lang} after {retry_count} attempts")
                        failed_languages.append(lang)
                        pending_languages.remove(lang)
                except Exception as e:
                    logging.error(f"Error processing {lang}: {str(e)}")
                    if attempts < retry_count:
                        # Retry on error
                        logging.warning(f"Retrying translation for {lang}, attempt {attempts + 1}")
                        new_future = executor.submit(process_language, next(
                            args for args in process_args if args[3] == lang
                        ))
                        future_to_lang[new_future] = (lang, attempts + 1)
                    else:
                        # Max retries reached
                        logging.error(f"Translation failed for {lang} after {retry_count} attempts")
                        failed_languages.append(lang)
                        pending_languages.remove(lang)

            # Check if we still have pending translations
            if not future_to_lang and pending_languages:
                logging.error(f"Translations incomplete for languages: {pending_languages}")
                failed_languages.extend(pending_languages)
                break

    return translated_files, list(set(failed_languages))  # Remove any duplicates

def perform_docx_to_pdf_conversion(file):
    """Convert DOCX to PDF using LibreOffice"""
    if not file.filename.lower().endswith('.docx'):
        raise ValueError("Please provide a valid DOCX file")

    pdf_filename = os.path.splitext(file.filename)[0] + '.pdf'
    temp_dir = tempfile.mkdtemp()

    try:
        # Save the uploaded file
        temp_docx = os.path.join(temp_dir, 'temp.docx')
        file.save(temp_docx)

        # Convert to PDF
        pdf_path = convert_docx_to_pdf_using_libreoffice(temp_docx, temp_dir)

        # Read the PDF into a buffer
        with open(pdf_path, 'rb') as pdf_file:
            pdf_buffer = BytesIO(pdf_file.read())

        return pdf_buffer, pdf_filename
    finally:
        # Clean up temporary directory
        shutil.rmtree(temp_dir, ignore_errors=True)