import sys
import os

# Add the src directory to Python path to resolve imports
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.dirname(current_dir)  # Go up from utils to src
if src_dir not in sys.path:
    sys.path.insert(0, src_dir)

from audio_processor import AudioProcessor
import logging

# Configure logging to see the transcription process
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def transcribe_local_audio_file(audio_file_path: str):
    """
    Test function to transcribe one audio file from local storage
    
    Args:
        audio_file_path (str): Path to the local audio file
    
    Returns:
        tuple: (transcription_text, metadata) or (None, None) if failed
    """
    try:
        # Check if file exists
        if not os.path.exists(audio_file_path):
            logger.error(f"Audio file not found: {audio_file_path}")
            return None, None
        
        # Initialize AudioProcessor
        logger.info("Initializing AudioProcessor...")
        processor = AudioProcessor()
        
        # Get file extension to determine file type
        file_ext = os.path.splitext(audio_file_path)[1].lower()
        
        if file_ext in ['.mp4', '.avi', '.mov', '.mkv', '.wmv']:
            # If it's a video file, extract audio first
            logger.info(f"Video file detected. Extracting audio from: {audio_file_path}")
            
            # Create output directory for extracted audio
            output_dir = os.path.join(os.path.dirname(audio_file_path), "extracted_audio")
            os.makedirs(output_dir, exist_ok=True)
            
            # Extract audio from video
            extracted_audio_path = processor.extract_audio(audio_file_path, output_dir)
            
            if not extracted_audio_path:
                logger.error("Failed to extract audio from video file")
                return None, None
            
            # Transcribe the extracted audio
            logger.info(f"Transcribing extracted audio: {extracted_audio_path}")
            transcription, metadata = processor.transcribe_audio(extracted_audio_path)
            
            # Clean up extracted audio file
            try:
                os.remove(extracted_audio_path)
                logger.info("Cleaned up extracted audio file")
            except Exception as e:
                logger.warning(f"Could not clean up extracted audio file: {e}")
                
        else:
            # Direct audio file transcription
            logger.info(f"Audio file detected. Transcribing: {audio_file_path}")
            transcription, metadata = processor.transcribe_media_file(audio_file_path)
        
        # Display results
        if transcription and metadata:
            logger.info("Transcription completed successfully!")
            print("\n" + "="*50)
            print("TRANSCRIPTION RESULTS")
            print("="*50)
            print(f"File: {audio_file_path}")
            print(f"Processing Time: {metadata.get('processing_time', 'N/A')} seconds")
            print(f"Word Count: {metadata.get('word_count', 'N/A')}")
            print(f"Character Count: {metadata.get('character_count', 'N/A')}")
            print(f"Number of Segments: {metadata.get('segments', 'N/A')}")
            print(f"Duration: {metadata.get('duration', 'N/A')}")
            print("\nTranscription Text:")
            print("-" * 30)
            print(transcription)
            print("="*50)
            
            return transcription, metadata
        else:
            logger.error("Transcription failed")
            return None, None
            
    except Exception as e:
        logger.error(f"Error during transcription: {str(e)}")
        return None, None

def main():
    """
    Main function to test audio transcription
    """
    # Example usage - replace with your actual file path
    # audio_file_path = r"C:\path\to\your\audio\file.wav"  # For Windows
    # audio_file_path = "/path/to/your/audio/file.wav"     # For Linux/Mac
    
    print("Audio Transcription Test")
    print("="*30)
    
    # Get file path from user input
    audio_file_path = input("Enter the path to your audio/video file: ").strip()
    
    # Remove quotes if user included them
    audio_file_path = audio_file_path.strip('"').strip("'")
    
    if not audio_file_path:
        print("No file path provided. Exiting.")
        return
    
    # Test transcription
    transcription, metadata = transcribe_local_audio_file(audio_file_path)
    
    if transcription:
        print("\n✅ Transcription test completed successfully!")
    else:
        print("\n❌ Transcription test failed!")

if __name__ == "__main__":
    main()
