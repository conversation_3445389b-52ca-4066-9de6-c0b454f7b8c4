"""
Visualization utilities for generating charts and tables.

This module provides functions for creating visualizations from data.
"""

import io
import base64
import json
import logging
from typing import Dict, List, Any, Optional, Union
import matplotlib
matplotlib.use('Agg')  # Use non-interactive backend
import matplotlib.pyplot as plt
import numpy as np
from matplotlib.figure import Figure

# Setup logger
logger = logging.getLogger(__name__)

def generate_chart(chart_data: Dict[str, Any]) -> Optional[str]:
    """
    Generate a chart image from structured data.

    Args:
        chart_data: Dictionary containing chart configuration and data
            {
                "type": "line|bar|pie|scatter",
                "title": "Chart title",
                "labels": ["label1", "label2", ...],
                "datasets": [
                    {
                        "label": "Dataset 1",
                        "data": [val1, val2, ...]
                    },
                    ...
                ]
            }

    Returns:
        Base64 encoded PNG image of the chart, or None if generation fails
    """
    try:
        if not chart_data:
            logger.warning("No chart data provided")
            return None

        # Extract chart properties
        chart_type = chart_data.get("type", "bar").lower()
        title = chart_data.get("title", "")
        labels = chart_data.get("labels", [])
        datasets = chart_data.get("datasets", [])

        if not labels or not datasets:
            logger.warning("Missing required chart data (labels or datasets)")
            return None

        # Create figure and axis
        plt.figure(figsize=(10, 6), dpi=100)
        plt.title(title, fontsize=16)

        # Generate chart based on type
        if chart_type == "pie":
            # For pie charts, use only the first dataset
            if datasets and len(datasets) > 0:
                data = datasets[0].get("data", [])
                if len(data) != len(labels):
                    logger.warning(f"Data length ({len(data)}) doesn't match labels length ({len(labels)})")
                    return None

                plt.pie(data, labels=labels, autopct='%1.1f%%', startangle=90)
                plt.axis('equal')  # Equal aspect ratio ensures that pie is drawn as a circle

        elif chart_type == "bar":
            # Set up x positions
            x = np.arange(len(labels))
            width = 0.8 / len(datasets)  # Width of the bars

            # Plot each dataset as a group of bars
            for i, dataset in enumerate(datasets):
                data = dataset.get("data", [])
                label = dataset.get("label", f"Dataset {i+1}")
                offset = width * i - width * (len(datasets) - 1) / 2
                plt.bar(x + offset, data, width, label=label)

            plt.xlabel('Categories')
            plt.ylabel('Values')
            plt.xticks(x, labels, rotation=45, ha="right")
            plt.legend()

        elif chart_type == "line":
            # Plot each dataset as a line
            for i, dataset in enumerate(datasets):
                data = dataset.get("data", [])
                label = dataset.get("label", f"Dataset {i+1}")
                plt.plot(labels, data, marker='o', label=label)

            plt.xlabel('X Axis')
            plt.ylabel('Y Axis')
            plt.xticks(rotation=45, ha="right")
            plt.legend()

        elif chart_type == "scatter":
            # For scatter plots, we need at least two datasets (x and y)
            if len(datasets) >= 2:
                x_data = datasets[0].get("data", [])
                y_data = datasets[1].get("data", [])
                label = datasets[0].get("label", "Data Points")
                plt.scatter(x_data, y_data, label=label)
                plt.xlabel(datasets[0].get("label", "X Axis"))
                plt.ylabel(datasets[1].get("label", "Y Axis"))
                plt.legend()
            else:
                logger.warning("Scatter plot requires at least two datasets (x and y)")
                return None
        else:
            # Default to bar chart for unknown types
            logger.warning(f"Unknown chart type: {chart_type}, defaulting to bar chart")
            for i, dataset in enumerate(datasets):
                data = dataset.get("data", [])
                label = dataset.get("label", f"Dataset {i+1}")
                plt.bar(labels, data, label=label)
            plt.xlabel('Categories')
            plt.ylabel('Values')
            plt.xticks(rotation=45, ha="right")
            plt.legend()

        # Add grid and tight layout
        plt.grid(True, linestyle='--', alpha=0.7)
        plt.tight_layout()

        # Save the figure to a bytes buffer
        buf = io.BytesIO()
        plt.savefig(buf, format='png')
        plt.close()

        # Encode the image as base64
        buf.seek(0)
        img_str = base64.b64encode(buf.getvalue()).decode('utf-8')

        # Return the base64 encoded image with data URI prefix
        return f"data:image/png;base64,{img_str}"

    except Exception as e:
        logger.error(f"Error generating chart: {str(e)}")
        return None

def determine_chart_type(query: str, data: Optional[Dict[str, Any]] = None) -> str:
    """
    Determine the most appropriate chart type based on the query and data.

    Args:
        query: The user's query
        data: Optional structured data to analyze

    Returns:
        The recommended chart type: 'line', 'bar', 'pie', 'scatter', etc.
    """
    query = query.lower()

    # Check for explicit chart type mentions in the query
    if any(term in query for term in ['line chart', 'line graph', 'trend line', 'time series']):
        return 'line'
    elif any(term in query for term in ['bar chart', 'bar graph', 'histogram', 'column chart']):
        return 'bar'
    elif any(term in query for term in ['pie chart', 'pie graph', 'percentage breakdown', 'market share']):
        return 'pie'
    elif any(term in query for term in ['scatter plot', 'scatter chart', 'correlation', 'x vs y']):
        return 'scatter'
    elif any(term in query for term in ['area chart', 'area graph', 'stacked area']):
        return 'area'
    elif any(term in query for term in ['heatmap', 'heat map']):
        return 'heatmap'

    # If no explicit mention, infer from query content
    if any(term in query for term in ['trend', 'over time', 'growth', 'decline', 'increase', 'decrease', 'year', 'month', 'decade']):
        return 'line'  # Time series data typically uses line charts
    elif any(term in query for term in ['compare', 'comparison', 'versus', 'vs', 'difference between', 'performance', 'quarterly performance']):
        return 'bar'  # Comparisons often use bar charts
    elif any(term in query for term in ['distribution', 'breakdown', 'composition', 'percentage', 'proportion', 'share']):
        return 'pie'  # Distributions often use pie charts
    elif any(term in query for term in ['relationship', 'correlation', 'scatter', 'plot']):
        return 'scatter'  # Relationships often use scatter plots

    # Special case for 'quarter' - could be time series or comparison
    if 'quarter' in query:
        # If comparing multiple items, use bar chart
        if any(term in query for term in ['different', 'departments', 'categories', 'products', 'companies']):
            return 'bar'
        # Otherwise, assume it's a time series
        return 'line'

    # If we have data, analyze it to determine the best chart type
    if data and isinstance(data, dict):
        datasets = data.get('datasets', [])
        labels = data.get('labels', [])

        # Check if we have time-based labels (years, months, dates)
        time_based = False
        if labels:
            # Check if labels look like years or dates
            if all(str(label).isdigit() and len(str(label)) == 4 and 1900 <= int(label) <= 2100 for label in labels):
                time_based = True  # Years
            elif any(month in str(label).lower() for label in labels for month in ['jan', 'feb', 'mar', 'apr', 'may', 'jun', 'jul', 'aug', 'sep', 'oct', 'nov', 'dec']):
                time_based = True  # Months

        # Single dataset with few categories is good for pie charts
        if len(datasets) == 1 and len(labels) <= 8:
            # Check if data sums to approximately 100 or if it's percentage-like
            data_values = datasets[0].get('data', [])
            if data_values:
                total = sum(data_values)
                if 95 <= total <= 105 or all(0 <= val <= 100 for val in data_values):
                    return 'pie'

        # Time-based data with multiple points is good for line charts
        if time_based and len(labels) > 3:
            return 'line'

        # Multiple datasets with categories is good for bar charts
        if len(datasets) > 1 and len(labels) > 0:
            return 'bar'

    # Default to bar chart as it's the most versatile
    return 'bar'

def parse_chart_data(chart_data_str: str) -> Optional[Dict[str, Any]]:
    """
    Parse chart data from a string representation.

    Args:
        chart_data_str: String containing chart data, either as JSON or a description

    Returns:
        Dictionary containing structured chart data, or None if parsing fails
    """
    try:
        # If it's already a dictionary, return it
        if isinstance(chart_data_str, dict):
            return chart_data_str

        # Try to parse as JSON
        chart_data = json.loads(chart_data_str)
        return chart_data
    except (json.JSONDecodeError, TypeError):
        logger.error(f"Failed to parse chart data: {chart_data_str}")
        return None
