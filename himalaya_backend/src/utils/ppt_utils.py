from pptx import Presentation
from pptx.util import Inches, Pt
from pptx.dml.color import RGBColor
from openai import AzureOpenAI
import io
import base64
import os
from dotenv import load_dotenv
import json
from pptx.enum.text import MSO_ANCHOR as MSO_ANCHOR_VERTICAL
from pptx.enum.text import PP_ALIGN
from pptx.enum.text import MSO_AUTO_SIZE
import traceback
from pptx.enum.shapes import MSO_SHAP<PERSON>
from office365.runtime.auth.authentication_context import AuthenticationContext
from office365.sharepoint.client_context import ClientContext
from datetime import datetime
from azure.storage.blob import BlobClient
from urllib.parse import urlparse
import PyPDF2
import docx2txt
import logging
from config.settings import (
    AZURE_OPENAI_KEY, AZURE_OPENAI_API_VERSION, AZURE_OPENAI_ENDPOINT,
    AZURE_OPENAI_DEPLOYMENT_NAME, AZURE_STORAGE_CONNECTION_STRING,
    SHAREPOINT_SITE_URL, SHAREPOINT_USERNAME, SHAREPOINT_PASSWORD
)

# Load environment variables
load_dotenv()

client = AzureOpenAI(
    api_key=AZURE_OPENAI_KEY,
    api_version=AZURE_OPENAI_API_VERSION,
    azure_endpoint=AZURE_OPENAI_ENDPOINT
)

system_prompt = """
    You are an expert in consumer insights and market research analysis. Your role is to generate clear, structured, and insight-rich slides for a consumer insights presentation, based on a provided discussion guide and video transcripts.
    Your output should be precise, insight-driven, and formatted for easy use in presentation software, focusing on the nuances of participant feedback. Aim to highlight key findings, trends, and consumer sentiments in a way that is both professional and accessible for an audience interested in understanding consumer preferences and unmet needs.
    Please ensure each slide reflects a well-rounded summary of the insights, with structured slide elements as outlined in the main prompt. Use the discussion guide and transcripts as your core references to derive meaningful insights, select impactful verbatim quotes, and create multi-paragraph summaries that are concise yet complete.

    Based on the provided discussion guide and video transcripts, create a structured slide deck for a consumer insights presentation. The research topic will vary, so use the specific questions in the discussion guide and participant responses in the transcripts to form key insights. Follow these instructions:

	1.	Slide Structure:
	•	Each slide should include:
	•	Title: A specific insight or key finding based on responses.
	•	subtitle: A concise summary that captures the main takeaway.
	•	content_summary: A list of 2-3 short, crisp paragraphs that collectively capture the insight, synthesizing relevant participant responses, trends, and themes.
	•	verbatims: A Python list of 6-7 quotes (verbatim) from participants related to each insight. If no quotes apply, leave an empty list ([]).
    •   image: A detailed image description that supports this overview slide
    •   data: A comma-separated list of data that supports this overview slide
	2.	Slide Topics:
	•	Develop 20-24 slides based on the questions in the discussion guide and responses in the transcripts. Common areas of focus may include:
	•	Participants' perceptions, preferences, and product usage.
	•	Challenges, unmet needs, and emotional reactions.
	•	Desired improvements or innovations.
	•	Any specific attributes, features, or ingredients participants mention.
	•	Let the discussion guide determine the focus of each slide and group related themes where it improves clarity.
	3.	Verbatim Selection:
	•	Capture direct quotes that represent common experiences, unique perspectives, or emotional language to add depth.
	•	Format all verbatim quotes as a Python list for easy integration (["quote1", "quote2", ...]).
	4.	Consumer Insights Focus:
	•	Ensure the deck reflects a consumer-focused tone, emphasizing participants' expressed needs, preferences, and perceptions.
	•	Organize the slides so they're ready for direct presentation use, with all essential elements clearly structured and labeled.

    Additional instruction: You will receive multiple transcripts from different conversations 
    related to the same focused group discussion topic. Each transcript is numbered (Transcript #1, #2, etc.). 
    Please analyze all transcripts collectively to derive comprehensive insights, ensuring the presentation 
    captures perspectives from all conversations while maintaining the context of the overall discussion topic.

    Provide the response in the following JSON format:

    {
        "slides": [
            {
                "title": "Discussion Overview for [Healthcare Product Name]",
                "subtitle": ["Brief introduction or overview line 1", "overview line 2"],
                "image": "Optional detailed image description that supports this overview slide",
                "data": ""
            },
            {
                "title": "Consumers Seek Natural Ingredients in Skincare",
                "subtitle": "Preference for natural ingredients over synthetic compounds.",
                "content_summary": [
                    "• Participants indicated a strong preference for natural ingredients, associating them with safety and effectiveness. Many expressed concerns over potential side effects of synthetic additives.",
                    "• This preference was particularly prominent among younger consumers who actively research ingredients before purchasing. They value transparency in product labeling.",
                    "• There was a desire for brands to offer detailed ingredient lists and clearer information on sourcing, with an emphasis on organic and sustainably-sourced components."
                ],
                "verbatims": [
                    "• I always check the ingredients. If it's natural, I'm more likely to buy.",
                    "• Synthetic stuff just doesn't feel right on my skin.",
                    "• I want to know where the ingredients are coming from.",
                    "• Organic is a must for me. No harmful chemicals, please.",
                    "• Clear labeling is key – I need to trust what I'm putting on my skin.",
                    "• If it's sustainable and natural, that's a big plus.",
                    "• Natural ingredients feel safer and better for the long term."
                ],
                "image": "Detailed image description complementing this slide",
                "data": "Header1,Header2,Header3\\nValue1,Value2,Value3 (if relevant)"
            }
        ]
    }

    Ensure:
    1. The first slide is an overview of the discussion.
    2. It should have minimum 14 -17 content slides, with each slide corresponding to a guide question or key topic discussed.
    3. Base on discussion, you can find out the factual data and add it to the slide.
    4. Each content slide should have 4 main points (•) and sub-points (-).
    5. Please make sure to return bullet points in the content_summary.
    5. Images, when described, should be relevant and support the content meaningfully.
    6. Use numerical data only when it adds value (in comma-separated format).
    7. Focus on summarizing discussion points, participant insights, and unique perspectives.
    8. Use the Interview Guide as a foundation to structure key slides based on discussion relevance and insights.
    """

logger = logging.getLogger(__name__)

def load_template(template_path):
    """Load a PowerPoint template"""
    try:
        # Always use the default template from templates directory
        template_path = os.path.join('templates', 'ppt', 'default_template.pptx')
        if not os.path.exists(template_path):
            raise FileNotFoundError(f"Template file not found: {template_path}")
            
        return Presentation(template_path)
    except Exception as e:
        raise Exception(f"Failed to load template: {str(e)}")

def parse_gpt_response(response_text):
    """Parse GPT response from JSON format into structured slide data"""
    try:
        logger.info("Starting to parse GPT response")
        logger.debug(f"Raw response: {response_text}")
        
        # Parse the JSON string into a Python dictionary
        slides_data = json.loads(response_text)
        
        if not slides_data or 'slides' not in slides_data:
            logger.error("Invalid response structure: missing 'slides' key")
            return []
            
        if not slides_data['slides']:
            logger.error("No slides data in response")
            return []
            
        # Convert the JSON structure into our expected slides format
        slides = []
        
        # Handle the first (overview) slide differently
        overview_slide = slides_data['slides'][0]
        if not isinstance(overview_slide, dict):
            logger.error(f"Invalid overview slide format: {type(overview_slide)}")
            return []
            
        formatted_overview = {
            'title': overview_slide.get('title', 'Overview'),
            'Subtitle': overview_slide.get('subtitle', []),  # Keep as list for overview
            'image': overview_slide.get('image', ''),
            'data': overview_slide.get('data', '')
        }
        
        # Ensure subtitle is a list
        if isinstance(formatted_overview['Subtitle'], str):
            formatted_overview['Subtitle'] = [formatted_overview['Subtitle']]
            
        slides.append(formatted_overview)
        
        # Handle content slides
        for slide in slides_data['slides'][1:]:
            if not isinstance(slide, dict):
                logger.warning(f"Skipping invalid slide format: {type(slide)}")
                continue
                
            formatted_slide = {
                'title': slide.get('title', ''),
                'subtitle': slide.get('subtitle', ''),
                'content_summary': slide.get('content_summary', []),
                'verbatims': [quote.strip() for quote in slide.get('verbatims', []) if quote],
                'image': slide.get('image', ''),
                'data': slide.get('data', '')
            }
            
            # Ensure content_summary is a list
            if isinstance(formatted_slide['content_summary'], str):
                formatted_slide['content_summary'] = [formatted_slide['content_summary']]
            elif not formatted_slide['content_summary']:
                formatted_slide['content_summary'] = []
            
            # Clean up verbatims
            formatted_slide['verbatims'] = [
                quote for quote in formatted_slide['verbatims']
                if quote and not quote.isspace()
            ]
            
            # Ensure data is properly formatted
            if formatted_slide['data']:
                formatted_slide['data'] = '\n'.join(
                    line.strip() 
                    for line in str(formatted_slide['data']).split('\\n')
                )
            
            slides.append(formatted_slide)
            
        logger.info(f"Successfully parsed {len(slides)} slides")
        return slides
        
    except json.JSONDecodeError as e:
        logger.error(f"JSON decode error: {str(e)}")
        logger.error(f"Failed response text: {response_text}")
        return []
    except Exception as e:
        logger.error(f"Error parsing GPT response: {str(e)}")
        logger.error(f"Response text: {response_text}")
        return []

def create_presentation(slides_data, template_path):
    """Create a PowerPoint presentation from structured slide data using a template"""
    try:
        prs = load_template(template_path)
        
        # First slide (Title slide)
        title_slide_layout = prs.slide_layouts[0]
        title_slide = prs.slides.add_slide(title_slide_layout)
        
        # Add title and subtitle to the first slide
        title = title_slide.shapes.title
        subtitle = title_slide.placeholders[1]
        title.text = slides_data[0]['title']
        
        # Handle subtitle as a list or string
        if isinstance(slides_data[0].get('Subtitle', ''), list):
            subtitle.text = '\n'.join(slides_data[0]['Subtitle'])
        else:
            subtitle.text = slides_data[0].get('Subtitle', '')
        
        # Style the title slide
        title.text_frame.paragraphs[0].font.size = Pt(44)
        title.text_frame.paragraphs[0].font.bold = True
        title.text_frame.paragraphs[0].font.color.rgb = RGBColor(255, 255, 255)
        subtitle.text_frame.paragraphs[0].font.size = Pt(24)
        subtitle.text_frame.paragraphs[0].font.color.rgb = RGBColor(80, 80, 80)
        
        # Content slides
        for slide_data in slides_data[1:]:
            slide_layout = prs.slide_layouts[1]
            slide = prs.slides.add_slide(slide_layout)
            
            # Clear existing placeholders except title
            for shape in slide.shapes:
                if shape.is_placeholder and shape.placeholder_format.idx != 0:
                    sp = shape.element
                    sp.getparent().remove(sp)
            
            # Add and style title
            title = slide.shapes.title
            title.text = slide_data['title']
            title.text_frame.paragraphs[0].font.bold = True
            title.text_frame.paragraphs[0].font.size = Pt(30)
            title.text_frame.paragraphs[0].font.color.rgb = RGBColor(255, 255, 255)
            
            # Add subtitle
            subtitle_top = Inches(1.3)
            subtitle_box = slide.shapes.add_textbox(
                Inches(0.4), subtitle_top, Inches(8), Inches(0.4)
            )
            subtitle_text = slide_data.get('subtitle', '')
            subtitle_box.text_frame.paragraphs[0].text = f'"{subtitle_text}"' if subtitle_text else ''
            subtitle_box.text_frame.paragraphs[0].font.size = Pt(20)
            subtitle_box.text_frame.paragraphs[0].font.italic = True
            subtitle_box.text_frame.paragraphs[0].font.color.rgb = RGBColor(128, 128, 128)
            
            # Add main content
            content_left = Inches(0.5)
            content_top = Inches(2)
            content_width = Inches(6.5)
            content_height = Inches(4)
            
            content_box = slide.shapes.add_textbox(
                content_left, content_top, content_width, content_height
            )
            
            text_frame = content_box.text_frame
            text_frame.word_wrap = True
            
            # Add content summary with proper formatting
            first_paragraph = True
            for point in slide_data.get('content_summary', []):
                if first_paragraph:
                    p = text_frame.paragraphs[0]
                    first_paragraph = False
                else:
                    p = text_frame.add_paragraph()
                
                p.text = point.strip()
                p.font.size = Pt(18)
                p.space_before = Pt(6)
                p.space_after = Pt(6)
                
                if point.startswith('•'):
                    p.level = 0
                    p.bullet = True
                elif point.startswith('-'):
                    p.level = 1
                    p.bullet = True
                else:
                    p.level = 0
                    p.bullet = True
                    
            # Add verbatims section if present
            if slide_data.get('verbatims'):
                # Position the chat box
                verbatim_top = content_top + Inches(2.5)
                chat_box = slide.shapes.add_shape(
                    MSO_SHAPE.ROUNDED_RECTANGULAR_CALLOUT,
                    Inches(7.2), verbatim_top, Inches(5.5), Inches(2.7)
                )
                
                # Adjust the shape's geometry
                geometry = chat_box.adjustments
                geometry[0] = -0.6   # Adjusts tip to the left side
                geometry[1] = 0.4   # Controls vertical position of the tip
                geometry[2] = 0.05  # Reduces corner roundness for sharper corners
                
                # Style the chat bubble
                fill = chat_box.fill
                fill.background()
                
                line = chat_box.line
                line.color.rgb = RGBColor(255, 192, 0)
                line.width = Pt(2)
                
                text_frame = chat_box.text_frame
                text_frame.word_wrap = True
                text_frame.auto_size = MSO_AUTO_SIZE.TEXT_TO_FIT_SHAPE

                # Add verbatim quotes
                first_quote = True
                for quote in slide_data['verbatims'][:6]:
                    if first_quote:
                        p = text_frame.paragraphs[0]
                        first_quote = False
                    else:
                        p = text_frame.add_paragraph()
                    
                    cleaned_quote = quote.strip().lstrip('•').lstrip('-').strip()
                    p.text = f'-"{cleaned_quote}"'
                    p.font.size = Pt(13)
                    p.font.italic = True
                    p.font.color.rgb = RGBColor(80, 80, 80)  # Darker gray text
                    p.space_before = Pt(6)
                    p.space_after = Pt(6)
                    p.alignment = PP_ALIGN.LEFT
            
            # Add image placeholder with description
            if slide_data.get('image'):
                image_left = Inches(9.1)
                image_top = Inches(1.3)
                image_width = Inches(3.5)
                image_height = Inches(3)
                
                img_box = slide.shapes.add_textbox(
                    image_left, image_top, image_width, image_height
                )
                img_box.fill.solid()
                img_box.fill.fore_color.rgb = RGBColor(240, 240, 240)
                
                text_frame = img_box.text_frame
                text_frame.word_wrap = True
                p = text_frame.paragraphs[0]
                p.text = "Image Placeholder"
                p.font.size = Pt(12)
                p.font.bold = True
                p.alignment = PP_ALIGN.CENTER
                
                # Add image description in a new paragraph
                p2 = text_frame.add_paragraph()
                p2.text = slide_data['image']
                p2.font.size = Pt(12)
                p2.alignment = PP_ALIGN.CENTER
            
        return prs
        
    except Exception as e:
        error_traceback = traceback.format_exc()
        raise Exception(f"Failed to create presentation: {str(e)}\n{error_traceback}")

def generate_slides_content(discussion_guide, transcript_texts):
    """Generates slides content using GPT with multiple numbered transcripts"""
    try:
        logger.info("Starting slides content generation")
        
        # Combine all transcripts with numbered labels
        numbered_transcripts = [
            f"\n=== Transcript #{i+1} ===\n{transcript}"
            for i, transcript in enumerate(transcript_texts)
        ]
        combined_transcript = "\n\n".join(numbered_transcripts)
        
        logger.info(f"Prepared {len(transcript_texts)} transcripts for processing")
        
        response = client.chat.completions.create(
            model="gpt-4o",
            temperature=0.3,
            response_format={"type": "json_object"},
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": f"Discussion Guide: {discussion_guide}"},
                {"role": "user", "content": (
                    f"Below are multiple transcripts from different conversations "
                    f"related to the same discussion topic:\n\n{combined_transcript}"
                )}
            ]
        )
        
        if not response.choices:
            logger.error("No choices in GPT response")
            return []
            
        content = response.choices[0].message.content
        logger.info("Received response from GPT")
        
        slides = parse_gpt_response(content)
        if not slides:
            logger.error("Failed to parse GPT response into slides")
            return []
            
        logger.info(f"Successfully generated {len(slides)} slides")
        return slides
        
    except Exception as e:
        logger.error(f"Error generating slides content: {str(e)}")
        return []

def upload_to_sharepoint(file_content, file_name):
    """Upload file to SharePoint with proper authentication"""
    try:
        # Create authentication context
        auth_context = AuthenticationContext(SHAREPOINT_SITE_URL)
        
        # Authenticate with username and password
        if auth_context.acquire_token_for_user(SHAREPOINT_USERNAME, SHAREPOINT_PASSWORD):
            ctx = ClientContext(SHAREPOINT_SITE_URL, auth_context)
            
            # Get the SharePoint web
            web = ctx.web
            ctx.load(web)
            ctx.execute_query()
            
            # Define the target folder path for Presentations
            folder_path = "Shared Documents/Presentations"
            
            # Check if folder exists, create if it doesn't
            try:
                target_folder = web.get_folder_by_server_relative_url(folder_path)
                ctx.load(target_folder)
                ctx.execute_query()
            except Exception as folder_error:
                # Folder doesn't exist, create it
                root_folder = web.get_folder_by_server_relative_url("Shared Documents")
                root_folder.folders.add("Presentations").execute_query()
                target_folder = web.get_folder_by_server_relative_url(folder_path)
                ctx.load(target_folder)
                ctx.execute_query()
            
            # Upload the file
            file_content_bytes = file_content.getvalue()
            file_upload = target_folder.upload_file(file_name, file_content_bytes).execute_query()
            
            # Get the file's server relative URL
            file_obj = ctx.web.get_file_by_server_relative_url(f"/sites/VideoConference2/Shared Documents/Presentations/{file_name}")
            ctx.load(file_obj, ["ServerRelativeUrl", "ListItemAllFields"])
            ctx.execute_query()
            
            # Get the complete access URL
            access_url = f"{SHAREPOINT_SITE_URL}/Shared%20Documents/Presentations/{file_name}?web=1"
            
            return True, "File uploaded successfully", access_url
        else:
            return False, "Authentication failed", None
    except Exception as e:
        error_message = f"SharePoint upload error: {str(e)}"
        return False, error_message, None

def read_blob_as_buffer(blob_url):
    """Read blob content as buffer using SAS token"""
    try:
        # Parse the blob URL
        parsed_url = urlparse(blob_url)
        
        # Create blob client with the full URL including SAS token
        blob_client = BlobClient.from_blob_url(blob_url)
        
        # Download to stream
        stream = io.BytesIO()
        download_stream = blob_client.download_blob()
        stream.write(download_stream.readall())
        stream.seek(0)
        
        return stream
        
    except Exception as e:
        error_msg = f"Failed to read blob content: {str(e)}"
        raise Exception(error_msg)

def process_discussion_guide(file_buffer, file_extension):
    """Process discussion guide from buffer based on file type"""
    try:
        if file_extension == 'docx':
            # Extract text using docx2txt which gets all text including text boxes
            text = docx2txt.process(file_buffer)
            return text.strip()
        elif file_extension == 'pdf':
            pdf_reader = PyPDF2.PdfReader(file_buffer)
            text = ''
            for page in pdf_reader.pages:
                text += page.extract_text() + '\n'
            return text
        elif file_extension == 'txt':
            return file_buffer.getvalue().decode('utf-8')
        else:
            raise Exception(f"Unsupported file format: {file_extension}")
    except Exception as e:
        raise Exception(f"Failed to process discussion guide: {str(e)}")

def process_transcript(file_buffer):
    """Process transcript from buffer with multiple encoding attempts"""
    encodings_to_try = ['utf-8', 'latin1', 'cp1252', 'iso-8859-1']
    
    for encoding in encodings_to_try:
        try:
            # Reset buffer position for each attempt
            file_buffer.seek(0)
            return file_buffer.getvalue().decode(encoding)
        except UnicodeDecodeError:
            continue
    
    # If all encodings fail, try with error handling
    try:
        file_buffer.seek(0)
        return file_buffer.getvalue().decode('utf-8', errors='replace')
    except Exception as e:
        raise Exception(f"Failed to process transcript with all encoding attempts: {str(e)}")

def process_presentation_request(discussion_guide_url, transcript_urls, theme_name, languages):
    """Process the presentation generation request and handle all related operations"""
    try:
        # Get discussion guide content from buffer
        guide_extension = discussion_guide_url.split('?')[0].split('.')[-1].lower()
        guide_buffer = read_blob_as_buffer(discussion_guide_url)
        discussion_guide = process_discussion_guide(guide_buffer, guide_extension)
        
        # Get transcript contents from buffers
        transcript_texts = []
        for url in transcript_urls:
            transcript_buffer = read_blob_as_buffer(url)
            transcript_text = process_transcript(transcript_buffer)
            transcript_texts.append(transcript_text)
        
        # Validation checks
        if not discussion_guide or not transcript_texts:
            return False, "Required data not provided", None
        
        if len(transcript_texts) == 0:
            return False, "At least one transcript is required", None
        
        # Generate slides content using the theme name
        slides_content = generate_slides_content(discussion_guide, transcript_texts)
        
        if not slides_content:
            return False, "Failed to generate presentation structure", None

        # Create and save presentation using the theme
        prs = create_presentation(slides_content, 'default_template.pptx')
        ppt_stream = io.BytesIO()
        prs.save(ppt_stream)
        ppt_stream.seek(0)
        
        # Get unique languages and name should be in format: 20241104_1530_English_Spanish.pptx
        unique_languages = list(set(languages))
        # Make all languages lowercase
        unique_languages = [lang.lower() for lang in unique_languages]
        # Generate file name
        file_name = f"{datetime.now().strftime('%Y%m%d_%H%M%S')}_{theme_name.lower()}_{'_'.join(unique_languages)}.pptx"
        
        # Upload to SharePoint
        success, message, file_url = upload_to_sharepoint(ppt_stream, file_name)
        
        # Generate base64 encoded presentation
        ppt_base64 = base64.b64encode(ppt_stream.read()).decode()
        
        if success:
            return True, "Presentation generated successfully", {
                "sharepoint": {
                    "success": success,
                    "file_url": file_url
                }
            }
        else:
            return False, message, None
            
    except Exception as e:
        error_traceback = traceback.format_exc()
        return False, str(e), {"traceback": error_traceback}