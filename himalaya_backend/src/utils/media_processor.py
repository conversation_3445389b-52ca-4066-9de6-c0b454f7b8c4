import os
import threading
from datetime import datetime
import tempfile
import uuid
import logging
from models.models import db, File
from utils.audio_processor import AudioProcessor
from azure.storage.blob import BlobServiceClient
from config.settings import (
    AZURE_STORAGE_CONNECTION_STRING,
    GENERAL_MEDIA_CONTAINER,
    AZURE_STORAGE_CONTAINER_NAME
)
from urllib.parse import urlparse

logger = logging.getLogger(__name__)

class MediaProcessor:
    def __init__(self, app=None):
        self.connection_string = AZURE_STORAGE_CONNECTION_STRING
        self.media_container = GENERAL_MEDIA_CONTAINER
        self.search_container = AZURE_STORAGE_CONTAINER_NAME
        self.blob_service_client = BlobServiceClient.from_connection_string(self.connection_string)
        self.audio_processor = AudioProcessor()
        self.app = app

    def download_blob(self, blob_url, local_path):
        try:
            # Parse the URL to extract container and blob path
            parsed_url = urlparse(blob_url)
            path_parts = parsed_url.path.strip('/').split('/')
            
            # The first part is the container name, the rest is the blob path
            container_name = path_parts[0]
            blob_path = '/'.join(path_parts[1:])
            
            logger.info(f"Downloading from container: {container_name}, blob: {blob_path}")
            
            # Get container client
            container_client = self.blob_service_client.get_container_client(container_name)
            
            # Get blob client
            blob_client = container_client.get_blob_client(blob_path)
            
            # Download the blob
            with open(local_path, "wb") as file:
                data = blob_client.download_blob()
                file.write(data.readall())
            
            logger.info(f"Successfully downloaded blob to: {local_path}")
            return True
            
        except Exception as e:
            logger.error(f"Error downloading blob: {str(e)}")
            return False

    def upload_blob(self, local_path, blob_name, container_name=None):
        try:
            # Use provided container name or default to self.container_name
            container_name = container_name or self.media_container
            container_client = self.blob_service_client.get_container_client(container_name)
            
            with open(local_path, "rb") as file:
                blob_client = container_client.upload_blob(name=blob_name, data=file, overwrite=True)
                logger.info(f"Successfully uploaded blob: {blob_name} to container: {container_name}")
                return blob_client.url
        except Exception as e:
            logger.error(f"Error uploading blob: {str(e)}")
            return None

    def process_media(self, file_id):
        with self.app.app_context():
            try:
                file = File.query.get(file_id)
                if not file:
                    logger.error(f"File not found: {file_id}")
                    return

                logger.info(f"Starting to process {file.media_type} file {file_id}")
                file.processing_status = 'processing'
                db.session.commit()

                with tempfile.TemporaryDirectory() as temp_dir:
                    # Download media file
                    media_path = os.path.join(temp_dir, f"media_{uuid.uuid4()}")
                    if not self.download_blob(file.blob_url, media_path):
                        raise Exception("Failed to download media file")

                    # For video, extract audio first
                    if file.media_type == 'video':
                        logger.info(f"Extracting audio from video file: {media_path}")
                        audio_path = self.audio_processor.extract_audio(media_path, temp_dir)
                        if not audio_path:
                            raise Exception("Failed to extract audio")
                        logger.info(f"Audio extracted successfully to: {audio_path}")
                    else:
                        audio_path = media_path
                        logger.info(f"Using audio file directly: {audio_path}")

                    # Before transcription
                    logger.info(f"Starting transcription for {file.media_type} file")
                    if os.path.exists(audio_path):
                        file_size = os.path.getsize(audio_path)
                        logger.info(f"Audio file exists, size: {file_size} bytes")
                    else:
                        logger.error("Audio file does not exist")
                        raise Exception("Audio file not found")

                    # Get transcription
                    logger.info("Generating transcription")
                    transcription, _ = self.audio_processor.transcribe_media_file(audio_path)
                    if not transcription:
                        raise Exception("Failed to generate transcription")

                    # Save transcription to blob
                    transcription_blob_name = f"{uuid.uuid4()}.txt"
                    transcription_path = os.path.join(temp_dir, transcription_blob_name)
                    with open(transcription_path, 'w') as f:
                        f.write(transcription)

                    # Upload to generalaisearch container
                    transcription_blob_url = self.upload_blob(
                        transcription_path,
                        transcription_blob_name,
                        self.search_container
                    )
                    if not transcription_blob_url:
                        raise Exception("Failed to upload transcription")

                    # Update file record
                    file.transcription_blob_url = transcription_blob_url
                    file.processing_status = 'completed'
                    file.processed_at = datetime.utcnow()
                    db.session.commit()
                    logger.info(f"Successfully processed {file.media_type} file {file_id}")

            except Exception as e:
                logger.error(f"Error processing {file.media_type} file {file_id}: {str(e)}")
                try:
                    file = File.query.get(file_id)
                    if file:
                        file.processing_status = 'failed'
                        file.error_message = str(e)
                        db.session.commit()
                except Exception as inner_e:
                    logger.error(f"Error updating file status: {str(inner_e)}")

def process_media_async(file_id, app):
    processor = MediaProcessor(app)
    thread = threading.Thread(target=processor.process_media, args=(file_id,))
    thread.daemon = True
    thread.start() 