import os
import threading
from datetime import datetime
from models.models import db, Video, FGD
from utils.audio_processor import AudioProcessor
from azure.storage.blob import BlobServiceClient
import tempfile
import uuid
import json
import logging
from urllib.parse import urlparse
from flask import current_app
from config.settings import (
    AZURE_STORAGE_CONNECTION_STRING,
    FGD_VIDEOS_CONTAINER,
    FGD_VIDEO_VECTOR_BLOB
)

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class VideoProcessor:
    def __init__(self, app=None):
        self.connection_string = AZURE_STORAGE_CONNECTION_STRING
        self.container_name = FGD_VIDEOS_CONTAINER
        self.vector_container_name = FGD_VIDEO_VECTOR_BLOB
        self.blob_service_client = BlobServiceClient.from_connection_string(self.connection_string)
        self.audio_processor = AudioProcessor()
        self.app = app

    def download_blob(self, blob_url, local_path):
        try:
            # Parse the URL to extract container and blob path
            parsed_url = urlparse(blob_url)
            path_parts = parsed_url.path.strip('/').split('/')
            
            # The first part is the container name, the rest is the blob path
            container_name = path_parts[0]
            blob_path = '/'.join(path_parts[1:])
            
            logger.info(f"Downloading from container: {container_name}, blob: {blob_path}")
            
            # Get container client
            container_client = self.blob_service_client.get_container_client(container_name)
            
            # Get blob client
            blob_client = container_client.get_blob_client(blob_path)
            
            # Download the blob
            with open(local_path, "wb") as file:
                data = blob_client.download_blob()
                file.write(data.readall())
            
            logger.info(f"Successfully downloaded blob to: {local_path}")
            return True
            
        except Exception as e:
            logger.error(f"Error downloading blob: {str(e)}")
            return False

    def upload_blob(self, local_path, blob_name, container_name=None):
        try:
            # Use provided container name or default to self.container_name
            container_name = container_name or self.container_name
            container_client = self.blob_service_client.get_container_client(container_name)
            
            with open(local_path, "rb") as file:
                blob_client = container_client.upload_blob(name=blob_name, data=file, overwrite=True)
                logger.info(f"Successfully uploaded blob: {blob_name} to container: {container_name}")
                return blob_client.url
        except Exception as e:
            logger.error(f"Error uploading blob: {str(e)}")
            return None

    def process_video(self, video_id):
        with self.app.app_context():
            try:
                video = Video.query.get(video_id)
                if not video:
                    logger.error(f"Video not found: {video_id}")
                    return
                
                logger.info(f"Starting to process video {video_id}")
                video.processing_status = 'processing'
                db.session.commit()

                with tempfile.TemporaryDirectory() as temp_dir:
                    # Download video
                    video_path = os.path.join(temp_dir, f"video_{uuid.uuid4()}.mp4")
                    logger.info(f"Downloading video from {video.blob_url}")
                    if not self.download_blob(video.blob_url, video_path):
                        raise Exception("Failed to download video")

                    # Extract audio
                    logger.info("Extracting audio from video")
                    audio_path = self.audio_processor.extract_audio(video_path, temp_dir)
                    if not audio_path:
                        raise Exception("Failed to extract audio")

                    # Upload audio to blob
                    logger.info("Uploading extracted audio")
                    audio_blob_name = f"fgd_audios/{uuid.uuid4()}.wav"
                    audio_blob_url = self.upload_blob(audio_path, audio_blob_name)
                    if not audio_blob_url:
                        raise Exception("Failed to upload audio")
                    video.audio_blob_url = audio_blob_url

                    # Download the uploaded audio file for local processing
                    local_audio_path = os.path.join(temp_dir, f"downloaded_audio_{uuid.uuid4()}.wav")
                    if not self.download_blob(audio_blob_url, local_audio_path):
                        raise Exception("Failed to download audio for transcription")

                    # Get transcription using local audio file
                    logger.info("Generating transcription")
                    transcription, metadata = self.audio_processor.transcribe_audio(local_audio_path)
                    if not transcription:
                        raise Exception("Failed to generate transcription")

                    # Save transcription text
                    video.transcription_text = transcription
                    video.transcription_metadata = json.dumps(metadata) if metadata else None

                    # Save plain text transcription
                    logger.info("Uploading plain text transcription")
                    transcription_path = os.path.join(temp_dir, f"transcription_{uuid.uuid4()}.txt")
                    with open(transcription_path, 'w') as f:
                        f.write(transcription)

                    # Upload to main container - with folder structure
                    transcription_blob_name = f"fgd_transcriptions/{uuid.uuid4()}.txt"
                    transcription_blob_url = self.upload_blob(
                        transcription_path, 
                        transcription_blob_name
                    )
                    if not transcription_blob_url:
                        raise Exception("Failed to upload plain text transcription")
                    video.transcription_blob_url = transcription_blob_url

                    # Upload to vector container - without folder structure
                    vector_blob_name = f"{uuid.uuid4()}.txt"  # No folder structure for vector container
                    vector_blob_url = self.upload_blob(
                        transcription_path, 
                        vector_blob_name,
                        self.vector_container_name
                    )
                    if not vector_blob_url:
                        raise Exception("Failed to upload transcription to vector container")
                    video.vector_blob_url = vector_blob_url

                    # Save JSON transcription - with folder structure
                    logger.info("Uploading JSON transcription with timestamps")
                    json_transcription = {
                        'metadata': metadata,
                        'segments': metadata.get('detailed_segments', []) if metadata else []
                    }
                    json_path = os.path.join(temp_dir, f"transcription_{uuid.uuid4()}.json")
                    with open(json_path, 'w') as f:
                        json.dump(json_transcription, f, indent=2)

                    json_blob_name = f"fgd_transcriptions/{uuid.uuid4()}.json"  # Keep folder structure
                    json_blob_url = self.upload_blob(json_path, json_blob_name)
                    if not json_blob_url:
                        raise Exception("Failed to upload JSON transcription")
                    video.transcription_json_url = json_blob_url

                    # After successful transcription upload and processing
                    video.processing_status = 'completed'
                    video.processed_at = datetime.utcnow()
                    
                    # Update FGD status to AI Processed
                    video.fgd.status = 'AI Processed'
                    
                    db.session.commit()
                    logger.info(f"Successfully processed video {video_id}")

            except Exception as e:
                logger.error(f"Error processing video {video_id}: {str(e)}")
                try:
                    video = Video.query.get(video_id)
                    if video:
                        video.processing_status = 'failed'
                        video.error_message = str(e)
                        db.session.commit()
                except Exception as inner_e:
                    logger.error(f"Error updating video status: {str(inner_e)}")

def process_video_async(video_id, app):
    processor = VideoProcessor(app)
    thread = threading.Thread(target=processor.process_video, args=(video_id,))
    thread.daemon = True
    thread.start() 