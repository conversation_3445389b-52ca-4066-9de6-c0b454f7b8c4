from azure.storage.blob import BlobServiceClient, generate_blob_sas, BlobSasPermissions, BlobClient
from datetime import datetime, timedelta
from urllib.parse import urlparse
import io
import os
import logging
from config.settings import AZURE_STORAGE_CONNECTION_STRING

logger = logging.getLogger(__name__)

def generate_sas_url(blob_url: str, expiry_hours: int = 2) -> str:
    """
    Generate a SAS URL for any blob type
    """
    try:
        connection_string = AZURE_STORAGE_CONNECTION_STRING
        blob_service_client = BlobServiceClient.from_connection_string(connection_string)
        
        # Parse the blob URL
        parsed_url = urlparse(blob_url)
        path_parts = parsed_url.path.strip('/').split('/')
        container_name = path_parts[0]
        blob_name = '/'.join(path_parts[1:])
        
        # Generate SAS token
        sas_token = generate_blob_sas(
            account_name=blob_service_client.account_name,
            container_name=container_name,
            blob_name=blob_name,
            account_key=blob_service_client.credential.account_key,
            permission=BlobSasPermissions(read=True),
            expiry=datetime.utcnow() + timedelta(hours=expiry_hours)
        )
        
        # Construct full URL
        return f"https://{blob_service_client.account_name}.blob.core.windows.net/{container_name}/{blob_name}?{sas_token}"
    
    except Exception as e:
        logger.error(f"Error generating SAS URL: {str(e)}")
        raise

# Keep the video-specific function for backward compatibility
def generate_video_sas_url(blob_url: str, expiry_hours: int = 2) -> str:
    return generate_sas_url(blob_url, expiry_hours)

def download_blob_to_memory(blob_url: str) -> bytes:
    """
    Download blob content directly to memory as bytes
    
    Args:
        blob_url: URL of the blob to download
        
    Returns:
        Bytes content of the blob
    """
    try:
        # If the URL doesn't have a SAS token, generate one
        if '?' not in blob_url:
            blob_url = generate_sas_url(blob_url)
            
        # Create blob client with the full URL including SAS token
        blob_client = BlobClient.from_blob_url(blob_url)
        
        # Download the blob
        download_stream = blob_client.download_blob()
        blob_data = download_stream.readall()
        
        logger.info(f"Successfully downloaded blob content from: {blob_url}")
        return blob_data
        
    except Exception as e:
        error_msg = f"Failed to download blob content: {str(e)}"
        logger.error(error_msg)
        raise Exception(error_msg)