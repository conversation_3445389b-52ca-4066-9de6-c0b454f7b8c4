from config.settings import (
    AZURE_OPENAI_KEY, AZURE_OPENAI_API_VERSION, AZURE_OPENAI_ENDPOINT,
    AZURE_OPENAI_DEPLOYMENT_NAME, AZURE_OPENAI_EMBEDDING_DEPLOYMENT,
    MIN_VECTOR_SCORE_THRESHOLD, AZURE_STORAGE_CONNECTION_STRING
)

from openai import AzureOpenAI
import os
from typing import List, Dict
from azure.storage.blob import BlobServiceClient
import json
from urllib.parse import urlparse
import logging
import re

# Initialize Azure OpenAI client
openai_client = AzureOpenAI(
    api_key=AZURE_OPENAI_KEY,
    api_version=AZURE_OPENAI_API_VERSION,
    azure_endpoint=AZURE_OPENAI_ENDPOINT
)

logger = logging.getLogger(__name__)

def get_ai_response(question: str, search_results: List[Dict], conversation_history: str = None, rephrased_question: str = None) -> dict:
    """
    Generate an AI response using the question and context from search results.
    
    Args:
        question (str): The user's question
        search_results (list): List of relevant chunks from Azure AI Search
        conversation_history (str): Previous conversation history
        rephrased_question (str): Rephrased question
        
    Returns:
        dict: Contains the AI response and source information
    """
    try:
        # Construct system prompt
        system_prompt = """You are an enthusiastic and intelligent AI assistant who excels at understanding, analyzing, and transforming information to help users. Your strengths include:

        1. Primary Knowledge Source:
           - Base your core answers on the provided context
           - Use the context as the foundation for your responses
           - Maintain accuracy with respect to factual information from the context
           - Consider conversation history for better context understanding
           - Build upon previous interactions while staying relevant

        2. Value-Added Capabilities:
           - Transform content into different formats (e.g., PPT outlines, summaries, guides)
           - Suggest improvements while preserving the original intent
           - Apply logical reasoning to enhance or structure the information
           - Provide examples or scenarios that illustrate the concepts
           - Offer creative suggestions based on the context's principles
           - Connect information across conversation history when relevant

        3. Contextual Understanding:
           - Use conversation history to maintain continuity
           - Reference previous relevant discussions when appropriate
           - Understand follow-up questions in context
           - Resolve ambiguous references using conversation history
           - Maintain context awareness across the conversation

        4. Response Formatting:
           - Use clear, well-structured markdown
           - Include headers, bullet points, tables as needed
           - Bold/italic for emphasis
           - Code blocks for technical content
           - Create organized sections for better readability
           
        5. Not enough context:
           - If the context is insufficient, respond with: "I cannot answer this question based on the provided context. The available information is not sufficient or relevant enough to provide an accurate response."
           - Do not make up information or assumptions
           - if the question is totally unrelated to the context, respond with: "I cannot answer this question based on the provided context. The available information is not sufficient or relevant enough to provide an accurate response."
        Your response must be in JSON format:
        {
            "answer": "Your detailed, well-structured answer in markdown format",
            "confidence": "high|medium|low",
            "context_sufficient": true|false
        }
        
        Confidence levels:
        - high: Strong understanding with comprehensive context support
        - medium: Good understanding with some room for enhancement
        - low: Basic understanding with significant room for enhancement

        Context sufficiency:
        - true: Context provides good foundation for the requested task
        - false: Insufficient context for the requested task

        When responding:
        1. Provide comprehensive, detailed explanations
        2. Use examples and illustrations when helpful
        3. Structure information logically
        4. Highlight key points and important details
        5. Maintain an enthusiastic and helpful tone
        6. Feel free to suggest improvements or transformations
        7. Apply creative thinking while staying grounded in the context
        8. Consider the conversation flow and history
        9. Build upon previous interactions when relevant"""

        # Filter results based on score threshold
        filtered_results = []
        for result in search_results:
            # Handle both old and new result structures
            if 'score' in result:
                # Old structure: score at top level
                score = result['score']
            elif 'metadata' in result and 'search_score' in result['metadata']:
                # New structure: score in metadata
                score = result['metadata']['search_score']
            else:
                # Fallback: assume good score if no score found
                score = 0.8
            
            if score >= MIN_VECTOR_SCORE_THRESHOLD:
                # Normalize the result structure for consistent processing
                normalized_result = {
                    'content': result.get('content', result.get('chunk', '')),
                    'chunk_id': result.get('chunk_id', result.get('metadata', {}).get('chunk_id', '')),
                    'score': score,
                    'metadata': result.get('metadata', {}),
                    'title': result.get('title', result.get('metadata', {}).get('title', ''))
                }
                filtered_results.append(normalized_result)

        if not filtered_results:
            # Calculate max score for debugging
            max_score = 0
            for result in search_results:
                if 'score' in result:
                    max_score = max(max_score, result['score'])
                elif 'metadata' in result and 'search_score' in result['metadata']:
                    max_score = max(max_score, result['metadata']['search_score'])
            
            return {
                'answer': "I cannot answer this question based on the provided context. The available information is not sufficient or relevant enough to provide an accurate response.",
                'sources': [],
                'token_usage': None,
                'metadata': {
                    'total_results': len(search_results),
                    'filtered_results': 0,
                    'threshold': MIN_VECTOR_SCORE_THRESHOLD,
                    'max_score': max_score
                }
            }

        # Prepare context from filtered results
        context = "\n\n".join([
            f"Context {i+1}:\n{result['content']}" 
            for i, result in enumerate(filtered_results)
        ])

        user_prompt = f"""Question: {question}

{f'Previous conversation:{conversation_history}' if conversation_history else ''}

Available context:
{context}

Please provide a detailed, well-structured answer that:
1. Builds upon our conversation history when relevant
2. Uses the provided context as a foundation
3. Applies creative thinking and transformation where appropriate
4. Maintains coherence with previous interactions
5. Clearly explains if the context is insufficient for the request
6. Suggests alternatives when the original request cannot be fulfilled"""

        if not AZURE_OPENAI_DEPLOYMENT_NAME:
            raise ValueError("AZURE_OPENAI_DEPLOYMENT_NAME setting is not configured")
        # Call Azure OpenAI
        try:
            response = openai_client.chat.completions.create(
                model=AZURE_OPENAI_DEPLOYMENT_NAME,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                temperature=0.2,
                response_format={"type": "json_object"}
            )
        except Exception as api_error:
            print(f"OpenAI API Error: {str(api_error)}")
            print(f"Using deployment name: {AZURE_OPENAI_DEPLOYMENT_NAME}")
            raise Exception(f"OpenAI API error: {str(api_error)}")

        # Parse the JSON response
        try:
            ai_response = json.loads(response.choices[0].message.content)
            answer = ai_response.get('answer', '')
        except json.JSONDecodeError:
            logger.error("Failed to parse AI response as JSON")
            answer = response.choices[0].message.content

        # Extract source information with blob_url and score
        sources = []
        for result in filtered_results:
            source = {
                'chunk_id': result['chunk_id'],
                'score': result['score']
            }
            
            # Add file info if available (for backward compatibility)
            if 'file_info' in result:
                source['file_info'] = result['file_info']
                source['blob_url'] = result['file_info']['blob_url']
            elif 'metadata' in result:
                # Extract what we can from metadata and preserve it for later processing
                metadata = result['metadata']
                source['metadata'] = metadata  # Preserve full metadata
                source['title'] = metadata.get('title', '')
                source['metadata_storage_name'] = metadata.get('metadata_storage_name', '')
            
            # Also preserve the original content for reference
            source['content'] = result.get('content', '')
            
            sources.append(source)

        # Sort sources by score in descending order
        sources = sorted(sources, key=lambda x: x['score'], reverse=True)

        # Return formatted response
        return {
            'answer': answer,
            'sources': sources,
            'token_usage': {
                'prompt_tokens': response.usage.prompt_tokens,
                'completion_tokens': response.usage.completion_tokens,
                'total_tokens': response.usage.total_tokens
            }
        }

    except ValueError as ve:
        # Handle missing environment variables
        raise Exception(f"Configuration error: {str(ve)}")
    except Exception as e:
        # Handle other errors
        raise Exception(f"Error generating AI response: {str(e)}")


def render_with_newlines(text):
    """
    Convert escaped newline characters in a string to actual newlines.

    Parameters:
        text (str): The input string with escaped newline characters.

    Returns:
        str: The formatted string with actual newlines.
    """
    return text.replace("\\n", "\n")

def rephrase_question(question, conversation_history=None):
    """
    Rephrase the question considering conversation history to make it more context-aware
    for vector search.
    """
    try:
        system_prompt = """You are an AI assistant that helps rephrase questions to be more specific and self-contained for document search. Your task is to:

1. **Analyze the question's intent and context**: Understand what the user is really asking for
2. **Determine if context is relevant**: Only use conversation history if it's directly relevant to the current question
3. **Preserve the original intent**: Never change the fundamental meaning or scope of the question
4. **Avoid context pollution**: Do not add irrelevant context from previous conversations about different topics

Guidelines:
- If the question is already clear and self-contained, return it as-is
- If the question references previous context AND that context is topically relevant, incorporate it
- If the conversation history is about completely different topics, ignore it entirely
- Focus on making the question better for document search, not changing its meaning
- Be conservative - when in doubt, preserve the original question

Your response must be in JSON format:
{
    "rephrased_question": "The rephrased standalone question"
}

Examples:
- Original: "What is the correlation between experience and salary?" → Keep as-is (already clear)
- Original: "What about the other departments?" + Context about salary analysis → "What about the salary correlation in other departments?"
- Original: "Show me the results" + Context about completely different topic → Keep as "Show me the results" (ignore irrelevant context)
"""

        user_content = question
        if conversation_history:
            user_content = f"""Current question: {question}

Previous conversation context:
{conversation_history}

Instructions: Only incorporate the conversation context if it's directly relevant to the current question. If the previous conversation is about a completely different topic, ignore it and return the original question unchanged."""
        else:
            user_content = f"""Current question: {question}

Instructions: Since there's no conversation history, return the question as-is or with minor improvements for clarity."""

        response = openai_client.chat.completions.create(
            model=AZURE_OPENAI_DEPLOYMENT_NAME,
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_content}
            ],
            temperature=0.1,  # Lower temperature for more consistent behavior
            response_format={"type": "json_object"}
        )
        rephrased_question = response.choices[0].message.content

        try:
            # First, attempt to parse the AI response as JSON
            ai_response_json = json.loads(rephrased_question)
            return {
                "original_question": question,
                "rephrased_question": ai_response_json['rephrased_question'],
                "token_usage": response.usage.total_tokens
            }

        except json.JSONDecodeError:
            # If JSON parsing fails, proceed with regex or other parsing methods
            pattern = re.compile(r'(\w+):((?:(?!,\w+:).)*)')
            matches = pattern.findall(rephrased_question)
            ai_response_json = {key.strip(): value.strip().replace('>', '').replace('<', '') for key, value in matches}
            return {
                "original_question": question,
                "rephrased_question": ai_response_json.get('rephrased_question', question),  # Fallback to original
                "token_usage": response.usage.total_tokens
            }
    except Exception as e:
        print(f"Error in rephrasing question: {str(e)}")
        return {
            "original_question": question,
            "rephrased_question": question,  # fallback to original question
            "token_usage": 0
        }

def get_fgd_ai_response(question: str, search_results: List[Dict], conversation_history: str = None, rephrased_question: str = None) -> dict:
    """
    Generate an AI response for FGD chat using the question and context from search results.
    
    Args:
        question (str): The user's question
        search_results (list): List of relevant chunks from Azure AI Search
        conversation_history (str): Previous conversation history
        rephrased_question (str): Rephrased question
        
    Returns:
        dict: Contains the AI response and source information
    """
    try:
        system_prompt = """You are an expert in analyzing Focus Group Discussion (FGD) transcripts.
        Your response must be in JSON format with the following structure:
        {
            "answer": "Your detailed analysis in markdown format",
            "key_themes": ["theme1", "theme2", ...],
            "confidence_level": "high|medium|low"
        }
        
        Guidelines:
        1. Analyze transcripts thoroughly
        2. Extract key insights and patterns
        3. Base answers strictly on transcript information
        4. Never mention sources or contexts
        5. Format response in markdown
        6. Acknowledge if information isn't found"""

        # Filter results based on score threshold
        filtered_results = [
            result for result in search_results 
            if result['score'] >= MIN_VECTOR_SCORE_THRESHOLD
        ]

        if not filtered_results:
            return {
                'answer': """Based on the available FGD transcripts, I cannot find relevant information to answer your question. 
                
Consider:
- Selecting different FGD sessions
- Rephrasing your question
- Checking if the relevant discussions are available""",
                'sources': [],
                'token_usage': None,
                'metadata': {
                    'total_results': len(search_results),
                    'filtered_results': 0,
                    'threshold': MIN_VECTOR_SCORE_THRESHOLD,
                    'max_score': max([r['score'] for r in search_results], default=0)
                }
            }

        # Prepare context from filtered results
        context = "\n\n".join([
            f"FGD Discussion {i+1} (conducted on {result['file_info']['fgd_info']['discussion_date']} in {result['file_info']['fgd_info']['country']}):\n{result['chunk']}" 
            for i, result in enumerate(filtered_results)
        ])

        user_prompt = f"""Question: {question}

{f'Previous conversation:{conversation_history}' if conversation_history else ''}

FGD Transcripts:
{context}

Provide insights based on these FGD discussions."""

        if not AZURE_OPENAI_DEPLOYMENT_NAME:
            raise ValueError("AZURE_OPENAI_DEPLOYMENT_NAME setting is not configured")

        # Call Azure OpenAI
        response = openai_client.chat.completions.create(
            model=AZURE_OPENAI_DEPLOYMENT_NAME,
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ],
            temperature=0.3,
            response_format={"type": "json_object"}
        )

        try:
            ai_response = json.loads(response.choices[0].message.content)
            answer = ai_response.get('answer', '')
        except json.JSONDecodeError:
            logger.error("Failed to parse FGD AI response as JSON")
            answer = response.choices[0].message.content

        # Extract source information
        sources = [
            {
                'video_id': result['file_info']['id'],
                'fgd_id': result['file_info']['fgd_id'],
                'theme_id': result['file_info']['theme_id'],
                'chunk_id': result['chunk_id'],
                'score': result['score'],
                'fgd_info': result['file_info']['fgd_info']
            }
            for result in filtered_results
        ]

        return {
            'answer': answer,
            'sources': sources,
            'token_usage': {
                'prompt_tokens': response.usage.prompt_tokens,
                'completion_tokens': response.usage.completion_tokens,
                'total_tokens': response.usage.total_tokens
            },
            'metadata': {
                'total_results': len(search_results),
                'filtered_results': len(filtered_results),
                'threshold': MIN_VECTOR_SCORE_THRESHOLD,
                'max_score': max([r['score'] for r in filtered_results], default=0)
            }
        }

    except Exception as e:
        raise Exception(f"Error generating FGD AI response: {str(e)}")

def get_video_chat_response(question: str, transcription_json_url: str, conversation_history: str = None) -> dict:
    """
    Generate AI response for video chat with timestamps.
    
    This function analyzes a focus group discussion (FGD) video transcript, 
    where participants and a conductor discuss their feelings and opinions about a product. 
    The marketing team may ask questions related to participants' sentiments, feedback, or suggestions.

    The AI should return a JSON response with:
    - A clear, concise, and accurate answer in markdown format that directly addresses the question
      based solely on the provided transcript segments.
    - A list of up to three relevant timestamp ranges from the transcript that support the answer, 
      including exact start/end times and the full text from those segments.

    If no relevant segments are found, return an empty timestamps array.

    Args:
        question (str): The user's question (from the marketing team)
        transcription_json_url (str): URL to the video transcription JSON
        conversation_history (str): Previous conversation history, if any

    Returns:
        dict: Contains:
              - 'answer': The answer to the question in markdown format
              - 'timestamps': A list of up to 3 timestamp entries, each with 
                {"start": float, "end": float, "text": str}
              - 'token_usage': Token usage details (if available)
    """
    try:
        
        
        print("question: ", question)
        # Download and parse the transcription JSON
        blob_service_client = BlobServiceClient.from_connection_string(AZURE_STORAGE_CONNECTION_STRING)
        parsed_url = urlparse(transcription_json_url)
        path_parts = parsed_url.path.strip('/').split('/')
        container_name = path_parts[0]
        blob_path = '/'.join(path_parts[1:])
        
        # Get the transcription JSON content
        blob_client = blob_service_client.get_container_client(container_name).get_blob_client(blob_path)
        transcription_data = json.loads(blob_client.download_blob().readall())
        
        # Format segments with clearer IDs and timestamps
        segments = transcription_data.get('segments', [])
        if not segments:
            return {
                'answer': "No transcription segments found for this video.",
                'timestamps': [],
                'token_usage': None
            }

        context = "\n".join([
            f"[{s['offset']/10000000:.2f}-{(s['offset'] + s['duration'])/10000000:.2f}] {s['text']}"
            for s in segments
        ])

        # Improved system prompt with scenario and instructions
        system_prompt = """
You are an AI assistant helping a marketing team analyze a focus group discussion (FGD) about a product.
In this FGD, multiple participants and a conductor are discussing their opinions, feelings, experiences, 
likes, dislikes, and suggestions about the product.

Your role is to assist by answering questions about the FGD based solely on the provided transcript. 
Your responses must:
- Be factually accurate and directly supported by the transcript.
- Be clear, concise, and in markdown format.
- Include a JSON structure with an "answer" field and a "timestamps" field.
- The "timestamps" field should be a list of up to three segments from the transcript that support your answer. 
  Each timestamp entry should include:
  {
    "start": <start_time_in_seconds>,
    "end": <end_time_in_seconds>,
    "text": "The exact transcript text from that time range"
  }
  
If the answer is based on multiple segments, choose the most relevant up to three. If no relevant segment can be found, 
return an empty timestamps array.

**Format of the final answer:**
{
    "answer": "Your answer in markdown",
    "timestamps": [
        {
            "start": <float>,
            "end": <float>,
            "text": "Exact transcript text"
        }
    ]
}

**Guidelines:**
1. Only use information from the provided transcript segments.
2. Provide direct, explicit citations of the segments that support your answer.
3. Do not guess or fabricate information.
4. If no relevant information is available, return an empty timestamps array.
5. The "answer" should be helpful and address the marketing team's question clearly.

**Example:**
{
    "answer": "The participants expressed positive initial impressions of the product, noting its ease of use and modern design.",
    "timestamps": [
        {
            "start": 30.50,
            "end": 45.20,
            "text": "I really like how simple the interface is... it feels very intuitive."
        },
        {
            "start": 60.00,
            "end": 75.30,
            "text": "The design is sleek and modern, it feels like it was made for today's tech-savvy users."
        }
    ]
}
"""

        user_prompt = f"""Question: {question}

Transcript:
{context}

{f'Previous conversation:{conversation_history}' if conversation_history else ''}

Please provide your answer following the specified JSON format and guidelines.
"""

        if not AZURE_OPENAI_DEPLOYMENT_NAME:
            raise ValueError("AZURE_OPENAI_DEPLOYMENT_NAME setting is not configured")

        # Call Azure OpenAI
        response = openai_client.chat.completions.create(
            model=AZURE_OPENAI_DEPLOYMENT_NAME,
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ],
            temperature=0.2,
            response_format={"type": "json_object"}
        )

        try:
            result = json.loads(response.choices[0].message.content)

            # Validate response structure
            if not isinstance(result, dict):
                raise ValueError("Response is not a dictionary")
            if 'answer' not in result:
                raise ValueError("Response missing 'answer' field")
            if 'timestamps' not in result:
                raise ValueError("Response missing 'timestamps' field")
            if not isinstance(result['timestamps'], list):
                raise ValueError("'timestamps' field is not an array")

            # Validate and clean timestamps
            cleaned_timestamps = []
            for ts in result['timestamps']:  # Limit to 3 timestamps
                if not isinstance(ts, dict):
                    continue
                if not all(k in ts for k in ('start', 'end', 'text')):
                    continue
                if not isinstance(ts['start'], (int, float)) or not isinstance(ts['end'], (int, float)):
                    continue
                
                cleaned_timestamps.append({
                    'start': round(float(ts['start']), 2),
                    'end': round(float(ts['end']), 2),
                    'text': str(ts['text']).strip()
                })
                
            return {
                'answer': result['answer'].strip(),
                'timestamps': cleaned_timestamps,
                'token_usage': {
                    'prompt_tokens': response.usage.prompt_tokens,
                    'completion_tokens': response.usage.completion_tokens,
                    'total_tokens': response.usage.total_tokens
                }
            }

        except (json.JSONDecodeError, ValueError, AttributeError) as e:
            logger.error(f"Error processing AI response: {str(e)}")
            logger.error(f"Response content: {response.choices[0].message.content[:500]}...")
            return {
                'answer': "I apologize, but I encountered an error processing the video transcript. Please try asking your question again.",
                'timestamps': [],
                'token_usage': {
                    'prompt_tokens': response.usage.prompt_tokens,
                    'completion_tokens': response.usage.completion_tokens,
                    'total_tokens': response.usage.total_tokens
                }
            }

    except Exception as e:
        logger.error(f"Error in video chat response: {str(e)}")
        return {
            'answer': "I apologize, but I encountered an error processing the video transcript. Please try asking your question again.",
            'timestamps': [],
            'token_usage': None
        }
