"""
Azure AI Search Service for Enhanced Document Processing

This service handles integration with Azure AI Search for storing and retrieving
enhanced document chunks, tables, and metadata using the specified vector index.
"""

import logging
import json
import uuid
from typing import Dict, List, Any, Optional
from datetime import datetime

from azure.search.documents import SearchClient
from azure.search.documents.models import VectorizedQuery
from azure.core.credentials import AzureKeyCredential
from azure.core.exceptions import ResourceNotFoundError
from openai import AzureOpenAI

from config.settings import (
    AZURE_SEARCH_SERVICE_ENDPOINT,
    AZURE_SEARCH_ENHANCED_INDEX_NAME,
    AZURE_SEARCH_ADMIN_KEY,
    AZURE_OPENAI_KEY,
    AZURE_OPENAI_ENDPOINT,
    AZURE_OPENAI_API_VERSION,
    AZURE_OPENAI_EMBEDDING_DEPLOYMENT,
    EMBEDDING_DIMENSIONS
)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AzureSearchService:
    """
    Service for enhanced Azure AI Search integration with custom document processing.
    """
    
    def __init__(self):
        """Initialize the Azure Search service."""
        try:
            # Initialize Azure AI Search client
            credential = AzureKeyCredential(AZURE_SEARCH_ADMIN_KEY)
            self.search_client = SearchClient(
                endpoint=AZURE_SEARCH_SERVICE_ENDPOINT,
                index_name=AZURE_SEARCH_ENHANCED_INDEX_NAME,
                credential=credential
            )
            
            # Initialize Azure OpenAI client for embeddings
            self.openai_client = AzureOpenAI(
                api_key=AZURE_OPENAI_KEY,
                api_version=AZURE_OPENAI_API_VERSION,
                azure_endpoint=AZURE_OPENAI_ENDPOINT
            )
            
            logger.info(f"Azure Search Service initialized with index: {AZURE_SEARCH_ENHANCED_INDEX_NAME}")
            
        except Exception as e:
            logger.error(f"Failed to initialize Azure Search Service: {str(e)}")
            raise
    
    def generate_embeddings(self, text: str) -> List[float]:
        """
        Generate embeddings for text using Azure OpenAI.
        
        Args:
            text: Text to generate embeddings for
            
        Returns:
            List of embedding values
        """
        try:
            response = self.openai_client.embeddings.create(
                input=text,
                model=AZURE_OPENAI_EMBEDDING_DEPLOYMENT
            )
            return response.data[0].embedding
        except Exception as e:
            logger.error(f"Failed to generate embeddings: {str(e)}")
            raise
    
    def upload_document_chunk(self, chunk_data: Dict[str, Any]) -> str:
        """
        Upload a document chunk to Azure AI Search.
        
        Args:
            chunk_data: Dictionary containing chunk information
            
        Returns:
            Document ID in Azure Search
        """
        try:
            # Generate unique document ID
            doc_id = f"chunk_{chunk_data['file_id']}_{chunk_data['chunk_index']}_{uuid.uuid4().hex[:8]}"
            
            # Generate embeddings for the chunk content
            embeddings = self.generate_embeddings(chunk_data['content'])
            
            # Prepare document for Azure Search with correct schema
            search_document = {
                'chunk_id': doc_id,  # Use chunk_id instead of id
                'parent_id': f"file_{chunk_data['file_id']}",  # Parent document reference
                'chunk': chunk_data['content'],  # Use chunk instead of content
                'title': chunk_data.get('original_file_name', chunk_data.get('file_name', '')),  # Use original file name for title
                'text_vector': embeddings,  # Use text_vector instead of content_vector
                'metadata_storage_name': chunk_data.get('file_name', ''),  # Use blob name for filtering
                # Additional metadata can be stored in a JSON string if needed
                '@search.action': 'upload'
            }
            
            # Upload to Azure Search
            result = self.search_client.upload_documents([search_document])
            
            if result[0].succeeded:
                logger.info(f"Successfully uploaded chunk {doc_id} to Azure Search")
                return doc_id
            else:
                logger.error(f"Failed to upload chunk {doc_id}: {result[0].error_message}")
                raise Exception(f"Upload failed: {result[0].error_message}")
                
        except Exception as e:
            logger.error(f"Error uploading document chunk: {str(e)}")
            raise
    
    def upload_document_table(self, table_data: Dict[str, Any]) -> str:
        """
        Upload a document table to Azure AI Search.
        
        Args:
            table_data: Dictionary containing table information
            
        Returns:
            Document ID in Azure Search
        """
        try:
            # Generate unique document ID for table
            doc_id = f"table_{table_data['file_id']}_{table_data['table_index']}_{uuid.uuid4().hex[:8]}"
            
            # Create searchable content from table
            table_content = self._create_table_search_content(table_data)
            
            # Generate embeddings for the table content
            embeddings = self.generate_embeddings(table_content)
            
            # Prepare document for Azure Search with correct schema
            search_document = {
                "chunk_id": doc_id,
                "parent_id": f"file_{table_data['file_id']}",  # Add 'file_' prefix for consistency
                "chunk": table_content,
                "title": f"Table {table_data['table_index'] + 1} from {table_data.get('original_file_name', table_data['file_name'])}",
                "text_vector": embeddings,
                "metadata_storage_name": table_data['file_name'],  # Use blob name for filtering
                '@search.action': 'upload'
            }
            
            # Upload to Azure Search using the existing client
            result = self.search_client.upload_documents([search_document])
            
            if result and len(result) > 0 and result[0].succeeded:
                logger.info(f"Successfully uploaded table {doc_id} to Azure Search")
                return doc_id
            else:
                error_msg = result[0].error_message if result and len(result) > 0 else "Unknown error"
                logger.error(f"Failed to upload table to Azure Search: {error_msg}")
                return None
                
        except Exception as e:
            logger.error(f"Error uploading table to Azure Search: {str(e)}")
            return None
    
    def search_enhanced_content(self, query: str, file_ids: List[int] = None, top_k: int = 5) -> List[Dict[str, Any]]:
        """
        Search enhanced document content using vector similarity.
        
        Args:
            query: Search query
            file_ids: Optional list of file IDs to filter by
            top_k: Number of results to return
            
        Returns:
            List of search results
        """
        try:
            # Generate embeddings for the query
            query_embeddings = self.generate_embeddings(query)
            
            # Create vector query with correct field name
            vector_query = VectorizedQuery(
                vector=query_embeddings,
                k_nearest_neighbors=top_k,
                fields="text_vector"  # Use text_vector instead of content_vector
            )
            
            # Build search parameters with correct field names
            search_params = {
                'search_text': query,
                'vector_queries': [vector_query],
                'top': top_k,
                'select': [
                    'chunk_id', 'chunk', 'parent_id', 'title', 'metadata_storage_name'
                ]
            }
            
            # Add file ID filter if specified (using parent_id field)
            if file_ids:
                parent_id_filter = " or ".join([f"parent_id eq 'file_{fid}'" for fid in file_ids])
                search_params['filter'] = f"({parent_id_filter})"
            
            # Perform search
            results = self.search_client.search(**search_params)
            
            # Process results with correct field names
            search_results = []
            for result in results:
                search_result = {
                    'chunk_id': result['chunk_id'],
                    'content': result['chunk'],  # Map chunk to content for compatibility
                    'parent_id': result['parent_id'],
                    'title': result.get('title', ''),
                    'metadata_storage_name': result.get('metadata_storage_name', ''),
                    'score': result.get('@search.score', 0)
                }
                
                search_results.append(search_result)
            
            logger.info(f"Found {len(search_results)} enhanced search results for query: {query}")
            return search_results
            
        except Exception as e:
            logger.error(f"Error searching enhanced content: {str(e)}")
            raise
    
    def delete_document_content(self, file_id: int) -> bool:
        """
        Delete all enhanced content for a specific file.
        
        Args:
            file_id: File ID to delete content for
            
        Returns:
            True if successful
        """
        try:
            # Search for all documents related to this file using parent_id
            search_results = self.search_client.search(
                search_text="*",
                filter=f"parent_id eq 'file_{file_id}'",
                select=['chunk_id']  # Use chunk_id instead of id
            )
            
            # Collect document IDs to delete
            doc_ids = [result['chunk_id'] for result in search_results]
            
            if doc_ids:
                # Delete documents using chunk_id
                delete_docs = [{'chunk_id': doc_id, '@search.action': 'delete'} for doc_id in doc_ids]
                result = self.search_client.upload_documents(delete_docs)  # Use upload_documents for delete action
                
                successful_deletes = sum(1 for r in result if r.succeeded)
                logger.info(f"Deleted {successful_deletes}/{len(doc_ids)} enhanced documents for file {file_id}")
                
                return successful_deletes == len(doc_ids)
            else:
                logger.info(f"No enhanced documents found for file {file_id}")
                return True
                
        except Exception as e:
            logger.error(f"Error deleting enhanced content for file {file_id}: {str(e)}")
            return False
    
    def _create_table_search_content(self, table_data: Dict[str, Any]) -> str:
        """
        Create searchable content from table data.
        
        Args:
            table_data: Table data dictionary
            
        Returns:
            Searchable text content
        """
        content_parts = []
        
        # Add table metadata
        content_parts.append(f"Table {table_data['table_index'] + 1} from {table_data['file_name']}")
        content_parts.append(f"Dimensions: {table_data['row_count']} rows × {table_data['column_count']} columns")
        
        # Add table summary if available
        if table_data.get('table_summary'):
            content_parts.append(f"Summary: {table_data['table_summary']}")
        
        # Add headers
        headers = table_data.get('table_headers', [])
        if headers:
            content_parts.append(f"Headers: {' | '.join(headers)}")
        
        # Add sample data (first few rows)
        table_rows = table_data.get('table_data', [])
        if isinstance(table_rows, list) and table_rows:
            content_parts.append("Sample data:")
            for i, row in enumerate(table_rows[:5]):  # First 5 rows
                if isinstance(row, list):
                    row_text = " | ".join([str(cell) for cell in row])
                    content_parts.append(f"Row {i + 1}: {row_text}")
            
            if len(table_rows) > 5:
                content_parts.append(f"... and {len(table_rows) - 5} more rows")
        
        return "\n".join(content_parts)

# Create a singleton instance
azure_search_service = AzureSearchService() 