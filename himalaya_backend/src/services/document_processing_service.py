"""
Enhanced Document Processing Service

This service handles comprehensive processing of Word, PowerPoint, and other
text-based documents with advanced content extraction and analysis.
"""

import logging
import os
import tempfile
from typing import Dict, List, Any, Optional
from pathlib import Path
import docx
import docx2txt
from pptx import Presentation
import zipfile
import xml.etree.ElementTree as ET

from models.models import db, DocumentTable, DocumentChunk
from services.azure_search_service import azure_search_service

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DocumentProcessingService:
    """
    Comprehensive document processing service for Word, PowerPoint, and text documents.
    """
    
    def __init__(self):
        """Initialize the document processing service."""
        logger.info("Document Processing Service initialized")
    
    def process_word_document(self, file_path: str, file_record, processing_metadata) -> Dict[str, Any]:
        """
        Process Word document (.doc, .docx) with comprehensive content extraction.
        
        Args:
            file_path: Path to Word document
            file_record: File database record
            processing_metadata: Processing metadata record
            
        Returns:
            Processing result dictionary
        """
        logger.info(f"Processing Word document: {file_record.file_name}")
        
        try:
            # Extract content based on file extension
            file_ext = os.path.splitext(file_path)[1].lower()
            
            if file_ext == '.docx':
                content_data = self._extract_docx_content(file_path)
            elif file_ext == '.doc':
                content_data = self._extract_doc_content(file_path)
            else:
                raise ValueError(f"Unsupported Word document type: {file_ext}")
            
            # Process extracted content
            return self._process_document_content(
                content_data, file_record, processing_metadata, 'word_processor'
            )
            
        except Exception as e:
            logger.error(f"Error processing Word document {file_record.file_name}: {str(e)}")
            return self._create_error_result('word_processor', str(e))
    
    def process_powerpoint_document(self, file_path: str, file_record, processing_metadata) -> Dict[str, Any]:
        """
        Process PowerPoint document (.ppt, .pptx) with slide content extraction.
        
        Args:
            file_path: Path to PowerPoint document
            file_record: File database record
            processing_metadata: Processing metadata record
            
        Returns:
            Processing result dictionary
        """
        logger.info(f"Processing PowerPoint document: {file_record.file_name}")
        
        try:
            # Extract content from PowerPoint
            content_data = self._extract_pptx_content(file_path)
            
            # Process extracted content
            return self._process_document_content(
                content_data, file_record, processing_metadata, 'powerpoint_processor'
            )
            
        except Exception as e:
            logger.error(f"Error processing PowerPoint document {file_record.file_name}: {str(e)}")
            return self._create_error_result('powerpoint_processor', str(e))
    
    def process_text_document(self, file_path: str, file_record, processing_metadata) -> Dict[str, Any]:
        """
        Process text document (.txt, .md, .html) with content extraction.
        
        Args:
            file_path: Path to text document
            file_record: File database record
            processing_metadata: Processing metadata record
            
        Returns:
            Processing result dictionary
        """
        logger.info(f"Processing text document: {file_record.file_name}")
        
        try:
            # Read text content with encoding detection
            content = self._read_text_file(file_path)
            
            # Estimate page count based on content length
            page_count = 1
            if content:
                # Rough estimate: 3000 characters per page
                page_count = max(1, (len(content) + 2999) // 3000)
            
            content_data = {
                'text': content,
                'tables': [],
                'metadata': {
                    'extraction_method': 'text_reader',
                    'file_type': os.path.splitext(file_path)[1].lower(),
                    'page_count': page_count,
                    'word_count': len(content.split()) if content else 0
                }
            }
            
            # Process extracted content
            return self._process_document_content(
                content_data, file_record, processing_metadata, 'text_processor'
            )
            
        except Exception as e:
            logger.error(f"Error processing text document {file_record.file_name}: {str(e)}")
            return self._create_error_result('text_processor', str(e))
    
    def _extract_docx_content(self, file_path: str) -> Dict[str, Any]:
        """
        Extract content from DOCX file including text and tables.
        
        Args:
            file_path: Path to DOCX file
            
        Returns:
            Dictionary with extracted content
        """
        try:
            # Method 1: Use python-docx for structured extraction
            doc = docx.Document(file_path)
            
            # Extract page count from document properties
            page_count = 1  # Default to 1 page
            try:
                # Try to get page count from document properties
                if hasattr(doc.core_properties, 'pages') and doc.core_properties.pages:
                    page_count = doc.core_properties.pages
                else:
                    # Alternative method: estimate pages based on content length
                    # Rough estimate: 500 words per page
                    total_words = 0
                    for para in doc.paragraphs:
                        total_words += len(para.text.split())
                    
                    if total_words > 0:
                        page_count = max(1, (total_words + 499) // 500)  # Round up
                    
                    # Also check if we can get page count from document statistics
                    try:
                        # Access document XML to get page count
                        from docx.oxml.ns import qn
                        doc_part = doc.part
                        if doc_part.package.part_exists('/docProps/app.xml'):
                            app_part = doc_part.package.part_related_by('/docProps/app.xml')
                            app_xml = app_part.blob
                            import xml.etree.ElementTree as ET
                            root = ET.fromstring(app_xml)
                            
                            # Look for Pages element
                            for elem in root.iter():
                                if elem.tag.endswith('Pages') and elem.text:
                                    try:
                                        page_count = int(elem.text)
                                        break
                                    except ValueError:
                                        pass
                    except Exception:
                        pass  # Fallback to estimated page count
                        
            except Exception as e:
                logger.warning(f"Could not extract page count from document properties: {str(e)}")
                # Fallback: estimate based on content length
                total_text = '\n'.join([para.text for para in doc.paragraphs])
                if total_text:
                    # Rough estimate: 3000 characters per page
                    page_count = max(1, (len(total_text) + 2999) // 3000)
            
            # Extract paragraphs
            paragraphs = []
            for para in doc.paragraphs:
                if para.text.strip():
                    paragraphs.append(para.text.strip())
            
            # Extract tables
            tables = []
            for table_idx, table in enumerate(doc.tables):
                table_data = []
                headers = []
                
                for row_idx, row in enumerate(table.rows):
                    row_data = []
                    for cell in row.cells:
                        cell_text = cell.text.strip()
                        row_data.append(cell_text)
                    
                    if row_idx == 0:
                        headers = row_data
                    table_data.append(row_data)
                
                if table_data:
                    tables.append({
                        'table_index': table_idx,
                        'headers': headers,
                        'data': table_data,
                        'row_count': len(table_data),
                        'column_count': len(headers) if headers else 0,
                        'summary': f"Table {table_idx + 1} from Word document"
                    })
            
            # Combine all text
            full_text = '\n\n'.join(paragraphs)
            
            # Fallback: Use docx2txt for additional content
            if not full_text.strip():
                full_text = docx2txt.process(file_path)
            
            return {
                'text': full_text,
                'tables': tables,
                'metadata': {
                    'extraction_method': 'python-docx',
                    'paragraph_count': len(paragraphs),
                    'table_count': len(tables),
                    'page_count': page_count,  # Add page count to metadata
                    'word_count': len(full_text.split()) if full_text else 0
                }
            }
            
        except Exception as e:
            logger.error(f"Error extracting DOCX content: {str(e)}")
            # Fallback to docx2txt
            try:
                text = docx2txt.process(file_path)
                # Estimate page count for fallback
                page_count = max(1, (len(text) + 2999) // 3000) if text else 1
                
                return {
                    'text': text,
                    'tables': [],
                    'metadata': {
                        'extraction_method': 'docx2txt_fallback',
                        'page_count': page_count,
                        'error': str(e)
                    }
                }
            except Exception as e2:
                logger.error(f"Fallback extraction also failed: {str(e2)}")
                return {
                    'text': '',
                    'tables': [],
                    'metadata': {
                        'extraction_method': 'failed',
                        'page_count': 1,
                        'error': str(e2)
                    }
                }
    
    def _extract_doc_content(self, file_path: str) -> Dict[str, Any]:
        """
        Extract content from DOC file (legacy format).
        
        Args:
            file_path: Path to DOC file
            
        Returns:
            Dictionary with extracted content
        """
        try:
            # Use docx2txt which supports both .doc and .docx
            text = docx2txt.process(file_path)
            
            # Estimate page count based on content length
            page_count = 1
            if text:
                # Rough estimate: 3000 characters per page
                page_count = max(1, (len(text) + 2999) // 3000)
            
            return {
                'text': text,
                'tables': [],  # Table extraction from .doc is limited
                'metadata': {
                    'extraction_method': 'docx2txt',
                    'page_count': page_count,
                    'word_count': len(text.split()) if text else 0,
                    'note': 'Legacy DOC format - limited table extraction'
                }
            }
            
        except Exception as e:
            logger.error(f"Error extracting DOC content: {str(e)}")
            return {
                'text': '',
                'tables': [],
                'metadata': {
                    'extraction_method': 'failed',
                    'page_count': 1,
                    'error': str(e)
                }
            }
    
    def _extract_pptx_content(self, file_path: str) -> Dict[str, Any]:
        """
        Extract content from PowerPoint file including slide text and tables.
        
        Args:
            file_path: Path to PowerPoint file
            
        Returns:
            Dictionary with extracted content
        """
        try:
            prs = Presentation(file_path)
            
            slide_texts = []
            tables = []
            
            for slide_idx, slide in enumerate(prs.slides):
                slide_text_parts = []
                
                # Extract text from shapes
                for shape in slide.shapes:
                    if hasattr(shape, "text") and shape.text.strip():
                        slide_text_parts.append(shape.text.strip())
                    
                    # Extract tables
                    if shape.has_table:
                        table = shape.table
                        table_data = []
                        headers = []
                        
                        for row_idx, row in enumerate(table.rows):
                            row_data = []
                            for cell in row.cells:
                                cell_text = cell.text.strip()
                                row_data.append(cell_text)
                            
                            if row_idx == 0:
                                headers = row_data
                            table_data.append(row_data)
                        
                        if table_data:
                            tables.append({
                                'table_index': len(tables),
                                'slide_number': slide_idx + 1,
                                'headers': headers,
                                'data': table_data,
                                'row_count': len(table_data),
                                'column_count': len(headers) if headers else 0,
                                'summary': f"Table from slide {slide_idx + 1}"
                            })
                
                # Combine slide text
                if slide_text_parts:
                    slide_text = f"Slide {slide_idx + 1}:\n" + '\n'.join(slide_text_parts)
                    slide_texts.append(slide_text)
            
            # Combine all slide text
            full_text = '\n\n'.join(slide_texts)
            
            return {
                'text': full_text,
                'tables': tables,
                'metadata': {
                    'extraction_method': 'python-pptx',
                    'slide_count': len(prs.slides),
                    'table_count': len(tables)
                }
            }
            
        except Exception as e:
            logger.error(f"Error extracting PowerPoint content: {str(e)}")
            return {
                'text': '',
                'tables': [],
                'metadata': {
                    'extraction_method': 'failed',
                    'error': str(e)
                }
            }
    
    def _read_text_file(self, file_path: str) -> str:
        """
        Read text file with encoding detection.
        
        Args:
            file_path: Path to text file
            
        Returns:
            File content as string
        """
        encodings = ['utf-8', 'latin-1', 'cp1252', 'iso-8859-1']
        
        for encoding in encodings:
            try:
                with open(file_path, 'r', encoding=encoding) as f:
                    content = f.read()
                logger.info(f"Successfully read text file with {encoding} encoding")
                return content
            except UnicodeDecodeError:
                continue
            except Exception as e:
                logger.error(f"Error reading text file with {encoding}: {str(e)}")
                continue
        
        logger.error(f"Failed to read text file with any encoding: {file_path}")
        return ""
    
    def _process_document_content(self, content_data: Dict[str, Any], file_record, 
                                 processing_metadata, processor_name: str) -> Dict[str, Any]:
        """
        Process extracted document content and store in database.
        
        Args:
            content_data: Extracted content dictionary
            file_record: File database record
            processing_metadata: Processing metadata record
            processor_name: Name of the processor
            
        Returns:
            Processing result dictionary
        """
        try:
            full_text = content_data.get('text', '')
            tables = content_data.get('tables', [])
            metadata = content_data.get('metadata', {})
            
            # Create text chunks
            chunks = []
            if full_text and len(full_text.strip()) >= 100:  # Minimum chunk size
                chunks = self._create_text_chunks(full_text, file_record, processing_metadata)
            
            # Store extracted tables
            table_records = []
            for table_data in tables:
                table_record = DocumentTable(
                    file_id=file_record.id,
                    table_index=table_data.get('table_index', 0),
                    page_number=table_data.get('slide_number', table_data.get('page_number', 1)),
                    table_data=table_data.get('data', []),
                    table_headers=table_data.get('headers', []),
                    table_summary=table_data.get('summary', ''),
                    row_count=table_data.get('row_count', 0),
                    column_count=table_data.get('column_count', 0),
                    table_metadata={
                        **metadata,
                        'table_source': processor_name
                    }
                )
                db.session.add(table_record)
                table_records.append(table_record)
            
            # Commit table records
            if table_records:
                db.session.commit()
                logger.info(f"Stored {len(table_records)} tables for {processor_name}")
            
            # Calculate statistics
            word_count = len(full_text.split()) if full_text else 0
            char_count = len(full_text) if full_text else 0
            
            # Get page count from metadata (prioritize direct page_count, then slide_count for PowerPoint)
            page_count = metadata.get('page_count', metadata.get('slide_count', 1))
            
            # Determine confidence and quality scores
            confidence_score = 0.9 if (full_text or tables) else 0.3
            quality_score = 0.9 if (word_count > 50 or len(tables) > 0) else 0.5
            
            logger.info(f"{processor_name} processing completed: {len(chunks)} chunks, {len(tables)} tables, {page_count} pages")
            
            return {
                'chunk_count': len(chunks),
                'table_count': len(tables),
                'word_count': word_count,
                'char_count': char_count,
                'page_count': page_count,
                'confidence_score': confidence_score,
                'quality_score': quality_score,
                'extracted_content': full_text,  # Add extracted content for summary generation
                'metadata': {
                    'processor': processor_name,
                    'processing_time': 2.0,
                    'extraction_method': metadata.get('extraction_method', 'unknown'),
                    'tables_extracted': len(tables),
                    'text_extracted': bool(full_text)
                }
            }
            
        except Exception as e:
            logger.error(f"Error processing document content: {str(e)}")
            return self._create_error_result(processor_name, str(e))
    
    def _create_text_chunks(self, content: str, file_record, processing_metadata) -> List:
        """
        Create text chunks from content and store them.
        
        Args:
            content: Text content to chunk
            file_record: File database record
            processing_metadata: Processing metadata record
            
        Returns:
            List of created DocumentChunk instances
        """
        from services.enhanced_processing_service import enhanced_processing_service
        
        # Use the enhanced processing service's chunking method
        return enhanced_processing_service._create_text_chunks(
            content, file_record, processing_metadata
        )
    
    def _create_error_result(self, processor_name: str, error: str) -> Dict[str, Any]:
        """
        Create an error result dictionary.
        
        Args:
            processor_name: Name of the processor
            error: Error message
            
        Returns:
            Error result dictionary
        """
        return {
            'chunk_count': 0,
            'table_count': 0,
            'word_count': 0,
            'char_count': 0,
            'page_count': 1,
            'confidence_score': 0.1,
            'quality_score': 0.1,
            'metadata': {
                'processor': processor_name,
                'processing_time': 1.0,
                'error': error
            }
        }

# Create a singleton instance
document_processing_service = DocumentProcessingService() 