"""
Enhanced Document Processing Service

This service coordinates the enhanced document processing workflow, integrating
document type detection, specialized processing agents, and storage services.
"""

import logging
import os
import tempfile
import uuid
import time
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
import json

# Add PDF processing imports
import PyPDF2
import pdfplumber
import pandas as pd

# Add Azure Blob Storage imports
from azure.storage.blob import BlobServiceClient

# Add Azure OpenAI imports for content summary
from openai import AzureOpenAI

# Add Azure Document Intelligence imports
import azure.ai.documentintelligence as adi
from azure.core.credentials import AzureKeyCredential

from sqlalchemy.orm import Session
from models.models import (
    db, File, FileProcessingMetadata, DocumentChunk, DocumentTable
)
from services.document_type_detector import document_type_detector
from services.azure_search_service import azure_search_service
from config.settings import (
    ENHANCED_PROCESSING_ENABLED,
    INTELLIGENT_CHUNKING_ENABLED,
    CHUNK_SIZE,
    CHUNK_OVERLAP,
    MIN_CHUNK_SIZE,
    MAX_CHUNK_SIZE,
    CONTENT_SUMMARY_ENABLED,
    CONTENT_SUMMARY_MAX_LENGTH,
    ENTITY_EXTRACTION_ENABLED,
    FALLBACK_TO_BASIC_PROCESSING,
    MAX_PROCESSING_RETRIES,
    RETRY_DELAY_SECONDS,
    AZURE_STORAGE_CONNECTION_STRING,
    AZURE_STORAGE_CONTAINER_NAME,
    AZURE_OPENAI_KEY,
    AZURE_OPENAI_ENDPOINT,
    AZURE_OPENAI_API_VERSION,
    AZURE_OPENAI_DEPLOYMENT_NAME,
    SEMANTIC_CHUNKING_ENABLED,
    SENTENCE_BOUNDARY_PRESERVATION,
    PARAGRAPH_BOUNDARY_PRESERVATION,
    OVERLAP_STRATEGY,
    DOCUMENT_INTELLIGENCE_ENDPOINT,
    DOCUMENT_INTELLIGENCE_KEY,
    SPLIT_TABLE_DETECTION_ENABLED,
    TABLE_DETECTION_ENABLED
)
from services.table_merger_service import table_merger_service

# Use enhanced logging configuration
from config.logging_config import get_logger, performance

logger = get_logger(__name__)

class EnhancedProcessingService:
    """
    Main service for enhanced document processing workflow.
    """
    
    def __init__(self):
        """Initialize the enhanced processing service."""
        self.type_detector = document_type_detector
        self.search_service = azure_search_service
        
        # Initialize Azure OpenAI client for content summary generation
        if CONTENT_SUMMARY_ENABLED and AZURE_OPENAI_KEY and AZURE_OPENAI_ENDPOINT:
            try:
                self.openai_client = AzureOpenAI(
                    api_key=AZURE_OPENAI_KEY,
                    api_version=AZURE_OPENAI_API_VERSION,
                    azure_endpoint=AZURE_OPENAI_ENDPOINT
                )
                logger.info("Azure OpenAI client initialized for content summary generation")
            except Exception as e:
                logger.warning(f"Failed to initialize Azure OpenAI client: {str(e)}")
                self.openai_client = None
        else:
            self.openai_client = None
            
        # Initialize Azure Document Intelligence client for table extraction
        if DOCUMENT_INTELLIGENCE_ENDPOINT and DOCUMENT_INTELLIGENCE_KEY:
            try:
                self.doc_intelligence_client = adi.DocumentIntelligenceClient(
                    endpoint=DOCUMENT_INTELLIGENCE_ENDPOINT,
                    credential=AzureKeyCredential(DOCUMENT_INTELLIGENCE_KEY)
                )
                logger.info("Azure Document Intelligence client initialized for table extraction")
            except Exception as e:
                logger.warning(f"Failed to initialize Azure Document Intelligence client: {str(e)}")
                self.doc_intelligence_client = None
        else:
            self.doc_intelligence_client = None
            
        logger.info("Enhanced Processing Service initialized")
    
    def process_file(self, file_id: int, force_reprocess: bool = False) -> Dict[str, Any]:
        """
        Process a file with enhanced document processing.
        
        Args:
            file_id: ID of the file to process
            force_reprocess: Whether to force reprocessing if already processed
            
        Returns:
            Processing result dictionary
        """
        try:
            # Check if enhanced processing is enabled
            if not ENHANCED_PROCESSING_ENABLED:
                logger.info("Enhanced processing is disabled")
                return {
                    'status': 'skipped',
                    'message': 'Enhanced processing is disabled',
                    'file_id': file_id
                }
            
            # Get file from database
            file_record = File.query.get(file_id)
            if not file_record:
                raise ValueError(f"File with ID {file_id} not found")
            
            # Check if already processed and not forcing reprocess
            if not force_reprocess and file_record.processing_metadata:
                if file_record.processing_metadata.enhanced_processing_status == 'completed':
                    logger.info(f"File {file_id} already processed")
                    return {
                        'status': 'already_processed',
                        'message': 'File already processed with enhanced processing',
                        'file_id': file_id,
                        'processing_metadata': self._serialize_processing_metadata(file_record.processing_metadata)
                    }
            
            # Start processing with performance tracking
            processing_start = time.time()
            logger.info(f"Starting enhanced processing for file {file_id}: {file_record.file_name}")
            
            # Create or update processing metadata
            processing_metadata = self._create_or_update_processing_metadata(file_record)
            
            try:
                # Download file from blob storage for processing
                temp_file_path = self._download_file_from_blob_storage(file_record)
                
                if not temp_file_path:
                    raise Exception("Failed to download file from blob storage")
                
                # Detect document type
                type_info = self.type_detector.detect_type(
                    temp_file_path, 
                    file_record.file_name,
                    getattr(file_record, 'mime_type', None)
                )
                
                # Update processing metadata with document type
                processing_metadata.document_type = type_info['document_type']
                processing_metadata.enhanced_processing_status = 'processing'
                processing_metadata.processing_started_at = datetime.utcnow()
                db.session.commit()
                
                # Route to appropriate processor based on document type
                processing_result = self._route_to_processor(
                    temp_file_path, 
                    file_record, 
                    type_info,
                    processing_metadata
                )
                
                # Generate content summary if enabled and content is available
                content_summary = None
                if CONTENT_SUMMARY_ENABLED and processing_result.get('extracted_content') and len(processing_result.get('extracted_content', '').strip()) > 100:
                    try:
                        content_summary = self._generate_content_summary(
                            processing_result['extracted_content'],
                            file_record.file_name,
                            type_info['document_type']
                        )
                        logger.info(f"Generated content summary for file {file_id}")
                    except Exception as e:
                        logger.warning(f"Failed to generate content summary for file {file_id}: {str(e)}")
                
                # Update final processing status
                processing_metadata.enhanced_processing_status = 'completed'
                processing_metadata.enhanced_processed_at = datetime.utcnow()
                processing_metadata.processing_confidence_score = processing_result.get('confidence_score', 0.8)
                processing_metadata.extraction_quality_score = processing_result.get('quality_score', 0.8)
                
                # Log performance metrics
                processing_duration = time.time() - processing_start
                chunks_created = processing_result.get('chunks_created', 0)
                tables_extracted = processing_result.get('tables_extracted', 0)
                pages_processed = processing_result.get('pages_processed', 0)
                
                performance.log_document_processing(
                    file_record.file_name,
                    pages_processed,
                    chunks_created,
                    tables_extracted,
                    processing_duration
                )
                
                # Update statistics
                processing_metadata.chunk_count = processing_result.get('chunk_count', 0)
                processing_metadata.table_count = processing_result.get('table_count', 0)
                processing_metadata.word_count = processing_result.get('word_count', 0)
                processing_metadata.char_count = processing_result.get('char_count', 0)
                processing_metadata.page_count = processing_result.get('page_count', 0)
                
                # Store content summary
                if content_summary:
                    processing_metadata.content_summary = content_summary
                
                # Store processing metadata
                processing_metadata.processing_metadata = processing_result.get('metadata', {})
                
                db.session.commit()
                
                # Clean up temporary file
                try:
                    os.unlink(temp_file_path)
                except:
                    pass
                
                logger.info(f"Successfully completed enhanced processing for file {file_id}")
                
                return {
                    'status': 'completed',
                    'message': 'Enhanced processing completed successfully',
                    'file_id': file_id,
                    'document_type': type_info['document_type'],
                    'processing_result': processing_result,
                    'processing_metadata': self._serialize_processing_metadata(processing_metadata)
                }
                
            except Exception as e:
                # Update processing status to failed
                processing_metadata.enhanced_processing_status = 'failed'
                processing_metadata.error_details = {
                    'error': str(e),
                    'timestamp': datetime.utcnow().isoformat()
                }
                db.session.commit()
                
                # SAFETY FIX: Don't automatically fallback to basic processing
                # This could process files incorrectly if enhanced processing failed for a reason
                if FALLBACK_TO_BASIC_PROCESSING:
                    logger.warning(f"Enhanced processing failed for file {file_id}. Manual review required before fallback processing.")
                    # Return error instead of automatically processing with potentially incorrect method
                    return {
                        'success': False,
                        'error': 'Enhanced processing failed and requires manual review before fallback processing',
                        'file_id': file_id
                    }
                
                raise
                
        except Exception as e:
            logger.error(f"Error in enhanced processing for file {file_id}: {str(e)}")
            return {
                'status': 'error',
                'message': f'Enhanced processing failed: {str(e)}',
                'file_id': file_id
            }
    
    def _create_or_update_processing_metadata(self, file_record: File) -> FileProcessingMetadata:
        """
        Create or update processing metadata for a file.
        
        Args:
            file_record: File database record
            
        Returns:
            FileProcessingMetadata instance
        """
        if file_record.processing_metadata:
            # Update existing metadata
            metadata = file_record.processing_metadata
            metadata.enhanced_processing_status = 'pending'
            metadata.processing_started_at = None
            metadata.enhanced_processed_at = None
            metadata.error_details = None
        else:
            # Create new metadata
            metadata = FileProcessingMetadata(
                file_id=file_record.id,
                enhanced_processing_status='pending',
                chunk_size_used=CHUNK_SIZE,
                chunk_overlap_used=CHUNK_OVERLAP,
                embedding_model_used='text-embedding-3-large'
            )
            db.session.add(metadata)
        
        db.session.commit()
        return metadata
    
    def _route_to_processor(self, file_path: str, file_record: File, type_info: Dict[str, Any], 
                           processing_metadata: FileProcessingMetadata) -> Dict[str, Any]:
        """
        Route file to appropriate processor based on document type.
        
        Args:
            file_path: Path to temporary file
            file_record: File database record
            type_info: Document type information
            processing_metadata: Processing metadata record
            
        Returns:
            Processing result dictionary
        """
        document_type = type_info['document_type']
        
        if document_type == 'pdf':
            return self._process_pdf(file_path, file_record, processing_metadata)
        elif document_type == 'excel':
            return self._process_excel(file_path, file_record, processing_metadata)
        elif document_type in ['word', 'text']:
            return self._process_text(file_path, file_record, processing_metadata)
        elif document_type == 'image':
            return self._process_image(file_path, file_record, processing_metadata)
        else:
            return self._process_basic(file_path, file_record, processing_metadata)
    
    def _process_pdf(self, file_path: str, file_record: File, 
                    processing_metadata: FileProcessingMetadata) -> Dict[str, Any]:
        """
        Process PDF document with real content extraction.
        
        Args:
            file_path: Path to PDF file
            file_record: File database record
            processing_metadata: Processing metadata record
            
        Returns:
            Processing result dictionary
        """
        logger.info(f"Processing PDF: {file_record.file_name}")
        
        try:
            # Try Azure Document Intelligence first for better table detection (especially image-based tables)
            if self.doc_intelligence_client:
                logger.info(f"Using Azure Document Intelligence for PDF processing: {file_record.file_name}")
                extracted_content = self._extract_pdf_content_with_azure_di(file_path)
                
                # Only fallback to pdfplumber if Azure DI completely fails (no text AND no tables)
                if not extracted_content['text'] and not extracted_content['tables']:
                    logger.warning("Azure Document Intelligence returned no content, falling back to pdfplumber")
                    extracted_content = self._extract_pdf_content_with_pdfplumber(file_path)
                else:
                    logger.info(f"✅ Azure DI SUCCESS: Extracted {len(extracted_content.get('text', ''))} chars text and {len(extracted_content.get('tables', []))} tables - PRESERVING RESULTS")
            else:
                logger.info(f"Azure Document Intelligence not available, using pdfplumber: {file_record.file_name}")
                extracted_content = self._extract_pdf_content_with_pdfplumber(file_path)
            
            # Final fallback to PyPDF2 only if ALL previous methods fail completely
            if not extracted_content['text'] and not extracted_content['tables']:
                logger.warning("All primary extraction methods failed, trying PyPDF2 as final fallback")
                extracted_content = self._extract_pdf_content_with_pypdf2(file_path)
            
            # Process extracted content
            full_text = extracted_content['text']
            tables = extracted_content['tables']
            page_count = extracted_content['page_count']
            extraction_method = extracted_content.get('extraction_method', 'unknown')
            
            # 🔄 TABLE PROCESSING CHECK: Skip table processing if disabled
            if not TABLE_DETECTION_ENABLED:
                logger.info(f"🚫 TABLE PROCESSING DISABLED - Processing document without table extraction")
                tables = []  # Clear any extracted tables
                enhanced_text = full_text  # Use original text without table replacement
            else:
                # 🔍 EXTRACTION RESULTS LOG
                logger.info(f"🔍 PDF EXTRACTION FINAL: Method={extraction_method}, Text={len(full_text) if full_text else 0} chars, Tables={len(tables)}, Pages={page_count}")
                if tables:
                    for i, table in enumerate(tables):
                        logger.info(f"🔍 PDF TABLE {i+1}: Page {table.get('page_number', '?')}, {table.get('row_count', '?')} rows, {table.get('column_count', '?')} cols, Method={table.get('metadata', {}).get('extraction_method', '?')}")
                
                # 🔄 HIMALAYA_AZURE ADAPTATION: Replace table placeholders with summaries in text
                enhanced_text = self._replace_table_placeholders_with_summaries(full_text, tables, file_record)
            
            # Create text chunks from enhanced content (with embedded table summaries)
            chunks = []
            if enhanced_text and len(enhanced_text.strip()) >= MIN_CHUNK_SIZE:
                chunks = self._create_text_chunks(enhanced_text, file_record, processing_metadata)
                logger.info(f"🔄 HIMALAYA_AZURE: Created {len(chunks)} chunks with embedded table summaries")
            
            # 🔗 SPLIT TABLE DETECTION AND MERGING
            if TABLE_DETECTION_ENABLED and SPLIT_TABLE_DETECTION_ENABLED and len(tables) > 1:
                logger.info(f"🔗 SPLIT TABLE DETECTION: Analyzing {len(tables)} tables for split table patterns")
                try:
                    # Apply header correction for Azure DI misdetection before split table detection
                    corrected_tables = self._apply_header_correction(tables)
                    
                    # Detect and merge split tables
                    merged_tables, individual_tables = table_merger_service.detect_and_merge_split_tables(corrected_tables)
                    
                    # Combine merged and individual tables
                    final_tables = []
                    
                    # Add merged tables with split table metadata
                    for merged_table in merged_tables:
                        merged_table.setdefault('metadata', {})['is_split_table'] = True
                        merged_table['metadata']['is_merged_table'] = True
                        final_tables.append(merged_table)
                    
                    # Add individual tables (not part of any split group)
                    for individual_table in individual_tables:
                        individual_table.setdefault('metadata', {})['is_split_table'] = False
                        individual_table['metadata']['is_merged_table'] = False
                        final_tables.append(individual_table)
                    
                    # Update tables list
                    tables = final_tables
                    
                    logger.info(f"🔗 SPLIT TABLE PROCESSING: {len(merged_tables)} merged groups, {len(individual_tables)} individual tables, {len(final_tables)} total tables")
                    
                except Exception as split_error:
                    logger.error(f"Error in split table detection: {str(split_error)}")
                    # Continue with original tables if split detection fails
                    for table in tables:
                        table.setdefault('metadata', {})['is_split_table'] = False
                        table['metadata']['is_merged_table'] = False
            else:
                # Mark all tables as non-split when detection is disabled or insufficient tables
                if TABLE_DETECTION_ENABLED:
                    for table in tables:
                        table.setdefault('metadata', {})['is_split_table'] = False
                        table['metadata']['is_merged_table'] = False
                    
                    if not SPLIT_TABLE_DETECTION_ENABLED:
                        logger.debug("Split table detection is disabled")
                    else:
                        logger.debug(f"Insufficient tables ({len(tables)}) for split table detection")
            
            # Store extracted tables (only if table detection is enabled)
            table_records = []
            if TABLE_DETECTION_ENABLED and tables:
                logger.info(f"Starting to store {len(tables)} extracted tables")
                
                for table_idx, table_data in enumerate(tables):
                    try:
                        logger.info(f"Creating table record {table_idx + 1}: Page {table_data.get('page_number', 0)}, {table_data.get('row_count', 0)} rows")
                        
                        # Extract split table metadata
                        is_split_table = table_data.get('metadata', {}).get('is_split_table', False)
                        merged_group_id = table_data.get('metadata', {}).get('merged_group_id', None)
                        
                        table_record = DocumentTable(
                            file_id=file_record.id,
                            table_index=table_idx,
                            page_number=table_data.get('page_number', 0),
                            table_data=table_data['data'],
                            table_headers=table_data.get('headers', []),
                            table_summary=table_data.get('summary', ''),
                            row_count=table_data.get('row_count', 0),
                            column_count=table_data.get('column_count', 0),
                            table_metadata=table_data.get('metadata', {}),
                            is_split_table=is_split_table,
                            merged_table_group_id=merged_group_id
                        )
                        db.session.add(table_record)
                        table_records.append(table_record)
                        
                        if is_split_table:
                            logger.info(f"Successfully created MERGED table record {table_idx + 1} (group: {merged_group_id})")
                        else:
                            logger.info(f"Successfully created table record {table_idx + 1}")
                        
                    except Exception as table_error:
                        logger.error(f"Error creating table record {table_idx + 1}: {str(table_error)}")
                        import traceback
                        traceback.print_exc()
            
            # Commit table records
            if table_records:
                try:
                    logger.info(f"Attempting to commit {len(table_records)} table records to database")
                    db.session.commit()
                    logger.info(f"Successfully stored {len(table_records)} tables for PDF {file_record.file_name}")
                    
                    # 🔍 AGENTIC PROCESSING: Process extracted tables through CSV agentic system
                    logger.info(f"🔍 PDF AGENTIC: Starting agentic processing for {len(tables)} extracted tables")
                    self._process_pdf_tables_through_agentic_system(tables, file_record, processing_metadata)
                    
                except Exception as commit_error:
                    logger.error(f"Error committing table records: {str(commit_error)}")
                    import traceback
                    traceback.print_exc()
                    db.session.rollback()
                    # Continue processing even if table storage fails
            else:
                logger.warning(f"No table records to commit for PDF {file_record.file_name}")
            
            # Calculate statistics
            word_count = len(full_text.split()) if full_text else 0
            char_count = len(full_text) if full_text else 0
            
            # Determine confidence and quality scores based on extraction success
            confidence_score = 0.95 if extraction_method == 'azure_document_intelligence' else 0.8 if (full_text or tables) else 0.3
            quality_score = 0.95 if extraction_method == 'azure_document_intelligence' else 0.8 if (word_count > 50 or len(tables) > 0) else 0.5
            
            logger.info(f"PDF processing completed: {page_count} pages, {len(chunks)} chunks, {len(tables)} tables using {extraction_method}")
            
            return {
                'chunk_count': len(chunks),
                'table_count': len(tables),
                'word_count': word_count,
                'char_count': char_count,
                'page_count': page_count,
                'confidence_score': confidence_score,
                'quality_score': quality_score,
                'extracted_content': full_text,
                'metadata': {
                    'processor': 'pdf_processor',
                    'processing_time': 2.0,
                    'extraction_method': extraction_method,
                    'tables_extracted': len(tables),
                    'text_extracted': bool(full_text)
                }
            }
            
        except Exception as e:
            logger.error(f"Error processing PDF {file_record.file_name}: {str(e)}")
            # Return basic fallback result
            return {
                'chunk_count': 0,
                'table_count': 0,
                'word_count': 0,
                'char_count': 0,
                'page_count': 1,
                'confidence_score': 0.1,
                'quality_score': 0.1,
                'metadata': {
                    'processor': 'pdf_processor',
                    'processing_time': 1.0,
                    'error': str(e)
                }
            }
    
    def _extract_pdf_content_with_pdfplumber(self, file_path: str) -> Dict[str, Any]:
        """
        Extract content from PDF using pdfplumber with enhanced table detection.
        
        Args:
            file_path: Path to PDF file
            
        Returns:
            Dictionary with extracted content
        """
        try:
            with pdfplumber.open(file_path) as pdf:
                all_text = []
                all_tables = []
                
                for page_num, page in enumerate(pdf.pages):
                    # Extract text
                    page_text = page.extract_text()
                    if page_text:
                        all_text.append(page_text)
                    
                    # Extract tables with multiple strategies for better detection (only if enabled)
                    if TABLE_DETECTION_ENABLED:
                        tables = self._extract_tables_from_page(page, page_num)
                        all_tables.extend(tables)
                    else:
                        logger.debug("🚫 TABLE PROCESSING DISABLED - Skipping table extraction from pdfplumber")
                
                return {
                    'text': '\n\n'.join(all_text),
                    'tables': all_tables,
                    'page_count': len(pdf.pages),
                    'extraction_method': 'pdfplumber'
                }
                
        except Exception as e:
            logger.error(f"pdfplumber extraction failed: {str(e)}")
            return {'text': '', 'tables': [], 'page_count': 0, 'extraction_method': 'pdfplumber_failed'}
    
    def _extract_pdf_content_with_azure_di(self, file_path: str) -> Dict[str, Any]:
        """
        Extract content from PDF using Azure Document Intelligence.
        Enhanced to handle image-based tables and provide comprehensive text extraction.
        
        Args:
            file_path: Path to PDF file
            
        Returns:
            Dictionary with extracted content
        """
        if not self.doc_intelligence_client:
            logger.error("Azure Document Intelligence client not initialized")
            return {'text': '', 'tables': [], 'page_count': 0, 'extraction_method': 'azure_document_intelligence_failed'}
        
        try:
            with open(file_path, "rb") as f:
                logger.info("Submitting document to Azure Document Intelligence (prebuilt-layout)")
                poller = self.doc_intelligence_client.begin_analyze_document(
                    "prebuilt-layout", f
                )
                
                timeout_seconds = 180
                start_time = time.time()
                
                while not poller.done() and time.time() - start_time < timeout_seconds:
                    time.sleep(5)
                
                if not poller.done():
                    raise TimeoutError(f"Document analysis timed out after {timeout_seconds} seconds")
                
                result = poller.result()
                logger.info(f"Azure DI analysis completed. Found {len(result.paragraphs) if result.paragraphs else 0} paragraphs and {len(result.tables) if result.tables else 0} tables")
                
                # Extract paragraphs with page information for better text organization
                paragraph_texts_by_page = {}
                if result.paragraphs:
                    for paragraph in result.paragraphs:
                        if paragraph.content and paragraph.content.strip():
                            page_num = paragraph.bounding_regions[0].page_number if paragraph.bounding_regions else 1
                            if page_num not in paragraph_texts_by_page:
                                paragraph_texts_by_page[page_num] = []
                            
                            # Include bounding region information for spatial analysis
                            bounding_region = None
                            if paragraph.bounding_regions:
                                region = paragraph.bounding_regions[0]
                                bounding_region = {
                                    'page_number': region.page_number,
                                    'polygon': region.polygon
                                }
                            
                            paragraph_texts_by_page[page_num].append({
                                "text_content": paragraph.content,
                                "bounding_region": bounding_region
                            })
                
                # Extract tables with enhanced processing for image-based tables (only if enabled)
                azure_tables = []
                table_text_by_page = {}  # Track table text content to avoid duplication
                
                if not TABLE_DETECTION_ENABLED:
                    logger.info("🚫 TABLE PROCESSING DISABLED - Skipping table extraction from Azure Document Intelligence")
                elif result.tables:
                    for table_idx, table in enumerate(result.tables):
                        if table.row_count == 0 or table.column_count == 0:
                            continue
                        
                        try:
                            rows = table.row_count
                            cols = table.column_count
                            
                            # Initialize empty DataFrame structure
                            df_data = [["" for _ in range(cols)] for _ in range(rows)]
                            
                            # Fill with cell data and track table text content
                            table_text_content = set()
                            for cell in table.cells:
                                if cell.content:
                                    row_idx = cell.row_index
                                    col_idx = cell.column_index
                                    if 0 <= row_idx < rows and 0 <= col_idx < cols:
                                        df_data[row_idx][col_idx] = cell.content
                                        table_text_content.add(cell.content.lower())
                            
                            # Process headers with explicit header detection
                            headers = []
                            has_explicit_headers = False
                            header_row_indices = set()
                            
                            # Check for explicit headers (Azure DI can detect column headers)
                            for cell in table.cells:
                                if hasattr(cell, 'kind') and cell.kind == 'columnHeader':
                                    header_row_indices.add(cell.row_index)
                                    has_explicit_headers = True
                            
                            if has_explicit_headers:
                                # Use EnhancedTableProcessor for robust header processing
                                from services.enhanced_table_processor import enhanced_table_processor
                                # Build raw_table as list of lists for the header rows and data
                                raw_table = df_data
                                # Use the processor to get headers and data rows
                                processed = enhanced_table_processor.process_complex_table(raw_table, page_num=1, table_idx=table_idx)
                                headers = processed['headers']
                                data_rows = processed['processed_data']
                            else:
                                # Use first row as headers if no headers provided
                                if rows > 0:
                                    headers = [str(h).strip() if h else f"Column_{i+1}" for i, h in enumerate(df_data[0])]
                                    data_rows = df_data[1:] if rows > 1 else []
                                else:
                                    headers = [f"Column_{j+1}" for j in range(cols)]
                                    data_rows = []
                            
                            # Get page number and bounding region
                            page_num = 1
                            table_region = None
                            if table.bounding_regions:
                                region = table.bounding_regions[0]
                                page_num = region.page_number
                                table_region = {
                                    'page_number': region.page_number,
                                    'polygon': region.polygon
                                }
                            
                            # Track table text content by page
                            if page_num not in table_text_by_page:
                                table_text_by_page[page_num] = set()
                            table_text_by_page[page_num].update(table_text_content)
                            
                            # 🚀 UNIFIED SEMANTIC ANALYSIS: Use centralized semantic analyzer
                            try:
                                from services.table_semantic_analyzer import table_semantic_analyzer
                                
                                # Create DataFrame for analysis
                                temp_df = pd.DataFrame(data_rows, columns=headers)
                                
                                # Prepare source info for PDF table
                                source_info = {
                                    'source_type': 'pdf',
                                    'file_name': 'unknown.pdf',  # File name not available in this context
                                    'page_number': page_num,
                                    'table_index': table_idx,
                                    'extraction_method': 'azure_document_intelligence',
                                    'table_type': 'image_based' if not has_explicit_headers else 'structured'
                                }
                                
                                # Generate comprehensive semantic summary
                                semantic_analysis = table_semantic_analyzer.analyze_table(
                                    df=temp_df,
                                    table_name=f"table_{table_idx}_page_{page_num}",
                                    source_info=source_info
                                )
                                
                                summary_result = {
                                    'summary': semantic_analysis.get('summary', f"Table {table_idx + 1} from page {page_num}"),
                                    'potential_analyses': semantic_analysis.get('potential_analyses', ['basic analysis'])
                                }
                                
                                logger.info(f"🔍 PDF UNIFIED: Generated {semantic_analysis.get('analysis_quality', 'unknown')} analysis for PDF table with {semantic_analysis.get('analysis_count', 0)} analyses")
                                
                            except Exception as semantic_error:
                                logger.error(f"❌ PDF UNIFIED: Error in semantic analysis: {str(semantic_error)}")
                                # Fallback to basic summary
                                summary_result = {
                                    'summary': f"Table {table_idx + 1} from page {page_num} with {len(headers)} columns",
                                    'potential_analyses': ['basic statistics', 'data analysis']
                                }
                            
                            # Create structured table data
                            structured_table = {
                                'page_number': page_num,
                                'table_index': table_idx,
                                'headers': headers,
                                'data': [headers] + data_rows,  # Include headers in data for compatibility
                                'row_count': len(data_rows) + 1,  # +1 for header row
                                'column_count': len(headers),
                                'summary': summary_result['summary'],
                                'potential_analyses': summary_result['potential_analyses'],
                                'metadata': {
                                    'extraction_method': 'azure_document_intelligence',
                                    'has_explicit_headers': has_explicit_headers,
                                    'page_number': page_num,
                                    'original_row_count': rows,
                                    'original_col_count': cols,
                                    'bounding_region': table_region,
                                    'table_type': 'image_based' if not has_explicit_headers else 'structured'
                                }
                            }
                            azure_tables.append(structured_table)
                            logger.info(f"Extracted table {table_idx + 1} from page {page_num}: {len(data_rows)} rows x {len(headers)} columns")
                            
                        except Exception as e:
                            logger.error(f"Error processing table {table_idx+1}: {str(e)}")
                
                # Helper function to check if text overlaps with table content
                def text_overlaps_with_table(text_content, page_num):
                    if page_num not in table_text_by_page:
                        return False
                    text_lower = text_content.lower()
                    for table_text in table_text_by_page[page_num]:
                        # Enhanced content-based overlap detection
                        if self._calculate_text_overlap_percentage(text_lower, table_text) > 0.3:
                            return True
                    return False
                
                # Enhanced function to check if polygons overlap spatially with sophisticated detection
                def polygons_overlap(poly1, poly2, overlap_threshold=0.1):
                    """
                    Enhanced polygon overlap detection with multiple strategies:
                    1. Bounding box overlap
                    2. Point-in-polygon testing
                    3. Area-based overlap calculation
                    4. Edge intersection detection
                    """
                    if not poly1 or not poly2 or not isinstance(poly1, list) or not isinstance(poly2, list):
                        return False
                    
                    try:
                        # Strategy 1: Bounding box overlap (fast initial check)
                        bbox1 = self._get_bounding_box(poly1)
                        bbox2 = self._get_bounding_box(poly2)
                        
                        if not self._bounding_boxes_overlap(bbox1, bbox2):
                            return False
                        
                        # Strategy 2: Calculate actual polygon overlap area
                        overlap_area = self._calculate_polygon_overlap_area(poly1, poly2)
                        poly1_area = self._calculate_polygon_area(poly1)
                        poly2_area = self._calculate_polygon_area(poly2)
                        
                        if poly1_area == 0 or poly2_area == 0:
                            return False
                        
                        # Calculate overlap percentage relative to smaller polygon
                        min_area = min(poly1_area, poly2_area)
                        overlap_percentage = overlap_area / min_area if min_area > 0 else 0
                        
                        # Strategy 3: Point-in-polygon testing for edge cases
                        if overlap_percentage < overlap_threshold:
                            # Check if any vertices of one polygon are inside the other
                            if (self._any_point_in_polygon(poly1, poly2) or 
                                self._any_point_in_polygon(poly2, poly1)):
                                overlap_percentage = max(overlap_percentage, overlap_threshold)
                        
                        # Strategy 4: Edge intersection detection
                        if overlap_percentage < overlap_threshold:
                            if self._polygons_have_intersecting_edges(poly1, poly2):
                                overlap_percentage = max(overlap_percentage, overlap_threshold)
                        
                        return overlap_percentage >= overlap_threshold
                        
                    except Exception as e:
                        logger.debug(f"Polygon overlap calculation failed: {str(e)}")
                        # Fallback to simple bounding box check
                        return self._simple_bounding_box_overlap(poly1, poly2)
                
                # Enhanced spatial overlap detection with content verification
                def enhanced_spatial_overlap_check(para, table, page_num):
                    """
                    Comprehensive overlap check combining spatial and content analysis
                    """
                    # Primary: Spatial overlap check
                    spatial_overlap = False
                    para_region = para.get('bounding_region')
                    table_region = table['metadata'].get('bounding_region')
                    
                    if para_region and para_region.get('polygon') and table_region and table_region.get('polygon'):
                        spatial_overlap = polygons_overlap(
                            para_region['polygon'], 
                            table_region['polygon'],
                            overlap_threshold=0.15  # More sensitive threshold
                        )
                    
                    # Secondary: Content-based verification
                    content_overlap = False
                    if spatial_overlap:
                        # Verify overlap with actual content analysis
                        para_text = para['text_content'].lower().strip()
                        table_content = table.get('content', '')
                        
                        if isinstance(table_content, list):
                            # Flatten table content for comparison
                            table_text = ' '.join([
                                ' '.join([str(cell) for cell in row if cell]) 
                                for row in table_content if row
                            ]).lower()
                        else:
                            table_text = str(table_content).lower()
                        
                        # Check for substantial content overlap
                        content_overlap = self._calculate_text_overlap_percentage(para_text, table_text) > 0.25
                        
                        # Additional check: Look for table-specific patterns in paragraph
                        if not content_overlap:
                            content_overlap = self._contains_table_patterns(para_text, table_text)
                    
                    # Tertiary: Proximity-based check for near-table content
                    proximity_overlap = False
                    if not spatial_overlap and para_region and table_region:
                        proximity_overlap = self._check_proximity_overlap(
                            para_region.get('polygon'), 
                            table_region.get('polygon'),
                            proximity_threshold=20  # pixels
                        )
                    
                    return spatial_overlap and content_overlap, proximity_overlap
                
                # Organize text by page, filtering out content that overlaps with tables
                organized_text_by_page = {}
                overlap_stats = {'spatial_overlaps': 0, 'content_overlaps': 0, 'proximity_overlaps': 0}
                
                for page_num, paragraphs in paragraph_texts_by_page.items():
                    page_text_parts = []
                    
                    for para in paragraphs:
                        # Enhanced overlap detection
                        skip_paragraph = False
                        skip_reason = None
                        
                        # Check content-based overlap first (faster)
                        if text_overlaps_with_table(para['text_content'], page_num):
                            skip_paragraph = True
                            skip_reason = "content_overlap"
                            overlap_stats['content_overlaps'] += 1
                        
                        # Check spatial overlap with enhanced detection
                        if not skip_paragraph:
                            for table in azure_tables:
                                if table['page_number'] == page_num:
                                    spatial_content_overlap, proximity_overlap = enhanced_spatial_overlap_check(
                                        para, table, page_num
                                    )
                                    
                                    if spatial_content_overlap:
                                        skip_paragraph = True
                                        skip_reason = "spatial_content_overlap"
                                        overlap_stats['spatial_overlaps'] += 1
                                        break
                                    elif proximity_overlap:
                                        # For proximity overlaps, check if it's table metadata/caption
                                        if self._is_table_metadata(para['text_content']):
                                            skip_paragraph = True
                                            skip_reason = "table_metadata"
                                            overlap_stats['proximity_overlaps'] += 1
                                            break
                        
                        if not skip_paragraph:
                            page_text_parts.append(para['text_content'])
                        else:
                            logger.debug(f"Skipped paragraph on page {page_num} due to {skip_reason}: {para['text_content'][:100]}...")
                    
                    # Add table placeholders in appropriate positions with enhanced metadata
                    table_placeholders = []
                    for table in azure_tables:
                        if table['page_number'] == page_num:
                            table_summary = table.get('summary', f"Table {table['table_index'] + 1}")
                            table_info = f"[TABLE {table['table_index'] + 1} - {table_summary}"
                            
                            # Add table dimensions and type info
                            if 'metadata' in table:
                                rows = table['metadata'].get('row_count', 0)
                                cols = table['metadata'].get('column_count', 0)
                                if rows and cols:
                                    table_info += f" ({rows}x{cols})"
                            
                            table_info += "]"
                            table_placeholders.append(table_info)
                    
                    # Combine text and table placeholders
                    if page_text_parts or table_placeholders:
                        page_content = []
                        if page_text_parts:
                            page_content.extend(page_text_parts)
                        if table_placeholders:
                            page_content.extend(table_placeholders)
                        
                        organized_text_by_page[page_num] = f"[Page {page_num}] " + " ".join(page_content)
                
                # Log overlap detection statistics
                logger.info(f"Text-table overlap detection: {overlap_stats['spatial_overlaps']} spatial, "
                           f"{overlap_stats['content_overlaps']} content, {overlap_stats['proximity_overlaps']} proximity overlaps filtered")
                
                # Combine all text
                all_text = []
                for page_num in sorted(organized_text_by_page.keys()):
                    all_text.append(organized_text_by_page[page_num])
                
                # Estimate page count
                page_count = 1
                if result.tables:
                    max_page = max([t.bounding_regions[0].page_number for t in result.tables if t.bounding_regions], default=1)
                    page_count = max(page_count, max_page)
                if result.paragraphs:
                    max_page = max([p.bounding_regions[0].page_number for p in result.paragraphs if p.bounding_regions], default=1)
                    page_count = max(page_count, max_page)
                
                final_text = '\n\n'.join(all_text)
                logger.info(f"Azure DI extracted {len(final_text)} characters of text and {len(azure_tables)} tables from {page_count} pages")
                
                return {
                    'text': final_text,
                    'tables': azure_tables,
                    'page_count': page_count,
                    'extraction_method': 'azure_document_intelligence'
                }
                
        except Exception as e:
            logger.error(f"Azure Document Intelligence extraction failed: {str(e)}")
            return {'text': '', 'tables': [], 'page_count': 0, 'extraction_method': 'azure_document_intelligence_failed'}
    
    def _extract_tables_from_page(self, page, page_num: int) -> List[Dict[str, Any]]:
        """
        Extract tables from a single page using improved detection logic.
        
        Args:
            page: pdfplumber page object
            page_num: Page number (0-indexed)
            
        Returns:
            List of table dictionaries
        """
        tables = []
        
        try:
            # Strategy 1: Default table extraction (most reliable)
            default_tables = page.extract_tables()
            logger.debug(f"Page {page_num + 1}: Default strategy found {len(default_tables) if default_tables else 0} tables")
            
            # Strategy 2: Lines-based detection (fallback if default finds nothing)
            lines_tables = []
            if not default_tables:
                lines_settings = {
                    "vertical_strategy": "lines",
                    "horizontal_strategy": "lines",
                    "snap_tolerance": 4,
                    "join_tolerance": 4,
                    "edge_min_length": 2
                }
                lines_tables = page.extract_tables(lines_settings)
                logger.debug(f"Page {page_num + 1}: Lines strategy found {len(lines_tables) if lines_tables else 0} tables")
            
            # Combine tables from reliable strategies only
            all_extracted_tables = []
            
            # Prioritize default strategy, use lines as fallback
            primary_tables = default_tables if default_tables else lines_tables
            strategy_name = "default" if default_tables else "lines"
            
            if primary_tables:
                for table_idx, table in enumerate(primary_tables):
                    if table and len(table) > 0:
                        # Validate table quality before adding
                        if self._is_valid_table(table):
                            all_extracted_tables.append({
                                'table': table,
                                'strategy': strategy_name,
                                'table_idx': table_idx
                            })
            
                        # Filter and select best tables
            unique_tables = self._filter_valid_tables(all_extracted_tables)
            
            # Convert to structured format using enhanced table processor
            for table_idx, table_data in enumerate(unique_tables):
                table = table_data['table']
                
                # 🚀 ENHANCED TABLE PROCESSING: Use enhanced table processor for complex tables
                try:
                    from services.enhanced_table_processor import enhanced_table_processor
                    
                    # Process the table with enhanced logic
                    enhanced_result = enhanced_table_processor.process_complex_table(
                        raw_table=table,
                        page_num=page_num,
                        table_idx=table_idx
                    )
                    
                    # Use enhanced results
                    clean_headers = enhanced_result['headers']
                    data_rows = enhanced_result['processed_data']
                    structure_type = enhanced_result['structure_type']
                    
                    logger.info(f"🔍 PDF ENHANCED: Processed table with structure type '{structure_type}' - {len(clean_headers)} columns, {len(data_rows)} data rows")
                    
                except Exception as enhanced_error:
                    logger.error(f"❌ PDF ENHANCED: Error in enhanced table processing: {str(enhanced_error)}")
                    # Fallback to original logic
                    headers = table[0] if table else []
                    
                    # Clean headers
                    clean_headers = []
                    for header in headers:
                        if header is None or str(header).strip() == '':
                            clean_headers.append(f"Column_{len(clean_headers) + 1}")
                        else:
                            clean_headers.append(str(header).strip())
                    
                    # Extract data rows (excluding header)
                    data_rows = table[1:] if len(table) > 1 else []
                    structure_type = 'fallback'
                
                # 🚀 UNIFIED SEMANTIC ANALYSIS: Use centralized semantic analyzer
                try:
                    from services.table_semantic_analyzer import table_semantic_analyzer
                    
                    # Create DataFrame for analysis
                    temp_df = pd.DataFrame(data_rows, columns=clean_headers)
                    
                    # Prepare source info for PDF table
                    source_info = {
                        'source_type': 'pdf',
                        'file_name': 'unknown.pdf',  # File name not available in this context
                        'page_number': page_num + 1,
                        'table_index': table_idx,
                        'extraction_method': 'pdfplumber_enhanced',
                        'table_type': 'structured'
                    }
                    
                    # Generate comprehensive semantic summary
                    semantic_analysis = table_semantic_analyzer.analyze_table(
                        df=temp_df,
                        table_name=f"table_{table_idx}_page_{page_num + 1}",
                        source_info=source_info
                    )
                    
                    summary_result = {
                        'summary': semantic_analysis.get('summary', f"Table {table_idx + 1} from page {page_num + 1}"),
                        'potential_analyses': semantic_analysis.get('potential_analyses', ['basic analysis'])
                    }
                    
                    logger.info(f"🔍 PDF UNIFIED: Generated {semantic_analysis.get('analysis_quality', 'unknown')} analysis for pdfplumber table with {semantic_analysis.get('analysis_count', 0)} analyses")
                    
                except Exception as semantic_error:
                    logger.error(f"❌ PDF UNIFIED: Error in semantic analysis: {str(semantic_error)}")
                    # Fallback to basic summary
                    summary_result = {
                        'summary': f"Table {table_idx + 1} from page {page_num + 1} with {len(clean_headers)} columns",
                        'potential_analyses': ['basic statistics', 'data analysis']
                    }
                
                structured_table = {
                    'page_number': page_num + 1,
                    'table_index': table_idx,
                    'headers': clean_headers,
                    'data': table,
                    'row_count': len(table),
                    'column_count': len(clean_headers),
                    'summary': summary_result['summary'],
                    'potential_analyses': summary_result['potential_analyses'],
                    'metadata': {
                        'extraction_method': 'pdfplumber_enhanced',
                        'extraction_strategy': table_data['strategy'],
                        'page_number': page_num + 1
                    }
                }
                tables.append(structured_table)
                
        except Exception as e:
            logger.error(f"Error extracting tables from page {page_num + 1}: {str(e)}")
        
        return tables
    
    def _deduplicate_tables(self, all_tables: List[Dict]) -> List[Dict]:
        """
        Deduplicate tables extracted by different strategies.
        
        Args:
            all_tables: List of tables from different extraction strategies
            
        Returns:
            List of unique tables
        """
        if not all_tables:
            return []
        
        unique_tables = []
        seen_signatures = set()
        
        for table_data in all_tables:
            table = table_data['table']
            
            # Create a signature for the table based on dimensions and content
            signature = self._create_table_signature(table)
            
            if signature not in seen_signatures:
                seen_signatures.add(signature)
                unique_tables.append(table_data)
        
        # Sort by table quality (prefer tables with more structure)
        unique_tables.sort(key=lambda x: self._score_table_quality(x['table']), reverse=True)
        
        return unique_tables
    
    def _create_table_signature(self, table: List[List]) -> str:
        """
        Create a signature for a table to help with deduplication.
        
        Args:
            table: Table data as list of lists
            
        Returns:
            String signature
        """
        if not table:
            return "empty"
        
        # Use dimensions and first/last row content as signature
        rows = len(table)
        cols = len(table[0]) if table else 0
        
        first_row = str(table[0][:3]) if table else ""  # First 3 cells of first row
        last_row = str(table[-1][:3]) if table else ""  # First 3 cells of last row
        
        return f"{rows}x{cols}_{hash(first_row + last_row)}"
    
    def _score_table_quality(self, table: List[List]) -> float:
        """
        Score table quality for deduplication ranking.
        
        Args:
            table: Table data as list of lists
            
        Returns:
            Quality score (higher is better)
        """
        if not table:
            return 0.0
        
        score = 0.0
        
        # Prefer tables with more rows and columns
        score += len(table) * 0.1
        score += len(table[0]) * 0.1 if table else 0
        
        # Prefer tables with more non-empty cells
        non_empty_cells = 0
        total_cells = 0
        
        for row in table:
            for cell in row:
                total_cells += 1
                if cell and str(cell).strip():
                    non_empty_cells += 1
        
        if total_cells > 0:
            score += (non_empty_cells / total_cells) * 10
        
        # Prefer tables with consistent column counts
        if len(table) > 1:
            col_counts = [len(row) for row in table]
            if len(set(col_counts)) == 1:  # All rows have same column count
                score += 5.0
        
        return score
    
    def _is_valid_table(self, table: List[List]) -> bool:
        """
        Validate if a table is worth keeping.
        
        Args:
            table: Table data as list of lists
            
        Returns:
            True if table is valid, False otherwise
        """
        if not table or len(table) < 2:  # Need at least header + 1 data row
            return False
        
        # Check if table has reasonable dimensions
        if len(table) > 100 or (table and len(table[0]) > 20):  # Too large, likely false positive
            return False
        
        # Check for consistent column counts
        col_counts = [len(row) for row in table]
        if len(set(col_counts)) > 2:  # Too many different column counts
            return False
        
        # Check if table has enough non-empty cells
        total_cells = sum(len(row) for row in table)
        non_empty_cells = sum(1 for row in table for cell in row if cell and str(cell).strip())
        
        if total_cells > 0 and (non_empty_cells / total_cells) < 0.3:  # Less than 30% filled
            return False
        
        return True
    
    def _filter_valid_tables(self, all_tables: List[Dict]) -> List[Dict]:
        """
        Filter and return only valid tables.
        
        Args:
            all_tables: List of table dictionaries
            
        Returns:
            List of valid tables
        """
        valid_tables = []
        
        for table_data in all_tables:
            table = table_data['table']
            if self._is_valid_table(table):
                valid_tables.append(table_data)
        
        # Sort by table quality
        valid_tables.sort(key=lambda x: self._score_table_quality(x['table']), reverse=True)
        
        return valid_tables
    
    def _extract_pdf_content_with_pypdf2(self, file_path: str) -> Dict[str, Any]:
        """
        Extract content from PDF using PyPDF2 (fallback method).
        
        Args:
            file_path: Path to PDF file
            
        Returns:
            Dictionary with extracted content
        """
        try:
            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                all_text = []
                
                for page_num, page in enumerate(pdf_reader.pages):
                    page_text = page.extract_text()
                    if page_text:
                        all_text.append(page_text)
                
                return {
                    'text': '\n\n'.join(all_text),
                    'tables': [],  # PyPDF2 doesn't extract tables well
                    'page_count': len(pdf_reader.pages),
                    'extraction_method': 'pypdf2'
                }
                
        except Exception as e:
            logger.error(f"PyPDF2 extraction failed: {str(e)}")
            return {'text': '', 'tables': [], 'page_count': 0, 'extraction_method': 'pypdf2_failed'}
    
    def _process_excel(self, file_path: str, file_record: File, 
                      processing_metadata: FileProcessingMetadata) -> Dict[str, Any]:
        """
        Process Excel/CSV document with comprehensive analysis.
        
        Args:
            file_path: Path to Excel/CSV file
            file_record: File database record
            processing_metadata: Processing metadata record
            
        Returns:
            Processing result dictionary
        """
        logger.info(f"Processing Excel/CSV: {file_record.file_name}")
        
        try:
            # Import the Excel processing service
            from services.excel_processing_service import excel_processing_service
            
            # Process the Excel/CSV file
            result = excel_processing_service.process_excel_file(
                file_path, file_record, processing_metadata
            )
            
            logger.info(f"Excel processing completed: {result.get('sheet_count', 0)} sheets, "
                       f"{result.get('chunk_count', 0)} chunks, {result.get('table_count', 0)} tables")
            
            return result
            
        except Exception as e:
            logger.error(f"Error processing Excel file {file_record.file_name}: {str(e)}")
            # Return basic fallback result
            return {
                'chunk_count': 0,
                'table_count': 0,
                'word_count': 0,
                'char_count': 0,
                'page_count': 1,
                'confidence_score': 0.1,
                'quality_score': 0.1,
                'metadata': {
                    'processor': 'excel_processor',
                    'processing_time': 1.0,
                    'error': str(e)
                }
            }
    
    def _process_text(self, file_path: str, file_record: File, 
                     processing_metadata: FileProcessingMetadata) -> Dict[str, Any]:
        """
        Process text-based documents (Word, PowerPoint, Text) with comprehensive analysis.
        
        Args:
            file_path: Path to text document
            file_record: File database record
            processing_metadata: Processing metadata record
            
        Returns:
            Processing result dictionary
        """
        logger.info(f"Processing text document: {file_record.file_name}")
        
        try:
            # Import the document processing service
            from services.document_processing_service import document_processing_service
            
            # Determine document type and process accordingly
            file_ext = os.path.splitext(file_path)[1].lower()
            
            if file_ext in ['.doc', '.docx']:
                result = document_processing_service.process_word_document(
                    file_path, file_record, processing_metadata
                )
            elif file_ext in ['.ppt', '.pptx']:
                result = document_processing_service.process_powerpoint_document(
                    file_path, file_record, processing_metadata
                )
            elif file_ext in ['.txt', '.md', '.html']:
                result = document_processing_service.process_text_document(
                    file_path, file_record, processing_metadata
                )
            else:
                # Fallback to basic text processing
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
                
                chunks = self._create_text_chunks(content, file_record, processing_metadata)
                
                result = {
                    'chunk_count': len(chunks),
                    'table_count': 0,
                    'word_count': len(content.split()),
                    'char_count': len(content),
                    'page_count': 1,
                    'confidence_score': 0.8,
                    'quality_score': 0.8,
                    'extracted_content': content,  # Add extracted content for summary generation
                    'metadata': {
                        'processor': 'basic_text_processor',
                        'processing_time': 1.0,
                        'file_type': file_ext
                    }
                }
            
            logger.info(f"Text document processing completed: {result.get('chunk_count', 0)} chunks, "
                       f"{result.get('table_count', 0)} tables")
            
            return result
            
        except Exception as e:
            logger.error(f"Error processing text document {file_record.file_name}: {str(e)}")
            # Return basic fallback result
            return {
                'chunk_count': 0,
                'table_count': 0,
                'word_count': 0,
                'char_count': 0,
                'page_count': 1,
                'confidence_score': 0.1,
                'quality_score': 0.1,
                'metadata': {
                    'processor': 'text_processor',
                    'processing_time': 1.0,
                    'error': str(e)
                }
            }
    
    def _process_image(self, file_path: str, file_record: File, 
                      processing_metadata: FileProcessingMetadata) -> Dict[str, Any]:
        """
        Process image document with OCR text extraction using Azure Document Intelligence.
        
        Args:
            file_path: Path to image file
            file_record: File database record
            processing_metadata: Processing metadata record
            
        Returns:
            Processing result dictionary
        """
        logger.info(f"Processing image: {file_record.file_name}")
        
        try:
            # Try Azure Document Intelligence first for better OCR results
            if self.doc_intelligence_client:
                logger.info(f"Using Azure Document Intelligence for image processing: {file_record.file_name}")
                extracted_content = self._extract_image_content_with_azure_di(file_path)
                
                # Only fallback to Tesseract if Azure DI completely fails (no text)
                if not extracted_content['text']:
                    logger.warning("Azure Document Intelligence returned no content, falling back to Tesseract OCR")
                    extracted_content = self._extract_image_content_with_tesseract(file_path, file_record)
                else:
                    logger.info(f"✅ Azure DI SUCCESS: Extracted {len(extracted_content.get('text', ''))} chars text from image - PRESERVING RESULTS")
            else:
                logger.info(f"Azure Document Intelligence not available, using Tesseract OCR: {file_record.file_name}")
                extracted_content = self._extract_image_content_with_tesseract(file_path, file_record)
            
            # Process the extracted content
            full_text = extracted_content.get('text', '')
            extraction_method = extracted_content.get('extraction_method', 'unknown')
            
            # Create text chunks if we have meaningful content
            chunks = []
            if full_text and len(full_text.strip()) >= 50:  # Minimum meaningful text
                chunks = self._create_text_chunks(full_text, file_record, processing_metadata)
            
            # If no meaningful text was extracted, create a descriptive chunk
            if not chunks:
                image_description = self._create_image_description(file_path, extracted_content)
                if image_description:
                    chunks = self._create_text_chunks(image_description, file_record, processing_metadata)
            
            # Calculate statistics
            word_count = len(full_text.split()) if full_text else 0
            char_count = len(full_text) if full_text else 0
            
            # Determine confidence and quality scores based on extraction method
            if extraction_method == 'azure_document_intelligence':
                confidence_score = 0.9 if word_count > 10 else 0.7 if word_count > 0 else 0.5
                quality_score = 0.95 if word_count > 50 else 0.8 if word_count > 10 else 0.6
            else:  # Tesseract or fallback
                confidence_score = 0.8 if word_count > 10 else 0.5 if word_count > 0 else 0.2
                quality_score = 0.9 if word_count > 50 else 0.7 if word_count > 10 else 0.3
            
            logger.info(f"Image processing completed: {len(chunks)} chunks, {word_count} words extracted via {extraction_method}")
            
            return {
                'chunk_count': len(chunks),
                'table_count': 0,  # Images don't contain structured tables
                'word_count': word_count,
                'char_count': char_count,
                'page_count': 1,
                'confidence_score': confidence_score,
                'quality_score': quality_score,
                'extracted_content': full_text,
                'metadata': {
                    'processor': 'image_processor',
                    'processing_time': 3.0,
                    'extraction_method': extraction_method,
                    'text_extracted': bool(full_text),
                    'azure_di_available': bool(self.doc_intelligence_client)
                }
            }
            
        except Exception as e:
            logger.error(f"Error processing image {file_record.file_name}: {str(e)}")
            # Return basic fallback result
            return {
                'chunk_count': 0,
                'table_count': 0,
                'word_count': 0,
                'char_count': 0,
                'page_count': 1,
                'confidence_score': 0.1,
                'quality_score': 0.1,
                'metadata': {
                    'processor': 'image_processor',
                    'processing_time': 1.0,
                    'error': str(e),
                    'azure_di_available': bool(self.doc_intelligence_client)
                }
            }
    
    def _process_basic(self, file_path: str, file_record: File, 
                      processing_metadata: FileProcessingMetadata) -> Dict[str, Any]:
        """
        Basic processing for unsupported document types.
        
        Args:
            file_path: Path to file
            file_record: File database record
            processing_metadata: Processing metadata record
            
        Returns:
            Processing result dictionary
        """
        logger.info(f"Basic processing for: {file_record.file_name}")
        
        # Create minimal processing result
        sample_text = f"Basic content from: {file_record.file_name}"
        chunks = self._create_text_chunks(sample_text, file_record, processing_metadata)
        
        return {
            'chunk_count': len(chunks),
            'table_count': 0,
            'word_count': len(sample_text.split()),
            'char_count': len(sample_text),
            'page_count': 1,
            'confidence_score': 0.5,
            'quality_score': 0.5,
            'extracted_content': sample_text,  # Add extracted content for summary generation
            'metadata': {
                'processor': 'basic_processor',
                'processing_time': 0.1
            }
        }
    
    def _create_text_chunks(self, content: str, file_record: File, 
                           processing_metadata: FileProcessingMetadata) -> List[DocumentChunk]:
        """
        Create text chunks from content and store them.
        
        Args:
            content: Text content to chunk
            file_record: File database record
            processing_metadata: Processing metadata record
            
        Returns:
            List of created DocumentChunk instances
        """
        if not INTELLIGENT_CHUNKING_ENABLED:
            # Create single chunk
            chunks = [content]
        else:
            # Create intelligent chunks
            chunks = self._intelligent_chunking(content)
        
        # Store chunks in database and Azure Search
        chunk_records = []
        for i, chunk_content in enumerate(chunks):
            if len(chunk_content.strip()) < MIN_CHUNK_SIZE:
                continue
            
            # Create database record
            chunk_record = DocumentChunk(
                file_id=file_record.id,
                chunk_index=i,
                content=chunk_content,
                word_count=len(chunk_content.split()),
                char_count=len(chunk_content),
                chunk_metadata={
                    'chunk_type': 'text',
                    'processing_method': 'intelligent' if INTELLIGENT_CHUNKING_ENABLED else 'basic',
                    'content_preview': chunk_content[:100] + '...' if len(chunk_content) > 100 else chunk_content,
                    'sentence_count': len([s for s in chunk_content.split('.') if s.strip()]),
                    'paragraph_count': len([p for p in chunk_content.split('\n\n') if p.strip()]),
                    'extraction_confidence': processing_metadata.processing_confidence_score if processing_metadata else 0.8,
                    'document_type': processing_metadata.document_type if processing_metadata else 'unknown'
                }
            )
            
            db.session.add(chunk_record)
            db.session.flush()  # Get the ID
            
            # Upload to Azure Search
            try:
                # Extract blob name from blob_url for proper filtering
                blob_name = file_record.blob_url.split('/')[-1] if file_record.blob_url else file_record.file_name
                
                azure_doc_id = self.search_service.upload_document_chunk({
                    'file_id': file_record.id,
                    'chunk_index': i,
                    'content': chunk_content,
                    'file_name': blob_name,  # Use blob name for metadata_storage_name field
                    'original_file_name': file_record.file_name,  # Keep original name for title/display
                    'document_type': processing_metadata.document_type,
                    'word_count': len(chunk_content.split()),
                    'char_count': len(chunk_content),
                    'chunk_metadata': chunk_record.chunk_metadata
                })
                
                chunk_record.azure_search_doc_id = azure_doc_id
                
            except Exception as e:
                logger.error(f"Failed to upload chunk to Azure Search: {str(e)}")
                # Continue processing even if Azure Search upload fails
            
            chunk_records.append(chunk_record)
        
        db.session.commit()
        return chunk_records
    
    def _intelligent_chunking(self, content: str) -> List[str]:
        """
        Create intelligent text chunks with semantic boundary preservation.
        Incorporates best practices from Himalaya Azure system.
        
        Args:
            content: Text content to chunk
            
        Returns:
            List of text chunks
        """
        if not SEMANTIC_CHUNKING_ENABLED:
            return self._simple_chunking(content)
        
        # Enhanced chunking strategy inspired by Himalaya Azure system
        chunks = []
        
        # Strategy 1: Page-based chunking (if content has page markers)
        if self._has_page_markers(content):
            chunks = self._page_based_chunking(content)
        else:
            # Strategy 2: Semantic paragraph-based chunking
            chunks = self._semantic_paragraph_chunking(content)
        
        # Post-process chunks to ensure quality
        chunks = self._post_process_chunks(chunks)
        
        logger.info(f"Created {len(chunks)} intelligent chunks from content using enhanced strategy")
        return chunks
    
    def _has_page_markers(self, content: str) -> bool:
        """
        Check if content has page markers (like from PDF processing).
        
        Args:
            content: Text content to check
            
        Returns:
            True if page markers are found
        """
        import re
        # Look for common page markers
        page_patterns = [
            r'\[PAGE \d+\]',
            r'Page \d+',
            r'--- Page \d+ ---',
            r'\f',  # Form feed character
        ]
        
        for pattern in page_patterns:
            if re.search(pattern, content, re.IGNORECASE):
                return True
        return False
    
    def _page_based_chunking(self, content: str) -> List[str]:
        """
        Chunk content based on page boundaries (Himalaya Azure approach).
        
        Args:
            content: Text content with page markers
            
        Returns:
            List of page-based chunks
        """
        import re
        
        # Split by page markers
        page_patterns = [
            r'\[PAGE \d+\]',
            r'Page \d+',
            r'--- Page \d+ ---',
            r'\f',  # Form feed character
        ]
        
        # Try each pattern to split content
        pages = [content]  # Default to single page
        
        for pattern in page_patterns:
            if re.search(pattern, content, re.IGNORECASE):
                pages = re.split(pattern, content, flags=re.IGNORECASE)
                break
        
        chunks = []
        for i, page_content in enumerate(pages):
            page_content = page_content.strip()
            if not page_content or len(page_content) < MIN_CHUNK_SIZE:
                continue
            
            # If page is too large, apply semantic chunking within the page
            if len(page_content) > MAX_CHUNK_SIZE:
                page_chunks = self._semantic_paragraph_chunking(page_content)
                # Add page context to each chunk
                for j, chunk in enumerate(page_chunks):
                    chunk_with_context = f"[Page {i+1}, Section {j+1}]\n{chunk}"
                    chunks.append(chunk_with_context)
            else:
                # Add page context
                chunk_with_context = f"[Page {i+1}]\n{page_content}"
                chunks.append(chunk_with_context)
        
        return chunks
    
    def _semantic_paragraph_chunking(self, content: str) -> List[str]:
        """
        Enhanced semantic chunking with paragraph and sentence boundary preservation.
        
        Args:
            content: Text content to chunk
            
        Returns:
            List of semantic chunks
        """
        chunks = []
        
        # Split content into logical sections first
        sections = self._split_into_sections(content)
        
        for section in sections:
            section_chunks = self._chunk_section(section)
            chunks.extend(section_chunks)
        
        return chunks
    
    def _split_into_sections(self, content: str) -> List[str]:
        """
        Split content into logical sections based on headers and structure.
        
        Args:
            content: Text content to split
            
        Returns:
            List of content sections
        """
        import re
        
        # Look for section headers (various formats)
        header_patterns = [
            r'^#{1,6}\s+.+$',  # Markdown headers
            r'^[A-Z][A-Z\s]{2,}:?\s*$',  # ALL CAPS headers
            r'^\d+\.\s+[A-Z].+$',  # Numbered sections
            r'^[A-Z][a-z]+(?:\s+[A-Z][a-z]+)*:?\s*$',  # Title Case headers
            r'^\*\*[^*]+\*\*\s*$',  # Bold headers
        ]
        
        # Find potential section breaks
        lines = content.split('\n')
        section_breaks = [0]  # Start with beginning
        
        for i, line in enumerate(lines):
            line = line.strip()
            if not line:
                continue
                
            # Check if line matches any header pattern
            for pattern in header_patterns:
                if re.match(pattern, line, re.MULTILINE):
                    section_breaks.append(i)
                    break
        
        section_breaks.append(len(lines))  # End with last line
        
        # Create sections
        sections = []
        for i in range(len(section_breaks) - 1):
            start_idx = section_breaks[i]
            end_idx = section_breaks[i + 1]
            section_lines = lines[start_idx:end_idx]
            section_content = '\n'.join(section_lines).strip()
            
            if section_content and len(section_content) >= MIN_CHUNK_SIZE:
                sections.append(section_content)
        
        # If no sections found, return original content
        if not sections:
            sections = [content]
        
        return sections
    
    def _chunk_section(self, section: str) -> List[str]:
        """
        Chunk a single section with semantic boundary preservation.
        
        Args:
            section: Section content to chunk
            
        Returns:
            List of chunks from the section
        """
        if len(section) <= CHUNK_SIZE:
            return [section]
        
        chunks = []
        
        # Split section into paragraphs
        paragraphs = [p.strip() for p in section.split('\n\n') if p.strip()]
        
        current_chunk = []
        current_length = 0
        
        for paragraph in paragraphs:
            # Handle table summaries specially (from Himalaya Azure approach)
            if paragraph.startswith('[Table Summary:') or paragraph.startswith('[TABLE'):
                # Always include table summaries in chunks
                if current_chunk:
                    # Finish current chunk
                    chunk_text = '\n\n'.join(current_chunk)
                    if len(chunk_text.strip()) >= MIN_CHUNK_SIZE:
                        chunks.append(chunk_text)
                    current_chunk = []
                    current_length = 0
                
                # Add table summary as separate chunk or with following content
                current_chunk.append(paragraph)
                current_length = len(paragraph)
                continue
            
            # Split long paragraphs into sentences if needed
            if SENTENCE_BOUNDARY_PRESERVATION and len(paragraph) > CHUNK_SIZE // 2:
                sentences = self._split_into_sentences(paragraph)
            else:
                sentences = [paragraph]
            
            for sentence in sentences:
                sentence = sentence.strip()
                if not sentence:
                    continue
                
                sentence_length = len(sentence)
                
                # Check if adding this sentence would exceed chunk size
                if current_length + sentence_length + 2 > CHUNK_SIZE and current_chunk:  # +2 for \n\n
                    # Create chunk from current content
                    chunk_text = '\n\n'.join(current_chunk)
                    if len(chunk_text.strip()) >= MIN_CHUNK_SIZE:
                        chunks.append(chunk_text)
                    
                    # Start new chunk with overlap
                    overlap_content = self._create_contextual_overlap(current_chunk, sentence)
                    current_chunk = overlap_content + [sentence]
                    current_length = sum(len(s) + 2 for s in current_chunk)
                else:
                    current_chunk.append(sentence)
                    current_length += sentence_length + 2  # +2 for \n\n
                
                # Ensure chunk doesn't exceed maximum size
                if current_length > MAX_CHUNK_SIZE:
                    # Force create chunk
                    if len(current_chunk) > 1:
                        chunk_text = '\n\n'.join(current_chunk[:-1])
                        if len(chunk_text.strip()) >= MIN_CHUNK_SIZE:
                            chunks.append(chunk_text)
                        
                        # Start new chunk with just the last sentence
                        current_chunk = [sentence]
                        current_length = sentence_length
                    else:
                        # Single sentence is too long, truncate it
                        truncated = sentence[:MAX_CHUNK_SIZE-100] + "..."
                        chunks.append(truncated)
                        current_chunk = []
                        current_length = 0
        
        # Add final chunk
        if current_chunk:
            chunk_text = '\n\n'.join(current_chunk)
            if len(chunk_text.strip()) >= MIN_CHUNK_SIZE:
                chunks.append(chunk_text)
        
        return chunks
    
    def _create_contextual_overlap(self, current_chunk: List[str], next_sentence: str) -> List[str]:
        """
        Create contextual overlap that preserves meaning (inspired by Himalaya Azure).
        
        Args:
            current_chunk: Current chunk content
            next_sentence: Next sentence to be added
            
        Returns:
            Overlap content for next chunk
        """
        if not current_chunk:
            return []
        
        overlap_content = []
        overlap_chars = 0
        target_overlap = min(CHUNK_OVERLAP, len('\n\n'.join(current_chunk)) // 3)
        
        # Prioritize table summaries and headers for overlap
        for item in reversed(current_chunk):
            if (item.startswith('[Table Summary:') or 
                item.startswith('[TABLE') or
                item.isupper() or  # Potential header
                len(item.split()) <= 10):  # Short important sentences
                
                if overlap_chars + len(item) <= target_overlap:
                    overlap_content.insert(0, item)
                    overlap_chars += len(item)
                else:
                    break
        
        # If no special content found, use standard overlap
        if not overlap_content:
            for item in reversed(current_chunk):
                if overlap_chars + len(item) <= target_overlap:
                    overlap_content.insert(0, item)
                    overlap_chars += len(item)
                else:
                    break
        
        return overlap_content
    
    def _simple_chunking(self, content: str) -> List[str]:
        """
        Simple word-based chunking (fallback method).
        
        Args:
            content: Text content to chunk
            
        Returns:
            List of text chunks
        """
        chunks = []
        words = content.split()
        
        current_chunk = []
        current_length = 0
        
        for word in words:
            word_length = len(word) + 1  # +1 for space
            
            if current_length + word_length > CHUNK_SIZE and current_chunk:
                # Create chunk with overlap
                chunk_text = ' '.join(current_chunk)
                chunks.append(chunk_text)
                
                # Start new chunk with overlap
                overlap_words = current_chunk[-CHUNK_OVERLAP//10:] if CHUNK_OVERLAP > 0 else []
                current_chunk = overlap_words + [word]
                current_length = sum(len(w) + 1 for w in current_chunk)
            else:
                current_chunk.append(word)
                current_length += word_length
        
        # Add final chunk
        if current_chunk:
            chunks.append(' '.join(current_chunk))
        
        return chunks
    
    def _split_into_sentences(self, text: str) -> List[str]:
        """
        Split text into sentences using simple heuristics.
        
        Args:
            text: Text to split
            
        Returns:
            List of sentences
        """
        import re
        
        # Simple sentence splitting using regex
        # This handles common sentence endings: . ! ?
        sentence_endings = r'[.!?]+(?:\s+|$)'
        sentences = re.split(sentence_endings, text)
        
        # Clean up and filter empty sentences
        sentences = [s.strip() for s in sentences if s.strip()]
        
        # If no sentences found, return the original text
        if not sentences:
            return [text]
        
        return sentences
    
    def _post_process_chunks(self, chunks: List[str]) -> List[str]:
        """
        Post-process chunks to ensure quality and consistency.
        
        Args:
            chunks: List of raw chunks
            
        Returns:
            List of processed chunks
        """
        processed_chunks = []
        
        for chunk in chunks:
            chunk = chunk.strip()
            
            # Skip chunks that are too small
            if len(chunk) < MIN_CHUNK_SIZE:
                continue
            
            # Truncate chunks that are too large
            if len(chunk) > MAX_CHUNK_SIZE:
                # Try to truncate at sentence boundary
                sentences = self._split_into_sentences(chunk)
                truncated_chunk = ""
                for sentence in sentences:
                    if len(truncated_chunk) + len(sentence) <= MAX_CHUNK_SIZE:
                        truncated_chunk += sentence + " "
                    else:
                        break
                chunk = truncated_chunk.strip()
            
            # Ensure chunk ends with proper punctuation
            if chunk and not chunk[-1] in '.!?':
                # Find last sentence-ending punctuation
                last_punct = max(
                    chunk.rfind('.'),
                    chunk.rfind('!'),
                    chunk.rfind('?')
                )
                if last_punct > len(chunk) * 0.8:  # If punctuation is near the end
                    chunk = chunk[:last_punct + 1]
            
            processed_chunks.append(chunk)
        
        return processed_chunks
    
    def _fallback_to_basic_processing(self, file_record: File) -> Dict[str, Any]:
        """
        Fallback to basic processing when enhanced processing fails.
        
        Args:
            file_record: File database record
            
        Returns:
            Processing result dictionary
        """
        logger.info(f"Falling back to basic processing for file {file_record.id}")
        
        # This would trigger the existing basic processing workflow
        # For now, return a basic result
        return {
            'status': 'fallback_completed',
            'message': 'Completed with basic processing fallback',
            'file_id': file_record.id,
            'processing_result': {
                'processor': 'basic_fallback',
                'chunk_count': 0,
                'table_count': 0
            }
        }
    
    def _serialize_processing_metadata(self, metadata: FileProcessingMetadata) -> Dict[str, Any]:
        """
        Serialize processing metadata for API response.
        
        Args:
            metadata: FileProcessingMetadata instance
            
        Returns:
            Serialized metadata dictionary
        """
        return {
            'id': metadata.id,
            'file_id': metadata.file_id,
            'document_type': metadata.document_type,
            'content_summary': metadata.content_summary,
            'page_count': metadata.page_count,
            'table_count': metadata.table_count,
            'chunk_count': metadata.chunk_count,
            'word_count': metadata.word_count,
            'char_count': metadata.char_count,
            'enhanced_processing_status': metadata.enhanced_processing_status,
            'processing_confidence_score': metadata.processing_confidence_score,
            'extraction_quality_score': metadata.extraction_quality_score,
            'processing_started_at': metadata.processing_started_at.isoformat() if metadata.processing_started_at else None,
            'enhanced_processed_at': metadata.enhanced_processed_at.isoformat() if metadata.enhanced_processed_at else None,
            'created_at': metadata.created_at.isoformat() if metadata.created_at else None,
            'updated_at': metadata.updated_at.isoformat() if metadata.updated_at else None
        }

    def _download_file_from_blob_storage(self, file_record: File) -> Optional[str]:
        """
        Download file from Azure Blob Storage to a temporary location.
        
        Args:
            file_record: File database record with blob_url
            
        Returns:
            Path to downloaded temporary file, or None if failed
        """
        try:
            # Extract blob name from blob_url
            blob_url = file_record.blob_url
            if not blob_url:
                logger.error(f"No blob_url found for file {file_record.id}")
                return None
            
            # Parse blob name from URL
            # URL format: https://account.blob.core.windows.net/container/blob_name
            blob_name = blob_url.split('/')[-1]
            
            # Create blob service client
            blob_service_client = BlobServiceClient.from_connection_string(AZURE_STORAGE_CONNECTION_STRING)
            container_client = blob_service_client.get_container_client(AZURE_STORAGE_CONTAINER_NAME)
            blob_client = container_client.get_blob_client(blob_name)
            
            # Create temporary file with correct extension
            file_extension = os.path.splitext(file_record.file_name)[1]
            with tempfile.NamedTemporaryFile(delete=False, suffix=file_extension) as temp_file:
                temp_file_path = temp_file.name
                
                # Download blob content to temporary file
                blob_data = blob_client.download_blob()
                temp_file.write(blob_data.readall())
                
            logger.info(f"Downloaded file {file_record.file_name} to {temp_file_path}")
            return temp_file_path
            
        except Exception as e:
            logger.error(f"Failed to download file {file_record.id} from blob storage: {str(e)}")
            return None

    def _generate_content_summary(self, content: str, file_name: str, document_type: str) -> str:
        """
        Generate a content summary for a document using Azure OpenAI.
        
        Args:
            content: Document content
            file_name: Document file name
            document_type: Document type
            
        Returns:
            Generated content summary
        """
        if not CONTENT_SUMMARY_ENABLED or not self.openai_client:
            logger.info(f"Content summary disabled or OpenAI client not available for {file_name}")
            return None
        
        if not content or len(content.strip()) < 50:
            logger.warning(f"Content too short for summary generation: {file_name}")
            return None
        
        try:
            # Truncate content if too long (keep first 4000 characters for summary)
            max_content_length = 4000
            truncated_content = content[:max_content_length] if len(content) > max_content_length else content
            
            # Clean the content for better processing
            truncated_content = truncated_content.replace('\n\n', ' ').replace('\n', ' ').strip()
            
            # Prepare the prompt for content summary generation
            prompt = f"""Please generate a concise summary of the following {document_type} document titled "{file_name}".

The summary should:
1. Capture the main topics and key points
2. Be between 100-{CONTENT_SUMMARY_MAX_LENGTH} characters
3. Be informative and well-structured
4. Focus on the most important information

Document content:
{truncated_content}

Summary:"""
            
            logger.info(f"Generating content summary for {file_name} ({len(truncated_content)} chars)")
            
            # Generate content summary using Azure OpenAI
            response = self.openai_client.chat.completions.create(
                model=AZURE_OPENAI_DEPLOYMENT_NAME,
                messages=[
                    {"role": "system", "content": "You are a helpful assistant that creates concise, informative document summaries."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=200,  # Limit response length
                temperature=0.3  # Lower temperature for more focused summaries
            )
            
            summary = response.choices[0].message.content.strip() if response.choices else None
            
            # Ensure summary doesn't exceed max length
            if summary and len(summary) > CONTENT_SUMMARY_MAX_LENGTH:
                summary = summary[:CONTENT_SUMMARY_MAX_LENGTH-3] + "..."
            
            logger.info(f"Generated summary for {file_name}: {len(summary) if summary else 0} characters")
            return summary
            
        except Exception as e:
            logger.error(f"Failed to generate content summary for file {file_name}: {str(e)}")
            import traceback
            traceback.print_exc()
            return None

    def _process_pdf_tables_through_agentic_system(self, tables: List[Dict[str, Any]], file_record: File, processing_metadata: FileProcessingMetadata):
        """
        Process extracted PDF tables through the agentic CSV system.
        
        This method converts extracted PDF tables to DataFrames and processes them
        through the Excel processing service to save them in agentic tables.
        
        Args:
            tables: List of extracted table dictionaries
            file_record: File database record
            processing_metadata: Processing metadata record
        """
        try:
            # Import Excel processing service
            from services.excel_processing_service import ExcelProcessingService
            excel_service = ExcelProcessingService()
            
            logger.info(f"🔍 PDF AGENTIC: Processing {len(tables)} tables through agentic system")
            
            for table_idx, table_data in enumerate(tables):
                try:
                    # Convert table data to DataFrame
                    table_rows = table_data.get('data', [])
                    if not table_rows or len(table_rows) < 2:  # Need at least header + 1 data row
                        logger.warning(f"🔍 PDF AGENTIC: Skipping table {table_idx} - insufficient data")
                        continue
                    
                    # Create DataFrame from table data
                    headers = table_data.get('headers', [])
                    if not headers and len(table_rows) > 0:
                        # Use first row as headers if no headers provided
                        headers = table_rows[0]
                        data_rows = table_rows[1:]
                    else:
                        data_rows = table_rows
                    
                    # Clean headers and data
                    clean_headers = [str(h).strip() if h else f"Column_{i+1}" for i, h in enumerate(headers)]
                    clean_data = []
                    for row in data_rows:
                        clean_row = [str(cell).strip() if cell else "" for cell in row]
                        # Ensure row has same length as headers
                        while len(clean_row) < len(clean_headers):
                            clean_row.append("")
                        clean_data.append(clean_row[:len(clean_headers)])  # Truncate if too long
                    
                    if not clean_data:
                        logger.warning(f"🔍 PDF AGENTIC: Skipping table {table_idx} - no data rows after cleaning")
                        continue
                    
                    # Create DataFrame
                    df = pd.DataFrame(clean_data, columns=clean_headers)
                    
                    # Generate table name
                    table_name = f"table_{table_idx}_page_{table_data.get('page_number', 1)}"
                    
                    logger.info(f"🔍 PDF AGENTIC: Processing table '{table_name}' with {len(df)} rows, {len(df.columns)} columns")
                    
                    # Save table as CSV to blob storage (similar to Azure system)
                    csv_blob_url = self._save_table_as_csv_to_blob(df, table_name, file_record)
                    
                    if csv_blob_url:
                        logger.info(f"🔍 PDF AGENTIC: Saved table '{table_name}' to blob: {csv_blob_url}")
                        
                        # Prepare analysis result with semantic summary for agentic processing
                        analysis_result_with_semantic = {
                            'semantic_summary': {
                                'summary': table_data.get('summary', ''),
                                'potential_analyses': table_data.get('potential_analyses', [])
                            },
                            'status': 'success'
                        }
                        
                        # Process through Excel service agentic system
                        result = excel_service._process_dataframe(
                            df=df,
                            sheet_name=table_name,
                            file_record=file_record,
                            processing_metadata=processing_metadata,
                            sheet_index=table_idx,
                            analysis_result=analysis_result_with_semantic,  # Pass semantic summary
                            csv_blob_url=csv_blob_url,  # Pass the CSV blob URL
                            total_sheet_count=1  # PDF tables are treated as standalone
                        )
                        
                        if result:
                            logger.info(f"✅ PDF AGENTIC: Successfully processed table '{table_name}' through agentic system")
                        else:
                            logger.warning(f"⚠️ PDF AGENTIC: Failed to process table '{table_name}' through agentic system")
                    else:
                        logger.warning(f"⚠️ PDF AGENTIC: Failed to save table '{table_name}' to blob storage")
                        
                except Exception as table_error:
                    logger.error(f"❌ PDF AGENTIC: Error processing table {table_idx}: {str(table_error)}")
                    import traceback
                    traceback.print_exc()
                    continue
            
            logger.info(f"✅ PDF AGENTIC: Completed agentic processing for all PDF tables")
            
        except Exception as e:
            logger.error(f"❌ PDF AGENTIC: Error in agentic table processing: {str(e)}")
            import traceback
            traceback.print_exc()

    def _save_table_as_csv_to_blob(self, df: pd.DataFrame, table_name: str, file_record: File) -> Optional[str]:
        """
        Save a DataFrame as CSV to Azure Blob Storage.
        
        Args:
            df: DataFrame to save
            table_name: Name for the table/CSV file
            file_record: Original file record
            
        Returns:
            Blob URL of saved CSV, or None if failed
        """
        try:
            import io
            from datetime import datetime
            from azure.storage.blob import BlobServiceClient
            from config.settings import AZURE_STORAGE_CONNECTION_STRING, AZURE_STORAGE_CONTAINER_NAME
            
            # Generate unique CSV filename
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            csv_filename = f"{file_record.id}_{table_name}_{timestamp}.csv"
            
            # Convert DataFrame to CSV string
            csv_buffer = io.StringIO()
            df.to_csv(csv_buffer, index=False)
            csv_content = csv_buffer.getvalue()
            
            # Upload to blob storage
            blob_service_client = BlobServiceClient.from_connection_string(AZURE_STORAGE_CONNECTION_STRING)
            container_client = blob_service_client.get_container_client(AZURE_STORAGE_CONTAINER_NAME)
            blob_client = container_client.get_blob_client(csv_filename)
            
            # Upload CSV content
            blob_client.upload_blob(csv_content, overwrite=True, content_type='text/csv')
            
            # Generate blob URL
            blob_url = f"https://{blob_service_client.account_name}.blob.core.windows.net/{AZURE_STORAGE_CONTAINER_NAME}/{csv_filename}"
            
            logger.info(f"🔍 PDF AGENTIC: Saved CSV to blob: {blob_url}")
            return blob_url
            
        except Exception as e:
            logger.error(f"❌ PDF AGENTIC: Error saving CSV to blob: {str(e)}")
            return None

    def _generate_table_semantic_summary(self, headers: List[str], data_rows: List[List[str]], 
                                        page_num: int, table_idx: int, table_type: str = 'structured') -> Dict[str, Any]:
        """
        Generate semantic summary for a table using AI analysis.
        
        Args:
            headers: Table headers
            data_rows: Table data rows
            page_num: Page number
            table_idx: Table index
            table_type: Type of table structure
            
        Returns:
            Dictionary with semantic summary and metadata
        """
        try:
            # Create a sample of the table for analysis
            sample_rows = data_rows[:5] if len(data_rows) > 5 else data_rows
            
            # Format table for analysis
            table_text = f"Headers: {', '.join(headers)}\n"
            for i, row in enumerate(sample_rows):
                row_text = ', '.join([str(cell) if cell else '' for cell in row])
                table_text += f"Row {i+1}: {row_text}\n"
            
            # Generate summary using AI
            summary_prompt = f"""Analyze this table and provide a brief, descriptive summary:

{table_text}

Provide a concise summary (1-2 sentences) describing what this table contains and its purpose."""

            try:
                from langchain_openai import AzureChatOpenAI
                from config.settings import (
                    AZURE_OPENAI_KEY, AZURE_OPENAI_API_VERSION, 
                    AZURE_OPENAI_ENDPOINT, AZURE_OPENAI_DEPLOYMENT_NAME
                )
                
                llm = AzureChatOpenAI(
                    azure_deployment=AZURE_OPENAI_DEPLOYMENT_NAME,
                    api_version=AZURE_OPENAI_API_VERSION,
                    azure_endpoint=AZURE_OPENAI_ENDPOINT,
                    api_key=AZURE_OPENAI_KEY,
                    temperature=0.1
                )
                
                response = llm.invoke(summary_prompt)
                ai_summary = response.content.strip()
                
            except Exception as ai_error:
                logger.debug(f"AI summary generation failed: {str(ai_error)}")
                # Fallback to rule-based summary
                ai_summary = f"Table with {len(headers)} columns and {len(data_rows)} rows containing {', '.join(headers[:3])}{'...' if len(headers) > 3 else ''}"
            
            return {
                'summary': ai_summary,
                'row_count': len(data_rows),
                'column_count': len(headers),
                'table_type': table_type,
                'page_number': page_num + 1,
                'table_index': table_idx
            }
            
        except Exception as e:
            logger.error(f"Error generating table semantic summary: {str(e)}")
            return {
                'summary': f"Table {table_idx + 1} on page {page_num + 1}",
                'row_count': len(data_rows) if data_rows else 0,
                'column_count': len(headers) if headers else 0,
                'table_type': table_type,
                'page_number': page_num + 1,
                'table_index': table_idx
            }

    # ===== ENHANCED TEXT-TABLE OVERLAP DETECTION METHODS =====
    
    def _calculate_text_overlap_percentage(self, text1: str, text2: str) -> float:
        """
        Calculate the percentage of text overlap between two strings using multiple strategies.
        
        Args:
            text1: First text string
            text2: Second text string
            
        Returns:
            Overlap percentage (0.0 to 1.0)
        """
        try:
            if not text1 or not text2:
                return 0.0
            
            # Normalize texts
            text1_clean = ' '.join(text1.lower().split())
            text2_clean = ' '.join(text2.lower().split())
            
            if not text1_clean or not text2_clean:
                return 0.0
            
            # Strategy 1: Exact substring matching
            if text1_clean in text2_clean or text2_clean in text1_clean:
                return 1.0
            
            # Strategy 2: Word-level overlap
            words1 = set(text1_clean.split())
            words2 = set(text2_clean.split())
            
            if not words1 or not words2:
                return 0.0
            
            common_words = words1.intersection(words2)
            word_overlap = len(common_words) / min(len(words1), len(words2))
            
            # Strategy 3: Character-level similarity (for short texts)
            if len(text1_clean) < 100 and len(text2_clean) < 100:
                char_overlap = self._calculate_character_overlap(text1_clean, text2_clean)
                return max(word_overlap, char_overlap)
            
            return word_overlap
            
        except Exception as e:
            logger.debug(f"Text overlap calculation failed: {str(e)}")
            return 0.0
    
    def _calculate_character_overlap(self, text1: str, text2: str) -> float:
        """Calculate character-level overlap using longest common subsequence."""
        try:
            # Simple LCS-based similarity
            m, n = len(text1), len(text2)
            if m == 0 or n == 0:
                return 0.0
            
            # Dynamic programming for LCS
            dp = [[0] * (n + 1) for _ in range(m + 1)]
            
            for i in range(1, m + 1):
                for j in range(1, n + 1):
                    if text1[i-1] == text2[j-1]:
                        dp[i][j] = dp[i-1][j-1] + 1
                else:
                        dp[i][j] = max(dp[i-1][j], dp[i][j-1])
            
            lcs_length = dp[m][n]
            return lcs_length / min(m, n)
            
        except Exception:
            return 0.0
    
    def _get_bounding_box(self, polygon: List[List[float]]) -> Dict[str, float]:
        """
        Get bounding box coordinates from a polygon.
        
        Args:
            polygon: List of [x, y] coordinate pairs
            
        Returns:
            Dictionary with min_x, max_x, min_y, max_y
        """
        try:
            x_coords = [point[0] for point in polygon]
            y_coords = [point[1] for point in polygon]
            
            return {
                'min_x': min(x_coords),
                'max_x': max(x_coords),
                'min_y': min(y_coords),
                'max_y': max(y_coords)
            }
        except Exception:
            return {'min_x': 0, 'max_x': 0, 'min_y': 0, 'max_y': 0}
    
    def _bounding_boxes_overlap(self, bbox1: Dict[str, float], bbox2: Dict[str, float]) -> bool:
        """Check if two bounding boxes overlap."""
        try:
            return not (bbox1['max_x'] < bbox2['min_x'] or 
                       bbox2['max_x'] < bbox1['min_x'] or 
                       bbox1['max_y'] < bbox2['min_y'] or 
                       bbox2['max_y'] < bbox1['min_y'])
        except Exception:
            return False
    
    def _calculate_polygon_area(self, polygon: List[List[float]]) -> float:
        """
        Calculate the area of a polygon using the shoelace formula.
        
        Args:
            polygon: List of [x, y] coordinate pairs
            
        Returns:
            Area of the polygon
        """
        try:
            if len(polygon) < 3:
                return 0.0
            
            # Shoelace formula
            area = 0.0
            n = len(polygon)
            
            for i in range(n):
                j = (i + 1) % n
                area += polygon[i][0] * polygon[j][1]
                area -= polygon[j][0] * polygon[i][1]
            
            return abs(area) / 2.0
            
        except Exception:
            return 0.0
    
    def _calculate_polygon_overlap_area(self, poly1: List[List[float]], poly2: List[List[float]]) -> float:
        """
        Calculate the overlap area between two polygons using Sutherland-Hodgman clipping.
        
        Args:
            poly1: First polygon
            poly2: Second polygon
            
        Returns:
            Overlap area
        """
        try:
            # Simplified polygon intersection using bounding box approximation
            # For more accurate results, would need full Sutherland-Hodgman implementation
            
            bbox1 = self._get_bounding_box(poly1)
            bbox2 = self._get_bounding_box(poly2)
            
            if not self._bounding_boxes_overlap(bbox1, bbox2):
                return 0.0
            
            # Calculate intersection rectangle area
            overlap_min_x = max(bbox1['min_x'], bbox2['min_x'])
            overlap_max_x = min(bbox1['max_x'], bbox2['max_x'])
            overlap_min_y = max(bbox1['min_y'], bbox2['min_y'])
            overlap_max_y = min(bbox1['max_y'], bbox2['max_y'])
            
            if overlap_max_x <= overlap_min_x or overlap_max_y <= overlap_min_y:
                return 0.0
            
            return (overlap_max_x - overlap_min_x) * (overlap_max_y - overlap_min_y)
            
        except Exception:
            return 0.0
    
    def _any_point_in_polygon(self, test_polygon: List[List[float]], target_polygon: List[List[float]]) -> bool:
        """
        Check if any point of test_polygon is inside target_polygon using ray casting.
        
        Args:
            test_polygon: Polygon whose points to test
            target_polygon: Polygon to test against
            
        Returns:
            True if any point is inside
        """
        try:
            for point in test_polygon:
                if self._point_in_polygon(point, target_polygon):
                    return True
            return False
        except Exception:
            return False
    
    def _point_in_polygon(self, point: List[float], polygon: List[List[float]]) -> bool:
        """
        Check if a point is inside a polygon using ray casting algorithm.
        
        Args:
            point: [x, y] coordinates
            polygon: List of [x, y] coordinate pairs
            
        Returns:
            True if point is inside polygon
        """
        try:
            x, y = point[0], point[1]
            n = len(polygon)
            inside = False
            
            p1x, p1y = polygon[0]
            for i in range(1, n + 1):
                p2x, p2y = polygon[i % n]
                if y > min(p1y, p2y):
                    if y <= max(p1y, p2y):
                        if x <= max(p1x, p2x):
                            if p1y != p2y:
                                xinters = (y - p1y) * (p2x - p1x) / (p2y - p1y) + p1x
                            if p1x == p2x or x <= xinters:
                                inside = not inside
                p1x, p1y = p2x, p2y
            
            return inside
        except Exception:
            return False
    
    def _polygons_have_intersecting_edges(self, poly1: List[List[float]], poly2: List[List[float]]) -> bool:
        """
        Check if any edges of the two polygons intersect.
        
        Args:
            poly1: First polygon
            poly2: Second polygon
            
        Returns:
            True if any edges intersect
        """
        try:
            # Check each edge of poly1 against each edge of poly2
            for i in range(len(poly1)):
                edge1_start = poly1[i]
                edge1_end = poly1[(i + 1) % len(poly1)]
                
                for j in range(len(poly2)):
                    edge2_start = poly2[j]
                    edge2_end = poly2[(j + 1) % len(poly2)]
                    
                    if self._line_segments_intersect(edge1_start, edge1_end, edge2_start, edge2_end):
                        return True
            
            return False
        except Exception:
            return False
    
    def _line_segments_intersect(self, p1: List[float], q1: List[float], p2: List[float], q2: List[float]) -> bool:
        """
        Check if two line segments intersect.
        
        Args:
            p1, q1: First line segment endpoints
            p2, q2: Second line segment endpoints
            
        Returns:
            True if segments intersect
        """
        try:
            def orientation(p, q, r):
                """Find orientation of ordered triplet (p, q, r)."""
                val = (q[1] - p[1]) * (r[0] - q[0]) - (q[0] - p[0]) * (r[1] - q[1])
                if val == 0:
                    return 0  # collinear
                return 1 if val > 0 else 2  # clockwise or counterclockwise
            
            def on_segment(p, q, r):
                """Check if point q lies on segment pr."""
                return (q[0] <= max(p[0], r[0]) and q[0] >= min(p[0], r[0]) and
                        q[1] <= max(p[1], r[1]) and q[1] >= min(p[1], r[1]))
            
            o1 = orientation(p1, q1, p2)
            o2 = orientation(p1, q1, q2)
            o3 = orientation(p2, q2, p1)
            o4 = orientation(p2, q2, q1)
            
            # General case
            if o1 != o2 and o3 != o4:
                return True
            
            # Special cases
            if (o1 == 0 and on_segment(p1, p2, q1)) or \
               (o2 == 0 and on_segment(p1, q2, q1)) or \
               (o3 == 0 and on_segment(p2, p1, q2)) or \
               (o4 == 0 and on_segment(p2, q1, q2)):
                return True
            
            return False
        except Exception:
            return False
    
    def _simple_bounding_box_overlap(self, poly1: List[List[float]], poly2: List[List[float]]) -> bool:
        """
        Fallback simple bounding box overlap check.
        
        Args:
            poly1: First polygon
            poly2: Second polygon
            
        Returns:
            True if bounding boxes overlap
        """
        try:
            bbox1 = self._get_bounding_box(poly1)
            bbox2 = self._get_bounding_box(poly2)
            return self._bounding_boxes_overlap(bbox1, bbox2)
        except Exception:
            return False
    
    def _contains_table_patterns(self, para_text: str, table_text: str) -> bool:
        """
        Check if paragraph contains table-specific patterns.
        
        Args:
            para_text: Paragraph text
            table_text: Table text content
            
        Returns:
            True if table patterns are found
        """
        try:
            # Look for common table indicators
            table_indicators = [
                'table', 'column', 'row', 'cell', 'header',
                'total', 'sum', 'average', 'percentage', '%',
                'data', 'value', 'amount', 'count', 'number'
            ]
            
            para_lower = para_text.lower()
            
            # Check for table-specific formatting patterns
            has_numbers = any(char.isdigit() for char in para_text)
            has_percentages = '%' in para_text
            has_currency = any(symbol in para_text for symbol in ['$', '€', '£', '¥'])
            
            # Check for table indicator words
            has_table_words = any(indicator in para_lower for indicator in table_indicators)
            
            # Check for structured data patterns (comma-separated values, etc.)
            has_structured_pattern = ',' in para_text and has_numbers
            
            return (has_table_words and has_numbers) or has_percentages or has_currency or has_structured_pattern
            
        except Exception:
            return False
    
    def _check_proximity_overlap(self, poly1: List[List[float]], poly2: List[List[float]], proximity_threshold: float = 20) -> bool:
        """
        Check if two polygons are in close proximity.
        
        Args:
            poly1: First polygon
            poly2: Second polygon
            proximity_threshold: Distance threshold in pixels
            
        Returns:
            True if polygons are in proximity
        """
        try:
            if not poly1 or not poly2:
                return False
            
            bbox1 = self._get_bounding_box(poly1)
            bbox2 = self._get_bounding_box(poly2)
            
            # Calculate minimum distance between bounding boxes
            dx = max(0, max(bbox1['min_x'] - bbox2['max_x'], bbox2['min_x'] - bbox1['max_x']))
            dy = max(0, max(bbox1['min_y'] - bbox2['max_y'], bbox2['min_y'] - bbox1['max_y']))
            
            distance = (dx * dx + dy * dy) ** 0.5
            
            return distance <= proximity_threshold
            
        except Exception:
            return False
    
    def _is_table_metadata(self, text: str) -> bool:
        """
        Check if text appears to be table metadata (caption, title, etc.).
        
        Args:
            text: Text to analyze
            
        Returns:
            True if text appears to be table metadata
        """
        try:
            text_lower = text.lower().strip()
            
            # Common table metadata patterns
            metadata_patterns = [
                r'^table\s+\d+',
                r'^figure\s+\d+',
                r'source:', 
                r'note:', 
                r'caption:',
                r'continued',
                r'see table',
                r'refer to table'
            ]
            
            import re
            for pattern in metadata_patterns:
                if re.search(pattern, text_lower):
                    return True
            
            # Check for short descriptive text near tables
            if len(text.strip()) < 100 and any(word in text_lower for word in ['table', 'data', 'source', 'note']):
                return True
            
            return False
            
        except Exception:
            return False

    def _apply_header_correction(self, tables: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Apply header correction logic to fix Azure DI header misdetection.
        This fixes cases where continuation tables have their first data row misidentified as headers.
        
        Args:
            tables: List of table dictionaries from Azure Document Intelligence
            
        Returns:
            List of tables with corrected headers
        """
        corrected_tables = []
        
        for table in tables:
            corrected_table = table.copy()
            
            # Check if this looks like a continuation table with misdetected headers
            headers = table.get('headers', [])
            data = table.get('data', [])
            page_num = table.get('page_number', 1)
            
            # Heuristic: If headers look like data (all numeric or short strings), 
            # and this is not page 1, it might be a continuation table
            if page_num > 1 and headers and data:
                if self._looks_like_data_not_headers(headers):
                    logger.info(f"🔧 Correcting headers for Table on Page {page_num}")
                    logger.debug(f"   ❌ Old headers: {headers}")
                    
                    # Try to find a similar table on previous pages with proper headers
                    proper_headers = self._find_proper_headers_for_continuation(table, tables)
                    
                    if proper_headers:
                        logger.debug(f"   ✅ New headers: {proper_headers}")
                        
                        # Move the misdetected headers back to data
                        corrected_data = [headers] + data
                        
                        corrected_table['headers'] = proper_headers
                        corrected_table['data'] = corrected_data
                        corrected_table['row_count'] = len(corrected_data)
                        
                        # Add correction metadata
                        corrected_table.setdefault('metadata', {})['header_corrected'] = True
                        corrected_table['metadata']['original_headers'] = headers
            
            corrected_tables.append(corrected_table)
        
        return corrected_tables
    
    def _looks_like_data_not_headers(self, headers: List[str]) -> bool:
        """
        Heuristic to detect if 'headers' are actually data rows.
        
        Args:
            headers: List of header strings to analyze
            
        Returns:
            True if headers look like data, False if they look like proper headers
        """
        if not headers:
            return False
        
        # Check for patterns that suggest these are data, not headers
        data_indicators = 0
        
        for header in headers:
            header_str = str(header).strip()
            
            # Skip empty headers
            if not header_str:
                continue
            
            # Numeric values (like "7", "2.2")
            try:
                float(header_str)
                data_indicators += 1
                continue
            except ValueError:
                pass
            
            # Very short strings that look like codes or data
            if len(header_str) <= 2 and header_str.isdigit():
                data_indicators += 1
            
            # All caps place names (common in data, less common in headers)
            elif header_str.isupper() and len(header_str) > 3:
                data_indicators += 1
            
            # Common data patterns
            elif any(pattern in header_str.lower() for pattern in ['state', 'country', 'city', 'region']):
                if header_str.isupper():  # Likely a place name in data
                    data_indicators += 1
        
        # If more than half look like data, probably misdetected headers
        return data_indicators > len([h for h in headers if str(h).strip()]) / 2
    
    def _find_proper_headers_for_continuation(self, continuation_table: Dict[str, Any], all_tables: List[Dict[str, Any]]) -> Optional[List[str]]:
        """
        Find proper headers for a continuation table by looking at similar tables on previous pages.
        
        Args:
            continuation_table: The table that needs header correction
            all_tables: All tables to search for similar structure
            
        Returns:
            List of proper headers if found, None otherwise
        """
        continuation_page = continuation_table.get('page_number', 1)
        continuation_cols = continuation_table.get('column_count', 0)
        
        # Look for tables on previous pages with similar column count
        for table in all_tables:
            if (table.get('page_number', 1) < continuation_page and 
                table.get('column_count', 0) == continuation_cols):
                
                headers = table.get('headers', [])
                
                # Check if these headers make sense (not data-like)
                if headers and not self._looks_like_data_not_headers(headers):
                    logger.debug(f"   🔍 Found matching table on page {table.get('page_number')} with proper headers")
                    return headers
        
        logger.debug(f"   ⚠️  Could not find proper headers for continuation table on page {continuation_page}")
        return None

    def _replace_table_placeholders_with_summaries(self, text_content: str, tables: List[Dict[str, Any]], file_record: File) -> str:
        """
        Replace table placeholders in text content with AI-generated summaries.
        This adapts the himalaya_azure approach where tables become part of the narrative.
        
        Args:
            text_content: Original text content with table placeholders
            tables: List of extracted tables with summaries
            file_record: File record for context
            
        Returns:
            Enhanced text content with table summaries embedded
        """
        if not text_content or not tables:
            return text_content
        
        enhanced_content = text_content
        table_replacements = 0
        
        try:
            # Create mapping of table identifiers to summaries
            table_summaries = {}
            for i, table_data in enumerate(tables):
                page_num = table_data.get('page_number', 1)
                table_summary = table_data.get('summary', '')
                
                # If no summary available, generate a basic one from table data
                if not table_summary:
                    table_summary = self._generate_basic_table_summary(table_data)
                
                # Create various possible table identifiers that might appear in text
                possible_identifiers = [
                    f"[TABLE {i}]",
                    f"[TABLE {i+1}]",  # 1-based indexing
                    f"[Table {i}]",
                    f"[Table {i+1}]",
                    f"[TABLE page{page_num}_table{i}]",
                    f"[TABLE page{page_num}_table{i+1}]",
                    f"table_{i}",
                    f"table_{i+1}",
                    f"Table {i}",
                    f"Table {i+1}",
                    f"table{i}",
                    f"table{i+1}"
                ]
                
                for identifier in possible_identifiers:
                    table_summaries[identifier] = table_summary
            
            # Replace table placeholders with summaries
            for table_id, summary in table_summaries.items():
                if table_id in enhanced_content:
                    # Create enhanced replacement with clear formatting
                    replacement = f"\n[Table Summary: {summary}]\n"
                    enhanced_content = enhanced_content.replace(table_id, replacement)
                    table_replacements += 1
                    logger.info(f"🔄 HIMALAYA_AZURE: Replaced '{table_id}' with summary")
            
            # Also look for generic table references and enhance them
            import re
            
            # Pattern to find table references in text
            table_patterns = [
                r'\btable\s+(\d+)\b',
                r'\bTable\s+(\d+)\b',
                r'\bTABLE\s+(\d+)\b'
            ]
            
            for pattern in table_patterns:
                matches = re.finditer(pattern, enhanced_content, re.IGNORECASE)
                for match in matches:
                    table_num = int(match.group(1))
                    # Try to find corresponding table (both 0-based and 1-based)
                    table_data = None
                    if table_num - 1 < len(tables):  # 1-based to 0-based
                        table_data = tables[table_num - 1]
                    elif table_num < len(tables):  # 0-based
                        table_data = tables[table_num]
                    
                    if table_data:
                        summary = table_data.get('summary', '')
                        if not summary:
                            summary = self._generate_basic_table_summary(table_data)
                        
                        # Replace the table reference with enhanced version
                        original_ref = match.group(0)
                        enhanced_ref = f"{original_ref} [Summary: {summary}]"
                        enhanced_content = enhanced_content.replace(original_ref, enhanced_ref, 1)
                        table_replacements += 1
            
            logger.info(f"🔄 HIMALAYA_AZURE: Made {table_replacements} table summary replacements in text content")
            
            # Create additional table summary documents (like himalaya_azure)
            self._create_additional_table_documents(tables, file_record)
            
            return enhanced_content
            
        except Exception as e:
            logger.error(f"❌ Error replacing table placeholders: {str(e)}")
            return text_content  # Return original content if replacement fails

    def _generate_basic_table_summary(self, table_data: Dict[str, Any]) -> str:
        """
        Generate a basic summary for a table if none exists.
        
        Args:
            table_data: Table data dictionary
            
        Returns:
            Basic table summary
        """
        try:
            headers = table_data.get('headers', [])
            row_count = table_data.get('row_count', 0)
            column_count = table_data.get('column_count', 0)
            page_num = table_data.get('page_number', 1)
            
            if headers:
                header_preview = ', '.join(headers[:3])  # First 3 headers
                if len(headers) > 3:
                    header_preview += f", and {len(headers) - 3} more columns"
                
                return f"Table on page {page_num} with {row_count} rows and {column_count} columns. Columns include: {header_preview}."
            else:
                return f"Table on page {page_num} with {row_count} rows and {column_count} columns."
                
        except Exception as e:
            logger.error(f"Error generating basic table summary: {str(e)}")
            return "Table with structured data."

    def _create_additional_table_documents(self, tables: List[Dict[str, Any]], file_record: File):
        """
        Create additional table summary documents for enhanced search (himalaya_azure approach).
        
        Args:
            tables: List of extracted tables
            file_record: File record
        """
        try:
            for i, table_data in enumerate(tables):
                summary = table_data.get('summary', '')
                if not summary:
                    summary = self._generate_basic_table_summary(table_data)
                
                page_num = table_data.get('page_number', 1)
                
                # Create a table summary chunk
                table_summary_chunk = DocumentChunk(
                    file_id=file_record.id,
                    chunk_index=1000 + i,  # Use high index to distinguish from regular chunks
                    content=f"[Table Summary: {summary}]",
                    word_count=len(summary.split()),
                    char_count=len(summary),
                    chunk_metadata={
                        'chunk_type': 'table_summary',
                        'table_index': i,
                        'page_number': page_num,
                        'processing_method': 'himalaya_azure_adaptation',
                        'content_preview': summary[:100] + '...' if len(summary) > 100 else summary,
                        'is_table_document': True,
                        'table_row_count': table_data.get('row_count', 0),
                        'table_column_count': table_data.get('column_count', 0)
                    }
                )
                
                db.session.add(table_summary_chunk)
                db.session.flush()
                
                # Upload table summary to Azure Search
                try:
                    blob_name = file_record.blob_url.split('/')[-1] if file_record.blob_url else file_record.file_name
                    
                    azure_doc_id = self.search_service.upload_document_chunk({
                        'file_id': file_record.id,
                        'chunk_index': 1000 + i,
                        'content': f"[Table Summary: {summary}]",
                        'file_name': blob_name,
                        'original_file_name': file_record.file_name,
                        'document_type': 'table_summary',
                        'word_count': len(summary.split()),
                        'char_count': len(summary),
                        'chunk_metadata': table_summary_chunk.chunk_metadata
                    })
                    
                    table_summary_chunk.azure_search_doc_id = azure_doc_id
                    logger.info(f"🔄 HIMALAYA_AZURE: Created additional table summary document for table {i}")
                    
                except Exception as e:
                    logger.error(f"Failed to upload table summary to Azure Search: {str(e)}")
            
            db.session.commit()
            
        except Exception as e:
            logger.error(f"❌ Error creating additional table documents: {str(e)}")
            db.session.rollback()

    def _extract_image_content_with_azure_di(self, file_path: str) -> Dict[str, Any]:
        """
        Extract content from image using Azure Document Intelligence.
        
        Args:
            file_path: Path to image file
            
        Returns:
            Dictionary with extracted content
        """
        if not self.doc_intelligence_client:
            logger.error("Azure Document Intelligence client not initialized")
            return {'text': '', 'extraction_method': 'azure_document_intelligence_failed'}
        
        try:
            with open(file_path, "rb") as f:
                logger.info("Submitting document to Azure Document Intelligence (prebuilt-layout)")
                poller = self.doc_intelligence_client.begin_analyze_document(
                    "prebuilt-layout", f
                )
                
                timeout_seconds = 180
                start_time = time.time()
                
                while not poller.done() and time.time() - start_time < timeout_seconds:
                    time.sleep(5)
                
                if not poller.done():
                    raise TimeoutError(f"Document analysis timed out after {timeout_seconds} seconds")
                
                result = poller.result()
                logger.info(f"Azure DI analysis completed. Found {len(result.paragraphs) if result.paragraphs else 0} paragraphs and {len(result.tables) if result.tables else 0} tables")
                
                # Extract paragraphs with page information for better text organization
                paragraph_texts_by_page = {}
                if result.paragraphs:
                    for paragraph in result.paragraphs:
                        if paragraph.content and paragraph.content.strip():
                            page_num = paragraph.bounding_regions[0].page_number if paragraph.bounding_regions else 1
                            if page_num not in paragraph_texts_by_page:
                                paragraph_texts_by_page[page_num] = []
                            
                            # Include bounding region information for spatial analysis
                            bounding_region = None
                            if paragraph.bounding_regions:
                                region = paragraph.bounding_regions[0]
                                bounding_region = {
                                    'page_number': region.page_number,
                                    'polygon': region.polygon
                                }
                            
                            paragraph_texts_by_page[page_num].append({
                                "text_content": paragraph.content,
                                "bounding_region": bounding_region
                            })
                
                # Extract tables with enhanced processing for image-based tables (only if enabled)
                azure_tables = []
                table_text_by_page = {}  # Track table text content to avoid duplication
                
                if not TABLE_DETECTION_ENABLED:
                    logger.info("🚫 TABLE PROCESSING DISABLED - Skipping table extraction from Azure Document Intelligence")
                elif result.tables:
                    for table_idx, table in enumerate(result.tables):
                        if table.row_count == 0 or table.column_count == 0:
                            continue
                        
                        try:
                            rows = table.row_count
                            cols = table.column_count
                            
                            # Initialize empty DataFrame structure
                            df_data = [["" for _ in range(cols)] for _ in range(rows)]
                            
                            # Fill with cell data and track table text content
                            table_text_content = set()
                            for cell in table.cells:
                                if cell.content:
                                    row_idx = cell.row_index
                                    col_idx = cell.column_index
                                    if 0 <= row_idx < rows and 0 <= col_idx < cols:
                                        df_data[row_idx][col_idx] = cell.content
                                        table_text_content.add(cell.content.lower())
                            
                            # Process headers with explicit header detection
                            headers = []
                            has_explicit_headers = False
                            header_row_indices = set()
                            
                            # Check for explicit headers (Azure DI can detect column headers)
                            for cell in table.cells:
                                if hasattr(cell, 'kind') and cell.kind == 'columnHeader':
                                    header_row_indices.add(cell.row_index)
                                    has_explicit_headers = True
                            
                            if has_explicit_headers:
                                # Use EnhancedTableProcessor for robust header processing
                                from services.enhanced_table_processor import enhanced_table_processor
                                # Build raw_table as list of lists for the header rows and data
                                raw_table = df_data
                                # Use the processor to get headers and data rows
                                processed = enhanced_table_processor.process_complex_table(raw_table, page_num=1, table_idx=table_idx)
                                headers = processed['headers']
                                data_rows = processed['processed_data']
                            else:
                                # Use first row as headers if no headers provided
                                if rows > 0:
                                    headers = [str(h).strip() if h else f"Column_{i+1}" for i, h in enumerate(df_data[0])]
                                    data_rows = df_data[1:] if rows > 1 else []
                                else:
                                    headers = [f"Column_{j+1}" for j in range(cols)]
                                    data_rows = []
                            
                            # Get page number and bounding region
                            page_num = 1
                            table_region = None
                            if table.bounding_regions:
                                region = table.bounding_regions[0]
                                page_num = region.page_number
                                table_region = {
                                    'page_number': region.page_number,
                                    'polygon': region.polygon
                                }
                            
                            # Track table text content by page
                            if page_num not in table_text_by_page:
                                table_text_by_page[page_num] = set()
                            table_text_by_page[page_num].update(table_text_content)
                            
                            # 🚀 UNIFIED SEMANTIC ANALYSIS: Use centralized semantic analyzer
                            try:
                                from services.table_semantic_analyzer import table_semantic_analyzer
                                
                                # Create DataFrame for analysis
                                temp_df = pd.DataFrame(data_rows, columns=headers)
                                
                                # Prepare source info for PDF table
                                source_info = {
                                    'source_type': 'pdf',
                                    'file_name': 'unknown.pdf',  # File name not available in this context
                                    'page_number': page_num,
                                    'table_index': table_idx,
                                    'extraction_method': 'azure_document_intelligence',
                                    'table_type': 'image_based' if not has_explicit_headers else 'structured'
                                }
                                
                                # Generate comprehensive semantic summary
                                semantic_analysis = table_semantic_analyzer.analyze_table(
                                    df=temp_df,
                                    table_name=f"table_{table_idx}_page_{page_num}",
                                    source_info=source_info
                                )
                                
                                summary_result = {
                                    'summary': semantic_analysis.get('summary', f"Table {table_idx + 1} from page {page_num}"),
                                    'potential_analyses': semantic_analysis.get('potential_analyses', ['basic analysis'])
                                }
                                
                                logger.info(f"🔍 PDF UNIFIED: Generated {semantic_analysis.get('analysis_quality', 'unknown')} analysis for PDF table with {semantic_analysis.get('analysis_count', 0)} analyses")
                                
                            except Exception as semantic_error:
                                logger.error(f"❌ PDF UNIFIED: Error in semantic analysis: {str(semantic_error)}")
                                # Fallback to basic summary
                                summary_result = {
                                    'summary': f"Table {table_idx + 1} from page {page_num} with {len(headers)} columns",
                                    'potential_analyses': ['basic statistics', 'data analysis']
                                }
                            
                            # Create structured table data
                            structured_table = {
                                'page_number': page_num,
                                'table_index': table_idx,
                                'headers': headers,
                                'data': [headers] + data_rows,  # Include headers in data for compatibility
                                'row_count': len(data_rows) + 1,  # +1 for header row
                                'column_count': len(headers),
                                'summary': summary_result['summary'],
                                'potential_analyses': summary_result['potential_analyses'],
                                'metadata': {
                                    'extraction_method': 'azure_document_intelligence',
                                    'has_explicit_headers': has_explicit_headers,
                                    'page_number': page_num,
                                    'original_row_count': rows,
                                    'original_col_count': cols,
                                    'bounding_region': table_region,
                                    'table_type': 'image_based' if not has_explicit_headers else 'structured'
                                }
                            }
                            azure_tables.append(structured_table)
                            logger.info(f"Extracted table {table_idx + 1} from page {page_num}: {len(data_rows)} rows x {len(headers)} columns")
                            
                        except Exception as e:
                            logger.error(f"Error processing table {table_idx+1}: {str(e)}")
                
                # Helper function to check if text overlaps with table content
                def text_overlaps_with_table(text_content, page_num):
                    if page_num not in table_text_by_page:
                        return False
                    text_lower = text_content.lower()
                    for table_text in table_text_by_page[page_num]:
                        # Enhanced content-based overlap detection
                        if self._calculate_text_overlap_percentage(text_lower, table_text) > 0.3:
                            return True
                    return False
                
                # Enhanced function to check if polygons overlap spatially with sophisticated detection
                def polygons_overlap(poly1, poly2, overlap_threshold=0.1):
                    """
                    Enhanced polygon overlap detection with multiple strategies:
                    1. Bounding box overlap
                    2. Point-in-polygon testing
                    3. Area-based overlap calculation
                    4. Edge intersection detection
                    """
                    if not poly1 or not poly2 or not isinstance(poly1, list) or not isinstance(poly2, list):
                        return False
                    
                    try:
                        # Strategy 1: Bounding box overlap (fast initial check)
                        bbox1 = self._get_bounding_box(poly1)
                        bbox2 = self._get_bounding_box(poly2)
                        
                        if not self._bounding_boxes_overlap(bbox1, bbox2):
                            return False
                        
                        # Strategy 2: Calculate actual polygon overlap area
                        overlap_area = self._calculate_polygon_overlap_area(poly1, poly2)
                        poly1_area = self._calculate_polygon_area(poly1)
                        poly2_area = self._calculate_polygon_area(poly2)
                        
                        if poly1_area == 0 or poly2_area == 0:
                            return False
                        
                        # Calculate overlap percentage relative to smaller polygon
                        min_area = min(poly1_area, poly2_area)
                        overlap_percentage = overlap_area / min_area if min_area > 0 else 0
                        
                        # Strategy 3: Point-in-polygon testing for edge cases
                        if overlap_percentage < overlap_threshold:
                            # Check if any vertices of one polygon are inside the other
                            if (self._any_point_in_polygon(poly1, poly2) or 
                                self._any_point_in_polygon(poly2, poly1)):
                                overlap_percentage = max(overlap_percentage, overlap_threshold)
                        
                        # Strategy 4: Edge intersection detection
                        if overlap_percentage < overlap_threshold:
                            if self._polygons_have_intersecting_edges(poly1, poly2):
                                overlap_percentage = max(overlap_percentage, overlap_threshold)
                        
                        return overlap_percentage >= overlap_threshold
                        
                    except Exception as e:
                        logger.debug(f"Polygon overlap calculation failed: {str(e)}")
                        # Fallback to simple bounding box check
                        return self._simple_bounding_box_overlap(poly1, poly2)
                
                # Enhanced spatial overlap detection with content verification
                def enhanced_spatial_overlap_check(para, table, page_num):
                    """
                    Comprehensive overlap check combining spatial and content analysis
                    """
                    # Primary: Spatial overlap check
                    spatial_overlap = False
                    para_region = para.get('bounding_region')
                    table_region = table['metadata'].get('bounding_region')
                    
                    if para_region and para_region.get('polygon') and table_region and table_region.get('polygon'):
                        spatial_overlap = polygons_overlap(
                            para_region['polygon'], 
                            table_region['polygon'],
                            overlap_threshold=0.15  # More sensitive threshold
                        )
                    
                    # Secondary: Content-based verification
                    content_overlap = False
                    if spatial_overlap:
                        # Verify overlap with actual content analysis
                        para_text = para['text_content'].lower().strip()
                        table_content = table.get('content', '')
                        
                        if isinstance(table_content, list):
                            # Flatten table content for comparison
                            table_text = ' '.join([
                                ' '.join([str(cell) for cell in row if cell]) 
                                for row in table_content if row
                            ]).lower()
                        else:
                            table_text = str(table_content).lower()
                        
                        # Check for substantial content overlap
                        content_overlap = self._calculate_text_overlap_percentage(para_text, table_text) > 0.25
                        
                        # Additional check: Look for table-specific patterns in paragraph
                        if not content_overlap:
                            content_overlap = self._contains_table_patterns(para_text, table_text)
                    
                    # Tertiary: Proximity-based check for near-table content
                    proximity_overlap = False
                    if not spatial_overlap and para_region and table_region:
                        proximity_overlap = self._check_proximity_overlap(
                            para_region.get('polygon'), 
                            table_region.get('polygon'),
                            proximity_threshold=20  # pixels
                        )
                    
                    return spatial_overlap and content_overlap, proximity_overlap
                
                # Organize text by page, filtering out content that overlaps with tables
                organized_text_by_page = {}
                overlap_stats = {'spatial_overlaps': 0, 'content_overlaps': 0, 'proximity_overlaps': 0}
                
                for page_num, paragraphs in paragraph_texts_by_page.items():
                    page_text_parts = []
                    
                    for para in paragraphs:
                        # Enhanced overlap detection
                        skip_paragraph = False
                        skip_reason = None
                        
                        # Check content-based overlap first (faster)
                        if text_overlaps_with_table(para['text_content'], page_num):
                            skip_paragraph = True
                            skip_reason = "content_overlap"
                            overlap_stats['content_overlaps'] += 1
                        
                        # Check spatial overlap with enhanced detection
                        if not skip_paragraph:
                            for table in azure_tables:
                                if table['page_number'] == page_num:
                                    spatial_content_overlap, proximity_overlap = enhanced_spatial_overlap_check(
                                        para, table, page_num
                                    )
                                    
                                    if spatial_content_overlap:
                                        skip_paragraph = True
                                        skip_reason = "spatial_content_overlap"
                                        overlap_stats['spatial_overlaps'] += 1
                                        break
                                    elif proximity_overlap:
                                        # For proximity overlaps, check if it's table metadata/caption
                                        if self._is_table_metadata(para['text_content']):
                                            skip_paragraph = True
                                            skip_reason = "table_metadata"
                                            overlap_stats['proximity_overlaps'] += 1
                                            break
                        
                        if not skip_paragraph:
                            page_text_parts.append(para['text_content'])
                        else:
                            logger.debug(f"Skipped paragraph on page {page_num} due to {skip_reason}: {para['text_content'][:100]}...")
                    
                    # Add table placeholders in appropriate positions with enhanced metadata
                    table_placeholders = []
                    for table in azure_tables:
                        if table['page_number'] == page_num:
                            table_summary = table.get('summary', f"Table {table['table_index'] + 1}")
                            table_info = f"[TABLE {table['table_index'] + 1} - {table_summary}"
                            
                            # Add table dimensions and type info
                            if 'metadata' in table:
                                rows = table['metadata'].get('row_count', 0)
                                cols = table['metadata'].get('column_count', 0)
                                if rows and cols:
                                    table_info += f" ({rows}x{cols})"
                            
                            table_info += "]"
                            table_placeholders.append(table_info)
                    
                    # Combine text and table placeholders
                    if page_text_parts or table_placeholders:
                        page_content = []
                        if page_text_parts:
                            page_content.extend(page_text_parts)
                        if table_placeholders:
                            page_content.extend(table_placeholders)
                        
                        organized_text_by_page[page_num] = f"[Page {page_num}] " + " ".join(page_content)
                
                # Log overlap detection statistics
                logger.info(f"Text-table overlap detection: {overlap_stats['spatial_overlaps']} spatial, "
                           f"{overlap_stats['content_overlaps']} content, {overlap_stats['proximity_overlaps']} proximity overlaps filtered")
                
                # Combine all text
                all_text = []
                for page_num in sorted(organized_text_by_page.keys()):
                    all_text.append(organized_text_by_page[page_num])
                
                # Estimate page count
                page_count = 1
                if result.tables:
                    max_page = max([t.bounding_regions[0].page_number for t in result.tables if t.bounding_regions], default=1)
                    page_count = max(page_count, max_page)
                if result.paragraphs:
                    max_page = max([p.bounding_regions[0].page_number for p in result.paragraphs if p.bounding_regions], default=1)
                    page_count = max(page_count, max_page)
                
                final_text = '\n\n'.join(all_text)
                logger.info(f"Azure DI extracted {len(final_text)} characters of text and {len(azure_tables)} tables from {page_count} pages")
                
                return {
                    'text': final_text,
                    'tables': azure_tables,
                    'page_count': page_count,
                    'extraction_method': 'azure_document_intelligence'
                }
                
        except Exception as e:
            logger.error(f"Azure Document Intelligence extraction failed: {str(e)}")
            return {'text': '', 'tables': [], 'page_count': 0, 'extraction_method': 'azure_document_intelligence_failed'}
    
    def _extract_image_content_with_tesseract(self, file_path: str, file_record: File) -> Dict[str, Any]:
        """
        Extract content from image using Tesseract OCR.
        
        Args:
            file_path: Path to image file
            file_record: File database record
            
        Returns:
            Dictionary with extracted content
        """
        try:
            # Use Tesseract OCR to extract text from the image
            import pytesseract
            from PIL import Image
            
            # Load the image
            image = Image.open(file_path)
            
            # Use Tesseract OCR to extract text
            text = pytesseract.image_to_string(image)
            
            return {
                'text': text,
                'extraction_method': 'tesseract'
            }
            
        except Exception as e:
            logger.error(f"Tesseract extraction failed: {str(e)}")
            return {'text': '', 'extraction_method': 'tesseract_failed'}
    
    def _create_image_description(self, file_path: str, extracted_content: Dict[str, Any]) -> str:
        """
        Create a descriptive text for the image based on extracted content.
        
        Args:
            file_path: Path to image file
            extracted_content: Dictionary with extracted content
            
        Returns:
            Descriptive text for the image
        """
        try:
            # Use a simple heuristic to generate a description
            description = f"Image content: {extracted_content['text'][:100]}..."
            return description
            
        except Exception as e:
            logger.error(f"Failed to create image description: {str(e)}")
            return "Image content not available."

# Create a singleton instance
enhanced_processing_service = EnhancedProcessingService() 