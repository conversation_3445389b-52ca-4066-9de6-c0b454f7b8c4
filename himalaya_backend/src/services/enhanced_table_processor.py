#!/usr/bin/env python3
"""
Enhanced Table Processor for handling complex table structures.
This module provides advanced table processing capabilities for PDFs with:
- Multi-row headers
- Merged cells
- Complex table structures
- Better column alignment
"""

import pandas as pd
import logging
from typing import List, Dict, Any, Optional, Tuple

logger = logging.getLogger(__name__)

class EnhancedTableProcessor:
    """Enhanced table processor for complex table structures."""
    
    def __init__(self):
        """Initialize the enhanced table processor."""
        self.logger = logging.getLogger(__name__)
    
    def process_complex_table(self, raw_table: List[List], page_num: int, table_idx: int) -> Dict[str, Any]:
        """
        Process a complex table with multi-row headers and merged cells.
        
        Args:
            raw_table: Raw table data as list of lists
            page_num: Page number
            table_idx: Table index on the page
            
        Returns:
            Processed table dictionary
        """
        if not raw_table or len(raw_table) < 2:
            return self._create_empty_table_result(page_num, table_idx)
        
        try:
            # Detect table structure
            structure = self._analyze_table_structure(raw_table)
            
            # Process headers based on structure
            headers = self._process_complex_headers(raw_table, structure)
            
            # Extract data rows
            data_rows = self._extract_data_rows(raw_table, structure)
            
            # Create DataFrame
            df = pd.DataFrame(data_rows, columns=headers)
            df = df.fillna('')  # Replace None with empty string
            
            # Generate summary
            summary = self._generate_table_summary(df, headers, page_num, table_idx)
            
            return {
                'page_number': page_num,
                'table_index': table_idx,
                'headers': headers,
                'data': raw_table,
                'processed_data': data_rows,
                'dataframe': df,
                'row_count': len(raw_table),
                'column_count': len(headers),
                'data_row_count': len(data_rows),
                'summary': summary,
                'structure_type': structure['type'],
                'header_rows': structure['header_rows'],
                'metadata': {
                    'extraction_method': 'enhanced_complex_table_processor',
                    'page_number': page_num,
                    'structure_detected': structure['type']
                }
            }
            
        except Exception as e:
            logger.error(f"Error processing complex table: {str(e)}")
            return self._create_fallback_table_result(raw_table, page_num, table_idx)
    
    def _analyze_table_structure(self, raw_table: List[List]) -> Dict[str, Any]:
        """
        Analyze the table structure to detect header patterns.
        
        Args:
            raw_table: Raw table data
            
        Returns:
            Structure analysis dictionary
        """
        if not raw_table:
            return {'type': 'empty', 'header_rows': 0}
        
        # Check for multi-row header pattern
        # Pattern 1: First row has some headers, second row has sub-headers
        if len(raw_table) >= 2:
            first_row = raw_table[0]
            second_row = raw_table[1]
            
            # Count non-None values in each row
            first_row_values = sum(1 for cell in first_row if cell is not None and str(cell).strip())
            second_row_values = sum(1 for cell in second_row if cell is not None and str(cell).strip())
            
            # Check if first row has fewer values than columns (indicating merged headers)
            if first_row_values < len(first_row) and second_row_values > 0:
                # Check if second row has values in positions where first row is None
                has_complementary_headers = False
                for i, (first_cell, second_cell) in enumerate(zip(first_row, second_row)):
                    if first_cell is None and second_cell is not None:
                        has_complementary_headers = True
                        break
                
                if has_complementary_headers:
                    return {
                        'type': 'multi_row_header',
                        'header_rows': 2,
                        'merged_header_pattern': True
                    }
        
        # Pattern 2: Simple single-row header
        if len(raw_table) >= 1:
            first_row = raw_table[0]
            first_row_values = sum(1 for cell in first_row if cell is not None and str(cell).strip())
            
            if first_row_values > 0:
                return {
                    'type': 'single_row_header',
                    'header_rows': 1,
                    'merged_header_pattern': False
                }
        
        return {'type': 'no_header', 'header_rows': 0}
    
    def _process_complex_headers(self, raw_table: List[List], structure: Dict[str, Any]) -> List[str]:
        """
        Process complex headers based on the detected structure.
        
        Args:
            raw_table: Raw table data
            structure: Structure analysis
            
        Returns:
            List of processed header names
        """
        if structure['type'] == 'multi_row_header':
            return self._process_multi_row_headers(raw_table)
        elif structure['type'] == 'single_row_header':
            return self._process_single_row_headers(raw_table[0])
        else:
            # Generate default column names
            col_count = len(raw_table[0]) if raw_table else 0
            return [f"Column_{i+1}" for i in range(col_count)]
    
    def _process_multi_row_headers(self, raw_table: List[List]) -> List[str]:
        """
        Process multi-row headers using Azure Document Intelligence style concatenation.
        This exactly replicates the Azure logic where all header row content for each column
        is concatenated with spaces, including handling spanning headers (merged cells).
        
        Args:
            raw_table: Raw table data
            
        Returns:
            List of processed header names
        """
        if len(raw_table) < 2:
            return []
        
        # Assume first 2 rows are headers (can be extended to detect more header rows)
        header_row_indices = [0, 1]
        cols = len(raw_table[0]) if raw_table else 0
        
        # First, identify spanning headers (headers that span multiple columns)
        spanning_headers = {}  # {start_col: (header_text, end_col)}
        
        first_row = raw_table[0]
        for col_idx in range(cols):
            cell_content = first_row[col_idx]
            if cell_content is not None and str(cell_content).strip():
                clean_content = str(cell_content).replace('\n', ' ').strip()
                # Find the span of this header (how many columns it covers)
                span_end = col_idx
                for next_col in range(col_idx + 1, cols):
                    if next_col < len(first_row) and first_row[next_col] is None:
                        span_end = next_col
                    else:
                        break
                spanning_headers[col_idx] = (clean_content, span_end)
        
        # Azure-style header processing: concatenate all header text for each column
        combined_headers = []
        for col_idx in range(cols):
            header_text = ""
            
            # Check if this column is covered by a spanning header
            spanning_header_text = None
            for start_col, (span_text, end_col) in spanning_headers.items():
                if start_col <= col_idx <= end_col:
                    spanning_header_text = span_text
                    break
            
            # Add spanning header text if it exists
            if spanning_header_text:
                header_text += spanning_header_text + " "
            
            # Add content from other header rows
            for row_idx in header_row_indices:
                if row_idx < len(raw_table) and col_idx < len(raw_table[row_idx]):
                    cell_content = raw_table[row_idx][col_idx]
                    if cell_content is not None and str(cell_content).strip():
                        clean_content = str(cell_content).replace('\n', ' ').strip()
                        # Don't duplicate the spanning header text
                        if clean_content != spanning_header_text:
                            header_text += clean_content + " "
            
            # Clean up the final header text
            clean_header = header_text.strip()
            if not clean_header:
                clean_header = f"Column_{col_idx + 1}"
            combined_headers.append(clean_header)
        
        return combined_headers
    
    def _is_category_header(self, header: str) -> bool:
        """
        Check if a header is a category header (like "Percent Fuel Savings").
        
        Args:
            header: Header text
            
        Returns:
            True if it's a category header
        """
        category_indicators = [
            'percent', 'percentage', 'savings', 'fuel', 'analysis', 'summary',
            'total', 'average', 'statistics', 'metrics', 'data', 'information'
        ]
        
        header_lower = header.lower()
        return any(indicator in header_lower for indicator in category_indicators)
    
    def _is_likely_sub_header(self, header: str) -> bool:
        """
        Check if a header is likely a sub-header that should be combined with a category.
        
        Args:
            header: Header text
            
        Returns:
            True if it's likely a sub-header
        """
        sub_header_indicators = [
            'improved', 'decreased', 'eliminate', 'increased', 'reduced',
            'speed', 'accel', 'acceleration', 'stops', 'idle', 'time',
            'rate', 'level', 'factor', 'method', 'type', 'mode'
        ]
        
        header_lower = header.lower()
        return any(indicator in header_lower for indicator in sub_header_indicators)
    
    def _process_single_row_headers(self, header_row: List) -> List[str]:
        """
        Process single-row headers.
        
        Args:
            header_row: Header row data
            
        Returns:
            List of processed header names
        """
        headers = []
        for i, cell in enumerate(header_row):
            if cell is not None and str(cell).strip():
                headers.append(str(cell).replace('\n', ' ').strip())
            else:
                headers.append(f"Column_{i + 1}")
        return headers
    
    def _extract_data_rows(self, raw_table: List[List], structure: Dict[str, Any]) -> List[List]:
        """
        Extract data rows based on the table structure.
        
        Args:
            raw_table: Raw table data
            structure: Structure analysis
            
        Returns:
            List of data rows
        """
        header_rows = structure.get('header_rows', 1)
        
        if len(raw_table) <= header_rows:
            return []
        
        return raw_table[header_rows:]
    
    def _generate_table_summary(self, df: pd.DataFrame, headers: List[str], page_num: int, table_idx: int) -> str:
        """
        Generate a summary for the processed table.
        
        Args:
            df: Processed DataFrame
            headers: Column headers
            page_num: Page number
            table_idx: Table index
            
        Returns:
            Table summary string
        """
        if df.empty:
            return f"Empty table {table_idx + 1} on page {page_num}"
        
        # Analyze the table content
        summary_parts = [
            f"Table {table_idx + 1} from page {page_num}",
            f"contains {len(df)} data rows and {len(headers)} columns"
        ]
        
        # Add column information
        if headers:
            summary_parts.append(f"with columns: {', '.join(headers[:3])}")
            if len(headers) > 3:
                summary_parts.append(f"and {len(headers) - 3} more")
        
        # Add data type analysis
        numeric_cols = []
        percentage_cols = []
        text_cols = []
        
        for col in headers:
            if df[col].dtype in ['int64', 'float64']:
                numeric_cols.append(col)
            elif any('%' in str(val) for val in df[col].dropna()):
                percentage_cols.append(col)
            else:
                text_cols.append(col)
        
        if percentage_cols:
            summary_parts.append(f"The table includes percentage data in columns: {', '.join(percentage_cols)}")
        
        if numeric_cols:
            summary_parts.append(f"and numeric data in: {', '.join(numeric_cols)}")
        
        return ". ".join(summary_parts) + "."
    
    def _create_empty_table_result(self, page_num: int, table_idx: int) -> Dict[str, Any]:
        """Create result for empty table."""
        return {
            'page_number': page_num,
            'table_index': table_idx,
            'headers': [],
            'data': [],
            'processed_data': [],
            'dataframe': pd.DataFrame(),
            'row_count': 0,
            'column_count': 0,
            'data_row_count': 0,
            'summary': f"Empty table {table_idx + 1} on page {page_num}",
            'structure_type': 'empty',
            'header_rows': 0,
            'metadata': {
                'extraction_method': 'enhanced_complex_table_processor',
                'page_number': page_num,
                'structure_detected': 'empty'
            }
        }
    
    def _create_fallback_table_result(self, raw_table: List[List], page_num: int, table_idx: int) -> Dict[str, Any]:
        """Create fallback result when processing fails."""
        headers = [f"Column_{i+1}" for i in range(len(raw_table[0]) if raw_table else 0)]
        
        return {
            'page_number': page_num,
            'table_index': table_idx,
            'headers': headers,
            'data': raw_table,
            'processed_data': raw_table[1:] if len(raw_table) > 1 else [],
            'dataframe': pd.DataFrame(raw_table[1:] if len(raw_table) > 1 else [], columns=headers),
            'row_count': len(raw_table),
            'column_count': len(headers),
            'data_row_count': max(0, len(raw_table) - 1),
            'summary': f"Table {table_idx + 1} from page {page_num} (fallback processing)",
            'structure_type': 'fallback',
            'header_rows': 1,
            'metadata': {
                'extraction_method': 'enhanced_complex_table_processor_fallback',
                'page_number': page_num,
                'structure_detected': 'fallback'
            }
        }

# Global instance
enhanced_table_processor = EnhancedTableProcessor() 