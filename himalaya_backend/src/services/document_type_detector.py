"""
Document Type Detection Service

This service handles automatic detection of document types based on file extensions,
MIME types, and content analysis. Based on the Azure system's document_input_agent.py
but adapted for the Flask system architecture.
"""

import os
import mimetypes
import logging
from typing import Dict, Optional, List
from pathlib import Path

from config.settings import SUPPORTED_DOCUMENT_TYPES

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DocumentTypeDetector:
    """
    Service for detecting document types and routing to appropriate processors.
    """
    
    def __init__(self):
        """Initialize the document type detector."""
        self.supported_types = SUPPORTED_DOCUMENT_TYPES
        logger.info("Document Type Detector initialized")
    
    def detect_type(self, file_path: str, file_name: str = None, mime_type: str = None) -> Dict[str, str]:
        """
        Detect the document type based on file extension and MIME type.
        
        Args:
            file_path: Path to the file
            file_name: Original filename (optional)
            mime_type: MIME type of the file (optional)
            
        Returns:
            Dictionary with document type information
        """
        if file_name is None:
            file_name = os.path.basename(file_path)
            
        if mime_type is None:
            mime_type, _ = mimetypes.guess_type(file_name)
            mime_type = mime_type or "application/octet-stream"
        
        # Get file extension
        file_ext = os.path.splitext(file_name)[1].lower()
        
        # Determine document type based on extension first
        document_type = self._detect_by_extension(file_ext)
        
        # If not detected by extension, try MIME type
        if document_type == 'unknown':
            document_type = self._detect_by_mime_type(mime_type)
        
        # Get processing agent for this type
        processing_agent = self._get_processing_agent(document_type)
        
        result = {
            'document_type': document_type,
            'file_extension': file_ext,
            'mime_type': mime_type,
            'processing_agent': processing_agent,
            'supported': document_type != 'unknown',
            'file_name': file_name
        }
        
        logger.info(f"Detected document type: {document_type} for file: {file_name}")
        return result
    
    def _detect_by_extension(self, file_ext: str) -> str:
        """
        Detect document type by file extension.
        
        Args:
            file_ext: File extension (with dot)
            
        Returns:
            Document type string
        """
        for doc_type, extensions in self.supported_types.items():
            if file_ext in extensions:
                return doc_type
        return 'unknown'
    
    def _detect_by_mime_type(self, mime_type: str) -> str:
        """
        Detect document type by MIME type as fallback.
        
        Args:
            mime_type: MIME type string
            
        Returns:
            Document type string
        """
        mime_mappings = {
            # PDF
            'application/pdf': 'pdf',
            
            # Excel/CSV
            'application/vnd.ms-excel': 'excel',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': 'excel',
            'text/csv': 'excel',
            
            # Word
            'application/msword': 'word',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'word',
            
            # PowerPoint
            'application/vnd.ms-powerpoint': 'powerpoint',
            'application/vnd.openxmlformats-officedocument.presentationml.presentation': 'powerpoint',
            
            # Text
            'text/plain': 'text',
            'text/html': 'text',
            'text/markdown': 'text',
            
            # Images
            'image/jpeg': 'image',
            'image/jpg': 'image',
            'image/png': 'image',
            'image/gif': 'image',
            'image/bmp': 'image',
            'image/tiff': 'image',
            'image/webp': 'image'
        }
        
        # Check exact match first
        if mime_type in mime_mappings:
            return mime_mappings[mime_type]
        
        # Check partial matches
        if mime_type.startswith('image/'):
            return 'image'
        elif mime_type.startswith('text/'):
            return 'text'
        elif 'spreadsheet' in mime_type or 'excel' in mime_type:
            return 'excel'
        elif 'word' in mime_type or 'document' in mime_type:
            return 'word'
        elif 'presentation' in mime_type or 'powerpoint' in mime_type:
            return 'powerpoint'
        
        return 'unknown'
    
    def _get_processing_agent(self, document_type: str) -> str:
        """
        Get the appropriate processing agent for the document type.
        
        Args:
            document_type: Detected document type
            
        Returns:
            Processing agent name
        """
        agent_mappings = {
            'pdf': 'pdf_processor',
            'excel': 'excel_processor',
            'word': 'text_processor',
            'powerpoint': 'text_processor',
            'text': 'text_processor',
            'image': 'image_processor',
            'unknown': 'basic_processor'
        }
        
        return agent_mappings.get(document_type, 'basic_processor')
    
    def is_supported(self, file_path: str, file_name: str = None, mime_type: str = None) -> bool:
        """
        Check if the document type is supported for enhanced processing.
        
        Args:
            file_path: Path to the file
            file_name: Original filename (optional)
            mime_type: MIME type of the file (optional)
            
        Returns:
            True if supported, False otherwise
        """
        detection_result = self.detect_type(file_path, file_name, mime_type)
        return detection_result['supported']
    
    def get_supported_extensions(self) -> List[str]:
        """
        Get list of all supported file extensions.
        
        Returns:
            List of supported file extensions
        """
        extensions = []
        for doc_type, exts in self.supported_types.items():
            extensions.extend(exts)
        return sorted(extensions)
    
    def get_supported_types(self) -> Dict[str, List[str]]:
        """
        Get dictionary of supported document types and their extensions.
        
        Returns:
            Dictionary mapping document types to extensions
        """
        return self.supported_types.copy()

# Create a singleton instance
document_type_detector = DocumentTypeDetector() 