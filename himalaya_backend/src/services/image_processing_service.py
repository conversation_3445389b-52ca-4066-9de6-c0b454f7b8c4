"""
Enhanced Image Processing Service

This service handles comprehensive image processing with OCR capabilities
for extracting text from images and converting them to searchable content.
"""

import logging
import os
import tempfile
from typing import Dict, List, Any, Optional
from pathlib import Path
import base64
from PIL import Image
import io

# OCR imports
try:
    import pytesseract
    TESSERACT_AVAILABLE = True
except ImportError:
    TESSERACT_AVAILABLE = False
    logger.warning("Tesseract OCR not available. Install pytesseract for OCR functionality.")

from models.models import db, DocumentChunk
from services.azure_search_service import azure_search_service

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ImageProcessingService:
    """
    Comprehensive image processing service with OCR capabilities.
    """
    
    def __init__(self):
        """Initialize the image processing service."""
        logger.info("Image Processing Service initialized")
        self.supported_formats = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff', '.webp']
    
    def process_image_file(self, file_path: str, file_record, processing_metadata) -> Dict[str, Any]:
        """
        Process image file with OCR text extraction.
        
        Args:
            file_path: Path to image file
            file_record: File database record
            processing_metadata: Processing metadata record
            
        Returns:
            Processing result dictionary
        """
        logger.info(f"Processing image file: {file_record.file_name}")
        
        try:
            # Validate image format
            file_ext = os.path.splitext(file_path)[1].lower()
            if file_ext not in self.supported_formats:
                raise ValueError(f"Unsupported image format: {file_ext}")
            
            # Extract text using OCR
            extracted_text = self._extract_text_from_image(file_path)
            
            # Get image metadata
            image_metadata = self._get_image_metadata(file_path)
            
            # Process extracted content
            return self._process_image_content(
                extracted_text, image_metadata, file_record, processing_metadata
            )
            
        except Exception as e:
            logger.error(f"Error processing image file {file_record.file_name}: {str(e)}")
            return self._create_error_result(str(e))
    
    def _extract_text_from_image(self, file_path: str) -> str:
        """
        Extract text from image using OCR.
        
        Args:
            file_path: Path to image file
            
        Returns:
            Extracted text content
        """
        try:
            if not TESSERACT_AVAILABLE:
                logger.warning("Tesseract OCR not available. Returning empty text.")
                return ""
            
            # Open and preprocess image
            image = Image.open(file_path)
            
            # Convert to RGB if necessary
            if image.mode != 'RGB':
                image = image.convert('RGB')
            
            # Enhance image for better OCR results
            enhanced_image = self._enhance_image_for_ocr(image)
            
            # Extract text using Tesseract
            extracted_text = pytesseract.image_to_string(
                enhanced_image,
                config='--psm 6 -c tessedit_char_whitelist=0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz .,!?;:-()[]{}'
            )
            
            # Clean extracted text
            cleaned_text = self._clean_extracted_text(extracted_text)
            
            logger.info(f"OCR extracted {len(cleaned_text)} characters from image")
            return cleaned_text
            
        except Exception as e:
            logger.error(f"Error extracting text from image: {str(e)}")
            return ""
    
    def _enhance_image_for_ocr(self, image: Image.Image) -> Image.Image:
        """
        Enhance image for better OCR results.
        
        Args:
            image: PIL Image object
            
        Returns:
            Enhanced PIL Image object
        """
        try:
            # Resize image if too small (OCR works better on larger images)
            width, height = image.size
            if width < 300 or height < 300:
                scale_factor = max(300 / width, 300 / height)
                new_width = int(width * scale_factor)
                new_height = int(height * scale_factor)
                image = image.resize((new_width, new_height), Image.Resampling.LANCZOS)
            
            # Convert to grayscale for better OCR
            if image.mode != 'L':
                image = image.convert('L')
            
            # Enhance contrast (simple approach)
            from PIL import ImageEnhance
            enhancer = ImageEnhance.Contrast(image)
            image = enhancer.enhance(1.5)
            
            return image
            
        except Exception as e:
            logger.error(f"Error enhancing image: {str(e)}")
            return image  # Return original if enhancement fails
    
    def _clean_extracted_text(self, text: str) -> str:
        """
        Clean and normalize extracted OCR text.
        
        Args:
            text: Raw OCR text
            
        Returns:
            Cleaned text
        """
        if not text:
            return ""
        
        # Remove excessive whitespace
        lines = [line.strip() for line in text.split('\n') if line.strip()]
        
        # Filter out very short lines (likely OCR noise)
        meaningful_lines = [line for line in lines if len(line) > 2]
        
        # Join lines with proper spacing
        cleaned_text = '\n'.join(meaningful_lines)
        
        # Remove excessive spaces
        import re
        cleaned_text = re.sub(r'\s+', ' ', cleaned_text)
        
        return cleaned_text.strip()
    
    def _get_image_metadata(self, file_path: str) -> Dict[str, Any]:
        """
        Extract metadata from image file.
        
        Args:
            file_path: Path to image file
            
        Returns:
            Image metadata dictionary
        """
        try:
            image = Image.open(file_path)
            
            # Basic metadata
            metadata = {
                'format': image.format,
                'mode': image.mode,
                'size': image.size,
                'width': image.width,
                'height': image.height,
                'file_size': os.path.getsize(file_path)
            }
            
            # EXIF data if available
            if hasattr(image, '_getexif') and image._getexif():
                exif_data = image._getexif()
                if exif_data:
                    metadata['exif'] = {k: str(v) for k, v in exif_data.items() if isinstance(v, (str, int, float))}
            
            return metadata
            
        except Exception as e:
            logger.error(f"Error extracting image metadata: {str(e)}")
            return {
                'format': 'unknown',
                'mode': 'unknown',
                'size': (0, 0),
                'width': 0,
                'height': 0,
                'file_size': os.path.getsize(file_path) if os.path.exists(file_path) else 0
            }
    
    def _process_image_content(self, extracted_text: str, image_metadata: Dict[str, Any], 
                              file_record, processing_metadata) -> Dict[str, Any]:
        """
        Process extracted image content and store in database.
        
        Args:
            extracted_text: Text extracted from image
            image_metadata: Image metadata
            file_record: File database record
            processing_metadata: Processing metadata record
            
        Returns:
            Processing result dictionary
        """
        try:
            # Create text chunks if we have meaningful text
            chunks = []
            if extracted_text and len(extracted_text.strip()) >= 50:  # Minimum meaningful text
                chunks = self._create_text_chunks(extracted_text, file_record, processing_metadata)
            
            # Calculate statistics
            word_count = len(extracted_text.split()) if extracted_text else 0
            char_count = len(extracted_text) if extracted_text else 0
            
            # Determine confidence and quality scores
            confidence_score = 0.8 if word_count > 10 else 0.5 if word_count > 0 else 0.2
            quality_score = 0.9 if word_count > 50 else 0.7 if word_count > 10 else 0.3
            
            # Create image description for search
            image_description = self._create_image_description(image_metadata, extracted_text)
            
            # Store image description as a chunk if no text was extracted
            if not chunks and image_description:
                description_chunks = self._create_text_chunks(
                    image_description, file_record, processing_metadata
                )
                chunks.extend(description_chunks)
            
            logger.info(f"Image processing completed: {len(chunks)} chunks, {word_count} words extracted")
            
            return {
                'chunk_count': len(chunks),
                'table_count': 0,  # Images don't contain structured tables
                'word_count': word_count,
                'char_count': char_count,
                'page_count': 1,
                'confidence_score': confidence_score,
                'quality_score': quality_score,
                'metadata': {
                    'processor': 'image_processor',
                    'processing_time': 3.0,
                    'ocr_enabled': TESSERACT_AVAILABLE,
                    'image_format': image_metadata.get('format', 'unknown'),
                    'image_size': image_metadata.get('size', (0, 0)),
                    'text_extracted': bool(extracted_text),
                    'extraction_method': 'tesseract_ocr' if TESSERACT_AVAILABLE else 'metadata_only'
                }
            }
            
        except Exception as e:
            logger.error(f"Error processing image content: {str(e)}")
            return self._create_error_result(str(e))
    
    def _create_image_description(self, image_metadata: Dict[str, Any], extracted_text: str) -> str:
        """
        Create a searchable description of the image.
        
        Args:
            image_metadata: Image metadata
            extracted_text: Text extracted from image
            
        Returns:
            Image description text
        """
        description_parts = []
        
        # Basic image information
        format_info = image_metadata.get('format', 'unknown')
        size_info = image_metadata.get('size', (0, 0))
        description_parts.append(f"Image file in {format_info} format, size {size_info[0]}x{size_info[1]} pixels")
        
        # Text content summary
        if extracted_text:
            word_count = len(extracted_text.split())
            description_parts.append(f"Contains {word_count} words of text extracted via OCR")
            
            # Add first few lines of extracted text
            lines = extracted_text.split('\n')[:3]
            if lines:
                description_parts.append("Text content preview:")
                description_parts.extend([f"  {line}" for line in lines if line.strip()])
        else:
            description_parts.append("No readable text detected in image")
        
        return '\n'.join(description_parts)
    
    def _create_text_chunks(self, content: str, file_record, processing_metadata) -> List:
        """
        Create text chunks from content and store them.
        
        Args:
            content: Text content to chunk
            file_record: File database record
            processing_metadata: Processing metadata record
            
        Returns:
            List of created DocumentChunk instances
        """
        from services.enhanced_processing_service import enhanced_processing_service
        
        # Use the enhanced processing service's chunking method
        chunks = enhanced_processing_service._create_text_chunks(
            content, file_record, processing_metadata
        )
        
        # Update chunk metadata to indicate image source
        for chunk in chunks:
            if chunk.chunk_metadata is None:
                chunk.chunk_metadata = {}
            chunk.chunk_metadata['content_type'] = 'image_ocr'
            chunk.chunk_metadata['extraction_method'] = 'tesseract_ocr' if TESSERACT_AVAILABLE else 'description'
        
        return chunks
    
    def _create_error_result(self, error: str) -> Dict[str, Any]:
        """
        Create an error result dictionary.
        
        Args:
            error: Error message
            
        Returns:
            Error result dictionary
        """
        return {
            'chunk_count': 0,
            'table_count': 0,
            'word_count': 0,
            'char_count': 0,
            'page_count': 1,
            'confidence_score': 0.1,
            'quality_score': 0.1,
            'metadata': {
                'processor': 'image_processor',
                'processing_time': 1.0,
                'error': error,
                'ocr_enabled': TESSERACT_AVAILABLE
            }
        }

# Create a singleton instance
image_processing_service = ImageProcessingService() 