"""
Per-File Re-ranking Service for Deep Vector Search

This service implements the per-file re-ranking strategy where:
1. Each file gets allocated a portion of the search budget
2. Vector search is performed per file with higher initial retrieval
3. Per-file re-ranking is applied to get the best chunks from each file
4. Results are combined without global re-ranking to maintain file coverage
"""

import logging
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from azure.search.documents import SearchClient
from openai import AzureOpenAI

# Configure logging
logger = logging.getLogger(__name__)

@dataclass
class FileAllocation:
    """File allocation for per-file search strategy"""
    file_blob_name: str
    initial_retrieval_count: int
    final_allocation: int
    file_index: int

@dataclass
class PerFileSearchResult:
    """Result from per-file search and re-ranking"""
    file_blob_name: str
    chunks: List[Dict[str, Any]]
    total_retrieved: int
    final_count: int
    avg_score: float
    max_score: float
    min_score: float

class PerFileRerankingService:
    """
    Service for implementing per-file re-ranking strategy for deep vector search
    """
    
    def __init__(self, 
                 search_client: SearchClient,
                 openai_client: AzureOpenAI,
                 embedding_deployment: str,
                 verbose: bool = True):
        """
        Initialize the per-file re-ranking service
        
        Args:
            search_client: Azure Search client
            openai_client: Azure OpenAI client for embeddings
            embedding_deployment: Azure OpenAI embedding deployment name
            verbose: Whether to print verbose output
        """
        self.search_client = search_client
        self.openai_client = openai_client
        self.embedding_deployment = embedding_deployment
        self.verbose = verbose
    
    def perform_per_file_search(self,
                               query: str,
                               blob_names: List[str],
                               total_budget: int = 60,
                               final_budget: int = 12,
                               min_score_threshold: float = 0.5) -> List[Dict[str, Any]]:
        """
        Perform per-file vector search with re-ranking strategy
        
        Args:
            query: Search query
            blob_names: List of blob names to search
            total_budget: Total initial retrieval budget (DEEP_VECTOR_SEARCH_TOP_K)
            final_budget: Final budget after re-ranking (DEEP_VECTOR_SEARCH_RERANK_TOP_K)
            min_score_threshold: Minimum score threshold for filtering
            
        Returns:
            List of search results with guaranteed file coverage
        """
        if self.verbose:
            print(f"\n🔍 PER-FILE RERANKING: Starting deep vector search")
            print(f"   Total Budget: {total_budget}")
            print(f"   Final Budget: {final_budget}")
            print(f"   Files to Search: {len(blob_names)}")
            print(f"   Score Threshold: {min_score_threshold}")
        
        if not blob_names:
            if self.verbose:
                print(f"❌ PER-FILE RERANKING: No blob names provided")
            return []
        
        # Step 1: Create query embedding
        try:
            if self.verbose:
                print(f"🔍 PER-FILE RERANKING: Creating query embedding...")
            
            response = self.openai_client.embeddings.create(
                input=query,
                model=self.embedding_deployment
            )
            query_vector = response.data[0].embedding
            
            if self.verbose:
                print(f"✅ PER-FILE RERANKING: Query embedding created successfully")
        except Exception as e:
            logger.error(f"Error creating query embedding: {str(e)}")
            if self.verbose:
                print(f"❌ PER-FILE RERANKING: Failed to create query embedding: {str(e)}")
            return []
        
        # Step 2: Calculate file allocations
        file_allocations = self._calculate_file_allocations(blob_names, total_budget, final_budget)
        
        if self.verbose:
            print(f"📊 PER-FILE RERANKING: File allocation strategy:")
            for allocation in file_allocations:
                print(f"   File {allocation.file_index + 1}: {allocation.initial_retrieval_count} → {allocation.final_allocation} chunks")
        
        # Step 3: Perform per-file search and re-ranking
        per_file_results = []
        total_chunks_retrieved = 0
        
        for allocation in file_allocations:
            if self.verbose:
                print(f"\n🔍 PER-FILE RERANKING: Processing file {allocation.file_index + 1}/{len(file_allocations)}")
                print(f"   Blob: {allocation.file_blob_name}")
                print(f"   Initial retrieval: {allocation.initial_retrieval_count}")
                print(f"   Final allocation: {allocation.final_allocation}")
            
            file_result = self._search_and_rerank_single_file(
                query_vector=query_vector,
                blob_name=allocation.file_blob_name,
                initial_count=allocation.initial_retrieval_count,
                final_count=allocation.final_allocation,
                min_score_threshold=min_score_threshold
            )
            
            if file_result:
                per_file_results.append(file_result)
                total_chunks_retrieved += file_result.final_count
                
                if self.verbose:
                    print(f"✅ PER-FILE RERANKING: File {allocation.file_index + 1} processed")
                    print(f"   Retrieved: {file_result.total_retrieved} → {file_result.final_count} chunks")
                    print(f"   Score range: {file_result.min_score:.4f} - {file_result.max_score:.4f}")
                    print(f"   Average score: {file_result.avg_score:.4f}")
            else:
                if self.verbose:
                    print(f"❌ PER-FILE RERANKING: File {allocation.file_index + 1} returned no results")
        
        # Step 4: Combine results (no global re-ranking to maintain file coverage)
        final_results = self._combine_per_file_results(per_file_results)
        
        if self.verbose:
            print(f"\n📊 PER-FILE RERANKING: Final results summary:")
            print(f"   Total files processed: {len(per_file_results)}")
            print(f"   Total chunks retrieved: {total_chunks_retrieved}")
            print(f"   Final result count: {len(final_results)}")
            print(f"   File coverage: {len(per_file_results)}/{len(blob_names)} files ({len(per_file_results)/len(blob_names)*100:.1f}%)")
        
        return final_results
    
    def _calculate_file_allocations(self, 
                                   blob_names: List[str], 
                                   total_budget: int, 
                                   final_budget: int) -> List[FileAllocation]:
        """
        Calculate how to allocate search budget across files
        
        Args:
            blob_names: List of blob names
            total_budget: Total initial retrieval budget
            final_budget: Final budget after re-ranking
            
        Returns:
            List of file allocations
        """
        num_files = len(blob_names)
        
        # Strategy: Equal distribution with minimum guarantees
        # Each file gets an equal share of both initial and final budgets
        initial_per_file = max(1, total_budget // num_files)
        final_per_file = max(1, final_budget // num_files)
        
        # Handle remainder by distributing extra chunks to first few files
        initial_remainder = total_budget % num_files
        final_remainder = final_budget % num_files
        
        allocations = []
        for i, blob_name in enumerate(blob_names):
            # Base allocation
            initial_count = initial_per_file
            final_count = final_per_file
            
            # Add remainder to first few files
            if i < initial_remainder:
                initial_count += 1
            if i < final_remainder:
                final_count += 1
            
            allocations.append(FileAllocation(
                file_blob_name=blob_name,
                initial_retrieval_count=initial_count,
                final_allocation=final_count,
                file_index=i
            ))
        
        return allocations
    
    def _search_and_rerank_single_file(self,
                                      query_vector: List[float],
                                      blob_name: str,
                                      initial_count: int,
                                      final_count: int,
                                      min_score_threshold: float) -> Optional[PerFileSearchResult]:
        """
        Search and re-rank results for a single file
        
        Args:
            query_vector: Query embedding vector
            blob_name: Blob name to filter by
            initial_count: Initial number of chunks to retrieve
            final_count: Final number of chunks after re-ranking
            min_score_threshold: Minimum score threshold
            
        Returns:
            Per-file search result or None if no results
        """
        try:
            # Create vector query for this specific file
            vector_query = {
                "vector": query_vector,
                "fields": "text_vector",
                "k": initial_count,
                "kind": "vector"
            }
            
            # Create filter for this specific file
            filter_string = f"metadata_storage_name eq '{blob_name}'"
            
            # Perform search
            results = self.search_client.search(
                search_text=None,
                vector_queries=[vector_query],
                filter=filter_string,
                select=["chunk", "chunk_id", "metadata_storage_name", "title"],
                top=initial_count
            )
            
            # Process and filter results
            file_chunks = []
            for result in results:
                search_score = result.get("@search.score", 0)
                
                # Apply score threshold
                if search_score >= min_score_threshold:
                    file_chunks.append({
                        "content": result["chunk"],
                        "score": search_score,
                        "metadata": {
                            "chunk_id": result["chunk_id"],
                            "title": result.get("title", ""),
                            "metadata_storage_name": result.get("metadata_storage_name", ""),
                            "search_score": search_score
                        }
                    })
            
            if not file_chunks:
                return None
            
            # Per-file re-ranking: sort by score and take top results
            file_chunks.sort(key=lambda x: x['metadata']['search_score'], reverse=True)
            reranked_chunks = file_chunks[:final_count]
            
            # Calculate statistics
            scores = [chunk['metadata']['search_score'] for chunk in reranked_chunks]
            avg_score = sum(scores) / len(scores) if scores else 0
            max_score = max(scores) if scores else 0
            min_score = min(scores) if scores else 0
            
            return PerFileSearchResult(
                file_blob_name=blob_name,
                chunks=reranked_chunks,
                total_retrieved=len(file_chunks),
                final_count=len(reranked_chunks),
                avg_score=avg_score,
                max_score=max_score,
                min_score=min_score
            )
            
        except Exception as e:
            logger.error(f"Error searching file {blob_name}: {str(e)}")
            return None
    
    def _combine_per_file_results(self, per_file_results: List[PerFileSearchResult]) -> List[Dict[str, Any]]:
        """
        Combine per-file results into final result list
        
        Args:
            per_file_results: List of per-file search results
            
        Returns:
            Combined list of search results
        """
        combined_results = []
        
        for file_result in per_file_results:
            combined_results.extend(file_result.chunks)
        
        # Optional: Sort by score while maintaining file representation
        # We don't do global re-ranking to maintain guaranteed file coverage
        # But we can sort to put higher-quality chunks first
        combined_results.sort(key=lambda x: x['metadata']['search_score'], reverse=True)
        
        return combined_results

def create_per_file_reranking_service(search_client: SearchClient,
                                     openai_client: AzureOpenAI,
                                     embedding_deployment: str,
                                     verbose: bool = True) -> PerFileRerankingService:
    """
    Factory function to create a per-file re-ranking service
    
    Args:
        search_client: Azure Search client
        openai_client: Azure OpenAI client
        embedding_deployment: Azure OpenAI embedding deployment name
        verbose: Whether to print verbose output
        
    Returns:
        PerFileRerankingService instance
    """
    return PerFileRerankingService(
        search_client=search_client,
        openai_client=openai_client,
        embedding_deployment=embedding_deployment,
        verbose=verbose
    ) 