"""
Table Merger Service

This service provides functionality to detect and merge tables that are split across multiple pages in PDFs.
Adapted from Himalaya Azure system for the Enhanced Himalaya backend.
"""

import logging
import pandas as pd
import uuid
from typing import List, Dict, Tuple, Set, Optional
from datetime import datetime

# Configure logging
logger = logging.getLogger(__name__)

class TableMergerService:
    """
    Service for detecting and merging tables split across multiple pages.
    """
    
    def __init__(self):
        """Initialize the table merger service."""
        self.logger = logger
    
    def detect_and_merge_split_tables(self, tables: List[Dict[str, any]]) -> Tuple[List[Dict[str, any]], List[Dict[str, any]]]:
        """
        Detect and merge tables that are split across consecutive pages.
        
        Args:
            tables: List of table dictionaries from PDF extraction
            
        Returns:
            Tuple of (merged_tables, individual_tables)
            - merged_tables: List of merged table dictionaries
            - individual_tables: List of tables that weren't part of any split group
        """
        if len(tables) <= 1:
            return [], tables
        
        # Detect split table groups
        split_groups, grouped_table_indices = self._detect_split_table_groups(tables)
        
        merged_tables = []
        individual_tables = []
        
        # Process split groups
        for group_idx, group in enumerate(split_groups):
            try:
                merged_table = self._merge_table_group(group, group_idx)
                if merged_table:
                    merged_tables.append(merged_table)
                    logger.info(f"Successfully merged split table group {group_idx + 1} with {len(group)} parts")
                else:
                    logger.warning(f"Failed to merge split table group {group_idx + 1}")
                    # Add individual tables if merge fails
                    individual_tables.extend(group)
            except Exception as e:
                logger.error(f"Error merging split table group {group_idx + 1}: {str(e)}")
                # Add individual tables if merge fails
                individual_tables.extend(group)
        
        # Add tables that weren't part of any group
        for i, table in enumerate(tables):
            if i not in grouped_table_indices:
                individual_tables.append(table)
        
        logger.info(f"Split table processing complete: {len(merged_tables)} merged groups, {len(individual_tables)} individual tables")
        
        return merged_tables, individual_tables
    
    def _detect_split_table_groups(self, tables: List[Dict[str, any]]) -> Tuple[List[List[Dict[str, any]]], Set[int]]:
        """
        Detect tables that appear to be split across consecutive pages.
        
        Args:
            tables: List of table dictionaries
            
        Returns:
            Tuple of (groups, grouped_indices)
            - groups: List where each item is a list of table dicts representing a split group
            - grouped_indices: Set of table indices that are part of groups
        """
        if len(tables) <= 1:
            return [], set()
        
        # Sort tables by page number, then by table index
        tables_with_indices = [(i, table) for i, table in enumerate(tables)]
        tables_sorted = sorted(tables_with_indices, key=lambda x: (x[1]['page_number'], x[1]['table_index']))
        
        groups = []
        grouped_indices = set()
        processed_indices = set()
        
        for i, (original_idx, current_table) in enumerate(tables_sorted):
            if i in processed_indices:
                continue
            
            current_group = [(original_idx, current_table)]
            processed_indices.add(i)
            
            # Look for similar tables on consecutive pages
            j = i + 1
            while j < len(tables_sorted):
                next_original_idx, next_table = tables_sorted[j]
                
                # Check if tables are similar and on consecutive pages
                if self._are_tables_similar(current_table, next_table):
                    current_group.append((next_original_idx, next_table))
                    processed_indices.add(j)
                    j += 1
                else:
                    # Stop if we find a dissimilar table
                    break
            
            # If we found a group of related tables (more than 1)
            if len(current_group) > 1:
                # Extract just the table dictionaries (without indices)
                group_tables = [table for _, table in current_group]
                groups.append(group_tables)
                
                # Track original indices
                for original_idx, _ in current_group:
                    grouped_indices.add(original_idx)
                
                pages = [table['page_number'] for table in group_tables]
                logger.info(f"Detected split table group spanning pages: {pages}")
        
        return groups, grouped_indices
    
    def _are_tables_similar(self, table1: Dict[str, any], table2: Dict[str, any]) -> bool:
        """
        Check if two tables are similar enough to be considered parts of the same split table.
        
        Args:
            table1: First table dictionary
            table2: Second table dictionary
            
        Returns:
            True if tables appear to be parts of the same table
        """
        try:
            # Tables must be on consecutive pages
            if abs(table1['page_number'] - table2['page_number']) != 1:
                return False
            
            # Check if tables have same number of columns
            if table1['column_count'] != table2['column_count']:
                return False
            
            # Compare headers if available
            headers1 = table1.get('headers', [])
            headers2 = table2.get('headers', [])
            
            if headers1 and headers2:
                # If both have headers, they should match
                if len(headers1) == len(headers2):
                    # Allow for slight variations in header text
                    header_similarity = self._calculate_header_similarity(headers1, headers2)
                    if header_similarity < 0.8:  # 80% similarity threshold
                        return False
            
            # Check table data structure similarity
            table_data1 = table1.get('data', [])
            table_data2 = table2.get('data', [])
            
            if table_data1 and table_data2:
                # Create sample DataFrames to compare data types
                try:
                    df1 = self._create_sample_dataframe(table_data1, headers1)
                    df2 = self._create_sample_dataframe(table_data2, headers2)
                    
                    if df1 is not None and df2 is not None:
                        # Compare data types
                        if len(df1.columns) == len(df2.columns):
                            dtype_similarity = self._calculate_dtype_similarity(df1, df2)
                            return dtype_similarity >= 0.7  # 70% data type similarity
                except Exception as e:
                    logger.debug(f"Error comparing table data types: {str(e)}")
            
            # If we can't determine from data, assume they're similar if headers match
            return len(headers1) == len(headers2) if headers1 and headers2 else True
            
        except Exception as e:
            logger.debug(f"Error checking table similarity: {str(e)}")
            return False
    
    def _calculate_header_similarity(self, headers1: List[str], headers2: List[str]) -> float:
        """
        Calculate similarity between two header lists.
        
        Args:
            headers1: First header list
            headers2: Second header list
            
        Returns:
            Similarity score between 0 and 1
        """
        if len(headers1) != len(headers2):
            return 0.0
        
        matches = 0
        for h1, h2 in zip(headers1, headers2):
            # Normalize headers for comparison
            h1_norm = str(h1).lower().strip()
            h2_norm = str(h2).lower().strip()
            
            if h1_norm == h2_norm:
                matches += 1
            elif h1_norm in h2_norm or h2_norm in h1_norm:
                matches += 0.8  # Partial match
        
        return matches / len(headers1)
    
    def _create_sample_dataframe(self, table_data: List[List], headers: List[str]) -> Optional[pd.DataFrame]:
        """
        Create a sample DataFrame from table data for analysis.
        
        Args:
            table_data: Table data as list of lists
            headers: Table headers
            
        Returns:
            Sample DataFrame or None if creation fails
        """
        try:
            if not table_data:
                return None
            
            # Use headers if available, otherwise generate column names
            if headers:
                columns = headers
            else:
                # If no headers, use first row or generate names
                if table_data and len(table_data) > 0:
                    columns = [f"Column_{i+1}" for i in range(len(table_data[0]))]
                else:
                    return None
            
            # Use first few rows for sample (skip header row if headers came from data)
            sample_data = table_data[:5] if table_data else []
            
            if not sample_data:
                return None
            
            # Ensure all rows have same length as columns
            normalized_data = []
            for row in sample_data:
                normalized_row = row[:len(columns)] if len(row) >= len(columns) else row + [''] * (len(columns) - len(row))
                normalized_data.append(normalized_row)
            
            return pd.DataFrame(normalized_data, columns=columns)
            
        except Exception as e:
            logger.debug(f"Error creating sample DataFrame: {str(e)}")
            return None
    
    def _calculate_dtype_similarity(self, df1: pd.DataFrame, df2: pd.DataFrame) -> float:
        """
        Calculate data type similarity between two DataFrames.
        
        Args:
            df1: First DataFrame
            df2: Second DataFrame
            
        Returns:
            Similarity score between 0 and 1
        """
        try:
            if len(df1.columns) != len(df2.columns):
                return 0.0
            
            # Infer data types for both DataFrames
            dtypes1 = df1.dtypes
            dtypes2 = df2.dtypes
            
            matches = 0
            for dtype1, dtype2 in zip(dtypes1, dtypes2):
                if dtype1 == dtype2:
                    matches += 1
                elif self._are_compatible_dtypes(dtype1, dtype2):
                    matches += 0.8  # Partial match for compatible types
            
            return matches / len(dtypes1)
            
        except Exception as e:
            logger.debug(f"Error calculating dtype similarity: {str(e)}")
            return 0.0
    
    def _are_compatible_dtypes(self, dtype1, dtype2) -> bool:
        """
        Check if two data types are compatible (e.g., int64 and float64).
        
        Args:
            dtype1: First data type
            dtype2: Second data type
            
        Returns:
            True if types are compatible
        """
        # Convert to string for easier comparison
        str_dtype1 = str(dtype1)
        str_dtype2 = str(dtype2)
        
        # Numeric types are compatible with each other
        numeric_types = ['int', 'float', 'number']
        if any(nt in str_dtype1 for nt in numeric_types) and any(nt in str_dtype2 for nt in numeric_types):
            return True
        
        # Object types are compatible with string types
        text_types = ['object', 'string', 'str']
        if any(tt in str_dtype1 for tt in text_types) and any(tt in str_dtype2 for tt in text_types):
            return True
        
        return False
    
    def _merge_table_group(self, table_group: List[Dict[str, any]], group_idx: int) -> Optional[Dict[str, any]]:
        """
        Merge a group of tables that are parts of the same split table.
        
        Args:
            table_group: List of table dictionaries to merge
            group_idx: Index of the group for naming
            
        Returns:
            Merged table dictionary or None if merge fails
        """
        if not table_group:
            return None
        
        try:
            # Sort tables by page number to maintain order
            sorted_tables = sorted(table_group, key=lambda t: t['page_number'])
            
            # Use the first table as the base
            base_table = sorted_tables[0]
            merged_data = []
            
            # Start with the base table's data
            base_data = base_table.get('data', [])
            if base_data:
                merged_data.extend(base_data)
            
            # Merge data from subsequent tables
            for i, table in enumerate(sorted_tables[1:], 1):
                table_data = table.get('data', [])
                
                if table_data:
                    # Skip header row if it matches the base table's header
                    data_to_add = table_data
                    
                    # Check if first row is a duplicate header
                    if (len(table_data) > 0 and 
                        base_table.get('headers') and 
                        len(table_data[0]) == len(base_table['headers'])):
                        
                        # Compare first row with headers
                        first_row = [str(cell).lower().strip() for cell in table_data[0]]
                        headers = [str(h).lower().strip() for h in base_table['headers']]
                        
                        if first_row == headers:
                            data_to_add = table_data[1:]  # Skip header row
                            logger.debug(f"Removed duplicate header from table part {i+1}")
                    
                    merged_data.extend(data_to_add)
            
            # Calculate merged table statistics
            merged_row_count = len(merged_data)
            merged_column_count = base_table.get('column_count', 0)
            
            # Generate unique group ID for this merged table
            group_id = str(uuid.uuid4())
            
            # Create page range string
            page_numbers = [t['page_number'] for t in sorted_tables]
            page_range = f"{min(page_numbers)}-{max(page_numbers)}"
            
            # Create merged table summary
            original_summaries = [t.get('summary', '') for t in sorted_tables if t.get('summary')]
            if original_summaries:
                merged_summary = f"Merged table from pages {page_range}: {original_summaries[0]}"
            else:
                merged_summary = f"Merged table from pages {page_range} with {merged_row_count} rows"
            
            # Create the merged table dictionary
            merged_table = {
                'page_number': base_table['page_number'],  # Use first page number
                'table_index': base_table['table_index'],  # Use first table index
                'headers': base_table.get('headers', []),
                'data': merged_data,
                'row_count': merged_row_count,
                'column_count': merged_column_count,
                'summary': merged_summary,
                'potential_analyses': base_table.get('potential_analyses', []),
                'metadata': {
                    **base_table.get('metadata', {}),
                    'is_merged_table': True,
                    'merged_from_pages': page_numbers,
                    'merged_table_count': len(sorted_tables),
                    'merged_group_id': group_id,
                    'page_range': page_range,
                    'merge_timestamp': datetime.utcnow().isoformat()
                }
            }
            
            logger.info(f"Successfully merged {len(sorted_tables)} table parts into single table: "
                       f"{merged_row_count} rows x {merged_column_count} columns")
            
            return merged_table
            
        except Exception as e:
            logger.error(f"Error merging table group: {str(e)}")
            return None

# Create singleton instance
table_merger_service = TableMergerService() 