"""
Enhanced Excel Summarizer Service

This service provides AI-powered analysis and summarization of Excel/CSV data,
based on the Himalaya Azure system's ExcelSummarizer functionality.
Generates comprehensive content summaries with date detection, column analysis,
and potential analysis suggestions.
"""

import logging
import os
import pandas as pd
import numpy as np
from typing import Dict, List, Any, Optional, Tuple
import json
from datetime import datetime
import re

from config.settings import (
    AZURE_OPENAI_KEY, AZURE_OPENAI_ENDPOINT, AZURE_OPENAI_API_VERSION,
    AZURE_OPENAI_DEPLOYMENT_NAME
)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Try to import OpenAI clients
try:
    from openai import AzureOpenAI, OpenAI
    OPENAI_AVAILABLE = True
except ImportError:
    logger.warning("OpenAI library not available. AI summaries will be disabled.")
    OPENAI_AVAILABLE = False

# Get OpenAI settings from environment if not in config
OPENAI_API_KEY = os.environ.get('OPENAI_API_KEY')
OPENAI_MODEL = os.environ.get('OPENAI_MODEL', 'gpt-4o')

# Maximum string length for object fields
MAX_STRING_LENGTH = 300

class DateTimeEncoder(json.JSONEncoder):
    """Custom JSON encoder for handling pandas Timestamp objects."""
    def default(self, obj):
        if isinstance(obj, (pd.Timestamp, datetime)):
            return obj.isoformat()
        elif pd.isna(obj):
            return None
        return super(DateTimeEncoder, self).default(obj)

class ExcelSummarizerService:
    """
    Enhanced Excel and CSV summarization service with AI-powered analysis.
    """
    
    def __init__(self):
        """Initialize the Excel summarizer service."""
        self.llm_client = None
        self.use_azure = False
        
        # Initialize AI client if available
        if OPENAI_AVAILABLE:
            self._initialize_ai_client()
        
        logger.info("Excel Summarizer Service initialized")
    
    def _initialize_ai_client(self):
        """Initialize the AI client (Azure OpenAI or OpenAI)."""
        try:
            # Try Azure OpenAI first
            if AZURE_OPENAI_KEY and AZURE_OPENAI_ENDPOINT:
                self.llm_client = AzureOpenAI(
                    api_key=AZURE_OPENAI_KEY,
                    api_version=AZURE_OPENAI_API_VERSION,
                    azure_endpoint=AZURE_OPENAI_ENDPOINT
                )
                self.use_azure = True
                logger.info("Using Azure OpenAI for Excel summarization")
            
            # Fallback to OpenAI
            elif OPENAI_API_KEY:
                self.llm_client = OpenAI(api_key=OPENAI_API_KEY)
                self.use_azure = False
                logger.info("Using OpenAI for Excel summarization")
            
            else:
                logger.warning("No AI credentials available. Basic summaries only.")
                
        except Exception as e:
            logger.error(f"Error initializing AI client: {str(e)}")
            self.llm_client = None
    
    def analyze_and_summarize_dataframe(self, df: pd.DataFrame, sheet_name: str = "Sheet1", 
                                      file_path: str = None) -> Dict[str, Any]:
        """
        Perform comprehensive analysis and summarization of a DataFrame.
        
        Args:
            df: DataFrame to analyze
            sheet_name: Name of the sheet
            file_path: Path to the original file
            
        Returns:
            Comprehensive analysis and summary
        """
        try:
            # Step 1: Prepare DataFrame
            df_prepared = self._prepare_dataframe_for_analysis(df)
            
            # Step 2: Detect and convert dates
            df_with_dates = self._detect_and_convert_dates(df_prepared)
            
            # Step 3: Analyze DataFrame structure and content
            analysis = self._analyze_dataframe(df_with_dates)
            
            # Step 4: Generate comprehensive semantic summary using unified analyzer
            try:
                from services.table_semantic_analyzer import table_semantic_analyzer
                
                # Prepare source info for Excel/CSV analysis
                file_ext = os.path.splitext(file_path)[1].lower() if file_path else '.csv'
                source_type = 'excel' if file_ext in ['.xlsx', '.xls'] else 'csv'
                
                source_info = {
                    'source_type': source_type,
                    'file_name': file_path or f"{sheet_name}.csv",
                    'sheet_name': sheet_name
                }
                
                # Generate comprehensive semantic summary
                semantic_analysis = table_semantic_analyzer.analyze_table(
                    df=df_with_dates,
                    table_name=sheet_name,
                    source_info=source_info
                )
                
                # Convert to expected format
                summary_result = {
                    'summary': json.dumps({
                        'summary': semantic_analysis.get('summary', ''),
                        'file_path': file_path or f"{sheet_name}.csv",
                        'potential_analyses': semantic_analysis.get('potential_analyses', [])
                    }),
                    'summary_json': {
                        'summary': semantic_analysis.get('summary', ''),
                        'file_path': file_path or f"{sheet_name}.csv",
                        'potential_analyses': semantic_analysis.get('potential_analyses', [])
                    },
                    'potential_analyses': semantic_analysis.get('potential_analyses', []),
                    'columns_metadata': analysis.get('column_details', {}),
                    'status': 'success'
                }
                
                logger.info(f"🔍 EXCEL UNIFIED: Generated {semantic_analysis.get('analysis_quality', 'unknown')} analysis for {source_type} with {semantic_analysis.get('analysis_count', 0)} analyses")
                
            except Exception as semantic_error:
                logger.error(f"❌ EXCEL UNIFIED: Error in semantic analysis: {str(semantic_error)}")
                # Fallback to AI summary if available, otherwise basic
                if self.llm_client:
                    summary_result = self._generate_ai_summary(analysis, sheet_name, file_path)
                else:
                    summary_result = self._generate_basic_summary(analysis, sheet_name, file_path)
            
            # Step 5: Create comprehensive extracted content
            extracted_content = self._create_comprehensive_content(df_with_dates, analysis, summary_result, sheet_name)
            
            return {
                'analysis': analysis,
                'summary_result': summary_result,
                'extracted_content': extracted_content,
                'status': 'success'
            }
            
        except Exception as e:
            logger.error(f"Error analyzing DataFrame: {str(e)}")
            return {
                'analysis': {},
                'summary_result': self._generate_error_summary(str(e), sheet_name, file_path),
                'extracted_content': f"Error analyzing {sheet_name}: {str(e)}",
                'status': 'error'
            }
    
    def _prepare_dataframe_for_analysis(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Prepare DataFrame for analysis by truncating long string values.
        
        Args:
            df: DataFrame to prepare
            
        Returns:
            Prepared DataFrame
        """
        df_prepared = df.copy()
        
        # Truncate long strings in object columns
        for col in df_prepared.columns:
            if df_prepared[col].dtype == 'object':
                try:
                    # Apply truncation to the column
                    df_prepared[col] = df_prepared[col].apply(self._truncate_string)
                    
                    # Log if any values were truncated
                    original_lengths = df[col].astype(str).apply(len)
                    truncated_lengths = df_prepared[col].astype(str).apply(len)
                    num_truncated = (original_lengths > MAX_STRING_LENGTH).sum()
                    
                    if num_truncated > 0:
                        logger.info(f"Truncated {num_truncated} values in column '{col}' to {MAX_STRING_LENGTH} characters")
                except Exception as e:
                    logger.error(f"Error truncating values in column '{col}': {str(e)}")
        
        return df_prepared
    
    def _truncate_string(self, s: Any, max_length: int = MAX_STRING_LENGTH) -> Any:
        """
        Truncate a string if it's longer than max_length.
        
        Args:
            s: String or any other value
            max_length: Maximum length allowed
            
        Returns:
            Truncated string or original value if not a string
        """
        if not isinstance(s, str):
            return s
            
        if len(s) > max_length:
            return s[:max_length] + "..."
        return s
    
    def _detect_and_convert_dates(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Detect and convert date columns to datetime objects.
        
        Args:
            df: DataFrame to process
            
        Returns:
            DataFrame with date columns converted to datetime
        """
        df_processed = df.copy()
        date_columns = []
        date_ranges = {}
        
        # Common date formats to try
        date_formats = [
            '%Y-%m-%d', '%d-%m-%Y', '%m-%d-%Y', '%Y/%m/%d', '%d/%m/%Y', '%m/%d/%Y',
            '%Y-%m-%d %H:%M:%S', '%d-%m-%Y %H:%M:%S', '%m-%d-%Y %H:%M:%S',
            '%Y/%m/%d %H:%M:%S', '%d/%m/%Y %H:%M:%S', '%m/%d/%Y %H:%M:%S',
            '%d-%b-%Y', '%d %b %Y', '%b %d, %Y', '%d.%m.%Y', '%B %d, %Y'
        ]
        
        for col in df_processed.columns:
            if df_processed[col].dtype == 'object':
                # Try to convert to datetime
                for date_format in date_formats:
                    try:
                        # Test conversion on a sample
                        sample_values = df_processed[col].dropna().head(10)
                        if len(sample_values) == 0:
                            break
                            
                        # Try to convert sample values
                        converted_sample = pd.to_datetime(sample_values, format=date_format, errors='coerce')
                        
                        # If more than 70% of sample values are successfully converted, consider it a date column
                        success_rate = converted_sample.notna().sum() / len(sample_values)
                        if success_rate > 0.7:
                            # Convert the entire column
                            df_processed[col] = pd.to_datetime(df_processed[col], format=date_format, errors='coerce')
                            date_columns.append(col)
                            
                            # Calculate date range
                            valid_dates = df_processed[col].dropna()
                            if len(valid_dates) > 0:
                                date_ranges[col] = {
                                    'min_date': valid_dates.min(),
                                    'max_date': valid_dates.max(),
                                    'count': len(valid_dates)
                                }
                            
                            logger.info(f"Detected date column '{col}' with format '{date_format}'")
                            break
                    except Exception:
                        continue
        
        # Store date information as attributes (using setattr to avoid pandas warning)
        setattr(df_processed, '_date_columns', date_columns)
        setattr(df_processed, '_date_ranges', date_ranges)
        
        return df_processed
    
    def _analyze_dataframe(self, df: pd.DataFrame) -> Dict[str, Any]:
        """
        Perform comprehensive analysis of DataFrame.
        
        Args:
            df: DataFrame to analyze
            
        Returns:
            Analysis results
        """
        analysis = {
            'total_rows': int(len(df)),  # Convert to int for JSON serialization
            'total_columns': int(len(df.columns)),  # Convert to int for JSON serialization
            'dtypes': df.dtypes.astype(str).to_dict(),
            'column_details': {},
            'date_columns': getattr(df, '_date_columns', []),
            'date_ranges': getattr(df, '_date_ranges', {}),
            'memory_usage': int(df.memory_usage(deep=True).sum()),  # Convert to int for JSON serialization
            'null_counts': {k: int(v) for k, v in df.isnull().sum().to_dict().items()}  # Convert to int
        }
        
        # Analyze each column in detail
        for col in df.columns:
            col_analysis = self._analyze_column(df[col], col)
            analysis['column_details'][col] = col_analysis
        
        return analysis
    
    def _analyze_column(self, series: pd.Series, col_name: str) -> Dict[str, Any]:
        """
        Analyze a single column in detail.
        
        Args:
            series: Pandas Series to analyze
            col_name: Name of the column
            
        Returns:
            Column analysis
        """
        col_analysis = {
            'dtype': str(series.dtype),
            'null_count': int(series.isnull().sum()),  # Convert to int
            'null_percentage': float((series.isnull().sum() / len(series)) * 100),  # Convert to float
            'unique_count': int(series.nunique()),  # Convert to int
            'unique_percentage': float((series.nunique() / len(series)) * 100)  # Convert to float
        }
        
        # Type-specific analysis
        if pd.api.types.is_numeric_dtype(series):
            col_analysis.update({
                'min': float(series.min()) if not pd.isna(series.min()) else None,
                'max': float(series.max()) if not pd.isna(series.max()) else None,
                'mean': float(series.mean()) if not pd.isna(series.mean()) else None,
                'median': float(series.median()) if not pd.isna(series.median()) else None,
                'std': float(series.std()) if not pd.isna(series.std()) else None,
                'is_binary': bool(series.nunique() == 2)
            })
        
        elif pd.api.types.is_datetime64_any_dtype(series):
            valid_dates = series.dropna()
            if len(valid_dates) > 0:
                col_analysis.update({
                    'min_date': valid_dates.min().isoformat() if hasattr(valid_dates.min(), 'isoformat') else str(valid_dates.min()),
                    'max_date': valid_dates.max().isoformat() if hasattr(valid_dates.max(), 'isoformat') else str(valid_dates.max()),
                    'date_range_days': int((valid_dates.max() - valid_dates.min()).days)
                })
        
        else:  # Object/string columns
            value_counts = series.value_counts().head(10)
            col_analysis.update({
                'top_values': {str(k): int(v) for k, v in value_counts.to_dict().items()},  # Convert to serializable types
                'is_categorical': bool(series.nunique() < len(series) * 0.5),
                'avg_length': float(series.astype(str).str.len().mean()) if not series.empty else 0.0
            })
        
        return col_analysis
    
    def _generate_ai_summary(self, analysis: Dict[str, Any], sheet_name: str, file_path: str = None) -> Dict[str, Any]:
        """
        Generate AI-powered summary of the data.
        
        Args:
            analysis: DataFrame analysis results
            sheet_name: Name of the sheet
            file_path: Path to the file
            
        Returns:
            AI-generated summary
        """
        try:
            # Create compact analysis for AI
            compact_analysis = {
                'total_rows': analysis['total_rows'],
                'total_columns': analysis['total_columns'],
                'dtypes': analysis['dtypes'],
                'column_details': analysis['column_details'],
                'date_columns': analysis['date_columns'],
                'date_ranges': analysis['date_ranges']
            }
            
            # System prompt for AI summarization
            system_prompt = """You are an expert data analyst with deep knowledge across multiple business domains. Analyze the provided DataFrame information and create a comprehensive summary with highly specific, actionable potential analyses.

Your response must be in JSON format with this exact structure:
{
    "summary": "A detailed description of what this data represents and its contents",
    "file_path": "The file path provided",
    "potential_analyses": ["list", "of", "specific", "analyses", "that", "could", "be", "performed"]
}

For the summary:
- Be specific about what each column contains, especially binary and categorical columns
- If date columns exist, ALWAYS mention the specific date range (e.g., "data from January 1, 2023 to March 31, 2023")
- For categorical columns, mention the top categories when applicable
- Describe the data's business context and potential use cases
- Keep it concise but informative

For potential_analyses (provide 6-10 specific, actionable analyses):
- Tailor analyses to the specific data domain (HR, Finance, Sales, Operations, etc.)
- Include advanced statistical and business analyses beyond basic descriptives
- For time-based data: trend analysis, seasonality detection, forecasting, growth rate analysis
- For categorical data: segmentation analysis, performance comparison, distribution analysis
- For numerical data: correlation analysis, outlier detection, performance benchmarking
- For employee data: turnover analysis, performance evaluation, compensation analysis, diversity metrics
- For financial data: profitability analysis, cost analysis, budget variance, ROI calculation
- For sales data: conversion analysis, customer segmentation, revenue optimization, market analysis
- For operational data: efficiency metrics, capacity analysis, quality control, process optimization
- Include predictive and prescriptive analyses where applicable
- Be specific about what insights each analysis would provide

Examples of good potential analyses:
- "Employee turnover rate analysis by department and tenure"
- "Salary equity analysis across gender and experience levels"
- "Revenue trend analysis with seasonal decomposition"
- "Customer lifetime value calculation and segmentation"
- "Cost center performance benchmarking"
- "Predictive modeling for employee retention risk"
- "Budget variance analysis with root cause identification"

The date range information is CRITICAL for distinguishing between similar datasets."""

            # Prepare user message
            user_message = json.dumps({
                'sheet_name': sheet_name,
                'file_path': file_path or f"{sheet_name}.csv",
                'analysis': compact_analysis
            }, cls=DateTimeEncoder)
            
            # Generate summary using AI
            if self.use_azure:
                response = self.llm_client.chat.completions.create(
                    model=AZURE_OPENAI_DEPLOYMENT_NAME,
                    messages=[
                        {"role": "system", "content": system_prompt},
                        {"role": "user", "content": user_message}
                    ],
                    temperature=0.3,
                    max_tokens=1000
                )
            else:
                response = self.llm_client.chat.completions.create(
                    model=OPENAI_MODEL,
                    messages=[
                        {"role": "system", "content": system_prompt},
                        {"role": "user", "content": user_message}
                    ],
                    temperature=0.3,
                    max_tokens=1000
                )
            
            summary_text = response.choices[0].message.content
            
            # Parse JSON response
            try:
                # Extract JSON from response
                json_start = summary_text.find('{')
                json_end = summary_text.rfind('}')
                if json_start >= 0 and json_end >= 0:
                    json_str = summary_text[json_start:json_end+1]
                    summary_json = json.loads(json_str)
                else:
                    summary_json = json.loads(summary_text)
                
                # Ensure required fields
                if 'summary' not in summary_json:
                    summary_json['summary'] = f"Data with {analysis['total_rows']} rows and {analysis['total_columns']} columns"
                
                if 'file_path' not in summary_json:
                    summary_json['file_path'] = file_path or f"{sheet_name}.csv"
                
                if 'potential_analyses' not in summary_json:
                    summary_json['potential_analyses'] = ["descriptive statistics", "data quality analysis"]
                
                return {
                    'summary': json.dumps(summary_json),
                    'summary_json': summary_json,
                    'potential_analyses': summary_json.get('potential_analyses', []),
                    'columns_metadata': analysis.get('column_details', {}),
                    'status': 'success'
                }
                
            except json.JSONDecodeError as e:
                logger.error(f"Error parsing AI summary JSON: {str(e)}")
                return self._generate_basic_summary(analysis, sheet_name, file_path)
                
        except Exception as e:
            logger.error(f"Error generating AI summary: {str(e)}")
            return self._generate_basic_summary(analysis, sheet_name, file_path)
    
    def _generate_basic_summary(self, analysis: Dict[str, Any], sheet_name: str, file_path: str = None) -> Dict[str, Any]:
        """
        Generate basic summary without AI.
        
        Args:
            analysis: DataFrame analysis results
            sheet_name: Name of the sheet
            file_path: Path to the file
            
        Returns:
            Basic summary
        """
        # Create basic summary
        summary_parts = [
            f"Dataset with {analysis['total_rows']} rows and {analysis['total_columns']} columns"
        ]
        
        # Add date range information if available
        if analysis.get('date_columns'):
            date_info = []
            for col, date_range in analysis.get('date_ranges', {}).items():
                if 'min_date' in date_range and 'max_date' in date_range:
                    min_date = date_range['min_date']
                    max_date = date_range['max_date']
                    if hasattr(min_date, 'strftime') and hasattr(max_date, 'strftime'):
                        date_info.append(f"from {min_date.strftime('%Y-%m-%d')} to {max_date.strftime('%Y-%m-%d')}")
            
            if date_info:
                summary_parts.append(f"Time period: {', '.join(date_info)}")
        
        # Add column type information
        numeric_cols = [col for col, details in analysis.get('column_details', {}).items() 
                       if 'min' in details]
        text_cols = [col for col, details in analysis.get('column_details', {}).items() 
                    if 'top_values' in details]
        
        if numeric_cols:
            summary_parts.append(f"Contains {len(numeric_cols)} numeric columns")
        if text_cols:
            summary_parts.append(f"Contains {len(text_cols)} text/categorical columns")
        
        summary_text = ". ".join(summary_parts) + "."
        
        # Create summary JSON
        summary_json = {
            "summary": summary_text,
            "file_path": file_path or f"{sheet_name}.csv",
            "potential_analyses": [
                "descriptive statistics and data profiling",
                "data quality assessment and missing value analysis",
                "column correlation and relationship analysis",
                "outlier detection and anomaly identification",
                "distribution analysis and normality testing",
                "categorical variable frequency analysis"
            ]
        }
        
        # Add domain-specific analyses based on column names and content
        column_names = [col.lower() for col in analysis.get('column_details', {}).keys()]
        
        # Financial/Business analyses
        if any(term in ' '.join(column_names) for term in ['salary', 'revenue', 'cost', 'price', 'amount', 'budget', 'profit']):
            summary_json["potential_analyses"].extend([
                "financial performance analysis and benchmarking",
                "cost-benefit analysis and ROI calculation",
                "budget variance analysis and forecasting"
            ])
        
        # HR/Employee analyses
        if any(term in ' '.join(column_names) for term in ['employee', 'department', 'position', 'hire', 'performance', 'rating']):
            summary_json["potential_analyses"].extend([
                "employee performance evaluation and ranking",
                "departmental analysis and resource allocation",
                "workforce diversity and equity analysis"
            ])
        
        # Sales/Customer analyses
        if any(term in ' '.join(column_names) for term in ['sales', 'customer', 'order', 'product', 'quantity', 'region']):
            summary_json["potential_analyses"].extend([
                "sales performance analysis by region/product",
                "customer segmentation and behavior analysis",
                "product performance and market analysis"
            ])
        
        # Time-based analyses if dates present
        if analysis.get('date_columns'):
            summary_json["potential_analyses"].extend([
                "time series analysis and trend identification",
                "seasonal pattern detection and forecasting",
                "growth rate analysis and projection modeling",
                "period-over-period performance comparison"
            ])
        
        # Numerical data analyses
        if numeric_cols:
            summary_json["potential_analyses"].extend([
                "statistical modeling and predictive analytics",
                "performance benchmarking and ranking analysis",
                "variance analysis and control chart monitoring"
            ])
        
        # Categorical data analyses
        if text_cols:
            summary_json["potential_analyses"].extend([
                "categorical segmentation and cluster analysis",
                "cross-tabulation and association analysis",
                "classification modeling and pattern recognition"
            ])
        
        return {
            'summary': json.dumps(summary_json),
            'summary_json': summary_json,
            'potential_analyses': summary_json['potential_analyses'],
            'columns_metadata': analysis.get('column_details', {}),
            'status': 'success'
        }
    
    def _generate_error_summary(self, error: str, sheet_name: str, file_path: str = None) -> Dict[str, Any]:
        """
        Generate error summary.
        
        Args:
            error: Error message
            sheet_name: Name of the sheet
            file_path: Path to the file
            
        Returns:
            Error summary
        """
        summary_json = {
            "summary": f"Error analyzing {sheet_name}: {error}",
            "file_path": file_path or f"{sheet_name}.csv",
            "potential_analyses": ["error recovery", "data validation"]
        }
        
        return {
            'summary': json.dumps(summary_json),
            'summary_json': summary_json,
            'potential_analyses': summary_json['potential_analyses'],
            'columns_metadata': {},
            'status': 'error'
        }
    
    def _create_comprehensive_content(self, df: pd.DataFrame, analysis: Dict[str, Any], 
                                    summary_result: Dict[str, Any], sheet_name: str) -> str:
        """
        Create comprehensive searchable content from DataFrame analysis.
        
        Args:
            df: DataFrame
            analysis: Analysis results
            summary_result: Summary results
            sheet_name: Name of the sheet
            
        Returns:
            Comprehensive searchable content
        """
        content_parts = []
        
        # Add summary
        summary_json = summary_result.get('summary_json', {})
        content_parts.append(f"Sheet: {sheet_name}")
        content_parts.append(f"Summary: {summary_json.get('summary', '')}")
        
        # Add dimensions
        content_parts.append(f"Dimensions: {analysis['total_rows']} rows × {analysis['total_columns']} columns")
        
        # Add date range information
        if analysis.get('date_columns'):
            for col, date_range in analysis.get('date_ranges', {}).items():
                if 'min_date' in date_range and 'max_date' in date_range:
                    min_date = date_range['min_date']
                    max_date = date_range['max_date']
                    if hasattr(min_date, 'strftime') and hasattr(max_date, 'strftime'):
                        content_parts.append(f"Date range in {col}: {min_date.strftime('%Y-%m-%d')} to {max_date.strftime('%Y-%m-%d')}")
        
        # Add column information
        content_parts.append(f"Columns: {' | '.join(df.columns)}")
        
        # Add column details
        for col, details in analysis.get('column_details', {}).items():
            col_info = [f"Column {col}"]
            
            if 'min' in details and 'max' in details:
                col_info.append(f"numeric range {details['min']:.2f} to {details['max']:.2f}")
            elif 'top_values' in details:
                top_vals = list(details['top_values'].keys())[:3]
                col_info.append(f"categories: {', '.join(map(str, top_vals))}")
            
            if details.get('null_percentage', 0) > 10:
                col_info.append(f"{details['null_percentage']:.1f}% missing")
            
            content_parts.append(" - ".join(col_info))
        
        # Add potential analyses
        potential_analyses = summary_result.get('potential_analyses', [])
        if potential_analyses:
            content_parts.append(f"Potential analyses: {', '.join(potential_analyses)}")
        
        # Add sample data
        content_parts.append("Sample data:")
        for idx, (_, row) in enumerate(df.head(5).iterrows()):
            row_text = " | ".join([str(val)[:50] for val in row.values])
            content_parts.append(f"Row {idx + 1}: {row_text}")
        
        if len(df) > 5:
            content_parts.append(f"... and {len(df) - 5} more rows")
        
        return "\n".join(content_parts)

# Create a singleton instance
excel_summarizer_service = ExcelSummarizerService() 