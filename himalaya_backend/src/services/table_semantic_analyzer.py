"""
Table Semantic Analyzer Service

This service provides unified, comprehensive semantic analysis for tables from any source:
- PDF tables (extracted via Azure Document Intelligence or pdfplumber)
- Excel/CSV files 
- Image-based tables (via OCR)
- Any other tabular data

Ensures consistent, high-quality semantic summaries and potential analyses
for storage in the csv_files table.
"""

import logging
import pandas as pd
from typing import Dict, List, Any, Optional
import json

from config.settings import (
    AZURE_OPENAI_KEY, AZURE_OPENAI_ENDPOINT, AZURE_OPENAI_API_VERSION,
    AZURE_OPENAI_DEPLOYMENT_NAME
)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Try to import OpenAI client
try:
    from openai import AzureOpenAI
    OPENAI_AVAILABLE = True
except ImportError:
    logger.warning("OpenAI library not available. Enhanced semantic analysis will be disabled.")
    OPENAI_AVAILABLE = False

class TableSemanticAnalyzer:
    """
    Centralized service for generating enhanced semantic summaries and potential analyses
    for tables from any source (PDF, Excel, CSV, images, etc.).
    """
    
    def __init__(self):
        """Initialize the table semantic analyzer."""
        self.openai_client = None
        
        # Initialize Azure OpenAI client if available
        if OPENAI_AVAILABLE and AZURE_OPENAI_KEY and AZURE_OPENAI_ENDPOINT:
            try:
                self.openai_client = AzureOpenAI(
                    api_key=AZURE_OPENAI_KEY,
                    api_version=AZURE_OPENAI_API_VERSION,
                    azure_endpoint=AZURE_OPENAI_ENDPOINT
                )
                logger.info("Table Semantic Analyzer initialized with Azure OpenAI")
            except Exception as e:
                logger.warning(f"Failed to initialize Azure OpenAI client: {str(e)}")
                self.openai_client = None
        else:
            logger.warning("Azure OpenAI not available. Basic semantic analysis only.")
    
    def analyze_table(self, df: pd.DataFrame, table_name: str, source_info: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Generate comprehensive semantic summary and potential analyses for any table.
        
        Args:
            df: DataFrame containing the table data
            table_name: Name/identifier for the table
            source_info: Optional information about the table source (file type, page number, etc.)
            
        Returns:
            Dictionary with enhanced semantic summary and potential analyses
        """
        try:
            # Prepare table data for analysis
            headers = df.columns.tolist()
            data_rows = df.head(10).values.tolist()  # Use first 10 rows for analysis
            total_rows = len(df)
            
            # Extract source information
            source_info = source_info or {}
            
            # Generate enhanced semantic summary if OpenAI is available
            if self.openai_client:
                return self._generate_enhanced_semantic_summary(
                    headers, data_rows, total_rows, table_name, source_info
                )
            else:
                # Fallback to basic analysis
                return self._generate_basic_semantic_summary(
                    headers, data_rows, total_rows, table_name, source_info
                )
                
        except Exception as e:
            logger.error(f"Error analyzing table {table_name}: {str(e)}")
            return self._generate_fallback_summary(table_name, len(df.columns), len(df))
    
    def _generate_enhanced_semantic_summary(self, headers: List[str], data_rows: List[List], 
                                          total_rows: int, table_name: str, 
                                          source_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        Generate enhanced semantic summary using Azure OpenAI.
        
        Args:
            headers: Table column headers
            data_rows: Sample data rows (first 10)
            total_rows: Total number of rows in the table
            table_name: Table name/identifier
            source_info: Source information
            
        Returns:
            Enhanced semantic summary and analyses
        """
        try:
            # Create comprehensive system prompt
            system_message = {
                "role": "system",
                "content": """You are an expert data analyst with deep knowledge across multiple business domains. Analyze the provided table information and create a comprehensive summary with highly specific, actionable potential analyses.

Your response must be in JSON format with this exact structure:
{
    "summary": "A detailed description of what this table represents and its contents",
    "potential_analyses": ["list", "of", "specific", "analyses", "that", "could", "be", "performed"]
}

For the summary (2-4 sentences):
- Be specific about what each column contains, especially binary and categorical columns
- Identify the business domain and context (e.g., financial statements, employee records, sales data, operational metrics, research data)
- If you can infer date/time patterns, mention the time period or frequency
- For categorical columns, mention the key categories when visible in sample data
- Describe the data's business context and potential use cases
- Consider the source type (PDF report, Excel workbook, CSV export, etc.) for context

For potential_analyses (provide 10-20 specific, actionable analyses):
- Tailor analyses to the specific data domain (HR, Finance, Sales, Operations, Healthcare, Education, Research, Manufacturing, etc.)
- Include advanced statistical and business analyses beyond basic descriptives
- For time-based data: trend analysis, seasonality detection, forecasting, growth rate analysis, period-over-period comparisons
- For categorical data: segmentation analysis, performance comparison, distribution analysis, cross-tabulation analysis
- For numerical data: correlation analysis, outlier detection, performance benchmarking, statistical modeling, variance analysis
- For employee/HR data: turnover analysis, performance evaluation, compensation analysis, diversity metrics, productivity assessment
- For financial data: profitability analysis, cost analysis, budget variance, ROI calculation, financial ratios, cash flow analysis
- For sales data: conversion analysis, customer segmentation, revenue optimization, market analysis, sales funnel analysis
- For operational data: efficiency metrics, capacity analysis, quality control, process optimization, resource utilization
- For healthcare data: patient outcome analysis, treatment effectiveness, cost analysis, demographic health trends
- For educational data: performance analysis, graduation rates, demographic analysis, resource allocation
- For research data: statistical significance testing, experimental design analysis, hypothesis testing, data validation
- For manufacturing data: quality control analysis, production efficiency, defect rate analysis, supply chain optimization
- Include predictive and prescriptive analyses where applicable
- Be specific about what business insights each analysis would provide

Examples of comprehensive potential analyses:
- "Employee turnover rate analysis by department and tenure to identify retention risk factors"
- "Salary equity analysis across gender and experience levels with statistical significance testing"
- "Revenue trend analysis with seasonal decomposition and forecasting for next quarter"
- "Customer lifetime value calculation and segmentation for targeted marketing strategies"
- "Cost center performance benchmarking with variance analysis and efficiency metrics"
- "Predictive modeling for employee retention risk using demographic and performance factors"
- "Budget variance analysis with root cause identification and corrective action recommendations"
- "Product performance analysis with market share trends and competitive positioning"
- "Quality control analysis with statistical process control and defect rate monitoring"
- "Patient outcome analysis by treatment type with effectiveness and cost-benefit evaluation"
- "Student performance analysis by demographic factors with intervention recommendations"
- "Supply chain efficiency analysis with bottleneck identification and optimization opportunities"
- "Research hypothesis testing with statistical significance and confidence interval analysis"
- "Manufacturing defect rate analysis with root cause identification and process improvement recommendations"

Focus on providing actionable insights that would help users understand the business value and analytical potential of this table.
                """
            }
            
            # Create detailed human message with context
            source_type = source_info.get('source_type', 'unknown')
            page_number = source_info.get('page_number', 1)
            file_name = source_info.get('file_name', table_name)
            
            human_message = {
                "role": "user",
                "content": f"""Analyze this table and provide both a semantic summary and comprehensive potential analyses:

Table Information:
- Table Name: {table_name}
- Source Type: {source_type} (PDF, Excel, CSV, or image)
- Source File: {file_name}
- Page/Sheet: {page_number}
- Total Rows: {total_rows}
- Total Columns: {len(headers)}

Column Headers: {headers}

Sample Data (first {len(data_rows)} rows):
{data_rows}

Context:
- This table was extracted from a {source_type} source, indicating it's likely part of a formal document, report, spreadsheet, or database export
- The table's structure and content suggest specific business or research applications
- Consider the source type when inferring the table's purpose and analytical potential

Please provide a JSON response with both a detailed summary and comprehensive potential_analyses that reflect the business value and analytical opportunities this table presents."""
            }
            
            # Generate summary using Azure OpenAI
            response = self.openai_client.chat.completions.create(
                model=AZURE_OPENAI_DEPLOYMENT_NAME,
                messages=[system_message, human_message],
                temperature=0.3,
                max_tokens=1500  # Increased for comprehensive analyses
            )
            
            response_content = response.choices[0].message.content.strip()
            
            # Parse JSON response with robust error handling
            try:
                # Clean response content
                clean_content = response_content.strip()
                if clean_content.startswith('```json'):
                    clean_content = clean_content[7:]
                if clean_content.endswith('```'):
                    clean_content = clean_content[:-3]
                clean_content = clean_content.strip()
                
                # Extract JSON
                json_start = clean_content.find('{')
                json_end = clean_content.rfind('}')
                if json_start >= 0 and json_end >= 0:
                    json_str = clean_content[json_start:json_end+1]
                    result = json.loads(json_str)
                else:
                    result = json.loads(clean_content)
                
                # Validate and enhance result
                summary = result.get('summary', '')
                potential_analyses = result.get('potential_analyses', [])
                
                # Ensure minimum quality
                if not summary or len(summary) < 50:
                    summary = self._generate_basic_summary_text(headers, total_rows, table_name, source_info)
                
                if not potential_analyses or len(potential_analyses) < 5:
                    potential_analyses = self._generate_domain_specific_analyses(headers, data_rows)
                
                logger.info(f"✅ Generated enhanced semantic summary for {table_name}: {len(potential_analyses)} analyses")
                
                return {
                    'summary': summary,
                    'potential_analyses': potential_analyses,
                    'analysis_quality': 'enhanced',
                    'analysis_count': len(potential_analyses)
                }
                
            except json.JSONDecodeError as e:
                logger.error(f"JSON parsing failed for table {table_name}: {str(e)}")
                # Try manual extraction
                return self._extract_summary_manually(response_content, headers, total_rows, table_name, source_info)
                
        except Exception as e:
            logger.error(f"Error generating enhanced semantic summary for {table_name}: {str(e)}")
            return self._generate_basic_semantic_summary(headers, data_rows, total_rows, table_name, source_info)
    
    def _generate_basic_semantic_summary(self, headers: List[str], data_rows: List[List], 
                                       total_rows: int, table_name: str, 
                                       source_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        Generate basic semantic summary without AI (fallback).
        
        Args:
            headers: Table column headers
            data_rows: Sample data rows
            total_rows: Total number of rows
            table_name: Table name
            source_info: Source information
            
        Returns:
            Basic semantic summary
        """
        # Generate basic summary text
        summary = self._generate_basic_summary_text(headers, total_rows, table_name, source_info)
        
        # Generate domain-specific analyses based on column names
        potential_analyses = self._generate_domain_specific_analyses(headers, data_rows)
        
        logger.info(f"✅ Generated basic semantic summary for {table_name}: {len(potential_analyses)} analyses")
        
        return {
            'summary': summary,
            'potential_analyses': potential_analyses,
            'analysis_quality': 'basic',
            'analysis_count': len(potential_analyses)
        }
    
    def _generate_basic_summary_text(self, headers: List[str], total_rows: int, 
                                   table_name: str, source_info: Dict[str, Any]) -> str:
        """Generate basic summary text."""
        source_type = source_info.get('source_type', 'unknown')
        file_name = source_info.get('file_name', table_name)
        
        summary_parts = [
            f"Table '{table_name}' extracted from {source_type} source '{file_name}'",
            f"Contains {total_rows} rows and {len(headers)} columns"
        ]
        
        # Add column information
        if headers:
            if len(headers) <= 5:
                summary_parts.append(f"Columns: {', '.join(headers)}")
            else:
                summary_parts.append(f"Columns include: {', '.join(headers[:3])}, and {len(headers)-3} others")
        
        # Infer domain from column names
        domain = self._infer_domain_from_headers(headers)
        if domain:
            summary_parts.append(f"Appears to be {domain} data based on column structure")
        
        return ". ".join(summary_parts) + "."
    
    def _generate_domain_specific_analyses(self, headers: List[str], data_rows: List[List]) -> List[str]:
        """Generate domain-specific analyses based on column names and data."""
        analyses = []
        
        # Convert headers to lowercase for pattern matching
        header_text = ' '.join([str(h).lower() for h in headers])
        
        # Base statistical analyses (always include)
        analyses.extend([
            "Descriptive statistics analysis for all numerical columns",
            "Data quality assessment including missing values and outliers",
            "Column correlation analysis to identify relationships between variables",
            "Distribution analysis and normality testing for numerical data",
            "Categorical variable frequency analysis and cross-tabulation"
        ])
        
        # Domain-specific analyses based on column patterns
        
        # Financial/Business analyses
        if any(term in header_text for term in ['salary', 'revenue', 'cost', 'price', 'amount', 'budget', 'profit', 'income', 'expense']):
            analyses.extend([
                "Financial performance analysis and profitability assessment",
                "Cost-benefit analysis and ROI calculation",
                "Budget variance analysis with trend identification",
                "Revenue forecasting and growth rate analysis",
                "Financial ratio analysis and benchmarking"
            ])
        
        # HR/Employee analyses
        if any(term in header_text for term in ['employee', 'department', 'position', 'hire', 'performance', 'rating', 'tenure', 'manager']):
            analyses.extend([
                "Employee performance evaluation and ranking analysis",
                "Departmental analysis and resource allocation optimization",
                "Workforce diversity and equity analysis",
                "Employee turnover rate analysis by various factors",
                "Compensation analysis and salary equity assessment",
                "Performance improvement trend analysis"
            ])
        
        # Sales/Customer analyses
        if any(term in header_text for term in ['sales', 'customer', 'order', 'product', 'quantity', 'region', 'purchase', 'conversion']):
            analyses.extend([
                "Sales performance analysis by region, product, and time period",
                "Customer segmentation and behavior analysis",
                "Product performance and market analysis",
                "Sales funnel analysis and conversion optimization",
                "Customer lifetime value calculation and analysis",
                "Market share analysis and competitive positioning"
            ])
        
        # Operational/Manufacturing analyses
        if any(term in header_text for term in ['production', 'quality', 'defect', 'efficiency', 'capacity', 'process', 'machine', 'output']):
            analyses.extend([
                "Operational efficiency metrics and capacity analysis",
                "Quality control analysis and defect rate monitoring",
                "Process optimization and bottleneck identification",
                "Production forecasting and resource planning",
                "Equipment performance and maintenance analysis",
                "Supply chain efficiency and optimization analysis"
            ])
        
        # Healthcare analyses
        if any(term in header_text for term in ['patient', 'treatment', 'diagnosis', 'medical', 'health', 'clinical', 'outcome']):
            analyses.extend([
                "Patient outcome analysis by treatment type and demographics",
                "Treatment effectiveness and cost-benefit evaluation",
                "Clinical performance metrics and quality indicators",
                "Healthcare resource utilization analysis",
                "Patient satisfaction and experience analysis",
                "Medical cost analysis and budget optimization"
            ])
        
        # Educational analyses
        if any(term in header_text for term in ['student', 'grade', 'score', 'course', 'school', 'education', 'academic', 'graduation']):
            analyses.extend([
                "Student performance analysis by demographic factors",
                "Academic achievement trend analysis and forecasting",
                "Educational resource allocation and effectiveness analysis",
                "Graduation rate analysis and intervention recommendations",
                "Course performance evaluation and curriculum optimization",
                "Student retention and success factor analysis"
            ])
        
        # Research/Scientific analyses
        if any(term in header_text for term in ['experiment', 'test', 'sample', 'measurement', 'result', 'hypothesis', 'control', 'variable']):
            analyses.extend([
                "Statistical significance testing and hypothesis validation",
                "Experimental design analysis and power calculation",
                "Research data validation and quality assessment",
                "Comparative analysis between experimental groups",
                "Correlation and causation analysis",
                "Research outcome prediction and modeling"
            ])
        
        # Time-based analyses (if date/time columns detected)
        if any(term in header_text for term in ['date', 'time', 'year', 'month', 'day', 'period', 'quarter']):
            analyses.extend([
                "Time series analysis and trend identification",
                "Seasonal pattern detection and forecasting",
                "Growth rate analysis and projection modeling",
                "Period-over-period performance comparison",
                "Cyclical pattern analysis and prediction",
                "Time-based correlation and lag analysis"
            ])
        
        # Geographic analyses
        if any(term in header_text for term in ['location', 'region', 'city', 'state', 'country', 'address', 'zip', 'postal']):
            analyses.extend([
                "Geographic performance analysis and mapping",
                "Regional comparison and benchmarking",
                "Location-based trend analysis and optimization",
                "Geographic market penetration analysis",
                "Spatial clustering and pattern analysis"
            ])
        
        # Remove duplicates and limit to reasonable number
        unique_analyses = list(dict.fromkeys(analyses))  # Preserves order while removing duplicates
        
        # Ensure we have a good number of analyses (10-20)
        if len(unique_analyses) < 10:
            unique_analyses.extend([
                "Predictive modeling and machine learning analysis",
                "Anomaly detection and outlier identification",
                "Clustering analysis to identify data patterns",
                "Regression analysis for relationship modeling",
                "Variance analysis and statistical testing"
            ])
        
        return unique_analyses[:20]  # Limit to 20 analyses
    
    def _infer_domain_from_headers(self, headers: List[str]) -> Optional[str]:
        """Infer the business domain from column headers."""
        header_text = ' '.join([str(h).lower() for h in headers])
        
        domain_patterns = {
            'financial': ['salary', 'revenue', 'cost', 'price', 'amount', 'budget', 'profit'],
            'human resources': ['employee', 'department', 'position', 'hire', 'performance'],
            'sales and marketing': ['sales', 'customer', 'order', 'product', 'purchase'],
            'operational': ['production', 'quality', 'efficiency', 'capacity', 'process'],
            'healthcare': ['patient', 'treatment', 'diagnosis', 'medical', 'health'],
            'educational': ['student', 'grade', 'score', 'course', 'school'],
            'research': ['experiment', 'test', 'sample', 'measurement', 'hypothesis', 'variable']
        }
        
        for domain, keywords in domain_patterns.items():
            if any(keyword in header_text for keyword in keywords):
                return domain
        
        return None
    
    def _extract_summary_manually(self, response_content: str, headers: List[str], 
                                total_rows: int, table_name: str, 
                                source_info: Dict[str, Any]) -> Dict[str, Any]:
        """Manually extract summary and analyses from response when JSON parsing fails."""
        try:
            import re
            
            # Try to find summary and analyses in the text
            summary_match = re.search(r'"summary":\s*"([^"]+)"', response_content, re.DOTALL)
            analyses_match = re.search(r'"potential_analyses":\s*\[(.*?)\]', response_content, re.DOTALL)
            
            summary = summary_match.group(1) if summary_match else None
            analyses = []
            
            if analyses_match:
                analyses_text = analyses_match.group(1)
                analyses = re.findall(r'"([^"]+)"', analyses_text)
            
            if summary and analyses:
                logger.info(f"✅ Manually extracted semantic summary for {table_name}: {len(analyses)} analyses")
                return {
                    'summary': summary,
                    'potential_analyses': analyses,
                    'analysis_quality': 'enhanced_manual',
                    'analysis_count': len(analyses)
                }
        except Exception as e:
            logger.error(f"Manual extraction failed: {str(e)}")
        
        # Final fallback
        return self._generate_basic_semantic_summary(headers, [], total_rows, table_name, source_info)
    
    def _generate_fallback_summary(self, table_name: str, num_columns: int, num_rows: int) -> Dict[str, Any]:
        """Generate minimal fallback summary when all else fails."""
        return {
            'summary': f"Table '{table_name}' with {num_rows} rows and {num_columns} columns",
            'potential_analyses': [
                "Basic descriptive statistics",
                "Data quality assessment",
                "Column analysis and profiling"
            ],
            'analysis_quality': 'fallback',
            'analysis_count': 3
        }

# Create a singleton instance
table_semantic_analyzer = TableSemanticAnalyzer() 