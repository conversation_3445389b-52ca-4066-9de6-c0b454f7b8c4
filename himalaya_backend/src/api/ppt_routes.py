from flask import Blueprint, jsonify, request
from utils.decorators import login_required
from utils.ppt_utils import process_presentation_request

ppt_bp = Blueprint('ppt', __name__)

@ppt_bp.route('/generate', methods=['POST'])
@login_required
def generate_ppt():
    """Handles PowerPoint generation request"""
    try:
        data = request.get_json()
        success, message, result = process_presentation_request(
            data.get('discussion_guide'),
            data.get('discussion_transcripts', []),
            data.get('theme_name'),
            data.get('languages', [])
        )
        
        if success:
            return jsonify({
                "status": "success",
                "message": message,
                "data": result
            })
        else:
            return jsonify({
                "status": "error",
                "message": message,
                "data": result
            }), 500
            
    except Exception as e:
        return jsonify({
            "status": "error",
            "message": str(e),
            "data": None
        }), 500 