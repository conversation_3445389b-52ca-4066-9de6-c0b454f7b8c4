from flask import Blueprint, request, jsonify, current_app
from models.models import db, FGD, Participant, Video, Theme, Language, User, Presentation, ThemeDiscussionGuide
from utils.decorators import login_required, require_scope, SCOPE_CREATE_FGD
from azure.storage.blob import BlobServiceClient, generate_blob_sas, BlobSasPermissions
from datetime import datetime, timedelta
from utils.video_processor import process_video_async
import logging
from .ppt_routes import generate_ppt
import json
from config.settings import (
    AZURE_STORAGE_CONNECTION_STRING,
    AZURE_STORAGE_CONTAINER_NAME,
    FGD_VIDEOS_CONTAINER
)
import uuid

# Setup logger
logger = logging.getLogger(__name__)

fgd_bp = Blueprint('fgd', __name__)

def get_blob_service_client():
    try:
        return BlobServiceClient.from_connection_string(AZURE_STORAGE_CONNECTION_STRING)
    except Exception as e:
        raise Exception(f"Failed to connect to Azure Blob Storage: {str(e)}")

@fgd_bp.route('/themes/<int:theme_id>/languages', methods=['GET'])
@login_required
def get_theme_languages(theme_id):
    try:
        theme = Theme.query.get_or_404(theme_id)
        languages = theme.languages
        return jsonify([{
            'id': lang.id,
            'name': lang.name,
            'code': lang.code
        } for lang in languages]), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@fgd_bp.route('/themes/<int:theme_id>/fgds', methods=['POST'])
@login_required
@require_scope(SCOPE_CREATE_FGD)
def create_fgd(theme_id):
    try:
        theme = Theme.query.get_or_404(theme_id)

        # Check if user is one of the owners
        if request.user.id not in [owner.id for owner in theme.owners]:
            return jsonify({'error': 'Unauthorized to create FGD for this theme'}), 403

        data = request.json
        language_id = data.get('language_id')
        group_size = data.get('group_size')
        conductor_name = data.get('conductor_name')
        discussion_date = data.get('discussion_date')
        country = data.get('country')
        participants_data = data.get('participants')

        if not all([language_id, group_size, conductor_name, discussion_date, participants_data]):
            return jsonify({'error': 'Missing required fields'}), 400

        # Create FGD
        fgd = FGD(
            theme_id=theme_id,
            language_id=language_id,
            group_size=group_size,
            conductor_name=conductor_name,
            discussion_date=datetime.strptime(discussion_date, '%Y-%m-%d'),
            country=country
        )
        db.session.add(fgd)
        db.session.flush()  # Get FGD ID

        # Add participants
        for participant in participants_data:
            participant_obj = Participant(
                fgd_id=fgd.id,
                gender=participant.get('gender'),
                age=participant.get('age'),
                nationality=participant.get('nationality'),
                marital_status=participant.get('marital_status'),
                has_children=participant.get('has_children')
            )
            db.session.add(participant_obj)

        db.session.commit()

        return jsonify({'message': 'FGD created successfully', 'fgd_id': fgd.id}), 201

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@fgd_bp.route('/themes/<int:theme_id>/fgds', methods=['GET'])
@login_required
def get_fgds_by_theme(theme_id):
    try:
        fgds = FGD.query.filter_by(theme_id=theme_id).all()
        return jsonify([{
            'id': fgd.id,
            'language_id': fgd.language_id,
            'group_size': fgd.group_size,
            'conductor_name': fgd.conductor_name,
            'discussion_date': fgd.discussion_date.isoformat(),
            'country': fgd.country,
            'status': fgd.status,
            'participants': [{
                'gender': p.gender,
                'age': p.age,
                'nationality': p.nationality,
                'marital_status': p.marital_status,
                'has_children': p.has_children
            } for p in fgd.participants],
            'videos': [{
                'id': video.id,
                'url': video.blob_url,
                'uploaded_at': video.uploaded_at.isoformat(),
                'transcription_url': video.transcription_blob_url,
                'transcription_json_url': video.transcription_json_url,
                'processing_status': video.processing_status
            } for video in fgd.videos]
        } for fgd in fgds]), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@fgd_bp.route('/fgds/<int:fgd_id>/videos', methods=['POST'])
@login_required
@require_scope(SCOPE_CREATE_FGD)
def upload_video(fgd_id):
    try:
        fgd = FGD.query.get_or_404(fgd_id)

        # Check if user is one of the owners of the theme
        if request.user.id not in [owner.id for owner in fgd.theme.owners]:
            return jsonify({'error': 'Unauthorized to upload video for this FGD'}), 403

        # Get metadata and chunk data
        file_name = request.form.get('file_name')
        total_chunks = int(request.form.get('total_chunks'))
        current_chunk = int(request.form.get('current_chunk'))
        file_chunk = request.files['file_chunk']

        if not file_name or total_chunks is None or current_chunk is None or not file_chunk:
            return jsonify({'error': 'Missing required fields'}), 400

        # Create a unique blob name
        unique_id = str(uuid.uuid4())
        blob_name = f"{unique_id}_{file_name}"

        # Upload chunk to Azure Blob Storage
        blob_service_client = get_blob_service_client()
        container_client = blob_service_client.get_container_client(FGD_VIDEOS_CONTAINER)
        blob_client = container_client.get_blob_client(blob_name)

        # Append the chunk to the blob
        blob_client.upload_blob(file_chunk.read(), blob_type="AppendBlob", overwrite=(current_chunk == 0))

        # If this is the last chunk, finalize the upload
        if current_chunk == total_chunks - 1:
            # Create video entry
            video = Video(
                fgd_id=fgd.id,
                blob_url=blob_client.url
            )
            db.session.add(video)

            # Update FGD status
            fgd.status = 'Video Uploaded'
            db.session.commit()

            return jsonify({'message': 'Video uploaded successfully', 'video_url': video.blob_url}), 201

        return jsonify({'message': 'Chunk uploaded successfully'}), 200

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@fgd_bp.route('/generate-sas-token', methods=['GET'])
@login_required
@require_scope(SCOPE_CREATE_FGD)
def generate_sas_token():
    try:
        blob_service_client = get_blob_service_client()
        # container_client = blob_service_client.get_container_client(CONTAINER_NAME)

        # Generate a unique blob name
        file_name = request.args.get('file_name')
        unique_id = str(uuid.uuid4())
        blob_name = f"fgd_videos/{unique_id}_{file_name}"

        # Generate SAS token
        sas_token = generate_blob_sas(
            account_name=blob_service_client.account_name,
            container_name=FGD_VIDEOS_CONTAINER,
            blob_name=blob_name,
            account_key=blob_service_client.credential.account_key,
            permission=BlobSasPermissions(write=True),
            expiry=datetime.utcnow() + timedelta(hours=1)
        )

        return jsonify({
            'sas_token': sas_token,
            'blob_url': f"https://{blob_service_client.account_name}.blob.core.windows.net/{FGD_VIDEOS_CONTAINER}/{blob_name}"
        }), 200

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@fgd_bp.route('/fgds/<int:fgd_id>/notify-upload', methods=['POST'])
@login_required
def notify_upload(fgd_id):
    try:
        logger.info(f"Received upload notification for FGD {fgd_id}")
        fgd = FGD.query.get_or_404(fgd_id)

        # Check if user is one of the owners of the theme
        if request.user.id not in [owner.id for owner in fgd.theme.owners]:
            logger.warning(f"Unauthorized upload attempt for FGD {fgd_id} by user {request.user.id}")
            return jsonify({'error': 'Unauthorized to notify upload for this FGD'}), 403

        data = request.json
        blob_url = data.get('blob_url')
        original_file_name = data.get('file_name')  # Get original file name from request
        logger.info(f"Blob URL received: {blob_url}, Original filename: {original_file_name}")

        if not blob_url:
            return jsonify({'error': 'Missing blob URL'}), 400

        # Create video entry with original file name
        video = Video(
            fgd_id=fgd.id,
            blob_url=blob_url,
            processing_status='pending',
            file_name=original_file_name  # Store the original file name
        )
        db.session.add(video)

        # Update FGD status to Video Uploaded initially
        fgd.status = 'Video Uploaded'
        db.session.commit()
        logger.info(f"Created video entry with ID {video.id}")

        # Start background processing with app context
        process_video_async(video.id, current_app._get_current_object())
        logger.info(f"Started background processing for video {video.id}")

        return jsonify({
            'message': 'Upload notification received successfully',
            'video_id': video.id,
            'status': 'processing'
        }), 200

    except Exception as e:
        logger.error(f"Error in notify_upload: {str(e)}")
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

# Add a new endpoint to check processing status
@fgd_bp.route('/videos/<int:video_id>/status', methods=['GET'])
@login_required
def get_video_status(video_id):
    try:
        video = Video.query.get_or_404(video_id)
        
        return jsonify({
            'video_id': video.id,
            'status': video.processing_status,
            'error_message': video.error_message,
            'transcription': video.transcription_text if video.processing_status == 'completed' else None,
            'audio_url': video.audio_blob_url if video.processing_status == 'completed' else None,
            'transcription_url': video.transcription_blob_url if video.processing_status == 'completed' else None,
            'transcription_json_url': video.transcription_json_url if video.processing_status == 'completed' else None
        }), 200

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@fgd_bp.route('/themes/<int:theme_id>/presentation_eligible', methods=['GET'])
@login_required
def get_presentation_eligible_videos(theme_id):
    try:
        # Query FGDs associated with the theme
        fgds = FGD.query.filter_by(theme_id=theme_id).all()
        
        # Collect videos with completed transcriptions and include FGD information
        eligible_videos = []
        for fgd in fgds:
            for video in fgd.videos:
                if video.processing_status == 'completed':
                    eligible_videos.append({
                        'video_id': video.id,
                        'blob_url': video.blob_url,
                        'uploaded_at': video.uploaded_at.isoformat(),
                        'processed_at': video.processed_at.isoformat() if video.processed_at else None,
                        'original_file_name': video.file_name,  # Assuming file_name is stored in the Video model
                        'fgd': {
                            'id': fgd.id,
                            'language_id': fgd.language_id,
                            'group_size': fgd.group_size,
                            'conductor_name': fgd.conductor_name,
                            'discussion_date': fgd.discussion_date.isoformat(),
                            'country': fgd.country,
                            'status': fgd.status
                        }
                    })
        
        return jsonify(eligible_videos), 200

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@fgd_bp.route('/themes/<int:theme_id>/presentations', methods=['POST'])
@login_required
@require_scope(SCOPE_CREATE_FGD)
def create_presentation(theme_id):
    try:
        logger.info(f"Starting presentation creation for theme {theme_id}")
        
        # Get theme and validate it exists
        theme = Theme.query.get_or_404(theme_id)
        
        # Get video IDs from request
        data = request.json
        video_ids = data.get('video_ids', [])
        if not video_ids:
            return jsonify({'error': 'No video IDs provided'}), 400

        logger.info(f"Processing videos: {video_ids}")

        # Get videos and validate they exist and belong to the theme's FGDs
        videos = Video.query.filter(Video.id.in_(video_ids)).all()
        if len(videos) != len(video_ids):
            return jsonify({'error': 'One or more videos not found'}), 404

        # Validate all videos belong to the theme's FGDs
        theme_fgd_ids = [fgd.id for fgd in theme.fgds]
        for video in videos:
            if video.fgd_id not in theme_fgd_ids:
                return jsonify({'error': f'Video {video.id} does not belong to theme {theme_id}'}), 400

        # Get discussion guide for the theme
        base_guide = ThemeDiscussionGuide.query.filter_by(
            theme_id=theme_id,
            is_base_language=True
        ).first()
        
        if not base_guide:
            return jsonify({'error': 'No base discussion guide found for theme'}), 400

        # Get blob service client for generating SAS tokens
        blob_service_client = get_blob_service_client()

        # Generate SAS token for discussion guide
        guide_url = base_guide.guide_url
        guide_container = 'generalaisearch'
        guide_blob_name = guide_url.split(f'{guide_container}/')[-1]
        
        guide_sas_token = generate_blob_sas(
            account_name=blob_service_client.account_name,
            container_name=guide_container,
            blob_name=guide_blob_name,
            account_key=blob_service_client.credential.account_key,
            permission=BlobSasPermissions(read=True),
            expiry=datetime.utcnow() + timedelta(hours=1)
        )
        guide_url_with_sas = f"{guide_url}?{guide_sas_token}"

        # Prepare data for PPT generation
        discussion_transcripts = []
        languages = set()
        
        for video in videos:
            if not video.transcription_blob_url:
                return jsonify({'error': f'Video {video.id} has no transcription URL'}), 400
                
            fgd = video.fgd
            languages.add(fgd.language.code)
            
            # Generate SAS token for transcription
            trans_url = video.transcription_blob_url
            trans_container = 'fgd-videos'
            trans_blob_name = trans_url.split(f'{trans_container}/')[-1]
            
            trans_sas_token = generate_blob_sas(
                account_name=blob_service_client.account_name,
                container_name=trans_container,
                blob_name=trans_blob_name,
                account_key=blob_service_client.credential.account_key,
                permission=BlobSasPermissions(read=True),
                expiry=datetime.utcnow() + timedelta(hours=1)
            )
            trans_url_with_sas = f"{trans_url}?{trans_sas_token}"
            
            # Just append the URL string instead of a dictionary
            discussion_transcripts.append(trans_url_with_sas)

        logger.info(f"Calling PPT generation with {len(discussion_transcripts)} transcripts")

        # Call PPT generation with the URLs
        from utils.ppt_utils import process_presentation_request
        success, message, result = process_presentation_request(
            discussion_guide_url=guide_url_with_sas,
            transcript_urls=discussion_transcripts,  # Now it's a list of URL strings
            theme_name=theme.name,
            languages=list(languages)
        )

        if not success:
            logger.error(f"PPT generation failed: {message}")
            return jsonify({'error': 'Failed to generate presentation', 'details': message}), 500

        # Store presentation in database
        presentation = Presentation(
            theme_id=theme_id,
            ppt_url=result['sharepoint']['file_url']
        )
        db.session.add(presentation)
        db.session.commit()

        logger.info(f"Successfully created presentation with ID {presentation.id}")

        return jsonify({
            'message': 'Presentation created successfully',
            'presentation_id': presentation.id,
            'presentation_url': presentation.ppt_url
        }), 201

    except Exception as e:
        logger.error(f"Error in create_presentation: {str(e)}")
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@fgd_bp.route('/themes/<int:theme_id>/presentations', methods=['GET'])
@login_required
def get_theme_presentations(theme_id):
    try:
        logger.info(f"Fetching presentations for theme {theme_id}")
        
        # Get theme and validate it exists
        theme = Theme.query.get_or_404(theme_id)
        
        # Query presentations for the theme
        presentations = Presentation.query.filter_by(theme_id=theme_id).order_by(Presentation.created_at.desc()).all()
        
        # Format the response
        presentation_list = [{
            'presentation_id': presentation.id,
            'presentation_url': presentation.ppt_url,
            'presentation_name': presentation.ppt_url.split('/')[-1].split('?')[0],  # Extract filename from URL
            'created_at': presentation.created_at.isoformat(),
            'theme': {
                'id': theme.id,
                'name': theme.name
            }
        } for presentation in presentations]
        
        return jsonify({
            'theme_id': theme_id,
            'presentations': presentation_list
        }), 200

    except Exception as e:
        logger.error(f"Error in get_theme_presentations: {str(e)}")
        return jsonify({'error': str(e)}), 500

