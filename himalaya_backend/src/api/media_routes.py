from flask import Blueprint, request, jsonify, current_app
from models.models import db, File, User
from utils.decorators import login_required
from azure.storage.blob import BlobServiceClient, generate_blob_sas, BlobSasPermissions
from datetime import datetime, timedelta
from functools import wraps
from utils.media_processor import process_media_async
import logging
import uuid
from config.settings import (
    AZURE_STORAGE_CONNECTION_STRING,
    AZURE_STORAGE_CONTAINER_NAME,
    GENERAL_MEDIA_CONTAINER
)

media_bp = Blueprint('media', __name__)
logger = logging.getLogger(__name__)
# Scope constants
SCOPE_VIEW_FILES = 4  # VIEW_FILE
SCOPE_UPLOAD_FILE = 2  # UPLOAD_FILE
SCOPE_REMOVE_FILE = 3  # REMOVE_FILE

def require_scope(scope_id):
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            user = User.query.get(request.user.id)
            if not user or not user.scopes or scope_id not in user.scopes:
                return jsonify({
                    'error': 'Unauthorized',
                    'message': 'You do not have the required permissions for this operation'
                }), 403
            return f(*args, **kwargs)
        return decorated_function
    return decorator


def get_blob_service_client():
    try:
        return BlobServiceClient.from_connection_string(AZURE_STORAGE_CONNECTION_STRING)
    except Exception as e:
        raise Exception(f"Failed to connect to Azure Blob Storage: {str(e)}")


@media_bp.route('/generate-sas-token', methods=['GET'])
@login_required
@require_scope(SCOPE_UPLOAD_FILE)
def generate_media_sas_token():
    try:
        blob_service_client = get_blob_service_client()
        
        file_name = request.args.get('file_name')
        media_type = request.args.get('media_type')  # 'video' or 'audio'
        
        if not all([file_name, media_type]) or media_type not in ['video', 'audio']:
            return jsonify({'error': 'Invalid request parameters'}), 400

        unique_id = str(uuid.uuid4())
        blob_name = f"{media_type}/{unique_id}_{file_name}"

        # Generate SAS token
        sas_token = generate_blob_sas(
            account_name=blob_service_client.account_name,
            container_name=GENERAL_MEDIA_CONTAINER,
            blob_name=blob_name,
            account_key=blob_service_client.credential.account_key,
            permission=BlobSasPermissions(write=True),
            expiry=datetime.utcnow() + timedelta(hours=1)
        )

        return jsonify({
            'sas_token': sas_token,
            'blob_url': f"https://{blob_service_client.account_name}.blob.core.windows.net/{GENERAL_MEDIA_CONTAINER}/{blob_name}"
        }), 200

    except Exception as e:
        return jsonify({'error': str(e)}), 500


@media_bp.route('/notify-upload', methods=['POST'])
@login_required
@require_scope(SCOPE_UPLOAD_FILE)
def notify_media_upload():
    try:
        data = request.json
        blob_url = data.get('blob_url')
        file_name = data.get('file_name')
        media_type = data.get('media_type')
        file_type_id = data.get('file_type_id')
        department_id = data.get('department_id')
        vertical_id = data.get('vertical_id')
        sensitivity_id = data.get('sensitivity_id', 4)

        if not all([blob_url, file_name, media_type]) or media_type not in ['video', 'audio']:
            return jsonify({'error': 'Missing required fields'}), 400

        # Create file entry
        file = File(
            file_name=file_name,
            file_type_id=file_type_id,
            department_id=department_id,
            vertical_id=vertical_id,
            blob_url=blob_url,
            uploaded_by=request.user.id,
            media_type=media_type,
            processing_status='pending',
            sensitivity_id=sensitivity_id
        )
        db.session.add(file)
        db.session.commit()

        # Start background processing
        process_media_async(file.id, current_app._get_current_object())

        return jsonify({
            'message': 'Upload notification received successfully',
            'file_id': file.id,
            'status': 'processing'
        }), 200

    except Exception as e:
        db.session.rollback()
        logger.error(f"Error in notify_media_upload: {str(e)}")
        return jsonify({'error': str(e)}), 500

@media_bp.route('/<int:file_id>/status', methods=['GET'])
@login_required
def get_media_status(file_id):
    try:
        file = File.query.get_or_404(file_id)
        
        return jsonify({
            'file_id': file.id,
            'status': file.processing_status,
            'error_message': file.error_message,
            'processed_at': file.processed_at.isoformat() if file.processed_at else None,
            'transcription_url': file.transcription_blob_url
        }), 200

    except Exception as e:
        return jsonify({'error': str(e)}), 500 