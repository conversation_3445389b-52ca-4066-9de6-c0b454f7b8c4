from flask import Blueprint, request, jsonify
from models.models import db, FGDChatSession, FGDChatMessage, Video, FGD, Theme
from utils.decorators import login_required, require_scope, SCOPE_CHAT_FGD
from utils.openai_utils import get_ai_response, rephrase_question, get_fgd_ai_response, get_video_chat_response
from datetime import datetime
from openai import AzureOpenAI
from azure.core.credentials import AzureKeyCredential
from azure.search.documents import SearchClient
import logging
from utils.blob_utils import generate_video_sas_url
from config.settings import (
    AZURE_OPENAI_KEY, AZURE_OPENAI_API_VERSION, AZURE_OPENAI_ENDPOINT,
    AZURE_OPENAI_DEPLOYMENT_NAME, AZURE_OPENAI_EMBEDDING_DEPLOYMENT,
    AZURE_SEARCH_SERVICE_ENDPOINT, AZURE_SEARCH_FGD_VIDEO_INDEX_NAME,
    AZURE_SEARCH_ADMIN_KEY
)

# Add logger
logger = logging.getLogger(__name__)

fgd_chat_bp = Blueprint('fgd_chat', __name__)

# Initialize Azure OpenAI
openai_client = AzureOpenAI(
    api_key=AZURE_OPENAI_KEY,
    api_version=AZURE_OPENAI_API_VERSION,
    azure_endpoint=AZURE_OPENAI_ENDPOINT
)

# Initialize Azure Search client for FGD videos
search_client = SearchClient(
    endpoint=AZURE_SEARCH_SERVICE_ENDPOINT,
    index_name=AZURE_SEARCH_FGD_VIDEO_INDEX_NAME,
    credential=AzureKeyCredential(AZURE_SEARCH_ADMIN_KEY)
)

@fgd_chat_bp.route('/fgd-chat/sessions', methods=['GET'])
@login_required
@require_scope(SCOPE_CHAT_FGD)
def get_sessions():
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 10, type=int)
        
        sessions = FGDChatSession.query.filter_by(
            user_id=request.user.id, is_active=True
        ).order_by(FGDChatSession.updated_at.desc()).paginate(
            page=page,
            per_page=per_page,
            error_out=False
        )

        return jsonify({
            'items': [{
                'id': session.id,
                'session_name': session.session_name,
                'created_at': session.created_at.isoformat(),
                'updated_at': session.updated_at.isoformat(),
                'is_active': session.is_active,
                'message_count': len(session.messages)
            } for session in sessions.items],
            'pagination': {
                'total_items': sessions.total,
                'total_pages': sessions.pages,
                'current_page': sessions.page,
                'per_page': per_page,
                'has_next': sessions.has_next,
                'has_prev': sessions.has_prev
            }
        }), 200

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@fgd_chat_bp.route('/fgd-chat/sessions', methods=['POST'])
@login_required
@require_scope(SCOPE_CHAT_FGD)
def create_session():
    try:
        data = request.json
        session_name = data.get('session_name', f"FGD Chat {datetime.now().strftime('%Y-%m-%d %H:%M')}")

        session = FGDChatSession(
            user_id=request.user.id,
            session_name=session_name
        )
        db.session.add(session)
        db.session.commit()

        return jsonify({
            'id': session.id,
            'session_name': session.session_name,
            'created_at': session.created_at.isoformat()
        }), 201

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@fgd_chat_bp.route('/fgd-chat/sessions/<int:session_id>', methods=['PUT'])
@login_required
@require_scope(SCOPE_CHAT_FGD)
def update_session(session_id):
    try:
        data = request.json
        if not data or 'session_name' not in data:
            return jsonify({'error': 'No session name provided'}), 400

        session = FGDChatSession.query.filter_by(
            id=session_id, 
            user_id=request.user.id
        ).first_or_404()

        session.session_name = data['session_name']
        session.updated_at = datetime.utcnow()
        db.session.commit()

        return jsonify({
            'id': session.id,
            'session_name': session.session_name,
            'updated_at': session.updated_at.isoformat()
        }), 200

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@fgd_chat_bp.route('/fgd-chat/sessions/<int:session_id>/messages', methods=['GET'])
@login_required
@require_scope(SCOPE_CHAT_FGD)
def get_session_messages(session_id):
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)
        
        session = FGDChatSession.query.filter_by(
            id=session_id, 
            user_id=request.user.id
        ).first_or_404()

        messages = FGDChatMessage.query.filter_by(
            session_id=session_id
        ).order_by(FGDChatMessage.created_at).paginate(
            page=page,
            per_page=per_page,
            error_out=False
        )

        # Process messages and refresh SAS tokens for video URLs
        processed_messages = []
        for msg in messages.items:
            message_data = {
                'id': msg.id,
                'question': msg.question,
                'answer': msg.answer,
                'selected_video_ids': msg.selected_video_ids,
                'created_at': msg.created_at.isoformat(),
                'metadata': msg.search_metadata
            }

            # If metadata contains video_url, refresh the SAS token
            if msg.search_metadata and 'video_url' in msg.search_metadata:
                # Get the original blob URL from the video
                if msg.selected_video_ids and len(msg.selected_video_ids) > 0:
                    video = Video.query.get(msg.selected_video_ids[0])
                    if video and video.blob_url:
                        # Generate fresh SAS URL
                        fresh_sas_url = generate_video_sas_url(video.blob_url)
                        message_data['metadata']['video_url'] = fresh_sas_url

            processed_messages.append(message_data)

        return jsonify({
            'items': processed_messages,
            'pagination': {
                'total_items': messages.total,
                'total_pages': messages.pages,
                'current_page': messages.page,
                'per_page': per_page,
                'has_next': messages.has_next,
                'has_prev': messages.has_prev
            }
        }), 200

    except Exception as e:
        logger.error(f"Error in get_session_messages: {str(e)}")
        return jsonify({'error': str(e)}), 500

@fgd_chat_bp.route('/fgd-chat/sessions/<int:session_id>/messages', methods=['POST'])
@login_required
@require_scope(SCOPE_CHAT_FGD)
def add_message(session_id):
    try:
        session = FGDChatSession.query.filter_by(
            id=session_id, 
            user_id=request.user.id
        ).first_or_404()

        data = request.json
        if not data or 'question' not in data or 'type' not in data:
            return jsonify({'error': 'Missing required fields'}), 400

        search_type = data['type']
        if search_type not in ['all_files', 'selected_theme', 'filter', 'selected_videos', 'chat_video']:
            return jsonify({'error': 'Invalid search type'}), 400

        # Get previous messages for context
        previous_messages = FGDChatMessage.query.filter_by(
            session_id=session_id
        ).order_by(FGDChatMessage.created_at.desc()).limit(5).all()

        # Format conversation history
        conversation_history = "\n\n".join([
            f"Previous Question: {msg.question}\nPrevious Answer: {msg.answer}"
            for msg in reversed(previous_messages)
        ])

        # Rephrase the question if there's conversation history
        question_data = rephrase_question(
            data['question'],
            conversation_history if previous_messages else None
        )

        # Use the rephrased question for vector search
        response = openai_client.embeddings.create(
            input=question_data['rephrased_question'],
            model=AZURE_OPENAI_EMBEDDING_DEPLOYMENT
        )
        vector = response.data[0].embedding

        # Initialize search client for FGD videos
        search_client = SearchClient(
            endpoint=AZURE_SEARCH_SERVICE_ENDPOINT,
            index_name=AZURE_SEARCH_FGD_VIDEO_INDEX_NAME,
            credential=AzureKeyCredential(AZURE_SEARCH_ADMIN_KEY)
        )

        # Create base vector query
        vector_query = {
            "vector": vector,
            "fields": "text_vector",
            "k": 10,
            "kind": "vector"
        }

        # Initialize filter string
        blob_filter = None

        # Handle different search types
        if search_type == 'selected_theme':
            theme_id = data.get('theme_id')
            if not theme_id:
                return jsonify({'error': 'Theme ID is required for selected_theme type'}), 400

            # Get all videos under the theme
            theme = Theme.query.get_or_404(theme_id)
            video_blob_names = []
            for fgd in theme.fgds:
                for video in fgd.videos:
                    if video.vector_blob_url:  # Only include videos with vector files
                        blob_name = video.vector_blob_url.split('/')[-1]
                        video_blob_names.append(blob_name)

            if not video_blob_names:
                
                #insert a message into the database
                message = FGDChatMessage(
                    session_id=session_id,
                    question=data['question'],
                    answer="No video transcriptions found for the selected theme" ,
                    created_at=datetime.utcnow()
                )
                db.session.add(message)
                db.session.commit()
                
                
                return  jsonify({
                    'message': "No video transcriptions found for the selected theme" ,
                    'id': message.id,
                    'question': data['question'],
                    'answer': "No video transcriptions found for the selected theme" ,
                    'created_at': message.created_at.isoformat()
                }), 201

            blob_filter = " or ".join([f"metadata_storage_name eq '{name}'" for name in video_blob_names])

        elif search_type == 'selected_videos':
            video_ids = data.get('video_ids', [])
            if not video_ids:
                return jsonify({'error': 'Video IDs are required for selected_videos type'}), 400

            # Get the selected videos
            videos = Video.query.filter(Video.id.in_(video_ids)).all()
            video_blob_names = []
            for video in videos:
                if video.vector_blob_url:
                    blob_name = video.vector_blob_url.split('/')[-1]
                    video_blob_names.append(blob_name)

            if not video_blob_names:
                return jsonify({'error': 'No video transcriptions found for the selected videos'}), 400

            blob_filter = " or ".join([f"metadata_storage_name eq '{name}'" for name in video_blob_names])

        # Add special handling for chat_video type
        if search_type == 'chat_video':
            video_id = data.get('video_id')
            if not video_id:
                return jsonify({'error': 'Video ID is required for chat_video type'}), 400

            # Get the video
            video = Video.query.get_or_404(video_id)
            
            # Get the transcription JSON from blob
            if not video.transcription_json_url:
                return jsonify({'error': 'Video transcription not available'}), 400

            # Get AI response with timestamps
            ai_response = get_video_chat_response(
                data['question'],
                video.transcription_json_url,
                conversation_history if previous_messages else None
            )
            import json
            with open('ai_response.json', 'w') as f:
                json.dump(ai_response, f)

            # Generate SAS URL for video playback
            video_sas_url = generate_video_sas_url(video.blob_url)

            # Save the message
            message = FGDChatMessage(
                session_id=session_id,
                question=data['question'],
                answer=ai_response['answer'],
                selected_video_ids=[video_id],
                token_usage=ai_response.get('token_usage'),
                search_metadata={
                    'video_id': video_id,
                    'timestamps': ai_response.get('timestamps'),
                    'video_url': video_sas_url
                }
            )
            db.session.add(message)
            session.updated_at = datetime.utcnow()
            db.session.commit()

            return jsonify({
                'id': message.id,
                'question': message.question,
                'answer': message.answer,
                'created_at': message.created_at.isoformat(),
                'metadata': {
                    'video_id': video_id,
                    'video_url': video_sas_url,
                    'timestamps': ai_response.get('timestamps')
                }
            }), 201

        # Perform the search
        search_params = {
            "vector_queries": [vector_query],
            "select": ["chunk", "chunk_id", "metadata_storage_name", "title"],
            "top": 10
        }
        if blob_filter:
            search_params["filter"] = blob_filter

        results = search_client.search(
            search_text=None,
            **search_params
        )

        # Format search results and update FGD status
        search_results = []
        processed_fgds = set()  # Keep track of processed FGDs
        
        for result in results:
            blob_name = result['metadata_storage_name']
            # Find the corresponding video
            video = Video.query.filter(Video.vector_blob_url.endswith(blob_name)).first()
            
            if video:
                # Update FGD status if not already processed
                if video.fgd_id not in processed_fgds and video.fgd.status == "Video Uploaded":
                    video.fgd.status = "AI Processed"
                    processed_fgds.add(video.fgd_id)
                
                search_results.append({
                    'chunk': result['chunk'],
                    'chunk_id': result['chunk_id'],
                    'score': result['@search.score'],
                    'file_info': {
                        'id': video.id,
                        'file_name': video.file_name,
                        'fgd_id': video.fgd_id,
                        'theme_id': video.fgd.theme_id,
                        'uploaded_at': video.uploaded_at.isoformat(),
                        'blob_url': video.blob_url,
                        'type': 'video',
                        'fgd_info': {
                            'id': video.fgd.id,
                            'conductor_name': video.fgd.conductor_name,
                            'discussion_date': video.fgd.discussion_date.isoformat(),
                            'country': video.fgd.country
                        }
                    }
                })

        # Get AI response using the FGD-specific function
        ai_response = get_fgd_ai_response(
            data['question'],
            search_results,
            conversation_history if previous_messages else None,
            rephrased_question=question_data['rephrased_question']
        )

        # Save the message with original response
        message = FGDChatMessage(
            session_id=session_id,
            question=data['question'],
            answer=ai_response['answer'],
            selected_video_ids=data.get('video_ids', []),
            token_usage=ai_response.get('token_usage'),
            search_metadata={
                **ai_response.get('metadata', {}),
                'question_rephrasing': question_data,
                'search_type': search_type,
                'theme_id': data.get('theme_id'),
                'video_ids': data.get('video_ids', []),
                'search_results': search_results  # Keep search results in metadata
            }
        )
        db.session.add(message)

        # Update session timestamp
        session.updated_at = datetime.utcnow()
        db.session.commit()

        return jsonify({
            'id': message.id,
            'question': message.question,
            'answer': message.answer,
            'created_at': message.created_at.isoformat(),
            'metadata': message.search_metadata
        }), 201

    except Exception as e:
        db.session.rollback()
        logger.error(f"Error in add_message: {str(e)}")
        return jsonify({'error': str(e)}), 500 