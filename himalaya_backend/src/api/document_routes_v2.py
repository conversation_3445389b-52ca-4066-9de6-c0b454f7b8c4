"""
Enhanced Document Processing API Routes (v2)

This module provides enhanced document processing endpoints that integrate with
the new document type detection, specialized processing agents, and Azure AI Search.
"""

from flask import Blueprint, request, jsonify
from models.models import db, File, FileProcessingMetadata, DocumentChunk, DocumentTable
from utils.decorators import login_required
from services.enhanced_processing_service import enhanced_processing_service
from services.document_type_detector import document_type_detector
from services.azure_search_service import azure_search_service
from config.settings import ENHANCED_PROCESSING_ENABLED, AZURE_SEARCH_ENHANCED_INDEX_NAME
import logging
from functools import wraps

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create blueprint
document_v2_bp = Blueprint('document_v2', __name__)

# Scope constants
SCOPE_VIEW_FILES = 4  # VIEW_FILE
SCOPE_UPLOAD_FILE = 2  # UPLOAD_FILE
SCOPE_REMOVE_FILE = 3  # REMOVE_FILE

def require_scope(scope_id):
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            from models.models import User
            user = User.query.get(request.user.id)
            if not user or not user.scopes or scope_id not in user.scopes:
                return jsonify({
                    'error': 'Unauthorized',
                    'message': 'You do not have the required permissions for this operation'
                }), 403
            return f(*args, **kwargs)
        return decorated_function
    return decorator

@document_v2_bp.route('/v2/documents/process', methods=['POST'])
@login_required
@require_scope(SCOPE_UPLOAD_FILE)
def process_document_enhanced():
    """
    Process a document with enhanced document processing capabilities.
    
    This endpoint triggers enhanced processing for an existing file or processes
    a new file with enhanced capabilities.
    """
    try:
        if not ENHANCED_PROCESSING_ENABLED:
            return jsonify({
                'error': 'Enhanced processing disabled',
                'message': 'Enhanced document processing is currently disabled'
            }), 503
        
        # Get file ID from request
        data = request.get_json()
        if not data or 'file_id' not in data:
            return jsonify({
                'error': 'Missing file_id',
                'message': 'file_id is required for enhanced processing'
            }), 400
        
        file_id = data['file_id']
        force_reprocess = data.get('force_reprocess', False)
        
        # Validate file exists
        file_record = File.query.get(file_id)
        if not file_record:
            return jsonify({
                'error': 'File not found',
                'message': f'File with ID {file_id} not found'
            }), 404
        
        # Check user permissions for this file
        # Add your permission logic here based on your system's requirements
        
        logger.info(f"Starting enhanced processing for file {file_id} by user {request.user.id}")
        
        # Process the file
        result = enhanced_processing_service.process_file(file_id, force_reprocess)
        
        # Return appropriate response based on result status
        if result['status'] == 'completed':
            return jsonify({
                'success': True,
                'message': 'Enhanced processing completed successfully',
                'data': result
            }), 200
        elif result['status'] == 'already_processed':
            return jsonify({
                'success': True,
                'message': 'File already processed',
                'data': result
            }), 200
        elif result['status'] == 'skipped':
            return jsonify({
                'success': False,
                'message': result['message'],
                'data': result
            }), 200
        else:
            return jsonify({
                'success': False,
                'message': result['message'],
                'data': result
            }), 500
            
    except Exception as e:
        logger.error(f"Error in enhanced document processing: {str(e)}")
        return jsonify({
            'error': 'Processing failed',
            'message': f'Enhanced processing failed: {str(e)}'
        }), 500

@document_v2_bp.route('/v2/documents/<int:file_id>/reprocess', methods=['POST'])
@login_required
@require_scope(SCOPE_UPLOAD_FILE)
def reprocess_document(file_id):
    """
    Reprocess an existing document with enhanced processing.
    """
    try:
        if not ENHANCED_PROCESSING_ENABLED:
            return jsonify({
                'error': 'Enhanced processing disabled',
                'message': 'Enhanced document processing is currently disabled'
            }), 503
        
        # Validate file exists
        file_record = File.query.get(file_id)
        if not file_record:
            return jsonify({
                'error': 'File not found',
                'message': f'File with ID {file_id} not found'
            }), 404
        
        logger.info(f"Reprocessing file {file_id} by user {request.user.id}")
        
        # Force reprocess the file
        result = enhanced_processing_service.process_file(file_id, force_reprocess=True)
        
        return jsonify({
            'success': True,
            'message': 'File reprocessing completed',
            'data': result
        }), 200
        
    except Exception as e:
        logger.error(f"Error reprocessing document {file_id}: {str(e)}")
        return jsonify({
            'error': 'Reprocessing failed',
            'message': f'Document reprocessing failed: {str(e)}'
        }), 500

@document_v2_bp.route('/v2/documents/<int:file_id>/status', methods=['GET'])
@login_required
@require_scope(SCOPE_VIEW_FILES)
def get_processing_status(file_id):
    """
    Get detailed processing status and metadata for a document.
    """
    try:
        # Validate file exists
        file_record = File.query.get(file_id)
        if not file_record:
            return jsonify({
                'error': 'File not found',
                'message': f'File with ID {file_id} not found'
            }), 404
        
        # Get processing metadata
        processing_metadata = file_record.processing_metadata
        
        # Get chunks and tables count
        chunks_count = DocumentChunk.query.filter_by(file_id=file_id).count()
        tables_count = DocumentTable.query.filter_by(file_id=file_id).count()
        
        # Prepare response
        response_data = {
            'file_id': file_id,
            'file_name': file_record.file_name,
            'enhanced_processing_enabled': ENHANCED_PROCESSING_ENABLED,
            'chunks_count': chunks_count,
            'tables_count': tables_count
        }
        
        if processing_metadata:
            response_data['processing_metadata'] = {
                'document_type': processing_metadata.document_type,
                'enhanced_processing_status': processing_metadata.enhanced_processing_status,
                'page_count': processing_metadata.page_count,
                'table_count': processing_metadata.table_count,
                'chunk_count': processing_metadata.chunk_count,
                'word_count': processing_metadata.word_count,
                'char_count': processing_metadata.char_count,
                'processing_confidence_score': processing_metadata.processing_confidence_score,
                'extraction_quality_score': processing_metadata.extraction_quality_score,
                'processing_started_at': processing_metadata.processing_started_at.isoformat() if processing_metadata.processing_started_at else None,
                'enhanced_processed_at': processing_metadata.enhanced_processed_at.isoformat() if processing_metadata.enhanced_processed_at else None,
                'error_details': processing_metadata.error_details,
                'content_summary': processing_metadata.content_summary
            }
        else:
            response_data['processing_metadata'] = None
            response_data['message'] = 'No enhanced processing metadata found'
        
        return jsonify({
            'success': True,
            'data': response_data
        }), 200
        
    except Exception as e:
        logger.error(f"Error getting processing status for file {file_id}: {str(e)}")
        return jsonify({
            'error': 'Status retrieval failed',
            'message': f'Failed to get processing status: {str(e)}'
        }), 500

@document_v2_bp.route('/v2/documents/<int:file_id>/chunks', methods=['GET'])
@login_required
@require_scope(SCOPE_VIEW_FILES)
def get_document_chunks(file_id):
    """
    Get all chunks for a specific document.
    """
    try:
        # Validate file exists
        file_record = File.query.get(file_id)
        if not file_record:
            return jsonify({
                'error': 'File not found',
                'message': f'File with ID {file_id} not found'
            }), 404
        
        # Get pagination parameters
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 10, type=int)
        per_page = min(per_page, 100)  # Limit to 100 chunks per page
        
        # Get chunks with pagination
        chunks_query = DocumentChunk.query.filter_by(file_id=file_id).order_by(DocumentChunk.chunk_index)
        chunks_paginated = chunks_query.paginate(page=page, per_page=per_page, error_out=False)
        
        # Serialize chunks
        chunks_data = []
        for chunk in chunks_paginated.items:
            chunks_data.append({
                'id': chunk.id,
                'chunk_index': chunk.chunk_index,
                'content': chunk.content,
                'word_count': chunk.word_count,
                'char_count': chunk.char_count,
                'chunk_metadata': chunk.chunk_metadata,
                'azure_search_doc_id': chunk.azure_search_doc_id,
                'created_at': chunk.created_at.isoformat() if chunk.created_at else None
            })
        
        return jsonify({
            'success': True,
            'data': {
                'chunks': chunks_data,
                'pagination': {
                    'page': page,
                    'per_page': per_page,
                    'total': chunks_paginated.total,
                    'pages': chunks_paginated.pages,
                    'has_next': chunks_paginated.has_next,
                    'has_prev': chunks_paginated.has_prev
                }
            }
        }), 200
        
    except Exception as e:
        logger.error(f"Error getting chunks for file {file_id}: {str(e)}")
        return jsonify({
            'error': 'Chunks retrieval failed',
            'message': f'Failed to get document chunks: {str(e)}'
        }), 500

@document_v2_bp.route('/v2/documents/<int:file_id>/tables', methods=['GET'])
@login_required
@require_scope(SCOPE_VIEW_FILES)
def get_document_tables(file_id):
    """
    Get all tables for a specific document.
    """
    try:
        # Validate file exists
        file_record = File.query.get(file_id)
        if not file_record:
            return jsonify({
                'error': 'File not found',
                'message': f'File with ID {file_id} not found'
            }), 404
        
        # Get tables
        tables = DocumentTable.query.filter_by(file_id=file_id).order_by(DocumentTable.table_index).all()
        
        # Serialize tables
        tables_data = []
        for table in tables:
            tables_data.append({
                'id': table.id,
                'table_index': table.table_index,
                'page_number': table.page_number,
                'table_headers': table.table_headers,
                'table_data': table.table_data,
                'table_summary': table.table_summary,
                'row_count': table.row_count,
                'column_count': table.column_count,
                'is_split_table': table.is_split_table,
                'merged_table_group_id': table.merged_table_group_id,
                'table_metadata': table.table_metadata,
                'azure_search_doc_id': table.azure_search_doc_id,
                'created_at': table.created_at.isoformat() if table.created_at else None
            })
        
        return jsonify({
            'success': True,
            'data': {
                'tables': tables_data,
                'total_tables': len(tables_data)
            }
        }), 200
        
    except Exception as e:
        logger.error(f"Error getting tables for file {file_id}: {str(e)}")
        return jsonify({
            'error': 'Tables retrieval failed',
            'message': f'Failed to get document tables: {str(e)}'
        }), 500

@document_v2_bp.route('/v2/documents/search', methods=['POST'])
@login_required
@require_scope(SCOPE_VIEW_FILES)
def search_enhanced_documents():
    """
    Search enhanced document content using vector similarity.
    """
    try:
        data = request.get_json()
        if not data or 'query' not in data:
            return jsonify({
                'error': 'Missing query',
                'message': 'Search query is required'
            }), 400
        
        query = data['query']
        file_ids = data.get('file_ids', [])  # Optional filter by file IDs
        top_k = data.get('top_k', 5)
        top_k = min(top_k, 20)  # Limit to 20 results
        
        logger.info(f"Enhanced search query: {query} by user {request.user.id}")
        
        # Perform enhanced search
        search_results = azure_search_service.search_enhanced_content(
            query=query,
            file_ids=file_ids if file_ids else None,
            top_k=top_k
        )
        
        return jsonify({
            'success': True,
            'data': {
                'query': query,
                'results': search_results,
                'total_results': len(search_results),
                'search_index': AZURE_SEARCH_ENHANCED_INDEX_NAME
            }
        }), 200
        
    except Exception as e:
        logger.error(f"Error in enhanced document search: {str(e)}")
        return jsonify({
            'error': 'Search failed',
            'message': f'Enhanced document search failed: {str(e)}'
        }), 500

@document_v2_bp.route('/v2/documents/types', methods=['GET'])
@login_required
@require_scope(SCOPE_VIEW_FILES)
def get_supported_document_types():
    """
    Get list of supported document types for enhanced processing.
    """
    try:
        supported_types = document_type_detector.get_supported_types()
        supported_extensions = document_type_detector.get_supported_extensions()
        
        return jsonify({
            'success': True,
            'data': {
                'supported_types': supported_types,
                'supported_extensions': supported_extensions,
                'enhanced_processing_enabled': ENHANCED_PROCESSING_ENABLED
            }
        }), 200
        
    except Exception as e:
        logger.error(f"Error getting supported document types: {str(e)}")
        return jsonify({
            'error': 'Failed to get supported types',
            'message': f'Failed to retrieve supported document types: {str(e)}'
        }), 500

@document_v2_bp.route('/v2/documents/batch-process', methods=['POST'])
@login_required
@require_scope(SCOPE_UPLOAD_FILE)
def batch_process_documents():
    """
    Process multiple documents with enhanced processing.
    """
    try:
        if not ENHANCED_PROCESSING_ENABLED:
            return jsonify({
                'error': 'Enhanced processing disabled',
                'message': 'Enhanced document processing is currently disabled'
            }), 503
        
        data = request.get_json()
        if not data or 'file_ids' not in data:
            return jsonify({
                'error': 'Missing file_ids',
                'message': 'file_ids array is required for batch processing'
            }), 400
        
        file_ids = data['file_ids']
        force_reprocess = data.get('force_reprocess', False)
        
        if not isinstance(file_ids, list) or len(file_ids) == 0:
            return jsonify({
                'error': 'Invalid file_ids',
                'message': 'file_ids must be a non-empty array'
            }), 400
        
        # Limit batch size
        if len(file_ids) > 10:
            return jsonify({
                'error': 'Batch size too large',
                'message': 'Maximum 10 files can be processed in a single batch'
            }), 400
        
        logger.info(f"Starting batch processing for {len(file_ids)} files by user {request.user.id}")
        
        # Process each file
        results = []
        for file_id in file_ids:
            try:
                result = enhanced_processing_service.process_file(file_id, force_reprocess)
                results.append({
                    'file_id': file_id,
                    'status': result['status'],
                    'message': result['message']
                })
            except Exception as e:
                results.append({
                    'file_id': file_id,
                    'status': 'error',
                    'message': f'Processing failed: {str(e)}'
                })
        
        # Count successes and failures
        successful = sum(1 for r in results if r['status'] in ['completed', 'already_processed'])
        failed = len(results) - successful
        
        return jsonify({
            'success': True,
            'message': f'Batch processing completed: {successful} successful, {failed} failed',
            'data': {
                'results': results,
                'summary': {
                    'total': len(file_ids),
                    'successful': successful,
                    'failed': failed
                }
            }
        }), 200
        
    except Exception as e:
        logger.error(f"Error in batch document processing: {str(e)}")
        return jsonify({
            'error': 'Batch processing failed',
            'message': f'Batch processing failed: {str(e)}'
        }), 500

@document_v2_bp.route('/v2/documents/stats', methods=['GET'])
@login_required
@require_scope(SCOPE_VIEW_FILES)
def get_enhanced_processing_stats():
    """
    Get statistics about enhanced document processing.
    """
    try:
        # Get overall statistics
        total_files = File.query.count()
        files_with_metadata = File.query.join(FileProcessingMetadata).count()
        completed_processing = FileProcessingMetadata.query.filter_by(enhanced_processing_status='completed').count()
        failed_processing = FileProcessingMetadata.query.filter_by(enhanced_processing_status='failed').count()
        pending_processing = FileProcessingMetadata.query.filter_by(enhanced_processing_status='pending').count()
        
        # Get document type distribution
        from sqlalchemy import func
        type_distribution = db.session.query(
            FileProcessingMetadata.document_type,
            func.count(FileProcessingMetadata.id)
        ).group_by(FileProcessingMetadata.document_type).all()
        
        # Get chunk and table statistics
        total_chunks = DocumentChunk.query.count()
        total_tables = DocumentTable.query.count()
        
        return jsonify({
            'success': True,
            'data': {
                'files': {
                    'total': total_files,
                    'with_enhanced_metadata': files_with_metadata,
                    'enhanced_processing_coverage': round((files_with_metadata / total_files * 100), 2) if total_files > 0 else 0
                },
                'processing_status': {
                    'completed': completed_processing,
                    'failed': failed_processing,
                    'pending': pending_processing
                },
                'document_types': dict(type_distribution),
                'content': {
                    'total_chunks': total_chunks,
                    'total_tables': total_tables
                },
                'enhanced_processing_enabled': ENHANCED_PROCESSING_ENABLED,
                'search_index': AZURE_SEARCH_ENHANCED_INDEX_NAME
            }
        }), 200
        
    except Exception as e:
        logger.error(f"Error getting enhanced processing stats: {str(e)}")
        return jsonify({
            'error': 'Stats retrieval failed',
            'message': f'Failed to get processing statistics: {str(e)}'
        }), 500

@document_v2_bp.route('/v2/documents/upload-and-process', methods=['POST'])
@login_required
@require_scope(SCOPE_UPLOAD_FILE)
def upload_and_process_documents():
    """
    Upload files and automatically trigger enhanced document processing.
    
    This endpoint combines file upload with enhanced processing in a single API call.
    """
    try:
        if not ENHANCED_PROCESSING_ENABLED:
            return jsonify({
                'error': 'Enhanced processing disabled',
                'message': 'Enhanced document processing is currently disabled'
            }), 503
        
        if 'files[]' not in request.files:
            return jsonify({'error': 'No files provided'}), 400

        from azure.storage.blob import BlobServiceClient
        from werkzeug.utils import secure_filename
        from datetime import datetime
        import uuid
        from config.settings import AZURE_STORAGE_CONNECTION_STRING, AZURE_STORAGE_CONTAINER_NAME

        logger.info(f"Starting enhanced upload and processing by user {request.user.id}")

        files = request.files.getlist('files[]')
        file_format_id = request.form.get('file_format_id')
        file_type_id = request.form.get('file_type_id')
        department_id = request.form.get('department_id')
        vertical_id = request.form.get('vertical_id')
        comments = request.form.get('comments', '')
        sensitivity_id = request.form.get('sensitivity_id', 4)
        
        # Handle tags data
        tags_str = request.form.get('tags', '')
        file_tags_json = None
        
        if tags_str:
            try:
                import json
                tags_data = json.loads(tags_str)
                if isinstance(tags_data, dict):
                    file_tags_json = tags_data
                elif isinstance(tags_data, list):
                    file_tags_json = {"list": tags_data}
                else:
                    file_tags_json = {"single": tags_data}
            except Exception as e:
                logger.warning(f"Error parsing tags JSON: {str(e)}")

        # Initialize Azure Blob Storage
        blob_service_client = BlobServiceClient.from_connection_string(AZURE_STORAGE_CONNECTION_STRING)
        container_client = blob_service_client.get_container_client(AZURE_STORAGE_CONTAINER_NAME)

        uploaded_files = []
        processing_results = []

        for file in files:
            if file.filename == '':
                continue

            try:
                # Generate unique file name
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                unique_id = str(uuid.uuid4())
                secure_name = secure_filename(file.filename)
                blob_name = f"{unique_id}_{timestamp}_{secure_name}"

                # Upload to blob storage
                blob_client = container_client.get_blob_client(blob_name)
                file_contents = file.read()
                blob_client.upload_blob(file_contents, overwrite=True)

                # Create database record
                new_file = File(
                    file_name=secure_name,
                    file_format_id=file_format_id,
                    file_type_id=file_type_id,
                    department_id=department_id,
                    vertical_id=vertical_id,
                    blob_url=blob_client.url,
                    uploaded_by=request.user.id,
                    sensitivity_id=sensitivity_id,
                    comments=comments,
                    file_tags=file_tags_json
                )
                db.session.add(new_file)
                db.session.flush()  # Get ID without committing

                file_id = new_file.id
                logger.info(f"File uploaded successfully: {file_id} - {secure_name}")

                uploaded_files.append({
                    'id': file_id,
                    'original_name': secure_name,
                    'blob_name': blob_name,
                    'blob_url': blob_client.url
                })

            except Exception as e:
                logger.error(f"Error uploading file {file.filename}: {str(e)}")
                processing_results.append({
                    'file_name': file.filename,
                    'status': 'upload_failed',
                    'error': str(e)
                })
                continue

        # Commit the database transaction
        db.session.commit()
        logger.info(f"Database transaction committed for {len(uploaded_files)} files")

        # Now trigger enhanced processing for each uploaded file
        for uploaded_file in uploaded_files:
            file_id = uploaded_file['id']
            file_name = uploaded_file['original_name']
            
            try:
                logger.info(f"Starting enhanced processing for file {file_id}: {file_name}")
                
                # Trigger enhanced processing
                result = enhanced_processing_service.process_file(file_id, force_reprocess=False)
                
                processing_results.append({
                    'file_id': file_id,
                    'file_name': file_name,
                    'status': result.get('status', 'unknown'),
                    'message': result.get('message', ''),
                    'metadata': result.get('metadata', {}),
                    'processing_time': result.get('processing_time', 0),
                    'document_type': result.get('document_type'),
                    'chunk_count': result.get('chunk_count', 0),
                    'azure_search_doc_ids': result.get('azure_search_doc_ids', [])
                })
                
                logger.info(f"Enhanced processing completed for file {file_id}: {result.get('status')}")
                
            except Exception as e:
                logger.error(f"Enhanced processing failed for file {file_id}: {str(e)}")
                processing_results.append({
                    'file_id': file_id,
                    'file_name': file_name,
                    'status': 'processing_failed',
                    'error': str(e)
                })

        # Prepare summary statistics
        total_files = len(uploaded_files)
        successful_uploads = len(uploaded_files)
        successful_processing = len([r for r in processing_results if r.get('status') == 'completed'])
        failed_processing = len([r for r in processing_results if r.get('status') in ['failed', 'processing_failed']])
        
        return jsonify({
            'success': True,
            'message': f'Successfully uploaded and processed {successful_processing}/{total_files} files',
            'summary': {
                'total_files': total_files,
                'successful_uploads': successful_uploads,
                'successful_processing': successful_processing,
                'failed_processing': failed_processing,
                'enhanced_processing_enabled': True
            },
            'files': uploaded_files,
            'processing_results': processing_results
        }), 200

    except Exception as e:
        db.session.rollback()
        logger.error(f"Error in enhanced upload and processing: {str(e)}")
        return jsonify({
            'success': False,
            'error': 'Upload and processing failed',
            'message': f'Enhanced upload and processing failed: {str(e)}'
        }), 500 