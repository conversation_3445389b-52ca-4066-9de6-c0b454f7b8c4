from flask import Blueprint, request, jsonify
from models.models import db, Tag, User, Vertical, Department
from utils.decorators import login_required
from functools import wraps
from sqlalchemy import or_

tag_bp = Blueprint('tag', __name__)

# Scope constants - using same view files permission as files
SCOPE_VIEW_TAGS = 4  # VIEW_FILE
SCOPE_CREATE_TAG = 2  # UPLOAD_FILE

def require_scope(scope_id):
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            user = User.query.get(request.user.id)
            if not user or not user.scopes or scope_id not in user.scopes:
                return jsonify({
                    'error': 'Unauthorized',
                    'message': 'You do not have the required permissions for this operation'
                }), 403
            return f(*args, **kwargs)
        return decorated_function
    return decorator

@tag_bp.route('/tags', methods=['GET'])
@login_required
@require_scope(SCOPE_VIEW_TAGS)
def get_tags():
    """Get all tags with optional filtering by vertical and department"""
    try:
        # Get filter parameters
        vertical_id = request.args.get('vertical_id')
        department_id = request.args.get('department_id')
        search_term = request.args.get('search', '')
        
        # Start with base query
        query = Tag.query
        
        # Apply filters if provided
        if vertical_id and vertical_id != 'all':
            query = query.filter(or_(
                Tag.vertical_id == vertical_id,
                Tag.vertical_id.is_(None)  # Include tags not linked to any vertical
            ))
            
        if department_id and department_id != 'all':
            query = query.filter(or_(
                Tag.department_id == department_id,
                Tag.department_id.is_(None)  # Include tags not linked to any department
            ))
            
        # Apply search filter if provided
        if search_term:
            query = query.filter(Tag.name.ilike(f'%{search_term}%'))
        
        # Execute query and order by name
        tags = query.order_by(Tag.name).all()
        
        return jsonify({
            'tags': [{
                'id': tag.id,
                'name': tag.name,
                'vertical_id': tag.vertical_id,
                'department_id': tag.department_id,
                'vertical_name': tag.vertical.name if tag.vertical else None,
                'department_name': tag.department.name if tag.department else None,
                'created_by': tag.created_by,
                'created_at': tag.created_at.isoformat()
            } for tag in tags]
        }), 200
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@tag_bp.route('/tags', methods=['POST'])
@login_required
@require_scope(SCOPE_CREATE_TAG)
def create_tag():
    """Create a new tag"""
    try:
        # Get tag data from request body or query parameters
        body_data = request.json or {}
        
        # Get tag name (try request body first, then query params)
        tag_name = body_data.get('name')
        if not tag_name:
            tag_name = request.args.get('name')
        
        if not tag_name:
            return jsonify({'error': 'Tag name is required'}), 400
            
        tag_name = tag_name.strip()
        if not tag_name:
            return jsonify({'error': 'Tag name cannot be empty'}), 400
            
        # Get vertical_id and department_id (try request body first, then query params)
        vertical_id = body_data.get('vertical_id')
        if vertical_id is None:
            vertical_id = request.args.get('vertical_id')
            if vertical_id:
                vertical_id = int(vertical_id)
                
        department_id = body_data.get('department_id')
        if department_id is None:
            department_id = request.args.get('department_id')
            if department_id:
                department_id = int(department_id)
        
        # Check if tag with same name exists in the same vertical/department
        existing_tag = Tag.query.filter(
            Tag.name == tag_name,
            Tag.vertical_id == vertical_id,
            Tag.department_id == department_id
        ).first()
        
        if existing_tag:
            return jsonify({
                'message': 'Tag already exists',
                'tag': {
                    'id': existing_tag.id,
                    'name': existing_tag.name,
                    'vertical_id': existing_tag.vertical_id,
                    'department_id': existing_tag.department_id
                }
            }), 200
            
        # Create new tag
        new_tag = Tag(
            name=tag_name,
            vertical_id=vertical_id,
            department_id=department_id,
            created_by=request.user.id
        )
        
        db.session.add(new_tag)
        db.session.commit()
        
        return jsonify({
            'message': 'Tag created successfully',
            'tag': {
                'id': new_tag.id,
                'name': new_tag.name,
                'vertical_id': new_tag.vertical_id,
                'department_id': new_tag.department_id,
                'created_by': new_tag.created_by,
                'created_at': new_tag.created_at.isoformat()
            }
        }), 201
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@tag_bp.route('/tags/<int:tag_id>', methods=['DELETE'])
@login_required
@require_scope(SCOPE_CREATE_TAG)
def delete_tag(tag_id):
    """Delete a tag"""
    try:
        tag = Tag.query.get(tag_id)
        if not tag:
            return jsonify({'error': 'Tag not found'}), 404
            
        # Only the creator or admin can delete a tag
        user = User.query.get(request.user.id)
        if tag.created_by != request.user.id and not user.is_admin:
            return jsonify({'error': 'You do not have permission to delete this tag'}), 403
            
        db.session.delete(tag)
        db.session.commit()
        
        return jsonify({'message': 'Tag deleted successfully'}), 200
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@tag_bp.route('/tags/all', methods=['GET'])
@login_required
@require_scope(SCOPE_VIEW_TAGS)
def get_all_tags():
    """Get all tags without any filtering"""
    try:
        # Get all tags ordered by name
        tags = Tag.query.order_by(Tag.name).all()
        
        return jsonify({
            'tags': [{
                'id': tag.id,
                'name': tag.name
            } for tag in tags]
        }), 200
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500 