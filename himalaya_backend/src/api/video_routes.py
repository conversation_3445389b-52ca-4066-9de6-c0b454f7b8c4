from flask import Blueprint, request, jsonify, send_file
import ffmpeg
import os
import tempfile
import uuid
from utils.video_processor import VideoProcessor
from utils.blob_utils import generate_video_sas_url
from models.models import Video
from utils.decorators import login_required
import logging

video_bp = Blueprint('video', __name__)
logger = logging.getLogger(__name__)

@video_bp.route('/videos/<int:video_id>/snippets', methods=['POST'])
@login_required
def get_video_snippet(video_id):
    try:
        # Validate request data
        data = request.json
        if not data or 'start_time' not in data or 'end_time' not in data:
            return jsonify({'error': 'Start time and end time are required'}), 400

        start_time = float(data['start_time'])
        end_time = float(data['end_time'])

        if start_time >= end_time:
            return jsonify({'error': 'Start time must be less than end time'}), 400

        # Get video from database
        video = Video.query.get_or_404(video_id)
        
        # Generate SAS URL for the video
        try:
            video_url = generate_video_sas_url(video.blob_url)
        except Exception as e:
            logger.error(f"Error generating SAS URL: {str(e)}")
            return jsonify({'error': 'Failed to generate secure video access URL'}), 500
        
        # Create temporary directory for processing
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_output = os.path.join(temp_dir, f"output_{uuid.uuid4()}.mp4")
            
            # Download and process the segment using ffmpeg
            try:
                # Use the SAS URL instead of the direct blob URL
                stream = ffmpeg.input(video_url, ss=start_time, t=end_time-start_time)
                stream = ffmpeg.output(stream, temp_output, acodec='copy', vcodec='copy')
                ffmpeg.run(stream, capture_stdout=True, capture_stderr=True)
            except ffmpeg.Error as e:
                error_message = e.stderr.decode() if hasattr(e, 'stderr') else str(e)
                logger.error(f"FFmpeg error: {error_message}")
                return jsonify({'error': 'Failed to process video segment'}), 500

            # Send the file
            try:
                return send_file(
                    temp_output,
                    mimetype='video/mp4',
                    as_attachment=True,
                    download_name=f'snippet_{video_id}_{start_time}_{end_time}.mp4'
                )
            except Exception as e:
                logger.error(f"Error sending file: {str(e)}")
                return jsonify({'error': 'Failed to send video file'}), 500

    except Exception as e:
        logger.error(f"Error processing video snippet: {str(e)}")
        return jsonify({'error': str(e)}), 500 