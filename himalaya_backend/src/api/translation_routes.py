from flask import Blueprint, request, jsonify, send_file
from utils.decorators import login_required
from utils.fgd_translation_utils import (
    process_translation_batch,
    perform_docx_to_pdf_conversion
)
import json
import os
from io import BytesIO
import logging
from azure.storage.blob import BlobServiceClient
from werkzeug.utils import secure_filename
from datetime import datetime
from config.settings import AZURE_STORAGE_CONNECTION_STRING

translation_bp = Blueprint('translation', __name__)

@translation_bp.route('/translate', methods=['POST'])
@login_required
def translate_file():
    try:
        data = request.json
        if not all([
            'file_url' in data,
            'base_language' in data,
            'target_languages' in data
        ]):
            return jsonify({
                "error": "Missing required fields: file_url, base_language, target_languages"
            }), 400

        file_url = data['file_url']
        base_language = data['base_language']
        target_languages = data['target_languages']

        # Download file from blob storage
        blob_service_client = BlobServiceClient.from_connection_string(
            AZURE_STORAGE_CONNECTION_STRING
        )
        
        # Clean up the blob name from the URL
        blob_path = '/'.join(file_url.split('/')[3:])  # Remove the storage account part
        container_name = blob_path.split('/')[0]
        blob_name = '/'.join(blob_path.split('/')[1:])
        
        print(f"Container: {container_name}, Blob: {blob_name}")  # Debug log
        
        blob_client = blob_service_client.get_container_client(container_name)\
                                       .get_blob_client(blob_name)
        
        file_content = blob_client.download_blob().readall()
        file_buffer = BytesIO(file_content)
        file_extension = os.path.splitext(blob_name)[1].lower()

        # Generate a clean filename for translations
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        base_name = secure_filename(os.path.splitext(blob_name.split('/')[-1])[0])
        
        # Prepare translation arguments
        process_args = [
            (
                file_buffer, 
                file_extension, 
                f"{base_name}_{timestamp}_{lang['code'].lower()}{file_extension}",  # Use code for filename
                lang['name'],  # Pass language name for translation
                base_language,
                lang['code']  # Pass code separately for reference
            )
            for lang in target_languages
        ]

        # Process translations
        translated_files, failed_languages = process_translation_batch(process_args)

        if len(failed_languages) == len(target_languages):
            return jsonify({"error": "Translation failed for all languages"}), 500

        if failed_languages:
            logging.warning(f"Translation failed for languages: {', '.join(failed_languages)}")

        return jsonify({
            "translated_files": translated_files,
            "failed_languages": failed_languages
        })

    except Exception as e:
        logging.error(f"Translation error: {str(e)}")
        return jsonify({"error": str(e)}), 500

@translation_bp.route('/convert-docx-to-pdf', methods=['POST'])
@login_required
def convert_docx_to_pdf():
    try:
        if 'file' not in request.files:
            return jsonify({"error": "Please provide a DOCX file"}), 400

        file = request.files['file']
        try:
            pdf_buffer, pdf_filename = perform_docx_to_pdf_conversion(file)
            return send_file(
                pdf_buffer,
                as_attachment=True,
                download_name=pdf_filename,
                mimetype='application/pdf'
            )
        except ValueError as ve:
            return jsonify({"error": str(ve)}), 400
    except Exception as e:
        return jsonify({"error": str(e)}), 500

@translation_bp.route('/convert-docx-to-pdf-bytes', methods=['POST'])
@login_required
def convert_docx_to_pdf_bytes():
    try:
        if 'file' not in request.files:
            return jsonify({"error": "Please provide a DOCX file"}), 400

        file = request.files['file']
        try:
            pdf_buffer, pdf_filename = perform_docx_to_pdf_conversion(file)
            return pdf_buffer.getvalue(), 200, {
                'Content-Type': 'application/pdf',
                'Content-Disposition': f'attachment; filename="{pdf_filename}"'
            }
        except ValueError as ve:
            return jsonify({"error": str(ve)}), 400
    except Exception as e:
        return jsonify({"error": str(e)}), 500 