"""
Agentic Chat <PERSON> for Himalaya Backend System.

This module implements the agent-first approach for chat processing, similar to himalaya_azure's main.py.
Uses enhanced logging for better developer insights and reduced Azure SDK verbosity.
"""

import datetime
import traceback
from typing import Dict, List, Any, Optional

from models.models import db, ChatSession, ChatMessage, File
from utils.openai_utils import rephrase_question
from agents.planner import create_planner_agent, AgentType
from agents.web import create_web_search_agent
from agents.qa import create_qa_agent
from agents.rag.rag_agent import create_rag_agent
from agents.rag.rag_planner_agent import RAGPlannerAgent
from config.logging_config import performance, agentic, get_logger

# Get logger for this module
logger = get_logger(__name__)


def evaluate_answer_with_qa(
    query: str,
    answer: str,
    sources: List[Dict[str, Any]],
    conversation_history: str,
    conversation_summary: str,
    agent_type: str,
    max_improvement_loops: int = 2
) -> Dict[str, Any]:
    """
    Evaluate answer quality using QA agent and potentially improve it.
    
    Args:
        query: Original query
        answer: Answer to evaluate
        sources: Sources/references used
        conversation_history: Conversation context
        conversation_summary: Summary of conversation
        agent_type: Type of agent that generated the answer
        max_improvement_loops: Maximum number of improvement attempts
        
    Returns:
        Dict with final_answer, sources, needs_improvement, improvement_count
    """
    logger.info(f"🔍 QA EVALUATION: Starting evaluation for {agent_type} agent")
    agentic.log_agent_start("QA_EVALUATION", query)
    
    qa_start = datetime.datetime.now()
    final_answer = answer
    final_sources = sources
    improvement_count = 0
    
    try:
        # Create QA agent
        qa_agent = create_qa_agent(verbose=True, max_improvement_loops=max_improvement_loops)
        
        # Convert sources to references format for QA agent
        references = {}
        for i, source in enumerate(sources, 1):
            if isinstance(source, dict):
                if 'chunk_id' in source:
                    references[f"ref_{i}"] = source.get('chunk_id', f"source_{i}")
                elif 'content' in source:
                    references[f"ref_{i}"] = source.get('content', f"source_{i}")[:100] + "..."
                else:
                    references[f"ref_{i}"] = f"source_{i}"
            else:
                references[f"ref_{i}"] = str(source)
        
        # Evaluate the answer
        qa_result = qa_agent.graph.invoke({
            "messages": [],
            "query": query,
            "conversation_history": conversation_history,
            "conversation_summary": conversation_summary,
            "answer": answer,
            "references": references,
            "evaluation": {},
            "needs_improvement": False,
            "improvement_count": 0,
            "improvement_instructions": None
        })
        
        needs_improvement = qa_result.get("needs_improvement", False)
        improvement_instructions = qa_result.get("improvement_instructions", "")
        evaluation = qa_result.get("evaluation", {})
        
        qa_duration = (datetime.datetime.now() - qa_start).total_seconds()
        
        # Log QA evaluation results
        logger.info(f"🔍 QA EVALUATION: Agent: {agent_type}")
        logger.info(f"🔍 QA EVALUATION: Needs improvement: {needs_improvement}")
        logger.info(f"🔍 QA EVALUATION: Evaluation score: {evaluation.get('score', 'N/A')}")
        logger.info(f"🔍 QA EVALUATION: Processing time: {qa_duration:.2f}s")
        
        if improvement_instructions:
            logger.info(f"🔍 QA EVALUATION: Improvement instructions: {improvement_instructions}")
        
        # Log structured evaluation
        agentic.log_qa_evaluation(
            agent_type=agent_type,
            needs_improvement=needs_improvement,
            evaluation_score=evaluation.get('score', 0),
            improvement_instructions=improvement_instructions
        )
        
        # For RAG and Conversation agents, we can't easily re-run them with improvements
        # So we'll just log the evaluation but return the original answer
        # Web search agent already has improvement loops built in
        if needs_improvement and agent_type not in ['web_search']:
            logger.info(f"🔍 QA EVALUATION: {agent_type} agent answer needs improvement but cannot be easily re-run")
            logger.info(f"🔍 QA EVALUATION: Improvement suggestion: {improvement_instructions}")
            
            # Log improvement suggestion internally but don't expose to user
            # Users should receive clean, professional responses without internal feedback
            logger.warning(f"🔍 QA EVALUATION: Internal improvement needed - {improvement_instructions}")
        
        agentic.log_agent_complete("QA_EVALUATION", True, qa_duration)
        performance.log_agent_execution("QA_EVALUATION", query, qa_duration, True)
        
        return {
            'final_answer': final_answer,
            'sources': final_sources,
            'needs_improvement': needs_improvement,
            'improvement_count': improvement_count,
            'improvement_instructions': improvement_instructions,
            'evaluation': evaluation,
            'qa_processing_time': qa_duration
        }
        
    except Exception as e:
        logger.error(f"Error in QA evaluation: {str(e)}")
        qa_duration = (datetime.datetime.now() - qa_start).total_seconds()
        
        agentic.log_agent_complete("QA_EVALUATION", False, qa_duration)
        performance.log_agent_execution("QA_EVALUATION", query, qa_duration, False)
        
        # Return original answer if QA evaluation fails
        return {
            'final_answer': final_answer,
            'sources': final_sources,
            'needs_improvement': False,
            'improvement_count': 0,
            'improvement_instructions': "",
            'evaluation': {},
            'qa_processing_time': qa_duration,
            'qa_error': str(e)
        }


def process_agentic_chat(
    session_id: int,
    data: Dict[str, Any],
    message_metadata: Dict[str, Any],
    relevant_previous_messages: List[ChatMessage],
    conversation_history: str,
    blob_names: List[str],
    planner_result: Dict[str, Any] = None
) -> Dict[str, Any]:
    """
    Process chat using agent-first approach similar to himalaya_azure.
    
    Args:
        session_id: Chat session ID
        data: Request data
        message_metadata: Message metadata
        relevant_previous_messages: Filtered previous messages
        conversation_history: Formatted conversation history
        blob_names: List of blob names for filtering
        planner_result: Pre-computed planner result from universal planner (optional)
        
    Returns:
        Response data
    """
    start_time = datetime.datetime.now()
    web_search = message_metadata.get('web_search', False)
    query = data['question']
    
    # Enhanced logging for agentic processing start
    agentic.log_agent_start("AGENTIC_PIPELINE", query)
    logger.info(f"Session {session_id} - Mode: {'Web Search' if web_search else 'RAG'} - Files: {len(blob_names)}")

    try:
        conversation_summary = message_metadata.get('conversation_summary', '')
        
        # 🎯 RESPECT USER'S EXPLICIT CHOICES FIRST
        # Check if user has selected specific files and disabled web search
        has_selected_files = blob_names and len(blob_names) > 0
        user_disabled_web_search = web_search is False
        
        if has_selected_files and user_disabled_web_search:
            # User explicitly selected files and disabled web search - force RAG
            logger.info(f"🎯 USER PREFERENCE: Files selected ({len(blob_names)}) + Web search disabled → Forcing RAG agent")
            agent_type = AgentType.RAG
            
            # 🔧 FIX: Use rephrased query from message metadata if available
            rephrased_query = message_metadata.get('rephrased_question', query)
            standalone_query = rephrased_query
            
            logger.info(f"📝 QUERY ROUTING: Original: '{query}' → Rephrased: '{rephrased_query}'")
            
            # Log forced routing decision
            agentic.log_planner_decision(query, f"FORCED_RAG (User selected {len(blob_names)} files, web_search=False)")
            
            # Skip planner and route directly to RAG
            return handle_rag_agent(
                query, standalone_query, blob_names, conversation_history,
                conversation_summary, session_id, start_time, data, message_metadata
            )
        
        # 🤖 USE PLANNER FOR ROUTING DECISION
        if planner_result:
            # Use pre-computed planner result from universal planner
            logger.info("Using pre-computed planner result from universal planner...")
            plan_result = planner_result
            agent_type = plan_result["agent_type"]
            standalone_query = plan_result["query"]
            
            # Log planner decision
            agentic.log_planner_decision(query, f"PRE_COMPUTED_{str(agent_type)}")
        else:
            # Fallback: Create planner for autonomous decision making (legacy path)
            planner_start = datetime.datetime.now()
            logger.info("Creating fallback planner agent for query routing...")
            planner = create_planner_agent(verbose=True)
            
            plan_result = planner.graph.invoke({
                "messages": [],
                "query": query,
                "conversation_history": conversation_history,
                "conversation_summary": conversation_summary,
                "web_search": web_search,
                "agent_type": AgentType.UNKNOWN,
                "analysis": {},
                "require_table": False,
                "require_chart": False,
                "chart_type": None
            })

            # Get the agent type and standalone query from the plan
            agent_type = plan_result["agent_type"]
            standalone_query = plan_result["query"]
            
            planner_duration = (datetime.datetime.now() - planner_start).total_seconds()
            
            # Log planner decision
            agentic.log_planner_decision(query, f"FALLBACK_{str(agent_type)}")
            performance.log_agent_execution("FALLBACK_PLANNER", query, planner_duration, True)

        # Route to appropriate agent based on plan
        if agent_type == AgentType.WEB_SEARCH:
            return handle_web_search_agent(
                query, standalone_query, conversation_history, conversation_summary,
                session_id, start_time, plan_result
            )
        elif agent_type == AgentType.RAG:
            return handle_rag_agent(
                query, standalone_query, blob_names, conversation_history,
                conversation_summary, session_id, start_time, data, message_metadata
            )
        elif agent_type == AgentType.CONVERSATION:
            return handle_conversation_agent(
                query, conversation_history, conversation_summary,
                session_id, start_time, plan_result
            )
        else:
            # Fallback to RAG for unknown types
            logger.warning(f"Unknown agent type {agent_type}, falling back to RAG")
            return handle_rag_agent(
                query, standalone_query, blob_names, conversation_history,
                conversation_summary, session_id, start_time, data, message_metadata
            )

    except Exception as e:
        logger.error(f"Error in agentic processing: {str(e)}")
        end_time = datetime.datetime.now()
        processing_time = (end_time - start_time).total_seconds()
        
        agentic.log_agent_complete("AGENTIC_PIPELINE", False, processing_time)
        performance.log_agent_execution("AGENTIC_PIPELINE", query, processing_time, False)
        
        return {
            'answer': f"I'm experiencing technical difficulties: {str(e)}",
            'sources': [],
            'agent_type': 'error',
            'processing_time': processing_time,
            'conversation_id': session_id,
            'error': str(e)
        }


def handle_web_search_agent(
    query: str,
    standalone_query: str, 
    conversation_history: str,
    conversation_summary: str,
    session_id: int,
    start_time: datetime.datetime,
    plan_result: Dict[str, Any]
) -> Dict[str, Any]:
    """Handle web search agent processing with improvement loops."""
    
    agent_start = datetime.datetime.now()
    agentic.log_agent_start("WEB_SEARCH", query)
    
    # Initialize improvement loop variables
    improvement_count = 0
    max_improvement_loops = 3
    final_answer = None
    final_references = {}

    while improvement_count < max_improvement_loops:
        loop_start = datetime.datetime.now()
        
        # Create web search agent
        web_agent = create_web_search_agent(verbose=True)
        
        # Execute web search
        web_result = web_agent.graph.invoke({
            "messages": [],
            "query": standalone_query,
            "search_results": [],
            "scraped_content": {},
            "previous_content": {},
            "response": {},
            "conversation_id": session_id
        })

        # Extract answer and references
        answer = web_result["response"]["answer"]
        references = web_result["response"]["references"]

        # Create QA agent to evaluate the answer
        qa_agent = create_qa_agent(verbose=True, max_improvement_loops=max_improvement_loops)

        # Evaluate the answer
        qa_result = qa_agent.graph.invoke({
            "messages": [],
            "query": query,
            "conversation_history": conversation_history,
            "conversation_summary": conversation_summary,
            "answer": answer,
            "references": references,
            "evaluation": {},
            "needs_improvement": False,
            "improvement_count": improvement_count,
            "improvement_instructions": None
        })

        # Update final answer and references
        final_answer = answer
        final_references.update(references)

        # Check if improvement is needed
        needs_improvement = qa_result["needs_improvement"]
        improvement_instructions = qa_result["improvement_instructions"]
        
        loop_duration = (datetime.datetime.now() - loop_start).total_seconds()
        agentic.log_improvement_loop(improvement_count + 1, max_improvement_loops, needs_improvement)
        
        if not needs_improvement:
            logger.info(f"QA approved answer after {improvement_count + 1} iterations")
            break

        improvement_count += 1
        logger.info(f"QA requires improvement: {improvement_instructions}")

    # Calculate processing time
    end_time = datetime.datetime.now()
    processing_time = (end_time - start_time).total_seconds()
    agent_duration = (end_time - agent_start).total_seconds()
    
    agentic.log_agent_complete("WEB_SEARCH", True, agent_duration)
    performance.log_agent_execution("WEB_SEARCH", query, agent_duration, True)

    # Convert final_references to the expected key-value sources format
    # Expected format: {"1": "url1", "2": "url2", ...}
    web_sources = {}
    for key, ref in final_references.items():
        web_sources[str(key)] = ref

    return {
        'answer': final_answer,
        'sources': web_sources,  # Now in correct key-value format
        'agent_type': 'agentic_web_search_qa',  # Already includes QA evaluation
        'processing_time': processing_time,
        'conversation_id': session_id,
        'improvement_count': improvement_count,
        'agents_used': ['PLANNER', 'WEB_SEARCH', 'QA_EVALUATOR'],
        'token_usage': {  # Add token usage information
            'prompt_tokens': 0,  # Web search doesn't track individual token usage
            'completion_tokens': 0,
            'total_tokens': 0,
            'total_cost': 0.0,
            'note': 'Token usage not tracked for web search operations'
        },
        'qa_evaluation': {
            'improvement_loops_completed': improvement_count,
            'max_improvement_loops': max_improvement_loops,
            'final_approval': not needs_improvement,
            'processing_time': agent_duration
        }
    }


def handle_rag_agent(
    query: str,
    standalone_query: str,
    blob_names: List[str],
    conversation_history: str,
    conversation_summary: str,
    session_id: int,
    start_time: datetime.datetime,
    data: Dict[str, Any],
    message_metadata: Dict[str, Any]
) -> Dict[str, Any]:
    """Handle RAG agent processing with internal vector search."""
    
    agent_start = datetime.datetime.now()
    agentic.log_agent_start("RAG", query)
    logger.info(f"Processing with {len(blob_names)} available files")
    
    try:
        # Create RAG Agent (handles vector search internally)
        rag_agent = create_rag_agent(verbose=True)
        
        # Extract deep_search flag from message metadata
        deep_search = message_metadata.get('deep_search', False)
        
        # Process with RAG Agent (autonomous retrieval)
        retrieval_start = datetime.datetime.now()
        rag_result = rag_agent.process_query(
            query=query,
            standalone_query=standalone_query,
            blob_names=blob_names,
            deep_search=deep_search
        )
        
        # Get the retrieved documents
        csv_documents = rag_result.get("csv_documents", [])
        text_documents = rag_result.get("text_documents", [])
        total_chunks = len(csv_documents) + len(text_documents)
        
        retrieval_duration = (datetime.datetime.now() - retrieval_start).total_seconds()
        agentic.log_retrieval_stats(len(csv_documents), len(text_documents), total_chunks)
        performance.log_vector_search(query, total_chunks, retrieval_duration)

        # Use RAG Planner for intelligent processing if documents found
        if csv_documents or text_documents:
            planner_start = datetime.datetime.now()
            agentic.log_agent_start("RAG_PLANNER", query)
            
            # Create RAG Planner Agent
            rag_planner = RAGPlannerAgent(verbose=True)
            
            # Process with RAG Planner Agent
            planner_result = rag_planner.process_query(
                query=query,
                standalone_query=standalone_query,
                csv_documents=csv_documents,
                text_documents=text_documents,
                conversation_history=conversation_history if conversation_history else None,
                conversation_summary=conversation_summary
            )
            
            planner_duration = (datetime.datetime.now() - planner_start).total_seconds()
            
            if planner_result.get('status') == 'success':
                agents_used = planner_result.get('agents_used', ['RAG', 'RAG_PLANNER'])
                total_duration = (datetime.datetime.now() - agent_start).total_seconds()
                
                agentic.log_agent_complete("RAG_PLANNER", True, planner_duration)
                agentic.log_multi_agent_coordination(agents_used, total_duration)
                
                # Convert references to sources format (only legitimate chunk references with proper metadata)
                planner_sources = []
                references = planner_result.get('references', [])
                for ref in references:
                    # Only process actual chunk references that have proper file metadata
                    if ref.startswith('chunk_'):
                        planner_sources.append({
                            'chunk_id': ref,
                            'content': 'Document content',
                            'metadata': {'source_type': 'document'}
                        })
                    # NOTE: Removed synthetic csv_file_ reference handling as they're no longer generated
                    # CSV processing value is preserved in the answer content
                
                # Calculate processing time
                end_time = datetime.datetime.now()
                processing_time = (end_time - start_time).total_seconds()
                
                performance.log_agent_execution("RAG_MULTI_AGENT", query, total_duration, True)
                
                # ============ QA EVALUATION FOR RAG AGENT ============
                initial_answer = planner_result.get('answer', 'No answer generated')
                logger.info("🔍 QA EVALUATION: Evaluating RAG agent response...")
                
                qa_evaluation = evaluate_answer_with_qa(
                    query=query,
                    answer=initial_answer,
                    sources=planner_sources,
                    conversation_history=conversation_history,
                    conversation_summary=conversation_summary,
                    agent_type="rag",
                    max_improvement_loops=2
                )
                
                # Use QA-evaluated answer
                final_answer = qa_evaluation.get('final_answer', initial_answer)
                qa_processing_time = qa_evaluation.get('qa_processing_time', 0)
                
                logger.info(f"🔍 QA EVALUATION: RAG evaluation completed in {qa_processing_time:.2f}s")
                
                return {
                    'answer': final_answer,
                    'sources': planner_sources,
                    'agent_type': 'agentic_rag_planner_qa',
                    'processing_time': processing_time + qa_processing_time,
                    'conversation_id': session_id,
                    'agents_used': agents_used + ['QA_EVALUATOR'],
                    'token_usage': planner_result.get('token_usage', {}),
                    'qa_evaluation': {
                        'needs_improvement': qa_evaluation.get('needs_improvement', False),
                        'evaluation_score': qa_evaluation.get('evaluation', {}).get('score', 0),
                        'improvement_instructions': qa_evaluation.get('improvement_instructions', ''),
                        'processing_time': qa_processing_time
                    }
                }
            else:
                logger.warning("RAG Planner processing failed, using fallback")
                agentic.log_agent_complete("RAG_PLANNER", False, planner_duration)
                
        # Fallback: No documents or planner failed
        logger.info("Using RAG fallback response - no relevant documents found")
        
        # Calculate processing time
        end_time = datetime.datetime.now()
        processing_time = (end_time - start_time).total_seconds()
        agent_duration = (end_time - agent_start).total_seconds()
        
        agentic.log_agent_complete("RAG", False, agent_duration)
        performance.log_agent_execution("RAG", query, agent_duration, False)
        
        return {
            'answer': "I couldn't find relevant documents to answer your question. Please try rephrasing or check if the files are uploaded.",
            'sources': [],
            'agent_type': 'agentic_rag_fallback',
            'processing_time': processing_time,
            'conversation_id': session_id,
            'token_usage': {
                'prompt_tokens': 0,
                'completion_tokens': 0,
                'total_tokens': 0,
                'total_cost': 0.0,
                'note': 'No token usage for fallback response'
            }
        }

    except Exception as e:
        logger.error(f"Error in RAG processing: {str(e)}")
        
        # Calculate processing time
        end_time = datetime.datetime.now()
        processing_time = (end_time - start_time).total_seconds()
        agent_duration = (end_time - agent_start).total_seconds()
        
        agentic.log_agent_complete("RAG", False, agent_duration)
        performance.log_agent_execution("RAG", query, agent_duration, False)
        
        return {
            'answer': f"Error in document processing: {str(e)}",
            'sources': [],
            'agent_type': 'agentic_rag_error',
            'processing_time': processing_time,
            'conversation_id': session_id,
            'token_usage': {
                'prompt_tokens': 0,
                'completion_tokens': 0,
                'total_tokens': 0,
                'total_cost': 0.0,
                'note': 'No token usage due to error'
            },
            'error': str(e)
        }


def handle_conversation_agent(
    query: str,
    conversation_history: str,
    conversation_summary: str,
    session_id: int,
    start_time: datetime.datetime,
    plan_result: Dict[str, Any]
) -> Dict[str, Any]:
    """Handle conversation agent processing."""
    
    agent_start = datetime.datetime.now()
    agentic.log_agent_start("CONVERSATION", query)
    
    try:
        # Import conversation agent
        from agents.conversation import create_conversation_agent
        
        # Get conversation type from analysis
        conversation_type = plan_result.get("analysis", {}).get("conversation_type", "general")
        logger.info(f"Processing conversation type: {conversation_type}")
        
        # Create conversation agent
        conversation_agent = create_conversation_agent(verbose=True)
        
        # Process the query
        conversation_result = conversation_agent.process_query(
            query=query,
            conversation_type=conversation_type,
            conversation_history=conversation_history,
            conversation_summary=conversation_summary
        )
        
        # Get the answer
        initial_answer = conversation_result.get("answer", "I'm not sure how to respond to that.")
        
        # Calculate processing time
        end_time = datetime.datetime.now()
        processing_time = (end_time - start_time).total_seconds()
        agent_duration = (end_time - agent_start).total_seconds()
        
        agentic.log_agent_complete("CONVERSATION", True, agent_duration)
        performance.log_agent_execution("CONVERSATION", query, agent_duration, True)
        
        # ============ QA EVALUATION FOR CONVERSATION AGENT ============
        logger.info("🔍 QA EVALUATION: Evaluating conversation agent response...")
        
        qa_evaluation = evaluate_answer_with_qa(
            query=query,
            answer=initial_answer,
            sources=[],  # Conversation agent typically doesn't have sources
            conversation_history=conversation_history,
            conversation_summary=conversation_summary,
            agent_type="conversation",
            max_improvement_loops=2
        )
        
        # Use QA-evaluated answer
        final_answer = qa_evaluation.get('final_answer', initial_answer)
        qa_processing_time = qa_evaluation.get('qa_processing_time', 0)
        
        logger.info(f"🔍 QA EVALUATION: Conversation evaluation completed in {qa_processing_time:.2f}s")
        
        return {
            'answer': final_answer,
            'sources': {},
            'agent_type': f"agentic_conversation_{conversation_type}_qa",
            'processing_time': processing_time + qa_processing_time,
            'conversation_id': session_id,
            'agents_used': ['CONVERSATION', 'QA_EVALUATOR'],
            'token_usage': conversation_result.get('token_usage', {
                'prompt_tokens': 0,
                'completion_tokens': 0,
                'total_tokens': 0,
                'total_cost': 0.0,
                'note': 'Token usage not tracked for conversation agent'
            }),
            'qa_evaluation': {
                'needs_improvement': qa_evaluation.get('needs_improvement', False),
                'evaluation_score': qa_evaluation.get('evaluation', {}).get('score', 0),
                'improvement_instructions': qa_evaluation.get('improvement_instructions', ''),
                'processing_time': qa_processing_time
            }
        }

    except Exception as e:
        logger.error(f"Error in conversation processing: {str(e)}")
        
        # Calculate processing time
        end_time = datetime.datetime.now()
        processing_time = (end_time - start_time).total_seconds()
        agent_duration = (end_time - agent_start).total_seconds()
        
        agentic.log_agent_complete("CONVERSATION", False, agent_duration)
        performance.log_agent_execution("CONVERSATION", query, agent_duration, False)
        
        return {
            'answer': "I'm having trouble with conversational processing. Could you please rephrase your question?",
            'sources': {},
            'agent_type': 'agentic_conversation_error',
            'processing_time': processing_time,
            'conversation_id': session_id,
            'token_usage': {
                'prompt_tokens': 0,
                'completion_tokens': 0,
                'total_tokens': 0,
                'total_cost': 0.0,
                'note': 'No token usage due to error'
            },
            'error': str(e)
        } 