from flask import Blueprint, jsonify, request
from models.models import db, Department, Position, Vertical, Scope, User, user_departments
from utils.decorators import admin_required

master_bp = Blueprint('master', __name__)

# Department routes
@master_bp.route('/departments', methods=['GET'])
def get_departments():
    vertical_id = request.args.get('vertical_id', type=int)
    if not vertical_id:
        return jsonify({'error': 'vertical_id is required'}), 400
        
    departments = Department.query.filter_by(vertical_id=vertical_id).all()
    return jsonify([{
        'id': dept.id,
        'name': dept.name,
        'vertical_id': dept.vertical_id
    } for dept in departments])

@master_bp.route('/departments', methods=['POST'])
@admin_required
def create_department():
    data = request.json
    if not data.get('vertical_id'):
        return jsonify({'error': 'vertical_id is required'}), 400
        
    department = Department(
        name=data['name'],
        vertical_id=data['vertical_id']
    )
    db.session.add(department)
    db.session.commit()
    return jsonify({
        'message': 'Department added successfully',
        'department': {
            'id': department.id,
            'name': department.name,
            'vertical_id': department.vertical_id
        }
    })

# Vertical routes
@master_bp.route('/verticals', methods=['GET', 'POST'])
# @admin_required
def manage_verticals():
    if request.method == 'GET':
        verticals = Vertical.query.all()
        scopes = request.args.get('scopes', type=str)            
        return jsonify([{
            'id': vert.id,
            'name': vert.name
        } for vert in verticals])
    
    data = request.json
    vertical = Vertical(name=data['name'])
    db.session.add(vertical)
    db.session.commit()
    return jsonify({'message': 'Vertical added successfully'})

# Scope routes
@master_bp.route('/scopes', methods=['GET', 'POST'])
# @admin_required
def manage_scopes():
    if request.method == 'GET':
        scopes = Scope.query.all()
        return jsonify([{
            'id': scope.id,
            'name': scope.name
        } for scope in scopes])
    
    data = request.json
    scope = Scope(name=data['name'])
    db.session.add(scope)
    db.session.commit()
    return jsonify({'message': 'Scope added successfully'})

@master_bp.route('/users/<int:user_id>/verticals', methods=['GET', 'POST'])
# @admin_required
def manage_user_verticals(user_id):
    user = User.query.get_or_404(user_id)
    
    if request.method == 'GET':
        return jsonify([{
            'id': v.id,
            'name': v.name
        } for v in user.verticals])
    
    data = request.json
    vertical_ids = data.get('vertical_ids', [])
    verticals = Vertical.query.filter(Vertical.id.in_(vertical_ids)).all()
    user.verticals = verticals
    db.session.commit()
    return jsonify({'message': 'User verticals updated successfully'})

@master_bp.route('/users/<int:user_id>/departments', methods=['GET', 'POST'])
# @admin_required
def manage_user_departments(user_id):
    user = User.query.get_or_404(user_id)
    
    if request.method == 'GET':
        vertical_id = request.args.get('vertical_id', type=int)
        if vertical_id:
            departments = Department.query.join(user_departments).filter(
                user_departments.c.user_id == user_id,
                user_departments.c.vertical_id == vertical_id
            ).all()
        else:
            departments = user.departments
            
        return jsonify([{
            'id': d.id,
            'name': d.name,
            'vertical_id': d.vertical_id
        } for d in departments])
    
    data = request.json
    if not data.get('vertical_id'):
        return jsonify({'error': 'vertical_id is required'}), 400
        
    department_ids = data.get('department_ids', [])
    departments = Department.query.filter(
        Department.id.in_(department_ids),
        Department.vertical_id == data['vertical_id']
    ).all()
    
    # Update user departments for this vertical
    stmt = user_departments.delete().where(
        user_departments.c.user_id == user_id,
        user_departments.c.vertical_id == data['vertical_id']
    )
    db.session.execute(stmt)
    
    for dept in departments:
        stmt = user_departments.insert().values(
            user_id=user_id,
            department_id=dept.id,
            vertical_id=data['vertical_id']
        )
        db.session.execute(stmt)
    
    db.session.commit()
    return jsonify({'message': 'User departments updated successfully'})

@master_bp.route('/verticals/<int:vertical_id>', methods=['PUT', 'DELETE'])
@admin_required
def manage_vertical(vertical_id):
    vertical = Vertical.query.get_or_404(vertical_id)
    
    if request.method == 'DELETE':
        # Check if vertical has departments
        if vertical.departments:
            return jsonify({'error': 'Cannot delete vertical with existing departments'}), 400
        db.session.delete(vertical)
        db.session.commit()
        return jsonify({'message': 'Vertical deleted successfully'})
    
    # PUT method
    data = request.json
    vertical.name = data['name']
    db.session.commit()
    return jsonify({
        'message': 'Vertical updated successfully',
        'vertical': {'id': vertical.id, 'name': vertical.name}
    })

@master_bp.route('/departments/<int:dept_id>', methods=['PUT', 'DELETE'])
# @admin_required
def manage_department(dept_id):
    dept = Department.query.get_or_404(dept_id)
    
    if request.method == 'DELETE':
        # Check if department has users
        if dept.users:
            return jsonify({'error': 'Cannot delete department with assigned users'}), 400
        db.session.delete(dept)
        db.session.commit()
        return jsonify({'message': 'Department deleted successfully'})
    
    # PUT method
    data = request.json
    dept.name = data['name']
    if 'vertical_id' in data:
        dept.vertical_id = data['vertical_id']
    db.session.commit()
    return jsonify({
        'message': 'Department updated successfully',
        'department': {
            'id': dept.id,
            'name': dept.name,
            'vertical_id': dept.vertical_id
        }
    })

@master_bp.route('/users/<int:user_id>/master-data', methods=['GET'])
def get_user_master_data(user_id):
    user = User.query.get_or_404(user_id)
    
    return jsonify({
        'verticals': [{
            'id': v.id,
            'name': v.name,
            'departments': [{
                'id': d.id,
                'name': d.name
            } for d in Department.query.filter_by(vertical_id=v.id).all()]
        } for v in user.verticals],
        'scopes': [{
            'id': scope_id,
            'name': Scope.query.get(scope_id).name
        } for scope_id in (user.scopes or [])]
    })

@master_bp.route('/users/<int:user_id>/positions', methods=['GET'])
def get_user_positions(user_id):
    user = User.query.get_or_404(user_id)
    
    if not user.position_id:
        return jsonify([])
    
    # Get user's position level
    user_position = Position.query.get(user.position_id)
    if not user_position:
        return jsonify([])
    
    # Get all positions up to and including user's level
    positions = Position.query.filter(
        Position.level <= user_position.level
    ).order_by(Position.level).all()
    
    return jsonify([{
        'id': pos.id,
        'level': pos.level,
        'name': pos.name
    } for pos in positions])

@master_bp.route('/users/<int:user_id>/positions', methods=['PUT'])
# @admin_required
def update_user_position(user_id):  
    user = User.query.get_or_404(user_id)
    data = request.json
    
    position_id = data.get('position_id')
    if position_id:
        position = Position.query.get_or_404(position_id)
        user.position_id = position.id
        db.session.commit()
        return jsonify({
            'message': 'User position updated successfully',
            'position': {
                'id': position.id,
                'level': position.level,
                'name': position.name
            }
        })
    
    return jsonify({'error': 'position_id is required'}), 400

@master_bp.route('/users/<int:user_id>/scopes', methods=['GET'])
def get_user_scopes(user_id):
    user = User.query.get_or_404(user_id)
    
    if not user.scopes:
        return jsonify([])
    
    # Get all scopes that match the user's scope IDs
    scopes = Scope.query.filter(Scope.id.in_(user.scopes)).all()
    
    return jsonify([{
        'id': scope.id,
        'name': scope.name
    } for scope in scopes])