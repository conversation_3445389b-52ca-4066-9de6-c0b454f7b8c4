"""
Planner Agent for the Himalaya Azure system.

This agent analyzes user queries and determines which specialized agent should handle the request.
"""

import os
import json
from typing import Dict, List, Any, TypedDict, Annotated, Literal, Optional, Union, Tuple
import operator
from enum import Enum

from langchain_core.messages import AnyMessage, SystemMessage, HumanMessage
from langchain_openai import ChatOpenAI, AzureChatOpenAI
from langgraph.graph import StateGraph, END
import datetime
import re

# Import configuration
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
from config.settings import AZURE_OPENAI_KEY, AZURE_OPENAI_API_VERSION, AZURE_OPENAI_ENDPOINT, AZURE_OPENAI_DEPLOYMENT_NAME

class AgentType(str, Enum):
    """Enum for different agent types"""
    WEB_SEARCH = "web_search"
    RAG = "rag"
    CSV = "csv"   #we dont need this as this is part of "rag"
    SQL = "sql"
    CONVERSATION = "conversation"
    GPT = "gpt"  # New agent type for GPT knowledge-based responses
    UNIVERSAL = "universal" # Agent that combines multiple specialized agents
    DEEP_SEARCH = "deep_search" # Agent for deep research with decomposition and synthesis
    UNKNOWN = "unknown"

class PlannerState(TypedDict):
    """State for the planner agent"""
    messages: Annotated[List[AnyMessage], operator.add]
    query: str
    conversation_history: Optional[str]
    conversation_summary: Optional[str]
    web_search: bool
    agent_type: AgentType
    api_agent_type: Optional[str]
    analysis: Dict[str, Any]
    qa_observations: Optional[Dict[str, Any]]
    improvement_instructions: Optional[str]
    require_table: bool
    require_chart: bool
    chart_type: Optional[str]

class PlannerAgent:
    """
    Planner agent that analyzes user queries and determines which specialized agent should handle the request.
    """

    def __init__(self, model=None, system_prompt=None, verbose=True):
        """
        Initialize the planner agent.

        Args:
            model: LLM to use for planning
            system_prompt: System prompt for the planner
            verbose: Whether to print verbose output
        """
        self.verbose = verbose
        self.today_date = datetime.datetime.now().strftime("%Y-%m-%d")
        # Initialize the model
        if model:
            self.model = model
        else:
            self.model = AzureChatOpenAI(
                azure_deployment=AZURE_OPENAI_DEPLOYMENT_NAME,
                openai_api_version=AZURE_OPENAI_API_VERSION,
                azure_endpoint=AZURE_OPENAI_ENDPOINT,
                openai_api_key=AZURE_OPENAI_KEY,
                temperature=0.1
            )
            if self.verbose:
                print(f"Using Azure OpenAI with deployment {AZURE_OPENAI_DEPLOYMENT_NAME}")

        # Set the system prompt
        self.system_prompt = system_prompt or self._get_default_system_prompt()

        # Create the graph
        graph = StateGraph(PlannerState)

        # Add nodes
        graph.add_node("analyze_query", self.analyze_query)
        graph.add_node("determine_agent", self.determine_agent)

        # Add edges
        graph.add_edge("analyze_query", "determine_agent")
        graph.add_edge("determine_agent", END)

        # Set entry point
        graph.set_entry_point("analyze_query")

        # Compile the graph
        self.graph = graph.compile()

    def _get_default_system_prompt(self) -> str:
        """
        Get the default system prompt for the planner agent.

        Returns:
            The default system prompt
        """
        return f"""You are a planning agent that analyzes user queries to improve them and generate standalone versions.

Your primary tasks are:
1. Understand the intent of the query
2. Create a standalone version of the query with all references resolved
3. Determine if the query is conversational in nature

IMPORTANT: The agent_type is now determined by the API call (api_agent_type), not by you. Your role is to focus on creating the best standalone query for the specified agent type, not to decide which agent should handle the query, except where you think the query is conversational in nature.

AGENT TYPES:
- web_search: For queries that need real-time information from the web
- rag: For queries about internal documents and knowledge base
- gpt: For general knowledge questions that don't require specific documents
- universal: For complex queries that may need multiple types of information
- deep_search: For complex research questions requiring thorough analysis of documents, decomposition of questions, and comprehensive answers with citations

SPECIAL FILE SUMMARY DETECTION:
- If the user has selected specific files and asks for summaries, overviews, or "what are these files about", note this in your analysis
- Keywords indicating file summary requests: "summary", "about", "overview", "describe", "what is/are", "tell me about"
- When users select files and ask summary questions, they want comprehensive coverage of ALL selected files, not just semantically relevant chunks

PAY SPECIAL ATTENTION TO DATA VISUALIZATION NEEDS:
- If the query asks about trends, comparisons, distributions, or patterns, it likely requires a chart or graph
- If the query asks for numerical data across multiple categories, time periods, or entities, it likely requires a table
- Keywords that suggest tables: "list", "compare", "show data for", "statistics", "numbers", "figures", "breakdown"
- Keywords that suggest charts: "trend", "growth", "decline", "visualization", "graph", "chart", "plot", "compare visually", "percentage", "proportion"
- If the query mentions specific chart types (bar chart, line graph, pie chart, etc.), definitely set require_chart to true

DETERMINE THE MOST APPROPRIATE CHART TYPE:
- For time series, trends over time, or historical data: use 'line' charts
- For comparing values across categories: use 'bar' charts
- For showing parts of a whole or percentage distributions: use 'pie' charts
- For showing relationships between two variables: use 'scatter' charts
- For showing geographical data: use 'map' charts
- For showing hierarchical data: use 'tree' or 'sunburst' charts
- If the query explicitly mentions a chart type, use that type

PAY SPECIAL ATTENTION TO CONVERSATION CONTEXT:
- The conversation summary contains key entities, facts, and relationships from the entire conversation
- If the query contains pronouns (it, they, this, etc.), you MUST resolve them using the conversation summary
- If the query is a follow-up question, you MUST use the conversation summary to understand what it's following up on
- If the query refers to entities or concepts mentioned previously, you MUST use the conversation summary to identify them
- For common follow-up patterns like "do they have any kids?", "what is his name?", etc., ALWAYS resolve pronouns to the MOST RECENT entity mentioned in the conversation

YOUR MOST IMPORTANT TASK IS TO RESOLVE AMBIGUOUS REFERENCES:
- When the user says "it", "this", "that", "they", "their", "his", "her", etc., you MUST determine what they're referring to
- ALWAYS resolve pronouns to the MOST RECENT entity mentioned in the conversation summary
- Use the conversation summary to resolve these references, focusing on the most recently discussed entities
- Create a standalone, unambiguous query that explicitly includes the referenced entities
- For example, if the user asks "Where is it located?" and the conversation just discussed Atlas Systems, transform this to "Where is Atlas Systems located?"
- For example, if the user asks "What products do they offer?" and the conversation just discussed Microsoft, transform this to "What products does Microsoft offer?"
- For example, if the user asks "Who is his wife?" after discussing Dwayne Johnson, transform this to "Who is Dwayne Johnson's wife?"

INCORPORATE QA OBSERVATIONS AND IMPROVEMENT INSTRUCTIONS:
- If QA observations are provided, use them to understand what information was missing or unclear in previous responses
- If improvement instructions are provided, incorporate them into your analysis and standalone query
- Pay attention to the completeness, relevance, and clarity scores to understand what aspects need improvement
- Use the missing information list to identify specific details that should be included in the query
- If the previous answer was incomplete, make sure your standalone query explicitly asks for the missing information

DETECT CONVERSATIONAL QUERIES:
- If the query is a general greeting, thanks, or casual conversation, mark it as "conversation" agent_type
- Examples: "hello", "hi there", "how are you", "thanks", "thank you", "goodbye", etc.
- This is the ONLY case where you should override the API-specified agent_type

DEEP RESEARCH QUERIES:
- If the api_agent_type is "deep_search", format the standalone query to be comprehensive and specific
- Deep search is ideal for complex research questions that require analyzing multiple documents
- For deep search queries, include specific aspects that need to be covered in the answer
- Make the query detailed enough to guide thorough research while remaining focused on the core question

IMPORTANT: Your response must be a valid JSON object with the following structure:
{{
    "intent": "brief description of what the query is trying to achieve",
    "agent_type": "conversation" if it's conversational, otherwise use the API-specified agent_type,
    "standalone_query": "a clear, unambiguous version of the query with all references resolved",
    "require_table": true/false,
    "require_chart": true/false,
    "chart_type": "line" | "bar" | "pie" | "scatter" | "map" | "tree" | null,
    "visualization_reasoning": "explanation of why tables or charts are needed or not needed"
}}

For the standalone_query field:
- ALWAYS include the full entity name instead of pronouns
- Make the query completely self-contained so it can be understood without any context
- Ensure it's a complete, grammatically correct question
- Do not include phrases like "based on our previous conversation" or "as mentioned earlier"
- For follow-up questions with pronouns ("his", "her", "their", "they", etc.), ALWAYS resolve them to the MOST RECENT entity in the conversation summary
- Tailor the standalone query to work best with the specified agent_type from the API
"""

    def analyze_query(self, state: PlannerState) -> PlannerState:
        """
        Analyze the user query to determine intent and required agent.

        Args:
            state: The current state

        Returns:
            Updated state with analysis
        """
        if self.verbose:
            print("\n=== Starting analyze_query node ===")
            print(f"Query: {state['query']}")
            print(f"Incoming web_search flag: {state['web_search']}")
            print(f"Incoming api_agent_type: {state.get('api_agent_type', 'None')}")

        # Get the query and web_search flag from state
        query = state["query"]
        web_search = state["web_search"] is True  # Ensure it's a boolean
        api_agent_type = state.get("api_agent_type")

        # Format QA observations if available
        qa_observations_text = ""
        if state.get('qa_observations'):
            qa_observations = state.get('qa_observations', {})
            qa_observations_text = f"""
            QA Observations:
            Completeness: {qa_observations.get('evaluation', {}).get('completeness', 'N/A')}/5
            Relevance: {qa_observations.get('evaluation', {}).get('relevance', 'N/A')}/5
            Clarity: {qa_observations.get('evaluation', {}).get('clarity', 'N/A')}/5
            Missing Information: {', '.join(qa_observations.get('missing_information', []) or ['None'])}
            """

        # Format improvement instructions if available
        improvement_instructions_text = ""
        if state.get('improvement_instructions'):
            improvement_instructions_text = f"""
            Improvement Instructions: {state['improvement_instructions']}
            """

        messages = [
            SystemMessage(content=self.system_prompt),
            HumanMessage(content=f"""
            Query: {state['query']}
            Conversation History: {state.get('conversation_history', 'None')}
            Conversation Summary: {state.get('conversation_summary', 'None')}
            API Agent Type: {api_agent_type or "None"}
            {qa_observations_text}
            {improvement_instructions_text}

            Analyze this query and create a standalone version with all references resolved.
            If the query is conversational in nature, mark it as "conversation" agent type.
            Otherwise, use the API-specified agent type: {api_agent_type or "None"}
            If there are QA observations or improvement instructions, incorporate them into your analysis and standalone query.
            """)
        ]

        response = self.model.invoke(messages)

        try:
            # Extract JSON from response
            content = response.content
            if "```json" in content:
                content = content.split("```json")[1].split("```")[0].strip()
            elif "```" in content:
                content = content.split("```")[1].strip()

            analysis = json.loads(content)

            if self.verbose:
                print("Parsed analysis:", json.dumps(analysis, indent=2))

            # Get the standalone query and agent type from the analysis
            standalone_query = analysis.get("standalone_query", state["query"])
            
            # Determine agent type based on API agent type if provided
            if api_agent_type:
                # Use the API-specified agent type, but allow override to conversation for conversational queries
                llm_agent_type = analysis.get("agent_type", "unknown")
                if llm_agent_type == "conversation":
                    agent_type = llm_agent_type
                    if self.verbose:
                        print(f"Overriding API agent_type '{api_agent_type}' to 'conversation' based on query analysis")
                else:
                    agent_type = api_agent_type
                    if self.verbose:
                        print(f"Using API-specified agent_type: {api_agent_type}")
            else:
                # Fall back to LLM-determined agent type if API doesn't specify one
                agent_type = analysis.get("agent_type", "unknown")

            # Add web_search information to the analysis
            # This is determined by the API call, not the LLM
            analysis["requires_web_search"] = web_search
            if web_search:
                analysis["reasoning"] = "Web search flag is set to True in the API call"
            else:
                analysis["reasoning"] = "Web search flag is set to False in the API call"

        except (json.JSONDecodeError, KeyError) as e:
            if self.verbose:
                print(f"Error parsing response: {e}")

            # SAFETY FIX: When query analysis fails, route to conversation agent for safe error handling
            analysis = {
                "intent": "Unable to analyze query due to technical error",
                "requires_web_search": False,
                "reasoning": "Query analysis system failure - routing to conversation agent for safe handling",
                "agent_type": "conversation",
                "standalone_query": "I'm having trouble understanding your request. Could you please rephrase it?"
            }
            standalone_query = "I'm having trouble understanding your request. Could you please rephrase it?"

            # Always route to conversation agent when analysis fails to prevent unsafe processing
            agent_type = "conversation"

        # Store the original query in the analysis for debugging
        analysis["original_query"] = state["query"]
        analysis["standalone_query"] = standalone_query

        # Update state with analysis results
        state["analysis"] = analysis
        state["web_search"] = web_search
        state["agent_type"] = AgentType(agent_type)
        state["query"] = standalone_query

        if self.verbose:
            print(f"Analysis complete: {analysis}")
            print(f"Selected agent type: {agent_type}")
            print("=== Ending analyze_query node ===\n")

        return state

    def determine_agent(self, state: PlannerState) -> PlannerState:
        """
        Determine which agent should handle the query based on the analysis.

        Args:
            state: The current state

        Returns:
            Updated state with agent type
        """
        if self.verbose:
            print("\n=== Starting determine_agent node ===")
            print(f"Agent type: {state['agent_type']}")
            print(f"Web search: {state['web_search']}")
            print(f"API agent type: {state.get('api_agent_type', 'None')}")

        # If the agent type is already CONVERSATION, keep it that way
        # This allows the LLM to override to conversation for conversational queries
        if state['agent_type'] == AgentType.CONVERSATION:
            if self.verbose:
                print("Keeping agent type as CONVERSATION based on query analysis")
            return state

        if self.verbose:
            print("\n=== Ending determine_agent node ===")

        return state

def create_planner_agent(model=None, system_prompt=None, verbose=True) -> PlannerAgent:
    """
    Create a planner agent.

    Args:
        model: LLM to use for planning
        system_prompt: System prompt for the planner
        verbose: Whether to print verbose output

    Returns:
        A planner agent
    """
    return PlannerAgent(model=model, system_prompt=system_prompt, verbose=verbose)
