"""
Planner Agent for the Himalaya Azure system.

This agent analyzes user queries and determines which specialized agent should handle the request.
"""

import os
import json
from typing import Dict, List, Any, TypedDict, Annotated, Literal, Optional, Union, Tuple
import operator
from enum import Enum

from langchain_core.messages import AnyMessage, SystemMessage, HumanMessage
from langchain_openai import ChatOpenAI, AzureChatOpenAI
from langgraph.graph import StateGraph, END
import datetime
import re

# Import configuration
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
from config.settings import AZURE_OPENAI_KEY, AZURE_OPENAI_API_VERSION, AZURE_OPENAI_ENDPOINT, AZURE_OPENAI_DEPLOYMENT_NAME

class AgentType(str, Enum):
    """Enum for different agent types"""
    WEB_SEARCH = "web_search"
    RAG = "rag"
    CSV = "csv"
    SQL = "sql"
    CONVERSATION = "conversation"
    UNKNOWN = "unknown"

class PlannerState(TypedDict):
    """State for the planner agent"""
    messages: Annotated[List[AnyMessage], operator.add]
    query: str
    conversation_history: Optional[str]
    conversation_summary: Optional[str]
    web_search: bool
    agent_type: AgentType
    analysis: Dict[str, Any]
    qa_observations: Optional[Dict[str, Any]]
    improvement_instructions: Optional[str]
    require_table: bool
    require_chart: bool
    chart_type: Optional[str]

class PlannerAgent:
    """
    Planner agent that analyzes user queries and determines which specialized agent should handle the request.
    """

    def __init__(self, model=None, system_prompt=None, verbose=True):
        """
        Initialize the planner agent.

        Args:
            model: LLM to use for planning
            system_prompt: System prompt for the planner
            verbose: Whether to print verbose output
        """
        self.verbose = verbose
        self.today_date = datetime.datetime.now().strftime("%Y-%m-%d")
        # Initialize the model
        if model:
            self.model = model
        else:
            self.model = AzureChatOpenAI(
                azure_deployment=AZURE_OPENAI_DEPLOYMENT_NAME,
                openai_api_version=AZURE_OPENAI_API_VERSION,
                azure_endpoint=AZURE_OPENAI_ENDPOINT,
                openai_api_key=AZURE_OPENAI_KEY,
                temperature=0.1
            )
            if self.verbose:
                print(f"Using Azure OpenAI with deployment {AZURE_OPENAI_DEPLOYMENT_NAME}")

        # Set the system prompt
        self.system_prompt = system_prompt or self._get_default_system_prompt()

        # Create the graph
        graph = StateGraph(PlannerState)

        # Add nodes
        graph.add_node("analyze_query", self.analyze_query)
        graph.add_node("determine_agent", self.determine_agent)

        # Add edges
        graph.add_edge("analyze_query", "determine_agent")
        graph.add_edge("determine_agent", END)

        # Set entry point
        graph.set_entry_point("analyze_query")

        # Compile the graph
        self.graph = graph.compile()

    def _get_default_system_prompt(self) -> str:
        """
        Get the default system prompt for the planner agent.

        Returns:
            The default system prompt
        """
        return f"""You are a planning agent that analyzes user queries to determine:
1. The intent of the query
2. Whether the query requires web search or can be answered using existing knowledge
3. Which specialized agent should handle the query

Your task is to analyze the query, conversation history, and conversation summary to determine:
- If the query explicitly mentions web search or requires up-to-date information from the web
- If the query is about analyzing structured data (CSV, tables)
- If the query is about querying a database
- If the query can be answered using existing knowledge (RAG)

SPECIAL FILE SUMMARY DETECTION:
- If the user has selected specific files and asks for summaries, overviews, or "what are these files about", prefer direct file processing over semantic search
- Keywords indicating file summary requests: "summary", "about", "overview", "describe", "what is/are", "tell me about"
- When users select files and ask summary questions, they want comprehensive coverage of ALL selected files, not just semantically relevant chunks
- For file summary requests, set agent_type to "rag" but add a note in the reasoning that this should use direct file processing

PAY SPECIAL ATTENTION TO DATA VISUALIZATION NEEDS:
- If the query asks about trends, comparisons, distributions, or patterns, it likely requires a chart or graph
- If the query asks for numerical data across multiple categories, time periods, or entities, it likely requires a table
- Keywords that suggest tables: "list", "compare", "show data for", "statistics", "numbers", "figures", "breakdown"
- Keywords that suggest charts: "trend", "growth", "decline", "visualization", "graph", "chart", "plot", "compare visually", "percentage", "proportion"
- If the query mentions specific chart types (bar chart, line graph, pie chart, etc.), definitely set require_chart to true

DETERMINE THE MOST APPROPRIATE CHART TYPE:
- For time series, trends over time, or historical data: use 'line' charts
- For comparing values across categories: use 'bar' charts
- For showing parts of a whole or percentage distributions: use 'pie' charts
- For showing relationships between two variables: use 'scatter' charts
- For showing geographical data: use 'map' charts
- For showing hierarchical data: use 'tree' or 'sunburst' charts
- If the query explicitly mentions a chart type, use that type

IMPORTANT GUIDELINES FOR CURRENT INFORMATION:
- ALWAYS use web search for queries about current events, recent developments, or time-sensitive information
- NEVER rely on your pre-trained knowledge for current information - it may be outdated
- For your information, today is {self.today_date} and can be ahead of your pre-trained knowledge ( which is in past).
- For questions about "who is currently" in a position, "what is the current state" of something, "what is the recent state" of something or similar time-sensitive queries, ALWAYS use web search and include todays date in the Standalone Query which is {self.today_date}.
- When a user asks about information as of a specific date (especially future dates), ALWAYS use web search

PAY SPECIAL ATTENTION TO CONVERSATION CONTEXT:
- The conversation summary contains key entities, facts, and relationships from the entire conversation
- If the query contains pronouns (it, they, this, etc.), you MUST resolve them using the conversation summary
- If the query is a follow-up question, you MUST use the conversation summary to understand what it's following up on
- If the query refers to entities or concepts mentioned previously, you MUST use the conversation summary to identify them
- If the previous conversation was using web search, continue using web search unless explicitly directed otherwise
- For common follow-up patterns like "do they have any kids?", "what is his name?", etc., ALWAYS resolve pronouns to the MOST RECENT entity mentioned in the conversation

YOUR MOST IMPORTANT TASK IS TO RESOLVE AMBIGUOUS REFERENCES:
- When the user says "it", "this", "that", "they", "their", "his", "her", etc., you MUST determine what they're referring to
- ALWAYS resolve pronouns to the MOST RECENT entity mentioned in the conversation summary
- Use the conversation summary to resolve these references, focusing on the most recently discussed entities
- Create a standalone, unambiguous query that explicitly includes the referenced entities
- For example, if the user asks "Where is it located?" and the conversation just discussed Atlas Systems, transform this to "Where is Atlas Systems located?"
- For example, if the user asks "What products do they offer?" and the conversation just discussed Microsoft, transform this to "What products does Microsoft offer?"
- For example, if the user asks "Who is his wife?" after discussing Dwayne Johnson, transform this to "Who is Dwayne Johnson's wife?"

INCORPORATE QA OBSERVATIONS AND IMPROVEMENT INSTRUCTIONS:
- If QA observations are provided, use them to understand what information was missing or unclear in previous responses
- If improvement instructions are provided, incorporate them into your analysis and standalone query
- Pay attention to the completeness, relevance, and clarity scores to understand what aspects need improvement
- Use the missing information list to identify specific details that should be included in the query
- If the previous answer was incomplete, make sure your standalone query explicitly asks for the missing information

IMPORTANT: Your response must be a valid JSON object with the following structure:
{{
    "intent": "brief description of what the query is trying to achieve",
    "requires_web_search": true/false,
    "reasoning": "explanation of why web search is or isn't needed",
    "agent_type": "web_search" | "rag" | "csv" | "sql",
    "standalone_query": "a clear, unambiguous version of the query with all references resolved",
    "require_table": true/false,
    "require_chart": true/false,
    "chart_type": "line" | "bar" | "pie" | "scatter" | "map" | "tree" | null,
    "visualization_reasoning": "explanation of why tables or charts are needed or not needed"
}}

For the standalone_query field:
- ALWAYS include the full entity name instead of pronouns
- Make the query completely self-contained so it can be understood without any context
- Ensure it's a complete, grammatically correct question
- Do not include phrases like "based on our previous conversation" or "as mentioned earlier"
- For follow-up questions with pronouns ("his", "her", "their", "they", etc.), ALWAYS resolve them to the MOST RECENT entity in the conversation summary

For the agent_type field:
- Use "web_search" if the query requires searching the web
- Use "rag" if the query can be answered using existing knowledge or documents
- Use "csv" if the query is about analyzing structured data, tables, or spreadsheets
- Use "sql" if the query is about querying a database
- Use "conversation" if the query is a general chat, greeting, or doesn't require specific document analysis

AUTOMATIC TABLE DETECTION FOR RAG QUERIES:
- If the query mentions tables, data analysis, statistics, numbers, calculations, or comparisons, consider using "rag" agent type
- The RAG agent will automatically detect if documents contain tables and delegate to CSV processing when needed
- Keywords that suggest table analysis: "table", "data", "statistics", "numbers", "calculate", "sum", "average", "count", "analyze", "compare", "trend", "breakdown", "distribution"
- The RAG agent has intelligent document segregation and will route table-containing documents to specialized CSV processing

If the query explicitly mentions "web=True", always set requires_web_search to true and agent_type to "web_search".
If the query explicitly mentions "web=False", always set requires_web_search to false and agent_type to "rag".
"""

    def analyze_query(self, state: PlannerState) -> PlannerState:
        """
        Analyze the user query to determine intent and required agent.

        Args:
            state: The current state

        Returns:
            Updated state with analysis
        """
        if self.verbose:
            print("\n=== Starting analyze_query node ===")
            print(f"Query: {state['query']}")
            print(f"Incoming web_search flag: {state['web_search']}")

        # Get the query and web_search flag from state
        query = state["query"]
        web_search = state["web_search"] is True  # Ensure it's a boolean

        # Check for explicit web search flag in query
        if "web=true" in query.lower():
            if self.verbose:
                print("Explicit web=True detected in query")
            web_search = True
        elif "web=false" in query.lower():
            if self.verbose:
                print("Explicit web=False detected in query")
            web_search = False

        # Format QA observations if available
        qa_observations_text = ""
        if state.get('qa_observations'):
            qa_observations = state.get('qa_observations', {})
            qa_observations_text = f"""
            QA Observations:
            Completeness: {qa_observations.get('evaluation', {}).get('completeness', 'N/A')}/5
            Relevance: {qa_observations.get('evaluation', {}).get('relevance', 'N/A')}/5
            Clarity: {qa_observations.get('evaluation', {}).get('clarity', 'N/A')}/5
            Missing Information: {', '.join(qa_observations.get('missing_information', []) or ['None'])}
            """

        # Format improvement instructions if available
        improvement_instructions_text = ""
        if state.get('improvement_instructions'):
            improvement_instructions_text = f"""
            Improvement Instructions: {state['improvement_instructions']}
            """

        messages = [
            SystemMessage(content=self.system_prompt),
            HumanMessage(content=f"""
            Query: {state['query']}
            Conversation History: {state.get('conversation_history', 'None')}
            Conversation Summary: {state.get('conversation_summary', 'None')}
            {qa_observations_text}
            {improvement_instructions_text}

            Analyze this query and determine if it requires web search and which agent should handle it.
            Create a standalone version of the query with all references resolved.
            If there are QA observations or improvement instructions, incorporate them into your analysis and standalone query.
            """)
        ]

        response = self.model.invoke(messages)

        try:
            # Extract JSON from response
            content = response.content
            if "```json" in content:
                content = content.split("```json")[1].split("```")[0].strip()
            elif "```" in content:
                content = content.split("```")[1].strip()

            analysis = json.loads(content)

            if self.verbose:
                print("Parsed analysis:", json.dumps(analysis, indent=2))

            # Get the standalone query and agent type from the analysis
            standalone_query = analysis.get("standalone_query", state["query"])
            agent_type = analysis.get("agent_type", "unknown")

            # IMPORTANT: Don't let the LLM override the web_search flag
            # Only use the LLM's web_search suggestion if it's False and the API flag is True
            llm_web_search = analysis.get("requires_web_search", False)
            if web_search is False and llm_web_search is True:
                if self.verbose:
                    print("LLM suggested web search but API flag is False. Converting to RAG.")
                web_search = False
                if agent_type == "web_search":
                    agent_type = "rag"
                    analysis["agent_type"] = "rag"
                    analysis["requires_web_search"] = False
                    analysis["reasoning"] += " (Converted to RAG as web_search flag is False in API call)"

        except (json.JSONDecodeError, KeyError) as e:
            if self.verbose:
                print(f"Error parsing response: {e}")

            # Default analysis if parsing fails
            analysis = {
                "intent": "Could not determine intent",
                "requires_web_search": web_search,
                "reasoning": "Error in analysis",
                "agent_type": "web_search" if web_search else "rag",
                "standalone_query": state["query"]
            }
            standalone_query = state["query"]
            agent_type = "web_search" if web_search else "rag"

        # Store the original query in the analysis for debugging
        analysis["original_query"] = state["query"]
        analysis["standalone_query"] = standalone_query

        # Update state with analysis results
        state["analysis"] = analysis
        state["web_search"] = web_search
        state["agent_type"] = AgentType(agent_type)
        state["query"] = standalone_query

        if self.verbose:
            print(f"Analysis complete: {analysis}")
            print("=== Ending analyze_query node ===\n")

        return state

    def determine_agent(self, state: PlannerState) -> PlannerState:
        """
        Determine which agent should handle the query based on the analysis.

        Args:
            state: The current state

        Returns:
            Updated state with agent type
        """
        if self.verbose:
            print("\n=== Starting determine_agent node ===")
            print(f"Agent type: {state['agent_type']}")
            print(f"Web search: {state['web_search']}")

        # IMPORTANT: If web_search is True, always override to WEB_SEARCH agent type
        # This should only happen if it was explicitly set to True in the API call
        if state['web_search'] is True:  # Explicitly check for boolean True
            if self.verbose:
                print("Overriding agent type to WEB_SEARCH due to web_search=True")
            return {
                **state,
                "agent_type": AgentType.WEB_SEARCH
            }
        
        # If web_search is False but agent_type is WEB_SEARCH, convert to RAG
        if state['web_search'] is False and state['agent_type'] == AgentType.WEB_SEARCH:
            if self.verbose:
                print("Converting agent type from WEB_SEARCH to RAG due to web_search=False")
            return {
                **state,
                "agent_type": AgentType.RAG
            }

        if self.verbose:
            print("\n=== Ending determine_agent node ===")

        return state

def create_planner_agent(model=None, system_prompt=None, verbose=True) -> PlannerAgent:
    """
    Create a planner agent.

    Args:
        model: LLM to use for planning
        system_prompt: System prompt for the planner
        verbose: Whether to print verbose output

    Returns:
        A planner agent
    """
    return PlannerAgent(model=model, system_prompt=system_prompt, verbose=verbose)
