"""
Web Search Agent for the Himalaya Azure system.

This agent handles web-based queries by searching the web, scraping content, and generating responses.
"""

import os
import re
import json
import time
import random
import urllib.parse
import hashlib
from typing import Dict, List, Any, TypedDict, Annotated, Optional, Union
import operator
import datetime
from pathlib import Path
import threading
from datetime import timedelta
import signal
import platform

from langchain_core.messages import AnyMessage, SystemMessage, HumanMessage
from langchain_openai import ChatOpenAI, AzureChatOpenAI
from langchain_community.utilities import GoogleSerperAPIWrapper
from langchain_community.document_loaders import WebBaseLoader
from langchain_community.document_loaders import PyPDFLoader
from langchain_community.document_loaders import YoutubeLoader
from langchain.schema import Document
from langgraph.graph import StateGraph, END
import datetime
import tiktoken

# Import configuration
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
from config.settings import SERPER_API_KEY, AZURE_OPENAI_KEY, AZURE_OPENAI_API_VERSION, AZURE_OPENAI_ENDPOINT, AZURE_OPENAI_DEPLOYMENT_NAME
top_search_results = 4

class WebSearchState(TypedDict):
    """State for the web search agent"""
    messages: Annotated[List[AnyMessage], operator.add]
    query: str
    search_results: List[str]  # list of URLs
    scraped_content: Dict[str, str]  # URL -> content
    previous_content: Optional[Dict[str, str]]  # Previously scraped content to check first
    response: Dict[str, Any]
    conversation_id: Optional[int] = None  # Conversation ID for saving scraped content

class WebSearchAgent:
    """
    Web search agent that handles web-based queries by searching the web,
    scraping content, and generating responses.
    """

    def __init__(self, model=None, verbose=True):
        """
        Initialize the web search agent.

        Args:
            model: LLM to use for response generation
            verbose: Whether to print verbose output
        """
        self.verbose = verbose
        self.today_date = datetime.datetime.now().strftime("%Y-%m-%d")
        
        # Initialize the model
        if model:
            self.model = model
        else:
            self.model = AzureChatOpenAI(
                azure_deployment=AZURE_OPENAI_DEPLOYMENT_NAME,
                openai_api_version=AZURE_OPENAI_API_VERSION,
                azure_endpoint=AZURE_OPENAI_ENDPOINT,
                openai_api_key=AZURE_OPENAI_KEY,
                temperature=0.1
            )
            if self.verbose:
                print(f"Using Azure OpenAI with deployment {AZURE_OPENAI_DEPLOYMENT_NAME}")

        # Create transcript directory if it doesn't exist
        self.transcript_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), 
                                         "data", "blob_storage", "yt_transcripts")
        os.makedirs(self.transcript_dir, exist_ok=True)

        # Create the graph
        graph = StateGraph(WebSearchState)

        # Add nodes
        graph.add_node("check_previous_content", self.check_previous_content)
        graph.add_node("search", self.search)
        graph.add_node("scrape_content", self.scrape_content)
        graph.add_node("generate_response", self.generate_response)

        # Add edges
        graph.add_edge("check_previous_content", "search")
        graph.add_edge("search", "scrape_content")
        graph.add_edge("scrape_content", "generate_response")
        graph.add_edge("generate_response", END)

        # Set entry point
        graph.set_entry_point("check_previous_content")

        # Compile the graph
        self.graph = graph.compile()

    def check_previous_content(self, state: WebSearchState) -> WebSearchState:
        """
        Check if we have previous content that might answer the query.

        Args:
            state: The current state

        Returns:
            Updated state with previous content if available
        """
        if self.verbose:
            print("\n=== Starting check_previous_content node ===")
            print(f"Query: {state['query']}")

        # Initialize scraped_content with previous content if available
        scraped_content = {}
        if state.get("previous_content") and isinstance(state["previous_content"], dict):
            if self.verbose:
                print(f"Found previous content with {len(state['previous_content'])} URLs")
            scraped_content = state["previous_content"]
        elif state.get("scraped_content") and isinstance(state["scraped_content"], dict):
            # If scraped_content is already in the state, use it
            if self.verbose:
                print(f"Using existing scraped content with {len(state['scraped_content'])} URLs")
            scraped_content = state["scraped_content"]

        # Update state
        return {
            **state,
            "scraped_content": scraped_content
        }

    def search(self, state: WebSearchState) -> WebSearchState:
        """
        Search the web for relevant results.

        Args:
            state: The current state

        Returns:
            Updated state with search results
        """
        if self.verbose:
            print("\n=== Starting search node ===")
            print(f"Query: {state['query']}")

        query = state["query"]

        # Check if we already have sufficient content from previous searches
        if state["scraped_content"] and len(state["scraped_content"]) > 0:
            # Try to generate a response with existing content first
            system_prompt = """You are an evaluator that determines if the existing content is sufficient to answer a query.

Your task is to:
1. Analyze the query and the existing content
2. Determine if the existing content contains enough information to answer the query
3. Provide a yes/no decision

IMPORTANT: Your response must be a valid JSON object with the following structure:
{
    "sufficient": true/false,
    "reasoning": "explanation of why the content is or isn't sufficient"
}

Be conservative - only say the content is sufficient if it clearly contains the information needed to answer the query.
If there's any doubt, say the content is not sufficient.
IMPORTANT: While evaluating, there may be factual questions asked by user which may be time sensitive. For your information please note that today is {self.today_date} and can be ahead of your pre-trained knowledge (which is in past).
"""

            user_prompt = f"""Query: {query}

Existing Content:
{json.dumps(state['scraped_content'], indent=2)}

Determine if this content is sufficient to answer the query.
"""

            messages = [
                SystemMessage(content=system_prompt),
                HumanMessage(content=user_prompt)
            ]

            try:
                if self.verbose:
                    print("Evaluating if existing content is sufficient")

                response = self.model.invoke(messages)

                # Parse the response
                content = response.content
                if "```json" in content:
                    content = content.split("```json")[1].split("```")[0].strip()
                elif "```" in content:
                    content = content.split("```")[1].strip()

                evaluation = json.loads(content)

                if evaluation.get("sufficient", False):
                    if self.verbose:
                        print("Existing content is sufficient, skipping search")

                    # Return the current state with empty search results
                    # The existing scraped_content will be used in generate_response
                    return {
                        **state,
                        "search_results": []
                    }
                else:
                    if self.verbose:
                        print("Existing content is not sufficient, performing search")
            except Exception as e:
                if self.verbose:
                    print(f"Error evaluating content sufficiency: {e}")

        # Extract any URLs directly mentioned in the query
        direct_urls = self._extract_urls(query)

        # If there are direct URLs, use them as search results
        if direct_urls:
            if self.verbose:
                print(f"Using {len(direct_urls)} direct URLs from query")
            search_results = direct_urls
        else:
            # Otherwise, perform a web search
            try:
                if self.verbose:
                    print("Performing web search")

                serper_client = GoogleSerperAPIWrapper(serper_api_key=SERPER_API_KEY)
                raw_results = serper_client.results(query)
                search_results = [result["link"] for result in raw_results.get("organic", [])[:top_search_results]]

                if self.verbose:
                    print(f"Found {len(search_results)} search results")
            except Exception as e:
                if self.verbose:
                    print(f"Error in web search: {e}")
                search_results = []

        # Update state
        # If we already have scraped content, make sure to include those URLs in search_results
        # so they can be properly handled in the scrape_content method
        if state.get("scraped_content") and isinstance(state["scraped_content"], dict) and state["scraped_content"]:
            existing_urls = list(state["scraped_content"].keys())
            # Add existing URLs to search_results if they're not already there
            for url in existing_urls:
                if url not in search_results:
                    search_results.append(url)

            if self.verbose and len(existing_urls) > 0:
                print(f"Added {len(existing_urls)} previously scraped URLs to search results")

        return {
            **state,
            "search_results": search_results
        }

    def scrape_content(self, state: WebSearchState) -> WebSearchState:
        """
        Scrape content from search results.

        Args:
            state: The current state

        Returns:
            Updated state with scraped content
        """
        if self.verbose:
            print("\n=== Starting scrape_content node ===")
            
        # Validate search results to ensure they are proper URLs
        search_results = []
        for url in state["search_results"]:
            try:
                # Attempt to parse the URL to validate it
                parsed = urllib.parse.urlparse(url)
                if parsed.scheme and parsed.netloc:
                    search_results.append(url)
                else:
                    if self.verbose:
                        print(f"Skipping invalid URL (missing scheme or netloc): {url}")
            except Exception as e:
                if self.verbose:
                    print(f"Skipping invalid URL: {url}, Error: {e}")
        
        if self.verbose:
            print(f"Scraping {len(search_results)} valid URLs (filtered from {len(state['search_results'])} total URLs)")
        
        # Add timeout configuration
        SCRAPING_TIMEOUT_PER_URL = 30  # seconds per URL
        MAX_SCRAPING_TIME = 120  # total maximum time for all URLs
        scraping_start_time = time.time()

        # Try to load existing content from file first
        scraped_content = {}
        if state.get('conversation_id'):
            try:
                file_path = Path("scraped_content") / f"scraped_content{state['conversation_id']}.json"
                if file_path.exists():
                    with open(file_path, 'r', encoding='utf-8') as f:
                        scraped_content = json.load(f)
                    if self.verbose:
                        print(f"Loaded {len(scraped_content)} URLs from existing file")
            except Exception as e:
                if self.verbose:
                    print(f"Error loading existing content file: {e}")

        # Then add any content from state
        if state.get("scraped_content") and isinstance(state.get("scraped_content"), dict):
            for url, content in state["scraped_content"].items():
                if url not in scraped_content:
                    scraped_content[url] = content

        if self.verbose and scraped_content:
            print(f"Total existing content for {len(scraped_content)} URLs")

        # Filter out URLs that are already in scraped_content
        new_urls = [url for url in search_results if url not in scraped_content]

        if self.verbose:
            print(f"Found {len(new_urls)} new URLs to scrape")
            if len(new_urls) < len(search_results):
                print(f"Skipping {len(search_results) - len(new_urls)} URLs that are already scraped")

        # If there are no new URLs, return the current state with existing scraped content
        if not new_urls and search_results:
            if self.verbose:
                print("No new URLs to scrape, using existing content")
            return {
                **state,
                "scraped_content": scraped_content
            }

        # Separate YouTube, PDF and non-PDF URLs from the new URLs
        youtube_urls = [url for url in new_urls if self._is_youtube_url(url)]
        pdf_urls = [url for url in new_urls if self._is_pdf_url(url) and not self._is_youtube_url(url)]
        non_pdf_urls = [url for url in new_urls if not self._is_pdf_url(url) and not self._is_youtube_url(url)]

        # Process YouTube URLs with timeout
        if youtube_urls:
            if self.verbose:
                print(f"Processing {len(youtube_urls)} YouTube videos")

            for url in youtube_urls:
                # Check global timeout
                if time.time() - scraping_start_time > MAX_SCRAPING_TIME:
                    if self.verbose:
                        print(f"⏰ Maximum scraping time exceeded, skipping remaining URLs")
                    break
                    
                try:
                    if self.verbose:
                        print(f"Transcribing YouTube video: {url}")

                    # Use signal for timeout on YouTube transcription (Unix only)
                    def timeout_handler(signum, frame):
                        raise TimeoutError("YouTube transcription timed out")
                    
                    # Only use signal on Unix systems
                    timeout_used = False
                    if platform.system() != 'Windows':
                        try:
                            signal.signal(signal.SIGALRM, timeout_handler)
                            signal.alarm(SCRAPING_TIMEOUT_PER_URL)
                            timeout_used = True
                        except (AttributeError, OSError):
                            # signal.SIGALRM not available
                            pass
                    
                    try:
                        transcript = self._transcribe_youtube_video(url)
                        scraped_content[url] = transcript
                        if self.verbose:
                            print(f"Successfully transcribed YouTube video: {url}")
                    finally:
                        if timeout_used:
                            signal.alarm(0)  # Cancel the alarm
                        
                except (TimeoutError, Exception) as e:
                    if self.verbose:
                        print(f"Error transcribing YouTube video {url}: {e}")
                    scraped_content[url] = f"Could not transcribe YouTube video from {url}. The video may not have captions available."

        # Process PDF URLs with timeout
        if pdf_urls:
            if self.verbose:
                print(f"Processing {len(pdf_urls)} PDF files")

            for url in pdf_urls:
                # Check global timeout
                if time.time() - scraping_start_time > MAX_SCRAPING_TIME:
                    if self.verbose:
                        print(f"⏰ Maximum scraping time exceeded, skipping remaining URLs")
                    break
                    
                try:
                    # Clean the URL for PDF loader
                    clean_url = self._clean_url_for_pdf_loader(url)

                    if self.verbose:
                        print(f"Loading PDF: {clean_url}")

                    # Set timeout for PDF loading
                    loader = PyPDFLoader(clean_url)
                    
                    # Use threading to enforce timeout
                    pdf_docs = []
                    exception_container = []
                    
                    def load_pdf():
                        try:
                            pdf_docs.extend(loader.load())
                        except Exception as e:
                            exception_container.append(e)
                    
                    thread = threading.Thread(target=load_pdf)
                    thread.start()
                    thread.join(timeout=SCRAPING_TIMEOUT_PER_URL)
                    
                    if thread.is_alive():
                        if self.verbose:
                            print(f"PDF loading timed out for {url}")
                        scraped_content[url] = f"Could not load PDF from {url}. Loading timed out."
                        continue
                    
                    if exception_container:
                        raise exception_container[0]
                    
                    if pdf_docs:
                        # Combine all pages into one document
                        combined_content = "\n\n".join([doc.page_content for doc in pdf_docs])
                        scraped_content[url] = combined_content
                        if self.verbose:
                            print(f"Successfully loaded PDF: {url}")
                    else:
                        raise Exception("No content extracted from PDF")
                        
                except Exception as e:
                    if self.verbose:
                        print(f"Error loading PDF {url}: {e}")
                    scraped_content[url] = f"Could not load content from {url}. The file may be inaccessible or in an unsupported format."

        # Process non-PDF URLs with improved timeout and error handling
        if non_pdf_urls:
            if self.verbose:
                print(f"Processing {len(non_pdf_urls)} web pages")

            # First try batch loading with strict timeout
            batch_success = False
            try:
                if self.verbose:
                    print("Attempting batch loading with timeout...")
                
                # Use threading to enforce timeout on batch loading
                web_docs = []
                exception_container = []
                
                def load_batch():
                    try:
                        loader = WebBaseLoader(non_pdf_urls)
                        web_docs.extend(loader.load())
                    except Exception as e:
                        exception_container.append(e)
                
                thread = threading.Thread(target=load_batch)
                thread.start()
                thread.join(timeout=60)  # 60 seconds for batch loading
                
                if thread.is_alive():
                    if self.verbose:
                        print("Batch loading timed out, falling back to individual loading")
                    batch_success = False
                elif exception_container:
                    if self.verbose:
                        print(f"Batch loading failed: {exception_container[0]}")
                    batch_success = False
                elif web_docs:
                    batch_success = True
                    if self.verbose:
                        print(f"Batch loading successful, got {len(web_docs)} documents")
                    
                    # Map documents to their URLs
                    for doc in web_docs:
                        url = doc.metadata.get("source", "")
                        if url in non_pdf_urls:
                            content = doc.page_content.strip()
                            if content and len(content) > 100:
                                scraped_content[url] = content
                                if self.verbose:
                                    print(f"Successfully loaded content for: {url}")
                            else:
                                # Try enhanced scraping for empty content
                                try:
                                    enhanced_content = self._enhanced_web_scraper_with_timeout(url, SCRAPING_TIMEOUT_PER_URL)
                                    if enhanced_content and len(enhanced_content) > 100:
                                        scraped_content[url] = enhanced_content
                                    else:
                                        scraped_content[url] = f"Could not extract meaningful content from {url}."
                                except Exception as e:
                                    scraped_content[url] = f"Could not extract content from {url}."
                        
            except Exception as e:
                if self.verbose:
                    print(f"Batch loading failed: {e}")
                batch_success = False

            # If batch loading failed, try individual URLs with strict timeout
            if not batch_success:
                if self.verbose:
                    print("Falling back to individual URL loading with timeouts...")
                
                for url in non_pdf_urls:
                    # Check global timeout
                    if time.time() - scraping_start_time > MAX_SCRAPING_TIME:
                        if self.verbose:
                            print(f"⏰ Maximum scraping time exceeded, skipping remaining URLs")
                        break
                    
                    # Skip if already processed
                    if url in scraped_content:
                        continue
                        
                    try:
                        if self.verbose:
                            print(f"⏱️ Loading individual URL (timeout: {SCRAPING_TIMEOUT_PER_URL}s): {url}")
                        
                        # Use threading to enforce strict timeout
                        docs = []
                        exception_container = []
                        
                        def load_individual():
                            try:
                                loader = WebBaseLoader([url])
                                docs.extend(loader.load())
                            except Exception as e:
                                exception_container.append(e)
                        
                        thread = threading.Thread(target=load_individual)
                        thread.start()
                        thread.join(timeout=SCRAPING_TIMEOUT_PER_URL)
                        
                        if thread.is_alive():
                            if self.verbose:
                                print(f"⏰ Individual loading timed out for: {url}")
                            scraped_content[url] = f"Could not load content from {url} - request timed out."
                            continue
                        
                        if exception_container:
                            raise exception_container[0]
                        
                        if docs and docs[0].page_content.strip():
                            scraped_content[url] = docs[0].page_content
                            if self.verbose:
                                print(f"✅ Successfully loaded: {url}")
                        else:
                            # Try enhanced scraping as final fallback
                            if self.verbose:
                                print(f"🔄 Trying enhanced scraping for: {url}")
                            
                            try:
                                enhanced_content = self._enhanced_web_scraper_with_timeout(url, SCRAPING_TIMEOUT_PER_URL)
                                if enhanced_content and len(enhanced_content) > 100:
                                    scraped_content[url] = enhanced_content
                                    if self.verbose:
                                        print(f"✅ Enhanced scraping successful for: {url}")
                                else:
                                    scraped_content[url] = f"Could not extract meaningful content from {url}."
                                    if self.verbose:
                                        print(f"❌ Enhanced scraping also failed for: {url}")
                            except Exception as e:
                                if self.verbose:
                                    print(f"❌ Enhanced scraping failed for {url}: {e}")
                                scraped_content[url] = f"Could not extract content from {url}."
                                
                    except Exception as url_e:
                        if self.verbose:
                            print(f"❌ Failed to load {url}: {url_e}")
                        scraped_content[url] = f"Could not load content from {url}."

        # Final timeout check
        total_time = time.time() - scraping_start_time
        if self.verbose:
            print(f"⏱️ Total scraping time: {total_time:.2f} seconds")
            print(f"📊 Successfully scraped {len([url for url, content in scraped_content.items() if 'Could not' not in content])} URLs")
            print(f"❌ Failed to scrape {len([url for url, content in scraped_content.items() if 'Could not' in content])} URLs")

        # Save scraped content to local storage as JSON
        if scraped_content:
            try:
                # Create scraped_content directory if it doesn't exist
                scraped_dir = Path("scraped_content")
                scraped_dir.mkdir(exist_ok=True)

                # Create a filename based on the conversation ID if available, otherwise use query
                if state.get('conversation_id'):
                    filename = f"scraped_content{state['conversation_id']}.json"
                else:
                    # Fallback to query-based filename if no conversation ID
                    query_part = state['query'].lower().replace(' ', '_')[:50]  # Limit length
                    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                    filename = f"{query_part}_{timestamp}.json"

                file_path = scraped_dir / filename
                existing_content = {}

                # Load existing content if file exists
                if file_path.exists():
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            existing_content = json.load(f)
                        if self.verbose:
                            print(f"Loaded existing content with {len(existing_content)} URLs")
                    except json.JSONDecodeError:
                        if self.verbose:
                            print(f"Error reading existing JSON, starting fresh")

                # Merge new content with existing content
                # Only add new URLs or update if content is different
                for url, content in scraped_content.items():
                    if url not in existing_content or existing_content[url] != content:
                        existing_content[url] = content
                        if self.verbose:
                            print(f"Added/Updated content for URL: {url}")

                # Save merged content back to file
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(existing_content, f, ensure_ascii=False, indent=2)

                if self.verbose:
                    print(f"Saved merged content to {filename} ({len(existing_content)} total URLs)")

            except Exception as e:
                if self.verbose:
                    print(f"Error saving scraped content: {e}")

            # Update state with merged content
            return {
                **state,
                "scraped_content": existing_content if 'existing_content' in locals() else scraped_content
            }
        
        # If no content was scraped, return current state
        return {
            **state,
            "scraped_content": scraped_content
        }

    def generate_response(self, state: WebSearchState) -> WebSearchState:
        """
        Generate a response based on the scraped content.

        Args:
            state: The current state

        Returns:
            Updated state with response
        """
        if self.verbose:
            print("\n=== Starting generate_response node ===")
            print(f"Total scraped URLs in database: {len(state['scraped_content'])}")
            print(f"Latest search results URLs: {len(state.get('search_results', []))}")

        query = state["query"]
        scraped_content = state["scraped_content"]
        search_results = state.get("search_results", [])

        # Filter scraped_content to only include URLs from latest search
        relevant_content = {
            url: content for url, content in scraped_content.items() 
            if url in search_results
        }

        if self.verbose:
            print("\nURLs being used for response generation:")
            print("----------------------------------------")
            for i, url in enumerate(relevant_content.keys(), 1):
                print(f"{i}. {url}")
            print("----------------------------------------\n")

        # Create a mapping of reference numbers to URLs
        ref_urls = {}
        context_parts = []

        for i, (url, content) in enumerate(relevant_content.items(), 1):
            ref_num = str(i)
            ref_urls[ref_num] = url

            # Truncate content if it's too long
            max_content_length = 8000  # Adjust based on model context window
            truncated_content = content[:max_content_length] + ("..." if len(content) > max_content_length else "")

            context_parts.append(f"[{ref_num}] {url}\n{truncated_content}\n")

        # Combine context parts
        context = "\n---\n".join(context_parts)

        # Check token length and trim if necessary
        encoding = tiktoken.get_encoding("cl100k_base")
        tokens = encoding.encode(context)
        if len(tokens) > 100000:
            context = encoding.decode(tokens[:100000])
            print(f"Context trimmed from {len(tokens)} to 100000 tokens")
        else:
            print(f"Context token length: {len(tokens)}")

        # Create system prompt
        system_prompt = """You are a web research assistant that provides comprehensive, accurate answers based on the search results provided.

            Your task is to:
            1. Analyze the search results and extract relevant information
            2. Synthesize a comprehensive answer to the user's query
            3. Include citations in the format [1], [2], etc. that reference the source URLs
            4. Structure your response in a clear, readable format with proper markdown

            
         

            IMPORTANT GUIDELINES:
            - ONLY use information from the provided search results - DO NOT use your pre-trained knowledge
            - For your information, today is {self.today_date} and can be ahead of your pre-trained knowledge (which is in past).
            - For questions about current events, positions, or time-sensitive information, ONLY use what's in the search results
            - If the search results don't provide information about a specific part of the query, clearly state that the information is not available in the search results
            - For questions about specific dates (especially future dates), acknowledge if the search results don't contain information for that time period
            - Be thorough and accurate
            - Cite your sources using the reference numbers
            - If there are contradictions in the sources, acknowledge them
            - Focus on factual information rather than opinions
            - Use markdown formatting in your answer for better readability
            - For comparative data, use markdown tables within the answer text
            - DO NOT include chart_data, table_data, or any other fields beyond "answer" and "references"
            """

        # Create user prompt with intent focus
        user_prompt = f"""USER'S ORIGINAL INTENT: {query}
        
        INTENT ANALYSIS:
        - Focus on directly answering what the user is asking for
        - Provide information that serves their specific goal
        - Format the response appropriately for their intent
        - Ensure completeness based on their information need

        Query: {query}

            Search Results:
            {context}

            Based on these search results, provide a comprehensive answer that directly addresses the user's intent.

            FORMATTING GUIDELINES:
            - If your response includes comparative data, format it as a proper markdown table within your answer text
            - Use markdown table syntax: | Column 1 | Column 2 | Column 3 |
            - DO NOT include separate chart_data or table_data fields in your JSON response
            - Focus on clear, well-structured text with proper markdown formatting
            """

        # Generate response
        messages = [
            SystemMessage(content=system_prompt),
            HumanMessage(content=user_prompt)
        ]

        if self.verbose:
            print("Generating response with LLM")
            llm_time1 = datetime.datetime.now()

        response = self.model.invoke(messages)

        if self.verbose:
            llm_time2 = datetime.datetime.now()
            llm_time = llm_time2 - llm_time1
            print(f"LLM time: {llm_time.total_seconds()} seconds")

        # Process the response
        try:
            # Clean the content to handle potential formatting issues
            content = re.sub(r'```json|```', '', response.content).strip()
            result = json.loads(content)

            # Validate the structure and ensure only answer and references are included
            if "answer" not in result or "references" not in result:
                # Fix missing fields
                result = {
                    "answer": result.get("answer", response.content),
                    "references": result.get("references", {})
                }
            else:
                # Keep only answer and references, remove any chart_data or table_data
                result = {
                    "answer": result["answer"],
                    "references": result["references"]
                }

            # Ensure references are a dictionary
            if not isinstance(result["references"], dict):
                result["references"] = {}

            # Extract citation numbers from the answer
            citation_pattern = r'\[(\d+)\]'
            cited_refs = set(re.findall(citation_pattern, result["answer"]))

            # Filter references to only include cited ones
            if cited_refs:
                # If references are empty or don't match citations, regenerate them
                if not result["references"] or not any(ref in result["references"] for ref in cited_refs):
                    result["references"] = {ref: ref_urls.get(ref, "") for ref in cited_refs}
        except json.JSONDecodeError:
            # Fallback: If the model didn't return valid JSON, extract answer and references manually
            if self.verbose:
                print("Failed to parse JSON, extracting manually")

            # Extract citations from the text
            citation_pattern = r'\[(\d+)\]'
            cited_refs = set(re.findall(citation_pattern, response.content))

            # Create references dictionary from cited references
            references = {ref: ref_urls.get(ref, "") for ref in cited_refs}

            # Check if there's a References section to separate
            parts = response.content.split("References:", 1)

            if len(parts) > 1:
                answer = parts[0].strip()
            else:
                answer = response.content

            result = {
                "answer": answer,
                "references": references
            }

        # Update state
        return {
            **state,
            "response": result
        }

    def _extract_urls(self, query: str) -> List[str]:
        """
        Extract URLs from a query.

        Args:
            query: The query to extract URLs from

        Returns:
            List of extracted URLs
        """
        url_pattern = r'(https?://(?:[-\w.]|(?:%[\da-fA-F]{2}))+[^\s,]*)'
        matches = re.findall(url_pattern, query)

        # Clean URLs by removing trailing punctuation
        cleaned_urls = [url.rstrip(',.;:') for url in matches]
        
        # Validate URLs
        valid_urls = []
        for url in cleaned_urls:
            try:
                # Attempt to parse the URL to validate it
                parsed = urllib.parse.urlparse(url)
                if parsed.scheme and parsed.netloc:
                    valid_urls.append(url)
                else:
                    if self.verbose:
                        print(f"Skipping invalid URL (missing scheme or netloc): {url}")
            except Exception as e:
                if self.verbose:
                    print(f"Skipping invalid URL: {url}, Error: {e}")

        if self.verbose and valid_urls:
            print(f"Extracted valid URLs from query: {valid_urls}")

        return valid_urls

    def _is_pdf_url(self, url: str) -> bool:
        """
        Check if a URL points to a PDF file.

        Args:
            url: The URL to check

        Returns:
            True if the URL points to a PDF file, False otherwise
        """
        try:
            # Parse the URL to separate components
            parsed_url = urllib.parse.urlparse(url)

            # Get the path component
            path = parsed_url.path.lower()

            # Check if the path ends with .pdf
            return path.endswith('.pdf')
        except Exception:
            return False

    def _clean_url_for_pdf_loader(self, url: str) -> str:
        """
        Clean a URL for the PDF loader.

        Args:
            url: The URL to clean

        Returns:
            Cleaned URL
        """
        try:
            # Parse the URL to separate components
            parsed_url = urllib.parse.urlparse(url)
            
            # Validate URL has necessary components
            if not parsed_url.scheme or not parsed_url.netloc:
                if self.verbose:
                    print(f"Invalid URL for PDF loader (missing scheme or netloc): {url}")
                return url  # Return original if invalid to let the loader handle the error
            
            # Remove fragments and query parameters
            clean_url = urllib.parse.urlunparse((
                parsed_url.scheme,
                parsed_url.netloc,
                parsed_url.path,
                '',  # params
                '',  # query
                ''   # fragment
            ))
            
            return clean_url
        except Exception as e:
            if self.verbose:
                print(f"Error cleaning URL for PDF loader: {url}, Error: {e}")
            return url  # Return original URL if parsing fails

    def _enhanced_web_scraper_with_timeout(self, url: str, timeout_seconds: int = 30) -> str:
        """
        Enhanced web scraper with strict timeout control.

        Args:
            url: The URL to scrape
            timeout_seconds: Maximum time to spend scraping this URL

        Returns:
            Scraped content
        """
        import threading
        import requests
        from bs4 import BeautifulSoup
        
        # Validate URL before proceeding
        try:
            parsed_url = urllib.parse.urlparse(url)
            if not parsed_url.scheme or not parsed_url.netloc:
                if self.verbose:
                    print(f"Invalid URL for enhanced scraping (missing scheme or netloc): {url}")
                return ""
        except Exception as e:
            if self.verbose:
                print(f"Invalid URL for enhanced scraping: {url}, Error: {e}")
            return ""
        
        result_container = []
        exception_container = []
        
        def scrape_with_timeout():
            try:
                if self.verbose:
                    print(f"Using timeout-enabled enhanced scraping for: {url}")

                # Clean up URL - some URLs work better without parameters
                try:
                    base_url = url.split('?')[0]
                    domain = urllib.parse.urlparse(url).netloc
                except Exception as e:
                    if self.verbose:
                        print(f"Error parsing URL parts: {e}")
                    base_url = url
                    domain = ""

                # Simplified user agent (just one for speed)
                headers = {
                    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
                    "Accept-Language": "en-US,en;q=0.5",
                    "Connection": "keep-alive",
                    "Timeout": str(timeout_seconds)
                }

                session = requests.Session()
                session.headers.update(headers)

                # Try the original URL first, then base URL if needed
                for target_url in [url, base_url]:
                    try:
                        # Skip if target_url is empty or invalid
                        if not target_url:
                            continue
                            
                        response = session.get(target_url, timeout=min(timeout_seconds-5, 15))
                        
                        if response.status_code == 200:
                            soup = BeautifulSoup(response.text, 'html.parser')

                            # Remove script, style, and other non-content elements
                            for element in soup(['script', 'style', 'nav', 'footer', 'header', 'aside', 'menu']):
                                element.decompose()

                            # Quick content extraction strategies (in order of preference)
                            content = ""
                            
                            # Strategy 1: Look for main content containers
                            for selector in ['article', 'main', '[role="main"]', '.main-content', '#main-content', '.post-content', '.article-content']:
                                if content:
                                    break
                                elements = soup.select(selector)
                                if elements:
                                    content = elements[0].get_text(separator='\n', strip=True)
                                    if len(content) > 200:
                                        break

                            # Strategy 2: Get all paragraphs if no main content found
                            if not content or len(content) < 200:
                                paragraphs = []
                                for tag in ['p', 'li', 'h1', 'h2', 'h3']:
                                    for element in soup.find_all(tag):
                                        text = element.get_text(strip=True)
                                        if text and len(text) > 20:
                                            paragraphs.append(text)
                                
                                if paragraphs:
                                    content = "\n\n".join(paragraphs[:20])  # Limit to first 20 paragraphs

                            # Strategy 3: Fallback to body text
                            if not content or len(content) < 100:
                                body = soup.find('body')
                                if body:
                                    content = body.get_text(separator='\n', strip=True)

                            if content and len(content) > 100:
                                result_container.append(content)
                                return

                    except Exception as e:
                        if self.verbose:
                            print(f"Failed to scrape {target_url}: {e}")
                        continue

                # If we get here, no content was found
                result_container.append("")
                
            except Exception as e:
                exception_container.append(e)

        # Run scraping in a separate thread with timeout
        thread = threading.Thread(target=scrape_with_timeout)
        thread.start()
        thread.join(timeout=timeout_seconds)

        if thread.is_alive():
            if self.verbose:
                print(f"Enhanced scraping timed out for {url}")
            return ""

        if exception_container:
            if self.verbose:
                print(f"Enhanced scraping failed for {url}: {exception_container[0]}")
            return ""

        if result_container:
            return result_container[0]
        
        return ""

    def _enhanced_web_scraper(self, url: str) -> str:
        """
        Enhanced web scraper that works for sites with complex protection or structure.
        This is the original method without timeout - kept for compatibility.

        Args:
            url: The URL to scrape

        Returns:
            Scraped content
        """
        # Use the timeout-enabled version with a longer timeout for compatibility
        return self._enhanced_web_scraper_with_timeout(url, timeout_seconds=60)

    def _is_youtube_url(self, url: str) -> bool:
        """
        Check if a URL is a YouTube URL.
        
        Args:
            url: The URL to check
            
        Returns:
            True if the URL is a YouTube URL, False otherwise
        """
        youtube_patterns = [
            r'(?:https?:\/\/)?(?:www\.)?youtube\.com\/watch\?v=[\w-]+',
            r'(?:https?:\/\/)?(?:www\.)?youtu\.be\/[\w-]+',
            r'(?:https?:\/\/)?(?:www\.)?youtube\.com\/embed\/[\w-]+',
            r'(?:https?:\/\/)?(?:www\.)?youtube\.com\/v\/[\w-]+'
        ]
        
        for pattern in youtube_patterns:
            if re.match(pattern, url):
                return True
        
        return False
    
    def _extract_youtube_id(self, url: str) -> str:
        """
        Extract the YouTube video ID from a URL.
        
        Args:
            url: The YouTube URL
            
        Returns:
            The YouTube video ID
        """
        # Extract YouTube video ID using regex patterns
        youtube_id_patterns = [
            r'(?:https?:\/\/)?(?:www\.)?youtube\.com\/watch\?v=([\w-]+)',
            r'(?:https?:\/\/)?(?:www\.)?youtu\.be\/([\w-]+)',
            r'(?:https?:\/\/)?(?:www\.)?youtube\.com\/embed\/([\w-]+)',
            r'(?:https?:\/\/)?(?:www\.)?youtube\.com\/v\/([\w-]+)'
        ]
        
        for pattern in youtube_id_patterns:
            match = re.search(pattern, url)
            if match:
                return match.group(1)
        
        return ""
    
    def _get_cache_file_path(self, video_id: str) -> str:
        """
        Get the cache file path for a YouTube video transcript.
        
        Args:
            video_id: The YouTube video ID
            
        Returns:
            The cache file path
        """
        return os.path.join(self.transcript_dir, f"{video_id}.txt")
    
    def _extract_youtube_id(self, url: str) -> str:
        """Extract YouTube video ID from various URL formats"""
        import re
        
        # Pattern for youtu.be URLs
        youtu_be_pattern = r'youtu\.be/([a-zA-Z0-9_-]+)'
        # Pattern for youtube.com URLs
        youtube_pattern = r'youtube\.com/watch\?v=([a-zA-Z0-9_-]+)'
        
        match = re.search(youtu_be_pattern, url) or re.search(youtube_pattern, url)
        if match:
            return match.group(1)
        
        # If no pattern matches, try to extract from the end of youtu.be URLs
        if 'youtu.be/' in url:
            return url.split('youtu.be/')[-1].split('?')[0]
        
        return ""

    def _transcribe_youtube_video(self, url: str) -> str:
        """
        Transcribe a YouTube video and cache the transcript.
        
        Args:
            url: The YouTube video URL
            
        Returns:
            The video transcript
        """
        # Extract the YouTube video ID for caching
        video_id = self._extract_youtube_id(url)
        
        if not video_id:
            if self.verbose:
                print(f"Could not extract video ID from {url}")
            return f"Could not extract video ID from {url}"
        
        # Check if the transcript is already cached
        cache_file_path = self._get_cache_file_path(video_id)
        
        if os.path.exists(cache_file_path):
            if self.verbose:
                print(f"Using cached transcript for video {video_id}")
            
            with open(cache_file_path, 'r', encoding='utf-8') as f:
                return f.read()
        
        # Transcribe the video - try multiple approaches
        if self.verbose:
            print(f"Transcribing YouTube video: {url}")
        
        # Method 1: Direct youtube-transcript-api (most reliable for transcripts)
        try:
            if self.verbose:
                print("Method 1: Trying direct youtube-transcript-api")
            
            from youtube_transcript_api import YouTubeTranscriptApi
            
            # First, try to get available transcript languages and use transcript objects directly
            try:
                if self.verbose:
                    print("Getting available transcript languages...")
                
                transcript_list = YouTubeTranscriptApi.list_transcripts(video_id)
                
                # Try to get transcript using transcript objects directly
                for transcript in transcript_list:
                    try:
                        if self.verbose:
                            print(f"Trying transcript: {transcript.language_code} - {transcript.language}")
                        
                        # Try to fetch the transcript directly using the transcript object
                        transcript_data = transcript.fetch()
                        
                        # Combine all transcript segments
                        transcript_text = ' '.join([item['text'] for item in transcript_data])
                        
                        if transcript_text and len(transcript_text.strip()) > 50:
                            if self.verbose:
                                print(f"Successfully got transcript using transcript object: {transcript.language_code}")
                            break
                            
                    except Exception as transcript_e:
                        if self.verbose:
                            print(f"Transcript object {transcript.language_code} failed: {transcript_e}")
                        continue
                
                # If transcript object approach didn't work, try traditional API calls
                if not transcript_text or len(transcript_text.strip()) <= 50:
                    if self.verbose:
                        print("Transcript object approach failed, trying traditional API calls...")
                    
                    # Try multiple language combinations
                    language_combinations = [
                        ['en'],
                        ['en-US'], 
                        ['en-GB'],
                        ['auto'],
                        ['hi'],
                        ['hi', 'en'],
                        ['en', 'hi'],
                        ['en-US', 'en', 'auto']
                    ]
                    
                    for languages in language_combinations:
                        try:
                            if self.verbose:
                                print(f"Trying languages: {languages}")
                            
                            transcript_data = YouTubeTranscriptApi.get_transcript(video_id, languages=languages)
                            
                            # Combine all transcript segments
                            transcript_text = ' '.join([item['text'] for item in transcript_data])
                            
                            if transcript_text and len(transcript_text.strip()) > 50:
                                if self.verbose:
                                    print(f"Successfully got transcript with languages: {languages}")
                                break
                                
                        except Exception as lang_e:
                            if self.verbose:
                                print(f"Language combination {languages} failed: {lang_e}")
                            continue
                
            except Exception as list_e:
                if self.verbose:
                    print(f"Could not list available transcripts: {list_e}")
                
                # Fallback to default combinations
                language_combinations = [
                    ['en'],
                    ['en-US'], 
                    ['en-GB'],
                    ['auto'],
                    ['hi'],
                    ['hi', 'en'],
                    ['en', 'hi'],
                    ['en-US', 'en', 'auto']
                ]
                
                transcript_text = None
                for languages in language_combinations:
                    try:
                        if self.verbose:
                            print(f"Trying languages: {languages}")
                        
                        transcript_data = YouTubeTranscriptApi.get_transcript(video_id, languages=languages)
                        
                        # Combine all transcript segments
                        transcript_text = ' '.join([item['text'] for item in transcript_data])
                        
                        if transcript_text and len(transcript_text.strip()) > 50:
                            if self.verbose:
                                print(f"Successfully got transcript with languages: {languages}")
                            break
                            
                    except Exception as lang_e:
                        if self.verbose:
                            print(f"Language combination {languages} failed: {lang_e}")
                        continue
            
            if transcript_text and len(transcript_text.strip()) > 50:
                # Try to get video metadata for title
                try:
                    from pytube import YouTube
                    yt = YouTube(url)
                    title = yt.title
                    author = yt.author
                except:
                    title = "YouTube Video"
                    author = "Unknown"
                
                formatted_output = f"YOUTUBE VIDEO: {title}\n"
                formatted_output += f"Author: {author}\n"
                formatted_output += f"URL: {url}\n\n"
                formatted_output += "TRANSCRIPT:\n\n"
                formatted_output += transcript_text
                
                # Cache the transcript
                with open(cache_file_path, 'w', encoding='utf-8') as f:
                    f.write(formatted_output)
                
                if self.verbose:
                    print(f"Successfully extracted transcript using direct API")
                
                return formatted_output
                
        except Exception as e:
            if self.verbose:
                print(f"Method 1 (direct API) failed: {e}")
        
        # Method 2: LangChain YoutubeLoader with multiple configurations
        try:
            if self.verbose:
                print("Method 2: Trying LangChain YoutubeLoader")
            
            # Configuration 1: English-first approach
            try:
                if self.verbose:
                    print("Config 1: English-first approach")
                
                loader = YoutubeLoader.from_youtube_url(
                    url,
                    add_video_info=True,
                    language=["en", "en-US", "en-GB", "auto"],
                    translation=None
                )
                
                documents = loader.load()
                
                if documents and documents[0].page_content and len(documents[0].page_content.strip()) > 50:
                    if self.verbose:
                        print(f"Successfully loaded transcript with English-first config")
                    
                    # Extract metadata
                    try:
                        metadata = documents[0].metadata
                        video_title = metadata.get('title', 'YouTube Video')
                        video_author = metadata.get('author', 'Unknown')
                    except:
                        video_title = "YouTube Video"
                        video_author = "Unknown"
                    
                    formatted_output = f"YOUTUBE VIDEO: {video_title}\n"
                    formatted_output += f"Author: {video_author}\n"
                    formatted_output += f"URL: {url}\n\n"
                    formatted_output += "TRANSCRIPT:\n\n"
                    formatted_output += documents[0].page_content
                    
                    # Cache the transcript
                    with open(cache_file_path, 'w', encoding='utf-8') as f:
                        f.write(formatted_output)
                    
                    return formatted_output
                    
            except Exception as config1_e:
                if self.verbose:
                    print(f"Config 1 failed: {config1_e}")
            
            # Configuration 2: Auto-detect with translation
            try:
                if self.verbose:
                    print("Config 2: Auto-detect with translation")
                
                loader = YoutubeLoader.from_youtube_url(
                    url,
                    add_video_info=True,
                    language=["auto"],
                    translation="en"
                )
                
                documents = loader.load()
                
                if documents and documents[0].page_content and len(documents[0].page_content.strip()) > 50:
                    if self.verbose:
                        print(f"Successfully loaded transcript with auto-detect config")
                    
                    # Extract metadata
                    try:
                        metadata = documents[0].metadata
                        video_title = metadata.get('title', 'YouTube Video')
                        video_author = metadata.get('author', 'Unknown')
                    except:
                        video_title = "YouTube Video"
                        video_author = "Unknown"
                    
                    formatted_output = f"YOUTUBE VIDEO: {video_title}\n"
                    formatted_output += f"Author: {video_author}\n"
                    formatted_output += f"URL: {url}\n\n"
                    formatted_output += "TRANSCRIPT:\n\n"
                    formatted_output += documents[0].page_content
                    
                    # Cache the transcript
                    with open(cache_file_path, 'w', encoding='utf-8') as f:
                        f.write(formatted_output)
                    
                    return formatted_output
                    
            except Exception as config2_e:
                if self.verbose:
                    print(f"Config 2 failed: {config2_e}")
            
            # Configuration 3: Hindi-first with translation (original working config)
            try:
                if self.verbose:
                    print("Config 3: Hindi-first with translation")
                
                loader = YoutubeLoader.from_youtube_url(
                    url,
                    add_video_info=False,
                    language=["hi", "en-US", "en", "auto"],
                    translation="en"
                )
                
                documents = loader.load()
                
                if documents and documents[0].page_content and len(documents[0].page_content.strip()) > 50:
                    if self.verbose:
                        print(f"Successfully loaded transcript with Hindi-first config")
                    
                    formatted_output = f"YOUTUBE VIDEO: (Transcript)\n"
                    formatted_output += f"URL: {url}\n\n"
                    formatted_output += "TRANSCRIPT:\n\n"
                    formatted_output += documents[0].page_content
                    
                    # Cache the transcript
                    with open(cache_file_path, 'w', encoding='utf-8') as f:
                        f.write(formatted_output)
                    
                    return formatted_output
                    
            except Exception as config3_e:
                if self.verbose:
                    print(f"Config 3 failed: {config3_e}")
                    
        except Exception as e:
            if self.verbose:
                print(f"Method 2 (LangChain) failed: {e}")
        
        # If transcript methods fail, try to get basic video information using pytube
        try:
            if self.verbose:
                print("Trying to get video metadata using pytube")
            
            from pytube import YouTube
            
            # Create YouTube object with error handling
            yt = YouTube(url)
            
            # Get basic video information
            video_info = f"YOUTUBE VIDEO: {yt.title}\n"
            video_info += f"Author: {yt.author}\n"
            video_info += f"Length: {yt.length} seconds\n"
            video_info += f"Views: {yt.views}\n"
            video_info += f"URL: {url}\n\n"
            
            # Add description if available
            if yt.description:
                description = yt.description[:1000] + "..." if len(yt.description) > 1000 else yt.description
                video_info += f"DESCRIPTION:\n{description}"
            else:
                video_info += "DESCRIPTION: No description available"
            
            if self.verbose:
                print("Successfully retrieved video metadata using pytube")
            
            # Cache the video info
            with open(cache_file_path, 'w', encoding='utf-8') as f:
                f.write(video_info)
            
            return video_info
            
        except Exception as e:
            if self.verbose:
                print(f"Pytube approach also failed: {e}")
        
        # Method 3: Enhanced web scraping with multiple approaches
        try:
            if self.verbose:
                print("Method 3: Trying enhanced web scraping")
            
            import requests
            from bs4 import BeautifulSoup
            import time
            
            # Normalize URL to youtube.com format
            if "youtu.be" in url:
                video_id = self._extract_youtube_id(url)
                youtube_url = f"https://www.youtube.com/watch?v={video_id}"
            else:
                youtube_url = url
            
            # Try multiple user agents and approaches
            user_agents = [
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0',
                'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
            ]
            
            for i, user_agent in enumerate(user_agents):
                try:
                    if self.verbose:
                        print(f"Trying user agent {i+1}/{len(user_agents)}")
                    
                    headers = {
                        'User-Agent': user_agent,
                        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                        'Accept-Language': 'en-US,en;q=0.5',
                        'Accept-Encoding': 'gzip, deflate',
                        'Connection': 'keep-alive',
                        'Upgrade-Insecure-Requests': '1',
                    }
                    
                    # Add small delay between requests
                    if i > 0:
                        time.sleep(1)
                    
                    response = requests.get(youtube_url, headers=headers, timeout=15, verify=False)
                    
                    if response.status_code == 200:
                        soup = BeautifulSoup(response.content, 'html.parser')
                        
                        # Try to extract title from multiple sources
                        title = "Unknown Title"
                        title_sources = [
                            soup.find('meta', property='og:title'),
                            soup.find('meta', {'name': 'title'}),
                            soup.find('title'),
                            soup.find('h1', {'class': 'title'})
                        ]
                        
                        for title_source in title_sources:
                            if title_source:
                                if hasattr(title_source, 'get') and title_source.get('content'):
                                    title = title_source['content']
                                    break
                                elif hasattr(title_source, 'text') and title_source.text:
                                    title = title_source.text.strip()
                                    break
                        
                        # Try to extract description
                        description = "No description available"
                        desc_sources = [
                            soup.find('meta', property='og:description'),
                            soup.find('meta', {'name': 'description'})
                        ]
                        
                        for desc_source in desc_sources:
                            if desc_source and desc_source.get('content'):
                                description = desc_source['content']
                                break
                        
                        # Try to extract channel name
                        channel = "Unknown Channel"
                        channel_sources = [
                            soup.find('link', {'itemprop': 'name'}),
                            soup.find('meta', {'itemprop': 'name'}),
                            soup.find('span', {'itemprop': 'author'})
                        ]
                        
                        for channel_source in channel_sources:
                            if channel_source:
                                if hasattr(channel_source, 'get') and channel_source.get('content'):
                                    channel = channel_source['content']
                                    break
                                elif hasattr(channel_source, 'text') and channel_source.text:
                                    channel = channel_source.text.strip()
                                    break
                        
                        # Clean up title (remove " - YouTube" suffix if present)
                        if title.endswith(" - YouTube"):
                            title = title[:-10]
                        
                        video_info = f"YOUTUBE VIDEO: {title}\n"
                        video_info += f"Channel: {channel}\n"
                        video_info += f"URL: {url}\n\n"
                        video_info += f"DESCRIPTION:\n{description}"
                        
                        if self.verbose:
                            print(f"Successfully scraped video metadata from YouTube page")
                        
                        # Cache the video info
                        with open(cache_file_path, 'w', encoding='utf-8') as f:
                            f.write(video_info)
                        
                        return video_info
                        
                except Exception as ua_e:
                    if self.verbose:
                        print(f"User agent {i+1} failed: {ua_e}")
                    continue
            
        except Exception as e:
            if self.verbose:
                print(f"Method 3 (enhanced web scraping) failed: {e}")
        
        # Method 4: Try alternative transcript extraction libraries
        try:
            if self.verbose:
                print("Method 4: Trying alternative transcript libraries")
            
            # Try yt-dlp for transcript extraction
            try:
                import subprocess
                import json
                
                if self.verbose:
                    print("Trying yt-dlp for transcript extraction")
                
                # Use yt-dlp to extract transcript
                cmd = [
                    'yt-dlp',
                    '--write-auto-sub',
                    '--write-sub',
                    '--sub-lang', 'en,en-US,en-GB',
                    '--skip-download',
                    '--print', '%(title)s|||%(uploader)s|||%(description)s',
                    url
                ]
                
                result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
                
                if result.returncode == 0 and result.stdout:
                    lines = result.stdout.strip().split('\n')
                    if lines:
                        parts = lines[0].split('|||')
                        title = parts[0] if len(parts) > 0 else "YouTube Video"
                        author = parts[1] if len(parts) > 1 else "Unknown"
                        description = parts[2] if len(parts) > 2 else "No description available"
                        
                        video_info = f"YOUTUBE VIDEO: {title}\n"
                        video_info += f"Author: {author}\n"
                        video_info += f"URL: {url}\n\n"
                        video_info += f"DESCRIPTION:\n{description}"
                        
                        if self.verbose:
                            print("Successfully extracted metadata using yt-dlp")
                        
                        # Cache the video info
                        with open(cache_file_path, 'w', encoding='utf-8') as f:
                            f.write(video_info)
                        
                        return video_info
                        
            except Exception as ytdlp_e:
                if self.verbose:
                    print(f"yt-dlp approach failed: {ytdlp_e}")
                    
        except Exception as e:
            if self.verbose:
                print(f"Method 4 (alternative libraries) failed: {e}")
        
        # If all methods fail, create a helpful error message
        error_msg = f"Unable to retrieve transcript or details for this YouTube video: {url}"
        error_msg += "\n\nThe video information could not be accessed due to:"
        error_msg += "\n• Captions/transcripts may not be available or are disabled"
        error_msg += "\n• YouTube may be blocking automated access"
        error_msg += "\n• Network or regional restrictions may apply"
        error_msg += "\n• The video may be private or restricted"
        error_msg += "\n\nTo get information about this video, you could try:"
        error_msg += "\n1. Watching the video directly on YouTube"
        error_msg += "\n2. Checking the video description and comments"
        error_msg += "\n3. Searching for the video title or topic online"
        error_msg += "\n4. Looking for reviews or summaries on other platforms"
        
        return error_msg

def create_web_search_agent(model=None, verbose=True) -> WebSearchAgent:
    """
    Create a web search agent.

    Args:
        model: LLM to use for response generation
        verbose: Whether to print verbose output

    Returns:
        A web search agent
    """
    return WebSearchAgent(model=model, verbose=verbose)
