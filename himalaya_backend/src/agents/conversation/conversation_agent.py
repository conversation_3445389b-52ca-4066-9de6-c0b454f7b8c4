"""
Conversation Agent for the Enhanced Himalaya system.

This agent manages conversation context and provides general chat capabilities.
Uses existing chat_sessions and chat_messages tables for compatibility.
"""
#comeback : check if this file is needed anymore because conversation is getting handedlled by agent.py in thius folder
import os
import json
import logging
from typing import Dict, List, Any, TypedDict, Annotated, Optional
import operator
import datetime

from langchain_core.messages import AnyMessage, SystemMessage, HumanMessage, AIMessage
from langchain_openai import AzureChatOpenAI
from langgraph.graph import StateGraph, END
from sqlalchemy.orm import sessionmaker
from sqlalchemy import create_engine

# Import configuration
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
from config.settings import (
    AZURE_OPENAI_KEY, AZURE_OPENAI_API_VERSION, AZURE_OPENAI_ENDPOINT, 
    AZURE_OPENAI_DEPLOYMENT_NAME, FLASK_CONFIG
)
from models.models import Chat<PERSON>ession, ChatMessage, db

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ConversationState(TypedDict):
    """State for the Conversation agent"""
    messages: Annotated[List[AnyMessage], operator.add]
    query: str
    session_id: Optional[int]
    user_id: Optional[int]
    conversation_history: List[Dict[str, str]]
    response: Optional[str]
    context_summary: Optional[str]

class ConversationManager:
    """Manages conversation persistence using existing chat_sessions and chat_messages tables"""
    
    def __init__(self, database_url=None):
        """Initialize the conversation manager with database connection"""
        self.database_url = database_url or FLASK_CONFIG['SQLALCHEMY_DATABASE_URI']
        self.engine = create_engine(self.database_url)
        self.SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=self.engine)
    
    def get_or_create_session(self, user_id: int, session_id: Optional[int] = None, session_name: str = None) -> int:
        """Get existing session or create a new one"""
        session = self.SessionLocal()
        try:
            if session_id:
                # Try to get existing session
                chat_session = session.query(ChatSession).filter(
                    ChatSession.id == session_id,
                    ChatSession.user_id == user_id
                ).first()
                if chat_session:
                    return chat_session.id
            
            # Create new session
            if not session_name:
                session_name = f"Chat Session {datetime.datetime.now().strftime('%Y-%m-%d %H:%M')}"
            
            chat_session = ChatSession(
                user_id=user_id,
                session_name=session_name,
                is_active=True
            )
            session.add(chat_session)
            session.commit()
            session.refresh(chat_session)
            return chat_session.id
            
        except Exception as e:
            session.rollback()
            logger.error(f"Error managing session: {e}")
            raise
        finally:
            session.close()
    
    def get_conversation_history(self, session_id: int, limit: int = 5) -> List[Dict[str, str]]:
        """Get conversation history from existing chat_messages table"""
        session = self.SessionLocal()
        try:
            messages = session.query(ChatMessage)\
                .filter(ChatMessage.session_id == session_id)\
                .order_by(ChatMessage.created_at.desc())\
                .limit(limit)\
                .all()
            
            # Reverse to get chronological order and convert to conversation format
            messages = list(reversed(messages))
            
            conversation_history = []
            for msg in messages:
                # Add user question
                conversation_history.append({
                    "role": "user",
                    "content": msg.question,
                    "created_at": msg.created_at.isoformat()
                })
                # Add assistant answer
                conversation_history.append({
                    "role": "assistant", 
                    "content": msg.answer,
                    "created_at": msg.created_at.isoformat()
                })
            
            return conversation_history
            
        except Exception as e:
            logger.error(f"Error getting conversation history: {e}")
            return []
        finally:
            session.close()
    
    def save_message(self, session_id: int, question: str, answer: str, agent_type: str = "conversation", 
                    agent_metadata: Dict = None, processing_time: float = None, sources: Dict = None) -> bool:
        """Save a Q&A pair to the existing chat_messages table with agent tracking"""
        session = self.SessionLocal()
        try:
            message = ChatMessage(
                session_id=session_id,
                question=question,
                answer=answer,
                agent_type=agent_type,
                agent_metadata=agent_metadata or {},
                processing_time=processing_time,
                sources=sources or {},
                web_search=False  # Conversation agent doesn't use web search
            )
            session.add(message)
            
            # Update session timestamp
            chat_session = session.query(ChatSession).filter(ChatSession.id == session_id).first()
            if chat_session:
                chat_session.updated_at = datetime.datetime.utcnow()
            
            session.commit()
            return True
            
        except Exception as e:
            session.rollback()
            logger.error(f"Error saving message: {e}")
            return False
        finally:
            session.close()
    
    def get_session_summary(self, session_id: int) -> Optional[str]:
        """Generate a summary of the conversation session"""
        session = self.SessionLocal()
        try:
            messages = session.query(ChatMessage)\
                .filter(ChatMessage.session_id == session_id)\
                .order_by(ChatMessage.created_at)\
                .all()
            
            if not messages:
                return None
            
            # Create a simple summary from recent messages
            recent_topics = []
            for msg in messages[-5:]:  # Last 5 messages
                if len(msg.question) > 10:  # Avoid very short questions
                    recent_topics.append(msg.question[:100])
            
            if recent_topics:
                return f"Recent topics: {'; '.join(recent_topics)}"
            
            return "General conversation"
            
        except Exception as e:
            logger.error(f"Error getting session summary: {e}")
            return None
        finally:
            session.close()

class ConversationAgent:
    """
    Conversation agent that handles general chat and maintains conversation context.
    Uses existing database tables for compatibility.
    """

    def __init__(self, model=None, system_prompt=None, verbose=True, database_url=None):
        """
        Initialize the Conversation agent.

        Args:
            model: LLM to use for conversation
            system_prompt: System prompt for the agent
            verbose: Whether to print verbose output
            database_url: Database URL for conversation persistence
        """
        self.verbose = verbose
        self.conversation_manager = ConversationManager(database_url)

        # Initialize the model
        if model:
            self.model = model
        else:
            self.model = AzureChatOpenAI(
                azure_deployment=AZURE_OPENAI_DEPLOYMENT_NAME,
                api_version=AZURE_OPENAI_API_VERSION,
                azure_endpoint=AZURE_OPENAI_ENDPOINT,
                api_key=AZURE_OPENAI_KEY,
                temperature=0.7  # Higher temperature for more conversational responses
            )
            if self.verbose:
                print(f"Using Azure OpenAI with deployment {AZURE_OPENAI_DEPLOYMENT_NAME}")

        # Set the system prompt
        self.system_prompt = system_prompt or self._get_default_system_prompt()

        # Create the graph
        graph = StateGraph(ConversationState)

        # Add nodes
        graph.add_node("load_conversation_context", self.load_conversation_context)
        graph.add_node("process_conversation", self.process_conversation)
        graph.add_node("generate_response", self.generate_response)
        graph.add_node("save_conversation", self.save_conversation)

        # Add edges
        graph.add_edge("load_conversation_context", "process_conversation")
        graph.add_edge("process_conversation", "generate_response")
        graph.add_edge("generate_response", "save_conversation")
        graph.add_edge("save_conversation", END)

        # Set entry point
        graph.set_entry_point("load_conversation_context")

        # Compile the graph
        self.graph = graph.compile()

    def _get_default_system_prompt(self) -> str:
        """Get the default system prompt for conversation"""
        return """You are a helpful AI assistant for the Himalaya document intelligence system. 
        
Your role is to:
1. Engage in natural, helpful conversations with users
2. Provide general assistance and answer questions
3. Maintain context from previous messages in the conversation
4. Be friendly, professional, and informative
5. If users ask about documents or data analysis, suggest they use the document search or data analysis features

Guidelines:
- Keep responses conversational and engaging
- Use conversation history to provide contextual responses
- Be concise but thorough in your explanations
- If you don't know something, admit it honestly
- Encourage users to explore the system's document and data analysis capabilities when relevant

Remember: You are part of a larger system that includes document analysis, web search, and data processing capabilities."""

    def load_conversation_context(self, state: ConversationState) -> ConversationState:
        """Load conversation context from database"""
        if self.verbose:
            print("🔄 Loading conversation context...")

        try:
            # Get or create session
            if state.get("session_id") and state.get("user_id"):
                session_id = self.conversation_manager.get_or_create_session(
                    user_id=state["user_id"],
                    session_id=state["session_id"]
                )
            elif state.get("user_id"):
                session_id = self.conversation_manager.get_or_create_session(
                    user_id=state["user_id"]
                )
            else:
                # No user context, create temporary session
                session_id = None

            # Load conversation history
            conversation_history = []
            if session_id:
                conversation_history = self.conversation_manager.get_conversation_history(session_id)
                state["session_id"] = session_id

            state["conversation_history"] = conversation_history

            if self.verbose:
                print(f"✅ Loaded {len(conversation_history)} previous messages")

        except Exception as e:
            logger.error(f"Error loading conversation context: {e}")
            state["conversation_history"] = []

        return state

    def process_conversation(self, state: ConversationState) -> ConversationState:
        """Process the conversation and prepare context"""
        if self.verbose:
            print("🔄 Processing conversation context...")

        try:
            # Create context summary from conversation history
            history = state.get("conversation_history", [])
            
            if history:
                # Get recent context (last 5 messages optimized)
                recent_history = history[-5:] if len(history) > 5 else history
                
                context_parts = []
                for msg in recent_history:
                    role = msg["role"]
                    content = msg["content"][:200]  # Truncate long messages
                    context_parts.append(f"{role.title()}: {content}")
                
                context_summary = "\n".join(context_parts)
                state["context_summary"] = context_summary
                
                if self.verbose:
                    print(f"✅ Created context summary from {len(recent_history)} recent messages")
            else:
                state["context_summary"] = "This is the start of a new conversation."
                if self.verbose:
                    print("✅ Starting new conversation")

        except Exception as e:
            logger.error(f"Error processing conversation: {e}")
            state["context_summary"] = "This is the start of a new conversation."

        return state

    def generate_response(self, state: ConversationState) -> ConversationState:
        """Generate a conversational response"""
        if self.verbose:
            print("🔄 Generating conversational response...")

        try:
            # Prepare messages for the model
            messages = [SystemMessage(content=self.system_prompt)]
            
            # Add context if available
            if state.get("context_summary"):
                context_msg = f"Previous conversation context:\n{state['context_summary']}\n\nCurrent user message: {state['query']}"
                messages.append(HumanMessage(content=context_msg))
            else:
                messages.append(HumanMessage(content=state["query"]))

            # Generate response
            response = self.model.invoke(messages)
            state["response"] = response.content

            if self.verbose:
                print(f"✅ Generated response: {response.content[:100]}...")

        except Exception as e:
            logger.error(f"Error generating response: {e}")
            state["response"] = "I apologize, but I'm having trouble processing your request right now. Please try again."

        return state

    def save_conversation(self, state: ConversationState) -> ConversationState:
        """Save the conversation to database"""
        if self.verbose:
            print("🔄 Saving conversation...")

        try:
            session_id = state.get("session_id")
            if session_id and state.get("response"):
                # Calculate processing time (approximate)
                processing_time = 1.0  # Placeholder
                
                # Prepare agent metadata
                agent_metadata = {
                    "agent_type": "conversation",
                    "context_used": bool(state.get("context_summary")),
                    "history_length": len(state.get("conversation_history", [])),
                    "timestamp": datetime.datetime.utcnow().isoformat()
                }

                # Save the Q&A pair
                success = self.conversation_manager.save_message(
                    session_id=session_id,
                    question=state["query"],
                    answer=state["response"],
                    agent_type="conversation",
                    agent_metadata=agent_metadata,
                    processing_time=processing_time
                )

                if success and self.verbose:
                    print("✅ Conversation saved successfully")

        except Exception as e:
            logger.error(f"Error saving conversation: {e}")

        return state

    def chat(self, query: str, session_id: Optional[int] = None, user_id: Optional[int] = None) -> Dict[str, Any]:
        """
        Main chat method for conversation agent.
        
        Args:
            query: User's message
            session_id: Optional existing session ID
            user_id: User ID for session management
            
        Returns:
            Dict with response and metadata
        """
        start_time = datetime.datetime.now()
        
        try:
            # Prepare initial state
            initial_state = ConversationState(
                messages=[],
                query=query,
                session_id=session_id,
                user_id=user_id,
                conversation_history=[],
                response=None,
                context_summary=None
            )

            # Run the conversation graph
            final_state = self.graph.invoke(initial_state)
            
            # Calculate processing time
            processing_time = (datetime.datetime.now() - start_time).total_seconds()

            # Prepare response
            response = {
                "answer": final_state.get("response", "I apologize, but I couldn't generate a response."),
                "session_id": final_state.get("session_id"),
                "agent_type": "conversation",
                "processing_time": processing_time,
                "context_used": bool(final_state.get("context_summary")),
                "metadata": {
                    "conversation_length": len(final_state.get("conversation_history", [])),
                    "context_summary": final_state.get("context_summary", "")[:200]  # Truncated summary
                }
            }

            if self.verbose:
                print(f"✅ Conversation completed in {processing_time:.2f}s")

            return response

        except Exception as e:
            logger.error(f"Error in conversation chat: {e}")
            return {
                "answer": "I apologize, but I encountered an error while processing your message. Please try again.",
                "session_id": session_id,
                "agent_type": "conversation",
                "processing_time": (datetime.datetime.now() - start_time).total_seconds(),
                "error": str(e)
            }

def create_conversation_agent(model=None, system_prompt=None, verbose=True, database_url=None) -> ConversationAgent:
    """
    Factory function to create a conversation agent.
    
    Args:
        model: Optional LLM model to use
        system_prompt: Optional custom system prompt
        verbose: Whether to enable verbose logging
        database_url: Optional database URL
        
    Returns:
        ConversationAgent instance
    """
    return ConversationAgent(
        model=model,
        system_prompt=system_prompt,
        verbose=verbose,
        database_url=database_url
    ) 