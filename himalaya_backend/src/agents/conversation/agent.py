"""
Conversation Agent for the Himalaya Azure system.

This agent handles simple conversational interactions like greetings, farewells, and expressions of gratitude.
"""

import os
import sys
import datetime
from typing import Dict, Any, Optional, List

from langchain_core.messages import SystemMessage, HumanMessage
from langchain_openai import ChatOpenAI, AzureChatOpenAI

# Import configuration
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
from config.settings import AZURE_OPENAI_KEY, AZURE_OPENAI_API_VERSION, AZURE_OPENAI_ENDPOINT, AZURE_OPENAI_DEPLOYMENT_NAME

class ConversationAgent:
    """
    Conversation agent that generates appropriate responses to simple conversational patterns.
    """

    def __init__(self, model=None, verbose=True):
        """
        Initialize the conversation agent.

        Args:
            model: LLM to use for conversation
            verbose: Whether to print verbose output
        """
        self.verbose = verbose
        self.today_date = datetime.datetime.now().strftime("%Y-%m-%d")

        # Initialize the model
        if model:
            self.model = model
        else:
            self.model = AzureChatOpenAI(
                azure_deployment=AZURE_OPENAI_DEPLOYMENT_NAME,
                openai_api_version=AZURE_OPENAI_API_VERSION,
                azure_endpoint=AZURE_OPENAI_ENDPOINT,
                openai_api_key=AZURE_OPENAI_KEY,
                temperature=0.7  # Higher temperature for more varied responses
            )
            if self.verbose:
                print(f"Using Azure OpenAI with deployment {AZURE_OPENAI_DEPLOYMENT_NAME} for Himalaya conversation agent")
        

    def _get_system_prompt(self, conversation_type: str) -> str:
        """
        Get the system prompt based on the conversation type.

        Args:
            conversation_type: Type of conversation (not used anymore, keeping for compatibility)

        Returns:
            System prompt
        """
        return """You are a helpful and friendly assistant for the Himalaya system.

Your role is to engage in natural conversations with users and provide assistance.

You can help users with:
1. Processing and answering questions about various document types including PDFs, Excel files, Word documents, and text files
2. Extracting and analyzing information from images, including both text and tables
3. Analyzing structured data from spreadsheets and tables
4. Performing document processing and analysis
5. Searching the web for current information when needed
6. Having natural conversations on various topics

Respond in a conversational, helpful manner that matches the user's tone and intent. If they're greeting you, respond warmly. If they're saying goodbye, respond appropriately. If they're asking about your capabilities, explain what you can do.

Keep your responses natural and helpful."""

    def process_query(
        self, 
        query: str, 
        conversation_type: str = "general",  # Kept for backward compatibility
        conversation_history: Optional[str] = None,
        conversation_summary: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Process a conversational query and generate an appropriate response.

        Args:
            query: User query
            conversation_type: Type of conversation (kept for backward compatibility)
            conversation_history: Optional conversation history
            conversation_summary: Optional conversation summary

        Returns:
            Dictionary with the response
        """
        if self.verbose:
            print(f"Processing conversation query: {query}")

        # Get system prompt
        system_prompt = self._get_system_prompt(conversation_type)
        
        # Create messages
        messages = [
            SystemMessage(content=system_prompt),
            HumanMessage(content=query)
        ]
        
        # If there's conversation history, include it for context
        if conversation_history and len(conversation_history) > 0:
            context_note = f"Previous conversation context:\n{conversation_history}"
            messages.insert(1, SystemMessage(content=context_note))
        
        # Generate response
        response = self.model.invoke(messages)
        
        if self.verbose:
            print(f"Generated response: {response.content}")
        
        # Return the result
        return {
            "answer": response.content,
            "references": {}
        }

def create_conversation_agent(model=None, verbose=True) -> ConversationAgent:
    """
    Create a conversation agent.

    Args:
        model: LLM to use for conversation
        verbose: Whether to print verbose output

    Returns:
        A conversation agent
    """
    return ConversationAgent(model=model, verbose=verbose) 