"""
Excel Embeddings Module

This module provides embedding capabilities for Excel/CSV data.
Adapted from Himalaya Azure system for the Enhanced Himalaya backend.
"""

import os
import logging
import pandas as pd
from typing import Dict, List, Any, Optional
import numpy as np
from openai import AzureOpenAI

# Import configuration
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
from config.settings import (
    AZURE_OPENAI_KEY, AZURE_OPENAI_API_VERSION, AZURE_OPENAI_ENDPOINT, 
    AZURE_OPENAI_EMBEDDING_DEPLOYMENT
)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ExcelEmbeddings:
    """
    A class to generate embeddings for Excel/CSV data.
    """

    def __init__(self, azure_endpoint: str = None, azure_api_key: str = None, 
                 azure_api_version: str = None, azure_embedding_deployment: str = None):
        """
        Initialize the Excel Embeddings.

        Args:
            azure_endpoint: Azure OpenAI endpoint
            azure_api_key: Azure OpenAI API key
            azure_api_version: Azure OpenAI API version
            azure_embedding_deployment: Azure OpenAI embedding model deployment name
        """
        # Use configuration values if not provided
        azure_endpoint = azure_endpoint or AZURE_OPENAI_ENDPOINT
        azure_api_key = azure_api_key or AZURE_OPENAI_KEY
        azure_api_version = azure_api_version or AZURE_OPENAI_API_VERSION
        azure_embedding_deployment = azure_embedding_deployment or AZURE_OPENAI_EMBEDDING_DEPLOYMENT

        # Initialize Azure OpenAI client
        self.client = None
        self.embedding_deployment = azure_embedding_deployment
        
        if azure_endpoint and azure_api_key:
            self.client = AzureOpenAI(
                api_key=azure_api_key,
                api_version=azure_api_version,
                azure_endpoint=azure_endpoint
            )
            logger.info(f"Using Azure OpenAI embeddings with deployment {azure_embedding_deployment}")
        else:
            logger.warning("No Azure OpenAI credentials provided. Embeddings will not be available.")

    def generate_text_embedding(self, text: str) -> Optional[List[float]]:
        """
        Generate embedding for a text string.
        
        Args:
            text: Text to generate embedding for
            
        Returns:
            Embedding vector or None if failed
        """
        if not self.client:
            logger.error("Azure OpenAI client not initialized")
            return None
            
        try:
            response = self.client.embeddings.create(
                input=text,
                model=self.embedding_deployment
            )
            return response.data[0].embedding
            
        except Exception as e:
            logger.error(f"Error generating embedding: {str(e)}")
            return None

    def generate_dataframe_embeddings(self, df: pd.DataFrame, text_columns: List[str] = None) -> Dict[str, Any]:
        """
        Generate embeddings for DataFrame content.
        
        Args:
            df: DataFrame to generate embeddings for
            text_columns: List of columns to use for text embedding
            
        Returns:
            Dictionary containing embeddings and metadata
        """
        if not self.client:
            return {
                'embeddings': [],
                'metadata': {'error': 'Azure OpenAI client not initialized'}
            }
        
        try:
            # If no text columns specified, use all string columns
            if text_columns is None:
                text_columns = df.select_dtypes(include=['object', 'string']).columns.tolist()
            
            # Generate summary text for the entire DataFrame
            summary_parts = []
            
            # Add basic info
            summary_parts.append(f"Dataset with {df.shape[0]} rows and {df.shape[1]} columns")
            summary_parts.append(f"Columns: {', '.join(df.columns.tolist())}")
            
            # Add sample data from text columns
            for col in text_columns[:3]:  # Limit to first 3 text columns
                if col in df.columns:
                    sample_values = df[col].dropna().head(3).tolist()
                    if sample_values:
                        summary_parts.append(f"{col} examples: {', '.join(str(v) for v in sample_values)}")
            
            # Add numeric column summaries
            numeric_cols = df.select_dtypes(include=['number']).columns.tolist()
            for col in numeric_cols[:3]:  # Limit to first 3 numeric columns
                if col in df.columns:
                    stats = df[col].describe()
                    summary_parts.append(f"{col} range: {stats['min']:.2f} to {stats['max']:.2f}, mean: {stats['mean']:.2f}")
            
            summary_text = ". ".join(summary_parts)
            
            # Generate embedding for the summary
            embedding = self.generate_text_embedding(summary_text)
            
            return {
                'embeddings': [embedding] if embedding else [],
                'summary_text': summary_text,
                'metadata': {
                    'shape': df.shape,
                    'columns': df.columns.tolist(),
                    'text_columns': text_columns,
                    'numeric_columns': numeric_cols
                }
            }
            
        except Exception as e:
            logger.error(f"Error generating DataFrame embeddings: {str(e)}")
            return {
                'embeddings': [],
                'metadata': {'error': str(e)}
            }

    def generate_row_embeddings(self, df: pd.DataFrame, text_columns: List[str] = None, 
                               max_rows: int = 100) -> Dict[str, Any]:
        """
        Generate embeddings for individual rows in a DataFrame.
        
        Args:
            df: DataFrame to generate embeddings for
            text_columns: List of columns to use for text embedding
            max_rows: Maximum number of rows to process
            
        Returns:
            Dictionary containing row embeddings and metadata
        """
        if not self.client:
            return {
                'embeddings': [],
                'metadata': {'error': 'Azure OpenAI client not initialized'}
            }
        
        try:
            # Limit the number of rows to process
            df_subset = df.head(max_rows)
            
            # If no text columns specified, use all string columns
            if text_columns is None:
                text_columns = df_subset.select_dtypes(include=['object', 'string']).columns.tolist()
            
            embeddings = []
            row_texts = []
            
            for idx, row in df_subset.iterrows():
                # Create text representation of the row
                row_parts = []
                
                # Add text column values
                for col in text_columns:
                    if col in row and pd.notna(row[col]):
                        row_parts.append(f"{col}: {row[col]}")
                
                # Add numeric column values
                numeric_cols = df_subset.select_dtypes(include=['number']).columns.tolist()
                for col in numeric_cols:
                    if col in row and pd.notna(row[col]):
                        row_parts.append(f"{col}: {row[col]}")
                
                row_text = ". ".join(row_parts)
                row_texts.append(row_text)
                
                # Generate embedding for this row
                embedding = self.generate_text_embedding(row_text)
                embeddings.append(embedding)
            
            return {
                'embeddings': embeddings,
                'row_texts': row_texts,
                'metadata': {
                    'rows_processed': len(embeddings),
                    'text_columns': text_columns,
                    'total_rows': len(df)
                }
            }
            
        except Exception as e:
            logger.error(f"Error generating row embeddings: {str(e)}")
            return {
                'embeddings': [],
                'metadata': {'error': str(e)}
            } 