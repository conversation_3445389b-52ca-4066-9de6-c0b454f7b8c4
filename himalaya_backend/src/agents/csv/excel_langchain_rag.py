"""
Excel LangChain RAG Module

This module provides RAG (Retrieval-Augmented Generation) capabilities for Excel data
using <PERSON><PERSON><PERSON><PERSON>'s pandas_dataframe_agent and python_repl_tool.
Adapted from Himalaya Azure system for the Enhanced Himalaya backend.
"""

import os
import json
import logging
import sys
from pathlib import Path
import pandas as pd
from typing import Dict, List, Any, Optional
import tempfile
from langchain_openai import AzureChatOpenAI
try:
    from langchain_experimental.agents import create_pandas_dataframe_agent
    from langchain.agents.agent_types import AgentType
    PANDAS_AGENT_AVAILABLE = True
except ImportError as e:
    # Logger not yet configured at import time, will be handled later
    PANDAS_AGENT_AVAILABLE = False
    create_pandas_dataframe_agent = None
    AgentType = None
from langchain_community.callbacks.manager import get_openai_callback
from datetime import datetime

# Import configuration
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
from config.settings import (
    AZURE_OPENAI_KEY, AZURE_OPENAI_API_VERSION, AZURE_OPENAI_ENDPOINT, 
    AZURE_OPENAI_DEPLOYMENT_NAME, AZURE_OPENAI_EMBEDDING_DEPLOYMENT,
    AZURE_STORAGE_CONNECTION_STRING, AZURE_STORAGE_CONTAINER_NAME
)

# Import Azure Blob Storage
from azure.storage.blob import BlobServiceClient

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ExcelLangChainRAG:
    """
    A class to provide RAG capabilities for Excel data using LangChain agents.
    """

    def __init__(self,
                 data_dir: str = None,
                 azure_deployment: str = None,
                 azure_embedding_deployment: str = None,
                 azure_api_version: str = None,
                 azure_endpoint: str = None,
                 azure_api_key: str = None,
                 verbose: bool = True):
        """
        Initialize the Excel RAG with LangChain agents.

        Args:
            data_dir: Directory where processed data is stored
            azure_deployment: Azure OpenAI model deployment name
            azure_embedding_deployment: Azure OpenAI embedding model deployment name
            azure_api_version: Azure OpenAI API version
            azure_endpoint: Azure OpenAI endpoint
            azure_api_key: Azure OpenAI API key
            verbose: Whether to print verbose output
        """
        # Use configuration values if not provided
        azure_deployment = azure_deployment or AZURE_OPENAI_DEPLOYMENT_NAME
        azure_embedding_deployment = azure_embedding_deployment or AZURE_OPENAI_EMBEDDING_DEPLOYMENT
        azure_api_version = azure_api_version or AZURE_OPENAI_API_VERSION
        azure_endpoint = azure_endpoint or AZURE_OPENAI_ENDPOINT
        azure_api_key = azure_api_key or AZURE_OPENAI_KEY

        self.data_dir = data_dir or "data"
        self.verbose = verbose
        logger.info(f"Using data directory for RAG: {self.data_dir}")

        # Set up directories
        self.csv_dir = Path(self.data_dir) / "csv"
        os.makedirs(self.csv_dir, exist_ok=True)

        # Initialize Azure OpenAI
        self.llm = None
        if azure_endpoint and azure_api_key:
            self.llm = AzureChatOpenAI(
                azure_deployment=azure_deployment,
                api_version=azure_api_version,
                azure_endpoint=azure_endpoint,
                api_key=azure_api_key,
                temperature=0.3
            )
            logger.info(f"Using Azure OpenAI with deployment {azure_deployment}")
        else:
            logger.warning("No Azure OpenAI credentials provided. RAG will not be available.")

        # Initialize Azure Blob Storage client
        self.blob_service_client = None
        if AZURE_STORAGE_CONNECTION_STRING:
            try:
                self.blob_service_client = BlobServiceClient.from_connection_string(AZURE_STORAGE_CONNECTION_STRING)
                logger.info("Azure Blob Storage client initialized")
            except Exception as e:
                logger.warning(f"Failed to initialize Azure Blob Storage client: {str(e)}")
        
        # Check if pandas agent is available
        if not PANDAS_AGENT_AVAILABLE:
            logger.warning("langchain_experimental package not available. Excel/CSV analysis with pandas agent will be limited. Please install with: pip install langchain_experimental")

    def _download_file_from_blob_storage(self, blob_url: str) -> Optional[str]:
        """
        Download file from Azure Blob Storage to a temporary location.
        
        Args:
            blob_url: URL to the blob file
            
        Returns:
            Path to downloaded temporary file, or None if failed
        """
        if not self.blob_service_client:
            logger.error("Azure Blob Storage client not initialized")
            return None
            
        try:
            # Extract blob name from URL
            blob_name = blob_url.split('/')[-1]
            
            # Get blob client
            container_client = self.blob_service_client.get_container_client(AZURE_STORAGE_CONTAINER_NAME)
            blob_client = container_client.get_blob_client(blob_name)
            
            # Create temporary file with correct extension
            file_extension = os.path.splitext(blob_name)[1]
            with tempfile.NamedTemporaryFile(delete=False, suffix=file_extension) as temp_file:
                temp_file_path = temp_file.name
                
                # Download blob content to temporary file
                blob_data = blob_client.download_blob()
                temp_file.write(blob_data.readall())
                
            logger.info(f"Downloaded blob {blob_name} to {temp_file_path}")
            return temp_file_path
            
        except Exception as e:
            logger.error(f"Failed to download blob from {blob_url}: {str(e)}")
            return None

    def _get_column_sample_data(self, df: pd.DataFrame, column_name: str, num_samples: int = 3) -> List[Any]:
        """
        Get sample data for a specific column.
        
        Args:
            df: DataFrame containing the data
            column_name: Name of the column to get samples for
            num_samples: Number of samples to retrieve
            
        Returns:
            List of sample values
        """
        try:
            # Get non-null values from the column
            non_null_values = df[column_name].dropna().tolist()
            
            # If there are fewer than num_samples non-null values, return all of them
            if len(non_null_values) <= num_samples:
                return non_null_values
            
            # Otherwise, return a sample
            return df[column_name].dropna().sample(num_samples).tolist()
        except Exception as e:
            logger.error(f"Error getting sample data for column {column_name}: {str(e)}")
            return []

    def _detect_date_columns(self, df: pd.DataFrame) -> List[str]:
        """
        Detect columns that likely contain date information.
        
        Args:
            df: DataFrame to analyze
            
        Returns:
            List of column names that likely contain dates
        """
        date_columns = []
        
        for col in df.columns:
            # Check if column name suggests it's a date
            col_lower = col.lower()
            if any(keyword in col_lower for keyword in ['date', 'time', 'created', 'updated', 'modified']):
                date_columns.append(col)
                continue
                
            # Check if column values look like dates
            try:
                sample_values = self._get_column_sample_data(df, col, 5)
                date_like_count = 0
                
                for value in sample_values:
                    if pd.isna(value):
                        continue
                    
                    # Try to parse as date
                    try:
                        pd.to_datetime(str(value))
                        date_like_count += 1
                    except:
                        pass
                
                # If most samples look like dates, consider it a date column
                if date_like_count >= len(sample_values) * 0.6:
                    date_columns.append(col)
                    
            except Exception as e:
                logger.debug(f"Error checking if column {col} contains dates: {str(e)}")
        
        return date_columns

    def _enhance_dataframe_metadata(self, df: pd.DataFrame, csv_metadata: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Enhance DataFrame with metadata for better analysis.
        
        Args:
            df: DataFrame to analyze
            csv_metadata: Existing metadata from database
            
        Returns:
            Enhanced metadata dictionary
        """
        metadata = csv_metadata or {}
        
        # Basic statistics
        metadata.update({
            'shape': df.shape,
            'columns': list(df.columns),
            'dtypes': {col: str(dtype) for col, dtype in df.dtypes.items()},
            'null_counts': df.isnull().sum().to_dict(),
            'memory_usage': df.memory_usage(deep=True).sum()
        })
        
        # Detect date columns
        date_columns = self._detect_date_columns(df)
        if date_columns:
            metadata['date_columns'] = date_columns
        
        # Sample data for each column
        column_samples = {}
        for col in df.columns:
            samples = self._get_column_sample_data(df, col, 3)
            column_samples[col] = samples
        metadata['column_samples'] = column_samples
        
        # Numeric column statistics
        numeric_columns = df.select_dtypes(include=['number']).columns.tolist()
        if numeric_columns:
            metadata['numeric_columns'] = numeric_columns
            metadata['numeric_stats'] = df[numeric_columns].describe().to_dict()
        
        return metadata

    def load_csv_data(self, csv_path: str) -> Optional[pd.DataFrame]:
        """
        Load CSV data from file path or blob URL.
        
        Args:
            csv_path: Path to CSV file or blob URL
            
        Returns:
            DataFrame or None if loading failed
        """
        original_path = csv_path
        try:
            if self.verbose:
                logger.info(f"Loading data from: {csv_path}")
            
            # Check if it's a blob URL
            if csv_path.startswith('http'):
                # Download from blob storage
                local_path = self._download_file_from_blob_storage(csv_path)
                if not local_path:
                    logger.error(f"Failed to download file from blob storage: {csv_path}")
                    return None
                csv_path = local_path
                if self.verbose:
                    logger.info(f"Downloaded to local path: {csv_path}")
            
            # Check if file exists
            if not os.path.exists(csv_path):
                logger.error(f"File does not exist: {csv_path}")
                return None
            
            # Load the CSV/Excel file based on extension
            if csv_path.endswith('.csv'):
                if self.verbose:
                    logger.info(f"Loading as CSV file: {csv_path}")
                df = pd.read_csv(csv_path)
            elif csv_path.endswith(('.xlsx', '.xls')):
                if self.verbose:
                    logger.info(f"Loading as Excel file: {csv_path}")
                df = pd.read_excel(csv_path)
            elif csv_path.endswith('.pdf'):
                # This is a PDF file being passed to CSV processor - this shouldn't happen
                # but we'll handle it gracefully
                logger.warning(f"PDF file passed to CSV processor: {csv_path}. This indicates the system tried to process a PDF table as CSV but couldn't find the generated CSV file.")
                return None
            else:
                logger.error(f"Unsupported file format: {csv_path}. Supported formats: .csv, .xlsx, .xls")
                return None
                
            if df is not None and not df.empty:
                logger.info(f"Successfully loaded data with shape: {df.shape}")
                if self.verbose:
                    logger.info(f"Columns: {list(df.columns)}")
                return df
            else:
                logger.warning(f"Loaded DataFrame is empty: {csv_path}")
                return None
            
        except Exception as e:
            logger.error(f"Error loading data from {original_path}: {str(e)}")
            logger.error(f"Local path (if downloaded): {csv_path}")
            import traceback
            logger.error(f"Full traceback: {traceback.format_exc()}")
            return None
        finally:
            # Clean up temporary file if it was downloaded
            if csv_path != original_path and (csv_path.startswith('/tmp') or csv_path.startswith('C:\\Users')):
                try:
                    if os.path.exists(csv_path):
                        os.unlink(csv_path)
                        if self.verbose:
                            logger.info(f"Cleaned up temporary file: {csv_path}")
                except Exception as cleanup_error:
                    logger.warning(f"Failed to cleanup temporary file {csv_path}: {cleanup_error}")

    def analyze_with_pandas_agent(self, query: str, dataframes: Dict[str, pd.DataFrame],
                                 metadata: Dict[str, Any]) -> Dict[str, Any]:
        """
        Analyze data using LangChain pandas agent.
        
        Args:
            query: User query
            dataframes: Dictionary of DataFrames to analyze
            metadata: Metadata about the data
            
        Returns:
            Analysis result
        """
        if not self.llm:
            return {
                'answer': 'Excel analysis not available - Azure OpenAI not configured',
                'error': True
            }
        
        if not PANDAS_AGENT_AVAILABLE:
            return {
                'answer': 'Excel analysis not available - langchain_experimental package not installed. Please install it with: pip install langchain_experimental',
                'error': True,
                'metadata': metadata
            }
        
        try:
            # Prepare context about the data
            context_parts = []
            
            for name, df in dataframes.items():
                df_info = f"""
Dataset: {name}
Shape: {df.shape[0]} rows, {df.shape[1]} columns
Columns: {', '.join(df.columns.tolist())}
"""
                context_parts.append(df_info)
                
                # Add sample data
                if not df.empty:
                    context_parts.append(f"Sample data from {name}:")
                    context_parts.append(df.head(3).to_string())
            
            context = "\n".join(context_parts)
            
            # Create enhanced query with context - IMPORTANT: Tell the agent the DataFrame is already loaded
            enhanced_query = f"""
Context about the available data:
{context}

IMPORTANT: The data is already loaded in a DataFrame called 'df'. Do NOT try to read from any file path. 
Use the existing 'df' DataFrame directly for all analysis.

USER'S ORIGINAL INTENT: {query}

INTENT ANALYSIS:
- Focus your analysis on directly answering the user's specific question
- Provide the most relevant information that addresses their need
- Generate tables and visualizations that support their intent
- Ensure your response is comprehensive yet targeted to their goal

User Query: {query}

🤖 INTELLIGENT TABLE GENERATION:
Based on the user's query, automatically determine which tables would be most helpful and generate them:

**QUERY ANALYSIS & TABLE SELECTION:**
- **"relationships", "correlations", "connected"** → Generate correlation matrix
- **"segments", "groups", "categories", "by department/region"** → Generate pivot tables and group summaries
- **"overview", "summary", "statistics", "describe data"** → Generate descriptive statistics table
- **"performance", "comparison", "differences"** → Generate comparative tables and cross-tabulations
- **"outliers", "anomalies", "unusual"** → Generate outlier detection tables
- **"missing", "quality", "completeness"** → Generate data quality summary table
- **"trends", "patterns", "distribution"** → Generate frequency tables and distribution summaries

**AUTOMATIC TABLE GENERATION RULES:**
1. **Always start with data overview** (shape, columns, data types) in table format
2. **For any analysis query**, include relevant descriptive statistics
3. **For business questions**, automatically generate pivot tables showing key metrics by relevant dimensions
4. **For data exploration**, show correlation matrix if multiple numerical variables exist
5. **For quality assessment**, generate missing values and data quality tables

**SMART TABLE COMBINATIONS:**
- **Customer Analysis** → Demographics table + Purchase behavior pivot + Correlation matrix
- **Sales Analysis** → Performance by region/product + Trend analysis + Top/bottom performers
- **Survey Data** → Response distribution + Cross-tabulation + Correlation analysis
- **Financial Data** → Summary statistics + Period comparisons + Variance analysis
- **HR Data** → Department summaries + Experience/salary analysis + Diversity metrics

**TABLE GENERATION EXAMPLES:**
- For customer analysis queries: Demographics table + Purchase behavior pivot + Correlation matrix
- For sales analysis queries: Performance by region/product + Trend analysis + Top/bottom performers  
- For survey data queries: Response distribution + Cross-tabulation + Correlation analysis
- For financial data queries: Summary statistics + Period comparisons + Variance analysis
- For HR data queries: Department summaries + Experience/salary analysis + Diversity metrics

COMPREHENSIVE DATA ANALYSIS INSTRUCTIONS:
You are an expert data analyst. When analyzing Excel/CSV data, provide comprehensive insights using these techniques:

🔍 DESCRIPTIVE STATISTICS:
- Calculate mean, median, mode, min, max, standard deviation, variance for numerical columns
- Show quartiles (Q1, Q2, Q3) and percentiles (95th, 99th)
- Identify data types and null value counts for all columns
- Generate summary statistics using df.describe() and df.info()

📊 CORRELATION ANALYSIS:
- Calculate correlation matrix for numerical columns using df.corr()
- Identify strong positive/negative correlations (>0.7 or <-0.7)
- Highlight the most correlated variable pairs
- Use methods like Pearson, Spearman where appropriate

🎯 OUTLIER DETECTION:
- Use IQR method: Q1 - 1.5*IQR and Q3 + 1.5*IQR
- Use Z-score method: values with |z-score| > 3
- Count and percentage of outliers per numerical column
- Show specific outlier values and their impact

📈 GROUP-WISE AGGREGATIONS:
- Group by categorical columns and calculate aggregates (sum, mean, count, std)
- Identify patterns across different groups/segments
- Compare group performance and variations
- Use df.groupby() with multiple aggregation functions

🗂️ PIVOT SUMMARIES:
- Create pivot tables for cross-tabulation analysis
- Show relationships between categorical variables
- Calculate percentages and proportions where relevant
- Use pd.pivot_table() and pd.crosstab()

🔧 DATA QUALITY CHECKS:
- Missing values: count and percentage per column
- Duplicate rows: identify and count using df.duplicated()
- Data consistency: check for anomalous values
- Data completeness: overall data quality score

📉 TREND & DISTRIBUTION ANALYSIS:
- Identify trends in time-series data (if date columns exist)
- Analyze distributions: skewness, kurtosis
- Detect seasonal patterns or cyclical behavior
- Show value counts for categorical variables

💼 BUSINESS INSIGHTS:
- Provide actionable business recommendations
- Identify key performance indicators (KPIs)
- Highlight significant patterns that impact business decisions
- Suggest areas for improvement or optimization

🎨 VISUALIZATION SUGGESTIONS:
- Recommend appropriate chart types (histograms, scatter plots, box plots, heatmaps)
- Suggest visualizations for key findings
- Mention which variables would benefit from visual analysis

📋 TABULAR DATA OUTPUT REQUIREMENTS:
When user requests tabular data (correlation matrix, pivot tables, crosstabs, summary statistics, etc.):

1. **ALWAYS INCLUDE FORMATTED TABLES IN YOUR RESPONSE**: 
   - Use tabulate library to format tables professionally
   - Import tabulate: from tabulate import tabulate
   - Format tables with: tabulate(data, headers=headers, tablefmt='pipe', floatfmt='.3f')
   - Use 'pipe' format for clean markdown table output that works in chat interfaces
   - If tabulate is not available, manually create markdown table format

2. **CRITICAL: RETURN TABLES IN TEXT RESPONSE**: 
   - DO NOT just print() tables - they won't appear in the final response
   - ALWAYS include the formatted table string in your response text
   - Create the table string first, then include it in your final answer
   - Make sure the table appears in the text you return to the user

3. **TABLE FORMATTING OPTIONS**: 
   - Use tablefmt='pipe' for markdown-compatible tables that display properly in chat
   - This creates | Column 1 | Column 2 | Column 3 | format
   - Use floatfmt='.3f' or '.2f' for consistent decimal precision
   - For large tables, consider limiting columns or rows for better readability
   - Always include proper headers using headers parameter
   - Add alignment row with |:---:|:---:|:---:| for proper markdown formatting

4. **ALTERNATIVE FORMATTING METHODS**:
   - If tabulate is not available, manually create markdown table format
   - For pandas dataframes, convert to markdown: df.to_markdown(index=True, floatfmt='.3f')
   - For correlation matrices, use: df.corr().round(3).to_markdown()
   - Always include the formatted markdown table string in your final response text
   - Manual markdown format: | Header1 | Header2 | with |:---:|:---:| alignment row

5. **PROVIDE DETAILED INTERPRETATION**:
   - Explain what each value in the table means
   - Highlight the most significant findings
   - Point out patterns, trends, or anomalies
   - Provide context for the numbers

6. **STRUCTURE YOUR RESPONSE**:
   ```
   ## [Table Name/Type]
   
   [Include the formatted table string here - NOT just print it]
   
   ### Key Findings:
   - [Specific insight 1 with numbers]
   - [Specific insight 2 with numbers]
   - [Pattern or trend identified]
   
   ### Business Implications:
   - [Actionable recommendation 1]
   - [Actionable recommendation 2]
   ```

7. **CORRECT APPROACH FOR TABLE INCLUSION**:
   - CORRECT: Create formatted table string and include it in your response text
   - WRONG: Just using print() statements - they won't appear in the final response
   - Always format the table first, then include the formatted string in your response
   - Structure your response with clear sections: Table → Key Findings → Business Implications

8. **IMPORT REQUIREMENTS**: 
   - Always try to import tabulate first: `from tabulate import tabulate`
   - If tabulate is not available, fall back to pandas formatting
   - Handle import errors gracefully and use alternative formatting

ANALYSIS APPROACH:
1. Start with data overview (shape, columns, data types)
2. Perform data quality assessment
3. Calculate comprehensive descriptive statistics
4. Analyze correlations and relationships
5. Detect and analyze outliers
6. Create meaningful groupings and aggregations
7. Generate pivot summaries for cross-analysis
8. Identify trends and distributions
9. Provide business-focused insights and recommendations

Remember: Use the existing 'df' DataFrame - do not attempt to load data from any file.
Always provide specific numbers, percentages, and concrete findings from the actual data.
Make your analysis comprehensive but focused on answering the user's specific query.
When tabular data is requested, ALWAYS show the actual table AND provide detailed interpretation.

DATA ANALYSIS EXPERTISE:
- You have deep knowledge of statistical analysis, data science, and business intelligence
- You can perform comprehensive data analysis including descriptive statistics, correlation analysis, outlier detection
- You understand data quality assessment, trend analysis, and business insights generation
- You can create meaningful aggregations, pivot tables, and cross-tabulations
- You provide actionable recommendations based on data findings
- You intelligently select which tables to generate based on the user's query context and intent

INTELLIGENT TABLE SELECTION:
- Analyze the user's query to determine the most relevant tables to generate automatically
- For customer/demographic questions: Show segmentation tables, demographic summaries, behavior analysis
- For performance questions: Generate comparison tables, ranking tables, trend analysis
- For relationship questions: Display correlation matrices, cross-tabulations
- For overview questions: Provide descriptive statistics, data quality summaries, distribution tables
- Always consider what tables would best answer the user's underlying business question
"""
            
            # Use the first DataFrame for the pandas agent
            main_df = list(dataframes.values())[0]
            
            # Create pandas agent with explicit instructions
            agent = create_pandas_dataframe_agent(
                llm=self.llm,
                df=main_df,
                verbose=True,
                agent_type=AgentType.OPENAI_FUNCTIONS,
                allow_dangerous_code=True,  # Required for pandas agent
                prefix="""
You are an expert data analyst working with a pandas dataframe in Python. The name of the dataframe is `df`.
The dataframe is already loaded and available. Do NOT attempt to read from any file path.
Use only the existing `df` dataframe for all operations.

DATA ANALYSIS EXPERTISE:
- You have deep knowledge of statistical analysis, data science, and business intelligence
- You can perform comprehensive data analysis including descriptive statistics, correlation analysis, outlier detection
- You understand data quality assessment, trend analysis, and business insights generation
- You can create meaningful aggregations, pivot tables, and cross-tabulations
- You provide actionable recommendations based on data findings
- You intelligently select which tables to generate based on the user's query context and intent

INTELLIGENT TABLE SELECTION:
- Analyze the user's query to determine the most relevant tables to generate automatically
- For customer/demographic questions: Show segmentation tables, demographic summaries, behavior analysis
- For performance questions: Generate comparison tables, ranking tables, trend analysis
- For relationship questions: Display correlation matrices, cross-tabulations
- For overview questions: Provide descriptive statistics, data quality summaries, distribution tables
- Always consider what tables would best answer the user's underlying business question

TECHNICAL CAPABILITIES:
- Use pandas methods like df.describe(), df.info(), df.corr(), df.groupby(), pd.pivot_table()
- Calculate statistical measures: mean, median, mode, std, var, quartiles, percentiles
- Detect outliers using IQR and Z-score methods
- Analyze missing values, duplicates, and data quality issues
- Perform correlation analysis and identify significant relationships
- Create meaningful business insights from statistical findings

TABULAR DATA OUTPUT STANDARDS:
- When user asks for tables (correlation matrix, pivot tables, crosstabs, etc.), ALWAYS include formatted tables in your response text
- CRITICAL: DO NOT just print() tables - they won't appear in the final response to the user
- Use tabulate library to format tables: tabulate(data, headers=headers, tablefmt='pipe', floatfmt='.3f')
- Use 'pipe' format to create markdown-compatible tables that display properly in chat interfaces
- ALWAYS include the formatted table in your final response text that gets returned to the user
- Pattern: Generate table → Format with tabulate as markdown → Include formatted table in response text
- If tabulate unavailable, manually create markdown table format with | Column | Column |
- Provide both the formatted markdown table AND detailed interpretation of what the numbers mean
- Structure responses with clear sections: Formatted Markdown Table → Key Findings → Business Implications

ANALYSIS STANDARDS:
- Always provide specific numbers, percentages, and concrete findings
- Include data quality assessment in your analysis
- Identify the most important patterns and relationships
- Suggest actionable business recommendations
- Mention appropriate visualization techniques for key findings
- Focus on answering the user's query while providing comprehensive insights
- For tabular requests, show the actual data table followed by detailed explanations

RESPONSE FORMAT FOR TABULAR DATA:
1. Display the actual table using print()
2. Explain what each column/row represents
3. Highlight the most significant values
4. Provide business context and implications
5. Suggest follow-up analysis if relevant

Remember: The dataframe `df` is already loaded - use it directly for all analysis.
"""
            )
            
            # Execute the query
            with get_openai_callback() as cb:
                result = agent.run(enhanced_query)
                
            return {
                'answer': result,
                'status': 'success',
                'token_usage': {
                    'total_tokens': cb.total_tokens,
                    'prompt_tokens': cb.prompt_tokens,
                    'completion_tokens': cb.completion_tokens,
                    'total_cost': cb.total_cost
                },
                'metadata': metadata,
                'agent_type': 'pandas_dataframe_agent'
            }
            
        except Exception as e:
            logger.error(f"Error in pandas agent analysis: {str(e)}")
            return {
                'answer': f'Error analyzing data: {str(e)}',
                'status': 'error',
                'metadata': metadata
            }

    def process_csv_directly(self, csv_path: str, query: str, csv_id: str = None, metadata: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Process a CSV file directly with a query.
        
        Args:
            csv_path: Path to CSV file or blob URL
            query: User query
            csv_id: Identifier for the CSV
            metadata: Additional metadata
            
        Returns:
            Processing result
        """
        if self.verbose:
            logger.info(f"Processing CSV directly: {csv_id}, Path: {csv_path}")
        
        try:
            # Validate inputs
            if not csv_path:
                return {
                    'csv_id': csv_id,
                    'csv_path': csv_path,
                    'answer': 'Error: No file path provided',
                    'status': 'error'
                }
            
            if not query or not query.strip():
                return {
                    'csv_id': csv_id,
                    'csv_path': csv_path,
                    'answer': 'Error: No query provided',
                    'status': 'error'
                }
            
            # Load the data
            df = self.load_csv_data(csv_path)
            if df is None:
                error_msg = f"Failed to load data from {csv_path}. "
                if csv_path.endswith('.csv'):
                    error_msg += "Please ensure the CSV file exists and is properly formatted."
                elif csv_path.endswith(('.xlsx', '.xls')):
                    error_msg += "Please ensure the Excel file exists and is accessible."
                else:
                    error_msg += "Please ensure the file format is supported (.csv, .xlsx, .xls)."
                
                return {
                    'csv_id': csv_id,
                    'csv_path': csv_path,
                    'answer': error_msg,
                    'status': 'error'
                }
            
            if df.empty:
                return {
                    'csv_id': csv_id,
                    'csv_path': csv_path,
                    'answer': f'The dataset appears to be empty. Please check the file content.',
                    'status': 'error'
                }
            
            if self.verbose:
                logger.info(f"Successfully loaded DataFrame with shape {df.shape} for analysis")
            
            # Enhance metadata
            enhanced_metadata = self._enhance_dataframe_metadata(df, metadata)
            
            # Analyze with pandas agent
            result = self.analyze_with_pandas_agent(
                query=query,
                dataframes={csv_id or 'data': df},
                metadata=enhanced_metadata
            )
            
            # Add CSV identification to result
            result.update({
                'csv_id': csv_id,
                'csv_path': csv_path,
                'data_shape': df.shape
            })
            
            if self.verbose:
                logger.info(f"Analysis completed for {csv_id}. Status: {result.get('status', 'unknown')}")
            
            return result
            
        except Exception as e:
            error_msg = f"Error processing file {csv_id}: {str(e)}"
            logger.error(error_msg)
            import traceback
            logger.error(f"Full traceback: {traceback.format_exc()}")
            
            return {
                'csv_id': csv_id,
                'csv_path': csv_path,
                'answer': error_msg,
                'status': 'error'
            }

    def answer_query(self, query: str) -> Dict[str, Any]:
        """
        Answer a query using available CSV data.
        
        Args:
            query: User query
            
        Returns:
            Query result
        """
        if not self.llm:
            return {
                'answer': 'Excel analysis not available - Azure OpenAI not configured',
                'status': 'error'
            }
        
        try:
            # For now, return a basic response
            # This would be enhanced with actual CSV discovery and processing
            return {
                'answer': 'CSV query processing not yet implemented for general queries. Use process_csv_directly for specific files.',
                'status': 'success',
                'metadata': {
                    'processor': 'excel_langchain_rag',
                    'query': query
                }
            }
            
        except Exception as e:
            logger.error(f"Error answering query: {str(e)}")
            return {
                'answer': f'Error processing query: {str(e)}',
                'status': 'error'
            }

    def _select_most_relevant_csv(self, query: str, csv_documents: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Select the most relevant CSV document based on query content and semantic summaries.
        
        Args:
            query: User query
            csv_documents: List of CSV document information
            
        Returns:
            Most relevant CSV document
        """
        if len(csv_documents) == 1:
            return csv_documents[0]
        
        try:
            # Extract key terms from the query
            query_lower = query.lower()
            query_terms = set(query_lower.split())
            
            # Define scoring weights for different relevance factors
            best_score = -1
            best_csv = csv_documents[0]  # Fallback to first if no good match
            
            if self.verbose:
                logger.info(f"🔍 CSV SELECTION: Analyzing {len(csv_documents)} tables for query: '{query}'")
            
            for i, csv_doc in enumerate(csv_documents):
                score = 0
                semantic_summary = csv_doc.get('semantic_summary', '')
                sheet_name = csv_doc.get('sheet_name', '')
                
                # Convert to lowercase for comparison
                summary_lower = semantic_summary.lower()
                sheet_lower = sheet_name.lower()
                
                # Score based on semantic summary content
                for term in query_terms:
                    if term in summary_lower:
                        score += 10  # High weight for exact term matches in summary
                    if term in sheet_lower:
                        score += 5   # Medium weight for term matches in sheet name
                
                # Specific scoring for common query patterns
                if 'sex ratio' in query_lower:
                    if 'sex ratio' in summary_lower or 'sex ratio' in sheet_lower:
                        score += 50  # Very high weight for exact phrase match
                    if 'population' in summary_lower and 'ratio' in summary_lower:
                        score += 30  # High weight for related terms
                    if 'demographic' in summary_lower:
                        score += 20  # Medium weight for demographic data
                    if 'census' in summary_lower:
                        score += 25  # High weight for census data
                    if '2011' in summary_lower or '2011' in sheet_lower:
                        score += 15  # Medium weight for year match
                
                elif 'population' in query_lower:
                    if 'population' in summary_lower:
                        score += 30
                    if 'demographic' in summary_lower:
                        score += 20
                    if 'census' in summary_lower:
                        score += 25
                
                elif 'state' in query_lower or 'states' in query_lower:
                    if 'state' in summary_lower or 'states' in summary_lower:
                        score += 20
                    if 'india' in summary_lower:
                        score += 15
                
                # Penalty for clearly unrelated content
                if any(unrelated in summary_lower for unrelated in ['fertility', 'birth rate', 'tfr', 'asymptote']):
                    if 'sex ratio' not in query_lower:  # Only penalize if not looking for sex ratio
                        score -= 20
                
                if self.verbose:
                    logger.info(f"🔍 CSV SELECTION: Table {i+1} ({csv_doc.get('csv_id', 'unknown')}) - Score: {score}")
                    logger.info(f"   Summary: {semantic_summary[:100]}...")
                
                if score > best_score:
                    best_score = score
                    best_csv = csv_doc
            
            if self.verbose:
                logger.info(f"✅ CSV SELECTION: Selected table with score {best_score}: {best_csv.get('csv_id', 'unknown')}")
            
            return best_csv
            
        except Exception as e:
            logger.error(f"Error in CSV selection: {str(e)}")
            # SAFETY FIX: Don't fallback to arbitrary document selection
            logger.error("Cannot safely select CSV document - returning None to prevent incorrect processing")
            return None

    def process_query_with_documents(self, query: str, csv_documents: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Process a query with specific CSV documents.
        
        Args:
            query: User query
            csv_documents: List of CSV document information
            
        Returns:
            Processing result with answer and metadata
        """
        if self.verbose:
            logger.info(f"Processing query with {len(csv_documents)} CSV documents")
        
        if not self.llm:
            return {
                'answer': 'Excel analysis not available - Azure OpenAI not configured',
                'status': 'error',
                'sources': [],
                'metadata': {'agent': 'csv', 'status': 'no_llm'}
            }
        
        try:
            if not csv_documents:
                return {
                    'answer': 'No CSV documents provided for analysis.',
                    'status': 'error',
                    'sources': [],
                    'metadata': {'agent': 'csv', 'status': 'no_documents'}
                }
            
            # Process the most relevant CSV document using intelligent selection
            primary_csv = self._select_most_relevant_csv(query, csv_documents)

            # SAFETY CHECK: Handle case where CSV selection fails
            if primary_csv is None:
                return {
                    'answer': 'Unable to safely select a relevant CSV document for processing.',
                    'status': 'error',
                    'sources': [],
                    'metadata': {'agent': 'csv', 'status': 'selection_failed'}
                }

            csv_path = primary_csv.get('file_path', primary_csv.get('azure_url', ''))
            csv_id = primary_csv.get('csv_id', 'unknown')
            
            if not csv_path:
                return {
                    'answer': 'No valid file path found for CSV document.',
                    'status': 'error',
                    'sources': [],
                    'metadata': {'agent': 'csv', 'status': 'no_path'}
                }
            
            # Process the CSV directly
            result = self.process_csv_directly(
                csv_path=csv_path,
                query=query,
                csv_id=csv_id,
                metadata=primary_csv.get('metadata', {})
            )
            
            # Format the result for consistency with other agents
            formatted_result = {
                'answer': result.get('answer', 'No answer generated'),
                'status': result.get('status', 'unknown'),
                'sources': [{
                    'csv_id': csv_id,
                    'file_path': csv_path,
                    'sheet_name': primary_csv.get('sheet_name', ''),
                    'row_count': primary_csv.get('row_count', 0),
                    'column_count': primary_csv.get('column_count', 0)
                }],
                'metadata': {
                    'agent': 'csv',
                    'status': result.get('status', 'unknown'),
                    'documents_processed': len(csv_documents),
                    'primary_csv': csv_id,
                    'data_shape': result.get('data_shape', 'unknown'),
                    'token_usage': result.get('token_usage', {}),
                    'agent_type': result.get('agent_type', 'pandas_dataframe_agent')
                }
            }
            
            return formatted_result
            
        except Exception as e:
            logger.error(f"Error processing query with documents: {str(e)}")
            return {
                'answer': f'Error processing CSV documents: {str(e)}',
                'status': 'error',
                'sources': [],
                'metadata': {'agent': 'csv', 'status': 'error', 'error': str(e)}
            } 