"""
Excel DB SQLAlchemy Module

This module provides database operations for Excel/CSV data using SQLAlchemy.
Adapted from Himalaya Azure system for the Enhanced Himalaya backend.
"""

import os
import logging
import pandas as pd
from typing import Dict, List, Any, Optional
from sqlalchemy import create_engine, text
from sqlalchemy.exc import SQLAlchemyError
import tempfile

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ExcelDBSQLAlchemy:
    """
    A class to provide database operations for Excel/CSV data using SQLAlchemy.
    """

    def __init__(self, database_url: str = None):
        """
        Initialize the Excel DB SQLAlchemy.

        Args:
            database_url: Database connection URL (defaults to SQLite in-memory)
        """
        # Use SQLite in-memory database if no URL provided
        self.database_url = database_url or "sqlite:///:memory:"
        
        try:
            self.engine = create_engine(self.database_url)
            logger.info(f"Database engine created with URL: {self.database_url}")
        except Exception as e:
            logger.error(f"Failed to create database engine: {str(e)}")
            self.engine = None

    def load_dataframe_to_db(self, df: pd.DataFrame, table_name: str) -> bool:
        """
        Load a DataFrame into the database as a table.
        
        Args:
            df: DataFrame to load
            table_name: Name of the table to create
            
        Returns:
            True if successful, False otherwise
        """
        if not self.engine:
            logger.error("Database engine not initialized")
            return False
            
        try:
            # Clean the table name to be SQL-safe
            clean_table_name = table_name.replace(' ', '_').replace('-', '_')
            clean_table_name = ''.join(c for c in clean_table_name if c.isalnum() or c == '_')
            
            # Load DataFrame to database
            df.to_sql(clean_table_name, self.engine, if_exists='replace', index=False)
            logger.info(f"Loaded DataFrame with {len(df)} rows to table '{clean_table_name}'")
            return True
            
        except Exception as e:
            logger.error(f"Error loading DataFrame to database: {str(e)}")
            return False

    def execute_query(self, query: str) -> Optional[pd.DataFrame]:
        """
        Execute a SQL query and return results as DataFrame.
        
        Args:
            query: SQL query to execute
            
        Returns:
            DataFrame with query results or None if failed
        """
        if not self.engine:
            logger.error("Database engine not initialized")
            return None
            
        try:
            # Execute query and return as DataFrame
            result_df = pd.read_sql_query(query, self.engine)
            logger.info(f"Query executed successfully, returned {len(result_df)} rows")
            return result_df
            
        except Exception as e:
            logger.error(f"Error executing query: {str(e)}")
            return None

    def get_table_info(self, table_name: str) -> Optional[Dict[str, Any]]:
        """
        Get information about a table.
        
        Args:
            table_name: Name of the table
            
        Returns:
            Dictionary with table information or None if failed
        """
        if not self.engine:
            logger.error("Database engine not initialized")
            return None
            
        try:
            # Get table schema information
            with self.engine.connect() as conn:
                # Get column information
                if 'sqlite' in self.database_url.lower():
                    schema_query = f"PRAGMA table_info({table_name})"
                    schema_result = conn.execute(text(schema_query))
                    columns = [row[1] for row in schema_result]  # Column names are in index 1
                else:
                    # For other databases, use INFORMATION_SCHEMA
                    schema_query = f"""
                    SELECT COLUMN_NAME 
                    FROM INFORMATION_SCHEMA.COLUMNS 
                    WHERE TABLE_NAME = '{table_name}'
                    """
                    schema_result = conn.execute(text(schema_query))
                    columns = [row[0] for row in schema_result]
                
                # Get row count
                count_query = f"SELECT COUNT(*) FROM {table_name}"
                count_result = conn.execute(text(count_query))
                row_count = count_result.scalar()
                
                # Get sample data
                sample_query = f"SELECT * FROM {table_name} LIMIT 5"
                sample_df = pd.read_sql_query(sample_query, conn)
                
                return {
                    'table_name': table_name,
                    'columns': columns,
                    'row_count': row_count,
                    'sample_data': sample_df.to_dict('records')
                }
                
        except Exception as e:
            logger.error(f"Error getting table info: {str(e)}")
            return None

    def list_tables(self) -> List[str]:
        """
        List all tables in the database.
        
        Returns:
            List of table names
        """
        if not self.engine:
            logger.error("Database engine not initialized")
            return []
            
        try:
            with self.engine.connect() as conn:
                if 'sqlite' in self.database_url.lower():
                    query = "SELECT name FROM sqlite_master WHERE type='table'"
                else:
                    query = "SELECT table_name FROM information_schema.tables"
                
                result = conn.execute(text(query))
                tables = [row[0] for row in result]
                
                logger.info(f"Found {len(tables)} tables in database")
                return tables
                
        except Exception as e:
            logger.error(f"Error listing tables: {str(e)}")
            return []

    def analyze_data(self, table_name: str, query: str) -> Dict[str, Any]:
        """
        Analyze data in a table based on a natural language query.
        
        Args:
            table_name: Name of the table to analyze
            query: Natural language query describing the analysis needed
            
        Returns:
            Dictionary with analysis results
        """
        try:
            # Get table information
            table_info = self.get_table_info(table_name)
            if not table_info:
                return {
                    'error': f'Table {table_name} not found or inaccessible',
                    'success': False
                }
            
            # For now, return basic table information
            # This could be enhanced with AI-powered SQL generation
            return {
                'table_info': table_info,
                'query': query,
                'analysis': f'Basic analysis for table {table_name}',
                'success': True
            }
            
        except Exception as e:
            logger.error(f"Error analyzing data: {str(e)}")
            return {
                'error': str(e),
                'success': False
            }

    def close(self):
        """
        Close the database connection.
        """
        if self.engine:
            self.engine.dispose()
            logger.info("Database connection closed")

    def __enter__(self):
        """Context manager entry."""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit."""
        self.close() 