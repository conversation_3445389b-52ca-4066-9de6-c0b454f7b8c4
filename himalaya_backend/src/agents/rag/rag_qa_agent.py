"""
RAG QA Agent for the Enhanced Himalaya system.

This agent evaluates RAG responses and determines if they need to process additional CSV files.
Adapted from Himalaya Azure system.
"""

import os
import json
import logging
from typing import Dict, List, Any, TypedDict, Annotated, Optional, Union
import operator
import datetime

from langchain_core.messages import AnyMessage, SystemMessage, HumanMessage
from langchain_openai import AzureChatOpenAI
from langgraph.graph import StateGraph, END

# Import configuration
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
from config.settings import (
    AZURE_OPENAI_KEY, AZURE_OPENAI_API_VERSION, AZURE_OPENAI_ENDPOINT, 
    AZURE_OPENAI_DEPLOYMENT_NAME
)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class RAGQAState(TypedDict):
    """State for the RAG QA agent"""
    messages: Annotated[List[AnyMessage], operator.add]
    query: str
    answer: str
    processed_csv_id: str
    processed_csv_path: str
    other_relevant_csvs: List[Dict[str, Any]]
    evaluation: Dict[str, Any]
    needs_additional_csv: bool
    next_csv_to_process: Optional[Dict[str, Any]]
    improvement_instructions: Optional[str]

class RAGQAAgent:
    """
    RAG QA agent that evaluates responses from CSV analysis and determines if additional CSV files need to be processed.
    """

    def __init__(self, model=None, system_prompt=None, verbose=True):
        """
        Initialize the RAG QA agent.

        Args:
            model: LLM to use for evaluation
            system_prompt: System prompt for the QA agent
            verbose: Whether to print verbose output
        """
        self.verbose = verbose
        self.today_date = datetime.datetime.now().strftime("%Y-%m-%d")

        # Initialize the model
        if model:
            self.model = model
        else:
            self.model = AzureChatOpenAI(
                azure_deployment=AZURE_OPENAI_DEPLOYMENT_NAME,
                openai_api_version=AZURE_OPENAI_API_VERSION,
                azure_endpoint=AZURE_OPENAI_ENDPOINT,
                openai_api_key=AZURE_OPENAI_KEY,
                temperature=0.1
            )
            if self.verbose:
                print(f"Using Azure OpenAI with deployment {AZURE_OPENAI_DEPLOYMENT_NAME}")

        # Set the system prompt
        self.system_prompt = system_prompt or self._get_default_system_prompt()

        # Create the graph
        graph = StateGraph(RAGQAState)

        # Add nodes
        graph.add_node("evaluate_answer", self.evaluate_answer)
        graph.add_node("determine_next_csv", self.determine_next_csv)

        # Add edges
        graph.add_edge("evaluate_answer", "determine_next_csv")
        graph.add_edge("determine_next_csv", END)

        # Set entry point
        graph.set_entry_point("evaluate_answer")

        # Compile the graph
        self.graph = graph.compile()

    def _get_default_system_prompt(self) -> str:
        """
        Get the default system prompt for the RAG QA agent.

        Returns:
            The default system prompt
        """
        return f"""You are a RAG (Retrieval-Augmented Generation) Quality Assurance agent that evaluates answers generated from CSV data analysis.

Your task is to:
1. Evaluate if the answer COMPLETELY addresses the USER'S SPECIFIC QUERY - nothing more, nothing less
2. Determine if additional CSV files should be processed ONLY if the current answer doesn't fully address the specific query
3. Provide specific instructions on what information to look for in the next CSV file ONLY as it relates to the user's query

EVALUATION GUIDELINES:
- STRICTLY focus on whether the answer addresses what was EXPLICITLY asked in the query
- DO NOT suggest looking for additional information that wasn't specifically requested in the query
- DO NOT recommend exploring tangential questions or analyses beyond the explicit query
- For date-based queries, verify that the date formats and ranges were correctly interpreted
- Today's date is {self.today_date}, which may be important for time-sensitive analysis

COMPLETENESS RATING GUIDELINES:
- 5: The answer fully addresses all aspects of the specific query with no missing information
- 4: The answer addresses most aspects of the query with minor details missing
- 3: The answer addresses the core question but is missing significant details explicitly asked for
- 2: The answer partially addresses the query but misses major components
- 1: The answer fails to address the main components of the query

IMPORTANT: Your response must be a valid JSON object with the following structure:
{{
    "evaluation": {{
        "completeness": 1-5 (1=very incomplete, 5=very complete),
        "relevance": 1-5 (1=not relevant, 5=highly relevant),
        "data_quality": 1-5 (1=poor data quality, 5=excellent data quality)
    }},
    "missing_information": ["list of SPECIFIC missing information EXPLICITLY requested in the query"],
    "needs_additional_csv": true/false,
    "improvement_instructions": "specific instructions for what to look for in the next CSV file to answer ONLY what was asked in the query"
}}

If needs_additional_csv is true, your improvement_instructions MUST ONLY focus on finding information to answer what was EXPLICITLY asked in the query.
"""

    def evaluate_answer(self, state: RAGQAState) -> RAGQAState:
        """
        Evaluate the answer to determine if it adequately addresses the query.

        Args:
            state: The current state

        Returns:
            Updated state with evaluation
        """
        if self.verbose:
            print("\n=== Starting RAG evaluate_answer node ===")
            print(f"Query: {state['query']}")
            print(f"Processed CSV: {state['processed_csv_id']}")

        # Format other relevant CSVs for evaluation
        other_csvs_text = ""
        for i, csv_info in enumerate(state['other_relevant_csvs']):
            csv_id = csv_info.get('csv_id', 'Unknown')
            relevance_score = csv_info.get('relevance_score', 0)
            other_csvs_text += f"{i+1}. {csv_id} (Relevance: {relevance_score:.2f})\n"

        # Create messages for evaluation
        messages = [
            SystemMessage(content=self.system_prompt),
            HumanMessage(content=f"""
            Query: {state['query']}

            CSV File Processed: {state['processed_csv_id']}
            CSV Path: {state['processed_csv_path']}

            Answer: {state['answer']}

            Other Available CSV Files:
            {other_csvs_text if other_csvs_text else "None"}

            Evaluate this answer and determine if additional CSV files should be processed to enhance it.
            """)
        ]

        # Invoke the model
        response = self.model.invoke(messages)

        if self.verbose:
            print("LLM response:")
            print(response.content)

        try:
            # Extract JSON from response
            content = response.content
            if "```json" in content:
                content = content.split("```json")[1].split("```")[0].strip()
            elif "```" in content:
                content = content.split("```")[1].strip()

            evaluation = json.loads(content)

            if self.verbose:
                print(f"Parsed evaluation: {evaluation}")

        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse evaluation JSON: {str(e)}")
            logger.error(f"Raw response: {response.content}")

            # SAFETY FIX: Fail safe when evaluation parsing fails
            evaluation = {
                "evaluation": {
                    "completeness": 1,  # FAIL SAFE
                    "relevance": 1,
                    "data_quality": 1
                },
                "missing_information": ["Unable to evaluate answer quality due to system error"],
                "needs_additional_csv": True,  # REQUEST MORE DATA FOR SAFETY
                "improvement_instructions": "Evaluation system failed. Please provide a new answer that explicitly states what information is available in the CSV data, or acknowledge if the data doesn't contain sufficient information to answer the query."
            }

        # Add evaluation messages to the state
        messages = state["messages"] + [
            self.model.invoke([
                SystemMessage(content=self.system_prompt),
                HumanMessage(content=f"I've evaluated the answer with the following results: {evaluation}")
            ])
        ]

        return {
            **state,
            "messages": messages,
            "evaluation": evaluation.get("evaluation", {}),
            "needs_additional_csv": evaluation.get("needs_additional_csv", False),
            "improvement_instructions": evaluation.get("improvement_instructions", "")
        }

    def determine_next_csv(self, state: RAGQAState) -> RAGQAState:
        """
        Determine which CSV file to process next if additional processing is needed.

        Args:
            state: The current state

        Returns:
            Updated state with next CSV selection
        """
        if self.verbose:
            print("\n=== Starting RAG determine_next_csv node ===")

        next_csv_to_process = None

        if state["needs_additional_csv"] and state["other_relevant_csvs"]:
            # Select the most relevant remaining CSV
            next_csv_to_process = state["other_relevant_csvs"][0]
            
            if self.verbose:
                csv_id = next_csv_to_process.get('csv_id', 'Unknown')
                relevance_score = next_csv_to_process.get('relevance_score', 0)
                print(f"Selected next CSV: {csv_id} (Relevance: {relevance_score:.2f})")

        return {
            **state,
            "next_csv_to_process": next_csv_to_process
        }

    def process_answer(self, query: str, answer: str, processed_csv_id: str, processed_csv_path: str, other_relevant_csvs: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Process and evaluate an answer from CSV analysis.

        Args:
            query: The user query
            answer: The generated answer
            processed_csv_id: ID of the processed CSV file
            processed_csv_path: Path to the processed CSV file
            other_relevant_csvs: List of other relevant CSV files

        Returns:
            Evaluation result
        """
        if self.verbose:
            print(f"\n=== RAG QA Processing Answer ===")
            print(f"Query: {query}")
            print(f"Processed CSV: {processed_csv_id}")
            print(f"Other CSVs available: {len(other_relevant_csvs)}")

        # Initialize state
        initial_state = {
            "messages": [],
            "query": query,
            "answer": answer,
            "processed_csv_id": processed_csv_id,
            "processed_csv_path": processed_csv_path,
            "other_relevant_csvs": other_relevant_csvs,
            "evaluation": {},
            "needs_additional_csv": False,
            "next_csv_to_process": None,
            "improvement_instructions": ""
        }

        # Run the graph
        result = self.graph.invoke(initial_state)

        return {
            "evaluation": result.get("evaluation", {}),
            "needs_additional_csv": result.get("needs_additional_csv", False),
            "next_csv_to_process": result.get("next_csv_to_process"),
            "improvement_instructions": result.get("improvement_instructions", ""),
            "missing_information": result.get("evaluation", {}).get("missing_information", [])
        }

def create_rag_qa_agent(model=None, system_prompt=None, verbose=True) -> RAGQAAgent:
    """
    Create a RAG QA agent.

    Args:
        model: LLM model to use
        system_prompt: System prompt for the agent
        verbose: Whether to print verbose output

    Returns:
        RAGQAAgent instance
    """
    return RAGQAAgent(
        model=model,
        system_prompt=system_prompt,
        verbose=verbose
    ) 