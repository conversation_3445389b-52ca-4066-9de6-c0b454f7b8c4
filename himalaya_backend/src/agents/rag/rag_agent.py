"""
RAG Agent for the Himalaya Backend system.

This agent handles retrieval-augmented generation for all document types with internal vector search.
"""

import os
import json
import logging
from typing import Dict, List, Any, TypedDict, Annotated, Optional, Union
import operator
import datetime

from langchain_core.messages import AnyMessage, SystemMessage, HumanMessage
from langchain_openai import Chat<PERSON>penA<PERSON>, AzureChatOpenAI
from langgraph.graph import StateGraph, END

# Import configuration
import sys
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
from config.settings import (
    AZURE_SEARCH_SERVICE_ENDPOINT, AZURE_SEARCH_ENHANCED_INDEX_NAME, AZURE_SEARCH_ADMIN_KEY,
    AZURE_OPENAI_KEY, AZURE_OPENAI_ENDPOINT, AZURE_OPENAI_API_VERSION,
    AZURE_OPENAI_DEPLOYMENT_NAME, AZURE_OPENAI_EMBEDDING_DEPLOYMENT,
    VECTOR_SEARCH_TOP_K, MIN_VECTOR_SCORE_THRESHOLD, VECTOR_SEARCH_RERANK_TOP_K,
    DEEP_VECTOR_SEARCH_TOP_K, DEEP_MIN_VECTOR_SCORE_THRESHOLD, DEEP_VECTOR_SEARCH_RERANK_TOP_K
)

# Import Azure Search
from azure.search.documents import SearchClient
from azure.core.credentials import AzureKeyCredential
from openai import AzureOpenAI

# Import per-file re-ranking service
from services.per_file_reranking_service import create_per_file_reranking_service

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class RAGState(TypedDict):
    """State for the RAG agent"""
    messages: Annotated[List[AnyMessage], operator.add]
    query: str
    standalone_query: str
    blob_names: Optional[List[str]]
    deep_search: Optional[bool]
    search_results: Optional[List[Dict[str, Any]]]
    csv_documents: Optional[List[Dict[str, Any]]]
    text_documents: Optional[List[Dict[str, Any]]]
    answer: Optional[str]
    references: Optional[List[str]]

class RAGAgent:
    """
    RAG Agent that handles retrieval-augmented generation with internal vector search.
    """

    def __init__(self, model=None, system_prompt=None, verbose=True):
        """
        Initialize the RAG agent.

        Args:
            model: LLM model to use
            system_prompt: System prompt for the agent
            verbose: Whether to print verbose output
        """
        self.verbose = verbose

        # Initialize the LLM
        if model:
            self.llm = model
        else:
            self.llm = AzureChatOpenAI(
                azure_deployment=AZURE_OPENAI_DEPLOYMENT_NAME,
                openai_api_version=AZURE_OPENAI_API_VERSION,
                azure_endpoint=AZURE_OPENAI_ENDPOINT,
                openai_api_key=AZURE_OPENAI_KEY,
                temperature=0.1
            )

        # Initialize Azure OpenAI client for embeddings
        self.openai_client = AzureOpenAI(
            api_key=AZURE_OPENAI_KEY,
            api_version=AZURE_OPENAI_API_VERSION,
            azure_endpoint=AZURE_OPENAI_ENDPOINT
        )

        # Initialize Azure Search client
        self.search_client = SearchClient(
            endpoint=AZURE_SEARCH_SERVICE_ENDPOINT,
            index_name=AZURE_SEARCH_ENHANCED_INDEX_NAME,
            credential=AzureKeyCredential(AZURE_SEARCH_ADMIN_KEY)
        )

        # Initialize per-file re-ranking service for deep search
        self.per_file_reranking_service = create_per_file_reranking_service(
            search_client=self.search_client,
            openai_client=self.openai_client,
            embedding_deployment=AZURE_OPENAI_EMBEDDING_DEPLOYMENT,
            verbose=verbose
        )

        # Set the system prompt
        self.system_prompt = system_prompt or self._get_default_system_prompt()

        # Create the graph
        graph = StateGraph(RAGState)

        # Add nodes
        graph.add_node("vector_search", self.vector_search)
        graph.add_node("classify_documents", self.classify_documents)

        # Add edges
        graph.add_edge("vector_search", "classify_documents")
        graph.add_edge("classify_documents", END)

        # Set entry point
        graph.set_entry_point("vector_search")

        # Compile the graph
        self.graph = graph.compile()

    def _get_default_system_prompt(self) -> str:
        """
        Get the default system prompt for the agent.

        Returns:
            Default system prompt
        """
        return """You are a RAG (Retrieval-Augmented Generation) Agent that performs intelligent document retrieval.
Your job is to:
1. Perform vector search to retrieve relevant documents
2. Classify documents as text documents or CSV/tabular documents
3. Return the classified documents for further processing by specialized agents

You handle the complete retrieval pipeline internally, including:
- Creating embeddings for the query
- Performing vector search with appropriate filters
- Analyzing and classifying the retrieved documents
- Preparing documents for downstream processing

Always provide clear information about the retrieved documents and their classification.
"""

    def vector_search(self, state: RAGState) -> RAGState:
        """
        Perform vector search to retrieve relevant documents.

        Args:
            state: The current state

        Returns:
            Updated state with search results
        """
        if self.verbose:
            print("\n=== RAG AGENT: Starting vector_search node ===")
            print(f"Query: {state['query']}")
            print(f"Standalone query: {state['standalone_query']}")

        query = state['standalone_query']
        blob_names = state.get('blob_names', [])
        deep_search = state.get('deep_search', False)
        adaptive_k = state.get('adaptive_k', None)  # Get adaptive k-value from state
        
        # Add system message
        messages = [
            SystemMessage(content=self.system_prompt),
            HumanMessage(content=f"Performing {'deep' if deep_search else 'standard'} vector search for query: {query}")
        ]
        try:
            # Standard vector search logic
            print(f"🔍 RAG AGENT: Using standard vector search...")
            print(f"   Standard search budget: {VECTOR_SEARCH_TOP_K} → {VECTOR_SEARCH_RERANK_TOP_K}")
            
            print(f"🔍 RAG AGENT: Creating embeddings for vector search...")
            # Create embeddings
            response = self.openai_client.embeddings.create(
                input=query,
                model=AZURE_OPENAI_EMBEDDING_DEPLOYMENT
            )
            vector = response.data[0].embedding
            print(f"✅ RAG AGENT: Embeddings created successfully")

            # Create vector query
            vector_query = {
                "vector": vector,
                "fields": "text_vector",
                "k": VECTOR_SEARCH_TOP_K,
                "kind": "vector"
            }

            # Create filter for blob names if provided
            filter_string = None
            if blob_names:
                blob_filters = [f"metadata_storage_name eq '{blob_name}'" for blob_name in blob_names]
                filter_string = " or ".join(blob_filters)
                print(f"🔍 RAG AGENT: Applying blob name filter for {len(blob_names)} files")
            else:
                print(f"🔍 RAG AGENT: No blob name filter applied")

            print(f"🔍 RAG AGENT: Executing vector search (top-k: {VECTOR_SEARCH_TOP_K})...")
            
            # Perform the search
            results = self.search_client.search(
                search_text=None,
                vector_queries=[vector_query],
                filter=filter_string,
                select=["chunk", "chunk_id", "metadata_storage_name", "title"],
                top=VECTOR_SEARCH_TOP_K
            )
            
            # Process and filter results
            search_results = []
            result_count = 0
            filtered_count = 0
            
            print(f"🔍 RAG AGENT: Processing search results with score threshold: {MIN_VECTOR_SCORE_THRESHOLD}")
            
            for result in results:
                result_count += 1
                search_score = result.get("@search.score", 0)
                
                # Apply score threshold filtering
                if search_score < MIN_VECTOR_SCORE_THRESHOLD:
                    if result_count <= 3:  # Log first few filtered results
                        print(f"🔍 RAG AGENT: Result {result_count} FILTERED (score: {search_score:.4f} < {MIN_VECTOR_SCORE_THRESHOLD})")
                    filtered_count += 1
                    continue
                
                # Log first few results in detail
                if len(search_results) <= 3:
                    print(f"🔍 RAG AGENT: Result {len(search_results)+1} ACCEPTED:")
                    print(f"   Chunk ID: {result['chunk_id']}")
                    print(f"   Metadata storage name: {result['metadata_storage_name']}")
                    print(f"   Title: {result.get('title', 'N/A')}")
                    print(f"   Content preview: {result['chunk'][:100]}...")
                    print(f"   Search score: {search_score:.4f}")
                
                search_results.append({
                    "content": result["chunk"],
                    "score": search_score,
                    "metadata": {
                        "chunk_id": result["chunk_id"],
                        "title": result.get("title", ""),
                        "metadata_storage_name": result.get("metadata_storage_name", ""),
                        "search_score": search_score
                    }
                })
            
            # Apply re-ranking if we have more results than needed
            if len(search_results) > VECTOR_SEARCH_RERANK_TOP_K:
                search_results.sort(key=lambda x: x['metadata']['search_score'], reverse=True)
                search_results = search_results[:VECTOR_SEARCH_RERANK_TOP_K]
                print(f"🔍 RAG AGENT: Re-ranked to top {VECTOR_SEARCH_RERANK_TOP_K} results")
            
            print(f"🔍 RAG AGENT: Total results processed: {result_count}")
            print(f"🔍 RAG AGENT: Results filtered by score: {filtered_count}")
            print(f"🔍 RAG AGENT: Final results returned: {len(search_results)}")
            
            if len(search_results) > 0:
                avg_score = sum(r['metadata']['search_score'] for r in search_results) / len(search_results)
                max_score = max(r['metadata']['search_score'] for r in search_results)
                min_score = min(r['metadata']['search_score'] for r in search_results)
                print(f"🔍 RAG AGENT: Score statistics - Min: {min_score:.4f}, Max: {max_score:.4f}, Avg: {avg_score:.4f}")
            
            # Add success message
            messages.append(
                HumanMessage(content=f"Successfully retrieved {len(search_results)} relevant documents")
            )
            
            return {
                **state,
                "messages": messages,
                "search_results": search_results
            }
            
        except Exception as e:
            logger.error(f"Error performing vector search: {str(e)}")
            print(f"❌ RAG AGENT: Vector search failed: {str(e)}")
            
            # Add error message
            messages.append(
                HumanMessage(content=f"Error performing vector search: {str(e)}")
            )
            
            return {
                **state,
                "messages": messages,
                "search_results": []
            }

    def classify_documents(self, state: RAGState) -> RAGState:
        """
        Classify retrieved documents as text or CSV/tabular.

        Args:
            state: The current state

        Returns:
            Updated state with classified documents
        """
        if self.verbose:
            print("\n=== RAG AGENT: Starting classify_documents node ===")

        search_results = state.get("search_results", [])
        
        if not search_results:
            messages = state["messages"] + [
                HumanMessage(content="No documents to classify - search returned no results")
            ]
            
            return {
                **state,
                "messages": messages,
                "csv_documents": [],
                "text_documents": [],
                "references": []
            }
        
        # Classify documents by content analysis
        csv_documents = []
        text_documents = []
        references = []
        
        print(f"🔍 RAG AGENT: Analyzing {len(search_results)} search results for document classification...")
        
        for result in search_results:
            chunk_id = result['metadata'].get('chunk_id', '')
            storage_name = result['metadata'].get('metadata_storage_name', '')
            content = result.get('content', '')
            
            # Extract file_id from chunk_id to get file info
            is_tabular_content = False
            try:
                chunk_parts = chunk_id.split('_')
                if len(chunk_parts) >= 2 and chunk_parts[0] == 'chunk':
                    file_id = int(chunk_parts[1])
                    
                    # Import File model here to avoid circular imports
                    from models.models import File
                    file = File.query.get(file_id)
                    
                    if file:
                        file_name = file.file_name.lower()
                        
                        # Check if it's a CSV or Excel file
                        if file_name.endswith(('.csv', '.xlsx', '.xls')):
                            is_tabular_content = True
                            print(f"📊 RAG AGENT: Found CSV/Excel file: {file.file_name} (ID: {file_id})")
                            csv_documents.append({
                                'file_id': file_id,
                                'file_name': file.file_name,
                                'blob_url': file.blob_url,
                                'search_result': result,
                                'score': result.get('score', 0),
                                'content': content,
                                'metadata': result['metadata'],
                                'is_extracted_table': False,
                                'source_type': 'csv_excel'
                            })
                            # Use actual chunk_id instead of synthetic csv_file_ reference
                            references.append(chunk_id)
                        # Check if this is a PDF table (content contains table indicators)
                        elif (any(indicator in chunk_id.lower() for indicator in ['table_']) or
                              any(indicator in content.lower() for indicator in [
                                  'sheet:', 'dimensions:', 'columns:', 'rows ×', 'table', 
                                  'sex ratio', 'birth', 'demographic', 'population'
                              ]) or
                              any(indicator in storage_name.lower() for indicator in [
                                  'table_', '.csv', '.xlsx'
                              ])):
                            is_tabular_content = True
                            print(f"📊 RAG AGENT: Found PDF table content: {file.file_name} (ID: {file_id})")
                            csv_documents.append({
                                'file_id': file_id,
                                'file_name': file.file_name,
                                'blob_url': file.blob_url,
                                'search_result': result,
                                'score': result.get('score', 0),
                                'content': content,
                                'metadata': result['metadata'],
                                'is_extracted_table': True,
                                'source_type': 'pdf_table'
                            })
                            references.append(f"csv_table_{file_id}")
                        else:
                            text_documents.append(result)
                            references.append(chunk_id)
                    else:
                        text_documents.append(result)
                        references.append(chunk_id)
                else:
                    text_documents.append(result)
                    references.append(chunk_id)
            except (ValueError, IndexError, ImportError):
                text_documents.append(result)
                references.append(chunk_id)
        
        print(f"🔍 RAG AGENT: Document classification completed:")
        print(f"   📊 Tabular documents (CSV/Excel + PDF tables): {len(csv_documents)}")
        print(f"   📄 Text documents: {len(text_documents)}")
        
        # Add classification message
        messages = state["messages"] + [
            HumanMessage(content=f"Classified documents: {len(csv_documents)} CSV/tabular documents and {len(text_documents)} text documents")
        ]
        
        return {
            **state,
            "messages": messages,
            "csv_documents": csv_documents,
            "text_documents": text_documents,
            "references": references
        }

    def process_query(self, query: str, standalone_query: str, blob_names: Optional[List[str]] = None, deep_search: bool = False, k: Optional[int] = None) -> Dict[str, Any]:
        """
        Process a query using the RAG agent.

        Args:
            query: Original user query
            standalone_query: Standalone version of the query
            blob_names: Optional list of blob names to filter search
            deep_search: Whether to use per-file re-ranking strategy
            k: Adaptive k-value for search (overrides default settings)

        Returns:
            Processing results
        """
        if self.verbose:
            print(f"\n🤖 RAG AGENT: Processing query with agentic retrieval")
            print(f"   Query: {query}")
            print(f"   Standalone Query: {standalone_query}")
            print(f"   Blob Names Filter: {len(blob_names) if blob_names else 0} files")
            print(f"   Deep Search: {'Enabled' if deep_search else 'Disabled'}")

        # Invoke the graph
        result = self.graph.invoke({
            "messages": [],
            "query": query,
            "standalone_query": standalone_query,
            "blob_names": blob_names or [],
            "deep_search": deep_search,
            "adaptive_k": k,  # Pass adaptive k-value through state
            "search_results": None,
            "csv_documents": None,
            "text_documents": None,
            "references": None
        })
        
        # Return the result
        return {
            "csv_documents": result.get("csv_documents", []),
            "text_documents": result.get("text_documents", []),
            "references": result.get("references", []),
            "search_results": result.get("search_results", []),
            "messages": [msg.content if hasattr(msg, 'content') else str(msg) for msg in result.get("messages", [])]
        }

def create_rag_agent(model=None, system_prompt=None, verbose=True):
    """
    Create a RAG agent.

    Args:
        model: LLM model to use
        system_prompt: System prompt for the agent
        verbose: Whether to print verbose output

    Returns:
        RAG agent
    """
    return RAGAgent(
        model=model,
        system_prompt=system_prompt,
        verbose=verbose
    ) 