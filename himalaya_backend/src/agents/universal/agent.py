"""
Universal Agent for the Himalaya system.

This agent combines the capabilities of multiple specialized agents (RAG, web search, GPT)
to provide comprehensive answers by orchestrating their execution and synthesizing results.
"""

import os
import json
import sys
from typing import Dict, List, Any, TypedDict, Annotated, Optional, Union
import operator
import datetime
import asyncio
from concurrent.futures import Thr<PERSON>PoolExecutor

from langchain_core.messages import AnyMessage, SystemMessage, HumanMessage
from langchain_openai import ChatOpenAI, AzureChatOpenAI
from langgraph.graph import StateGraph, END

# Import configuration
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
from config.settings import AZURE_OPENAI_KEY, AZURE_OPENAI_API_VERSION, AZURE_OPENAI_ENDPOINT, AZURE_OPENAI_DEPLOYMENT_NAME

# Import specialized agents
from agents.rag import create_rag_agent, create_rag_planner_agent
from agents.web import create_web_search_agent
from agents.gpt import create_gpt_agent
from agents.qa import create_qa_agent

class UniversalState(TypedDict):
    """State for the Universal agent"""
    messages: Annotated[List[AnyMessage], operator.add]
    query: str
    standalone_query: str
    conversation_history: Optional[str]
    conversation_summary: Optional[str]
    selected_file_ids: Optional[List[int]]
    blob_names: Optional[List[str]]
    query_type: Optional[str]
    rag_result: Optional[Dict[str, Any]]
    web_search_result: Optional[Dict[str, Any]]
    gpt_result: Optional[Dict[str, Any]]
    combined_result: Optional[Dict[str, Any]]
    final_answer: str
    references: Dict[str, Any]
    execution_plan: Dict[str, Any]
    agent_scores: Dict[str, float]
    
class UniversalAgent:
    """
    Universal agent that combines the capabilities of multiple specialized agents
    to provide comprehensive answers.
    """

    def __init__(self, model=None, system_prompt=None, qa_system_prompt=None, verbose=True):
        """
        Initialize the Universal agent.

        Args:
            model: LLM model to use
            system_prompt: System prompt for the agent
            qa_system_prompt: System prompt for the QA component
            verbose: Whether to print verbose output
        """
        self.verbose = verbose

        # Initialize the LLM
        if model:
            self.llm = model
        else:
            self.llm = AzureChatOpenAI(
                azure_deployment=AZURE_OPENAI_DEPLOYMENT_NAME,
                api_version=AZURE_OPENAI_API_VERSION,
                azure_endpoint=AZURE_OPENAI_ENDPOINT,
                api_key=AZURE_OPENAI_KEY,
                temperature=0.1
            )

        # Set the system prompts
        self.system_prompt = system_prompt or self._get_default_system_prompt()
        self.qa_system_prompt = qa_system_prompt or self._get_default_qa_system_prompt()

        # Initialize specialized agents (lazy loading)
        self._rag_agent = None
        self._rag_planner_agent = None
        self._web_search_agent = None
        self._gpt_agent = None
        self._qa_agent = None

        # Create the graph
        graph = StateGraph(UniversalState)

        # Add nodes
        graph.add_node("plan_execution", self.plan_execution)
        graph.add_node("execute_agents", self.execute_agents)
        graph.add_node("synthesize_results", self.synthesize_results)
        graph.add_node("evaluate_answer", self.evaluate_answer)

        # Add edges
        graph.add_edge("plan_execution", "execute_agents")
        graph.add_edge("execute_agents", "synthesize_results")
        graph.add_edge("synthesize_results", "evaluate_answer")
        graph.add_edge("evaluate_answer", END)

        # Set entry point
        graph.set_entry_point("plan_execution")

        # Compile the graph
        self.graph = graph.compile()

    def _get_default_system_prompt(self) -> str:
        """
        Get the default system prompt for the Universal agent.

        Returns:
            Default system prompt
        """
        return """You are a Universal Agent that plans and orchestrates the execution of multiple specialized agents to provide comprehensive answers.

Your task is to:
1. Analyze the user query to determine which specialized agents should be used
2. Create an execution plan for the specialized agents
3. Determine how to combine and synthesize results from multiple agents

AVAILABLE SPECIALIZED AGENTS:

1. RAG Agent:
   - Uses internal documents and knowledge base
   - Best for company-specific information, internal data, and documents
   - Good for specific details about products, policies, and proprietary information

2. Web Search Agent:
   - Searches the internet for up-to-date information
   - Best for current events, public information, and general knowledge
   - Good for time-sensitive information and broad topics

3. GPT Agent:
   - Uses the LLM's pre-trained knowledge
   - Best for general knowledge, concepts, and explanations
   - Good for common facts, definitions, and non-time-sensitive information

When planning:
- Consider which agents will provide the most relevant and accurate information
- Assign confidence scores to each agent based on the query type
- For complex queries, consider using multiple agents and combining their results
- For simple queries, use the most appropriate single agent

Your response must be a valid JSON object with the following structure:
{
    "analysis": "brief explanation of the query and required information",
    "execution_plan": {
        "use_rag": true/false,
        "use_web_search": true/false,
        "use_gpt": true/false,
        "execution_order": ["agent1", "agent2", "agent3"],
        "confidence_scores": {
            "rag": 0-1,
            "web_search": 0-1,
            "gpt": 0-1
        }
    },
    "reasoning": "explanation of why these agents were selected and how they should be used"
}
"""

    def _get_default_qa_system_prompt(self) -> str:
        """
        Get the default system prompt for the QA component.

        Returns:
            Default QA system prompt
        """
        today_date = datetime.datetime.now().strftime("%Y-%m-%d")
        return f"""You are a Quality Assurance agent that evaluates answers generated by the Universal Agent.

Your task is to:
1. Evaluate if the answer adequately addresses the user's query
2. Identify any inconsistencies between results from different agents
3. Check for hallucinations or unsupported claims
4. Determine if the answer needs improvement

HALLUCINATION DETECTION:
- Verify that claims in the answer are supported by the provided sources
- If different agents provide conflicting information, identify the discrepancies
- Check that the answer doesn't contain information that wasn't in any of the agent results

IMPORTANT GUIDELINES:
- Today's date is {today_date}
- Evaluate the answer based on how well it addresses the specific query
- Check if the answer appropriately combines information from multiple agents
- Verify that the answer correctly attributes information to the appropriate sources

Your response must be a valid JSON object with the following structure:
{{
    "evaluation": {{
        "completeness": 1-5 (1=very incomplete, 5=very complete),
        "relevance": 1-5 (1=not relevant, 5=highly relevant),
        "clarity": 1-5 (1=unclear, 5=very clear),
        "source_adherence": 1-5 (1=severe hallucinations, 5=perfectly sourced)
    }},
    "inconsistencies": ["list specific inconsistencies between agent results"],
    "needs_improvement": true/false,
    "improvement_instructions": "specific instructions for improvement (only if needs_improvement is true)"
}}
"""

    @property
    def rag_agent(self):
        """Lazy-loaded RAG agent"""
        if self._rag_agent is None:
            if self.verbose:
                print("🔍 UNIVERSAL AGENT: Initializing RAG agent")
            self._rag_agent = create_rag_agent(verbose=self.verbose)
        return self._rag_agent

    def refresh_rag_agent(self):
        """Force refresh the RAG agent to ensure it's using the latest version"""
        if self.verbose:
            print("🔄 UNIVERSAL AGENT: Refreshing RAG agent")
        self._rag_agent = None
        return self.rag_agent

    @property
    def rag_planner_agent(self):
        """Lazy-loaded RAG planner agent"""
        if self._rag_planner_agent is None:
            if self.verbose:
                print("🔍 UNIVERSAL AGENT: Initializing RAG Planner agent")
            self._rag_planner_agent = create_rag_planner_agent(verbose=self.verbose)
        return self._rag_planner_agent

    def refresh_rag_planner_agent(self):
        """Force refresh the RAG Planner agent to ensure it's using the latest version"""
        if self.verbose:
            print("🔄 UNIVERSAL AGENT: Refreshing RAG Planner agent")
        self._rag_planner_agent = None
        return self.rag_planner_agent

    @property
    def web_search_agent(self):
        """Lazy-loaded web search agent"""
        if self._web_search_agent is None:
            self._web_search_agent = create_web_search_agent(verbose=self.verbose)
        return self._web_search_agent

    @property
    def gpt_agent(self):
        """Lazy-loaded GPT agent"""
        if self._gpt_agent is None:
            self._gpt_agent = create_gpt_agent(verbose=self.verbose)
        return self._gpt_agent

    @property
    def qa_agent(self):
        """Lazy-loaded QA agent"""
        if self._qa_agent is None:
            self._qa_agent = create_qa_agent(verbose=self.verbose)
        return self._qa_agent

    def plan_execution(self, state: UniversalState) -> UniversalState:
        """
        Plan the execution of specialized agents based on the query.

        Args:
            state: The current state

        Returns:
            Updated state with execution plan
        """
        if self.verbose:
            print("\n=== Starting plan_execution node ===")
            print(f"Query: {state['query']}")
            print(f"Standalone query: {state['standalone_query']}")

        # Use the standalone query that's already provided by the main planner
        standalone_query = state['standalone_query']

        # Check if we have blob_names (attached files)
        has_attached_files = state.get('blob_names') and len(state.get('blob_names', [])) > 0
        if has_attached_files and self.verbose:
            print(f"Files attached: {len(state.get('blob_names', []))} files")
            print(f"File names: {state.get('blob_names', [])}")

        # Plan which agents to use
        messages = [
            SystemMessage(content=self.system_prompt),
            HumanMessage(content=f"""
            Query: {standalone_query}
            {f'Note: The user has attached {len(state.get("blob_names", []))} files to this query. These files should be analyzed using the RAG agent.' if has_attached_files else ''}
            
            Plan which specialized agents should be used to answer this query.
            """)
        ]
        
        response = self.llm.invoke(messages)
        
        try:
            # Extract JSON from response
            content = response.content
            if "```json" in content:
                content = content.split("```json")[1].split("```")[0].strip()
            elif "```" in content:
                content = content.split("```")[1].strip()
                
            execution_plan = json.loads(content)
            
            # If files are attached, ensure RAG is used
            if has_attached_files:
                execution_plan["execution_plan"]["use_rag"] = True
                
                # Ensure RAG has a high confidence score
                if "confidence_scores" in execution_plan["execution_plan"]:
                    execution_plan["execution_plan"]["confidence_scores"]["rag"] = max(
                        execution_plan["execution_plan"]["confidence_scores"].get("rag", 0),
                        0.9  # Minimum confidence score for RAG when files are attached
                    )
                
                # Add RAG to execution order if not already there
                if "execution_order" in execution_plan["execution_plan"]:
                    if "rag" not in execution_plan["execution_plan"]["execution_order"]:
                        execution_plan["execution_plan"]["execution_order"].insert(0, "rag")
                
                if self.verbose:
                    print("Modified execution plan to prioritize RAG for attached files")
            
            if self.verbose:
                print("Execution plan:", json.dumps(execution_plan, indent=2))
            
        except (json.JSONDecodeError, KeyError) as e:
            if self.verbose:
                print(f"Error parsing execution plan: {e}")

            # SAFETY FIX: Don't default to processing when planning fails
            execution_plan = {
                "analysis": "Unable to analyze query due to technical error",
                "execution_plan": {
                    "use_rag": False,
                    "use_web_search": False,
                    "use_gpt": False,
                    "execution_order": [],
                    "confidence_scores": {}
                },
                "reasoning": "Query analysis failed - cannot safely determine appropriate processing method"
            }
        
        # Extract agent scores for later use
        agent_scores = execution_plan.get("execution_plan", {}).get("confidence_scores", {})
        
        # Update state
        return {
            **state,
            "execution_plan": execution_plan,
            "agent_scores": agent_scores,
            "rag_result": None,
            "web_search_result": None,
            "gpt_result": None
        }

    def execute_agents(self, state: UniversalState) -> UniversalState:
        """
        Execute the specialized agents according to the execution plan.
        This implementation uses sequential execution, evaluating the results of each agent
        before deciding whether to proceed with the next one.

        Args:
            state: The current state

        Returns:
            Updated state with agent results
        """
        if self.verbose:
            print("\n=== Starting execute_agents node ===")
            print(f"Standalone query: {state['standalone_query']}")
            
        execution_plan = state["execution_plan"].get("execution_plan", {})
        execution_order = execution_plan.get("execution_order", [])
        
        # Results containers
        rag_result = None
        web_search_result = None
        gpt_result = None
        
        # Execute agents sequentially according to the execution order
        for agent_type in execution_order:
            if self.verbose:
                print(f"\n🔍 UNIVERSAL AGENT: Sequential execution - Now processing {agent_type} agent")
                
            if agent_type == "rag":
                # Execute RAG agent
                rag_result = self._execute_rag_agent(state)
                
                # Evaluate if RAG result is sufficient
                if self._is_result_sufficient(rag_result, agent_type, state["standalone_query"]):
                    if self.verbose:
                        print("✅ UNIVERSAL AGENT: RAG result is sufficient, skipping remaining agents")
                    break
                    
            elif agent_type == "web_search":
                # Execute Web Search agent
                web_search_result = self._execute_web_search_agent(state)
                
                # Evaluate if Web Search result is sufficient
                if self._is_result_sufficient(web_search_result, agent_type, state["standalone_query"]):
                    if self.verbose:
                        print("✅ UNIVERSAL AGENT: Web Search result is sufficient, skipping remaining agents")
                    break
                    
            elif agent_type == "gpt":
                # Execute GPT agent
                gpt_result = self._execute_gpt_agent(state)
                
                # GPT is typically the last agent, so no need to evaluate sufficiency
        
        # Update state
        return {
            **state,
            "rag_result": rag_result,
            "web_search_result": web_search_result,
            "gpt_result": gpt_result
        }
        
    def _execute_rag_agent(self, state: UniversalState) -> Dict[str, Any]:
        """
        Execute the RAG agent.
        
        Args:
            state: The current state
            
        Returns:
            RAG agent result
        """
        if self.verbose:
            print("\n🔍 UNIVERSAL AGENT: Executing RAG agent")
        
        # Get blob names from state and ensure it's a list
        blob_names = state.get("blob_names", [])
        if isinstance(blob_names, bool) or blob_names is None:
            blob_names = []
            
        # Get selected file IDs from state and ensure it's a list
        selected_file_ids = state.get("selected_file_ids", [])
        if isinstance(selected_file_ids, bool) or selected_file_ids is None:
            selected_file_ids = []
        
        if self.verbose:
            print(f"🔍 UNIVERSAL AGENT: Using {len(blob_names)} blob names for RAG")
            for i, name in enumerate(blob_names):
                print(f"  Blob {i+1}: {name}")
            print(f"🔍 UNIVERSAL AGENT: Using {len(selected_file_ids)} selected file IDs for RAG")
            print(f"🔍 UNIVERSAL AGENT: Selected file IDs: {selected_file_ids}")
        
        try:
            # Use direct execution for better error handling
            try:
                print("🔍 UNIVERSAL AGENT: Calling RAG agent directly for better error handling")
                # The RAG agent's process_query accepts query, standalone_query, blob_names, deep_search
                rag_result = self.rag_agent.process_query(
                    query=state["query"],
                    standalone_query=state["standalone_query"],
                    blob_names=blob_names,
                    deep_search=False
                )
                if self.verbose:
                    print(f"🔍 UNIVERSAL AGENT: RAG agent returned {len(rag_result.get('csv_documents', []))} CSV documents, {len(rag_result.get('text_documents', []))} text documents")
                    print(f"🔍 UNIVERSAL AGENT: RAG references: {len(rag_result.get('references', []))} references")
                    
                # If we have documents, use the RAG Planner to generate an answer
                if rag_result.get('csv_documents') or rag_result.get('text_documents'):
                    try:
                        print("🔍 UNIVERSAL AGENT: Using RAG Planner to process retrieved documents")
                        print(f"🔍 UNIVERSAL AGENT: Passing {len(rag_result.get('csv_documents', []))} CSV documents and {len(rag_result.get('text_documents', []))} text documents to RAG Planner")
                        
                        # Log the first CSV document structure if available
                        if rag_result.get('csv_documents') and len(rag_result.get('csv_documents', [])) > 0:
                            csv_doc = rag_result.get('csv_documents', [])[0]
                            print(f"🔍 UNIVERSAL AGENT: First CSV document keys: {list(csv_doc.keys() if isinstance(csv_doc, dict) else [])}")
                            if isinstance(csv_doc, dict) and 'score' in csv_doc:
                                print(f"🔍 UNIVERSAL AGENT: First CSV document score: {csv_doc.get('score', 0)}")
                        
                        # Call the RAG Planner agent with the retrieved documents
                        planner_result = self.rag_planner_agent.process_query(
                            query=state["query"],
                            standalone_query=state["standalone_query"],
                            csv_documents=rag_result.get('csv_documents', []),
                            text_documents=rag_result.get('text_documents', []),
                            conversation_history=state.get("conversation_history"),
                            conversation_summary=state.get("conversation_summary")
                        )
                        
                        # Merge the results
                        rag_result["answer"] = planner_result.get("answer", "")
                        rag_result["references"] = planner_result.get("references", [])
                        
                        if self.verbose:
                            print(f"🔍 UNIVERSAL AGENT: RAG Planner answer: {rag_result.get('answer', '')[:100]}...")
                            print(f"🔍 UNIVERSAL AGENT: RAG Planner returned {len(planner_result.get('references', []))} references")
                            print(f"🔍 UNIVERSAL AGENT: RAG Planner processing status: {planner_result.get('status', 'unknown')}")
                    except Exception as planner_e:
                        print(f"❌ UNIVERSAL AGENT: Error using RAG Planner: {planner_e}")
                        print(f"❌ UNIVERSAL AGENT: Error type: {type(planner_e)}")
                        import traceback
                        print(f"❌ UNIVERSAL AGENT: Traceback: {traceback.format_exc()}")
                        # Create a basic answer if planner fails
                        rag_result["answer"] = f"I found some relevant documents but couldn't analyze them properly. Error: {str(planner_e)}"
                        
                        # SAFETY FIX: Don't generate potentially hallucinated content from document fragments
                        print("🔄 UNIVERSAL AGENT: RAG planner failed - providing safe error message instead of generating content")
                        # Don't attempt to create answers from document fragments as this can lead to hallucinations
                else:
                    rag_result["answer"] = "I couldn't find relevant documents to answer your question."
                    print("❌ UNIVERSAL AGENT: No documents found by RAG agent")
                    
            except Exception as direct_e:
                print(f"❌ UNIVERSAL AGENT: Error in direct RAG execution: {direct_e}")
                print(f"❌ UNIVERSAL AGENT: Type of blob_names: {type(blob_names)}")
                print(f"❌ UNIVERSAL AGENT: Content of blob_names: {blob_names}")
                
                # Try with a fallback approach - create a new list
                try:
                    print("🔄 UNIVERSAL AGENT: Trying fallback approach with explicit list conversion")
                    safe_blob_names = list(blob_names) if isinstance(blob_names, (list, tuple)) else []
                    print(f"🔍 UNIVERSAL AGENT: Using safe_blob_names: {safe_blob_names}")
                    
                    rag_result = self.rag_agent.process_query(
                        query=state["query"],
                        standalone_query=state["standalone_query"],
                        blob_names=safe_blob_names,
                        deep_search=False
                    )
                    
                    # If we have documents, use the RAG Planner to generate an answer
                    if rag_result.get('csv_documents') or rag_result.get('text_documents'):
                        try:
                            print("🔍 UNIVERSAL AGENT: Using RAG Planner to process retrieved documents (fallback)")
                            print(f"🔍 UNIVERSAL AGENT: Passing {len(rag_result.get('csv_documents', []))} CSV documents and {len(rag_result.get('text_documents', []))} text documents to RAG Planner")
                            
                            # Log the first CSV document structure if available
                            if rag_result.get('csv_documents') and len(rag_result.get('csv_documents', [])) > 0:
                                csv_doc = rag_result.get('csv_documents', [])[0]
                                print(f"🔍 UNIVERSAL AGENT: First CSV document keys: {list(csv_doc.keys() if isinstance(csv_doc, dict) else [])}")
                                if isinstance(csv_doc, dict) and 'score' in csv_doc:
                                    print(f"🔍 UNIVERSAL AGENT: First CSV document score: {csv_doc.get('score', 0)}")
                            
                            planner_result = self.rag_planner_agent.process_query(
                                query=state["query"],
                                standalone_query=state["standalone_query"],
                                csv_documents=rag_result.get('csv_documents', []),
                                text_documents=rag_result.get('text_documents', []),
                                conversation_history=state.get("conversation_history"),
                                conversation_summary=state.get("conversation_summary")
                            )
                            
                            # Merge the results
                            rag_result["answer"] = planner_result.get("answer", "")
                            rag_result["references"] = planner_result.get("references", [])
                            
                            if self.verbose:
                                print(f"🔍 UNIVERSAL AGENT: RAG Planner answer (fallback): {rag_result.get('answer', '')[:100]}...")
                                print(f"🔍 UNIVERSAL AGENT: RAG Planner returned {len(planner_result.get('references', []))} references")
                        except Exception as planner_e:
                            print(f"❌ UNIVERSAL AGENT: Error using RAG Planner (fallback): {planner_e}")
                            # Create a basic answer if planner fails
                            rag_result["answer"] = f"I found some relevant documents but couldn't analyze them properly. Error: {str(planner_e)}"
                            
                            # SAFETY FIX: Don't generate potentially hallucinated content from document fragments
                            print("🔄 UNIVERSAL AGENT: RAG planner failed (fallback) - providing safe error message instead of generating content")
                            # Don't attempt to create answers from document fragments as this can lead to hallucinations
                    else:
                        rag_result["answer"] = "I couldn't find relevant documents to answer your question."
                        print("❌ UNIVERSAL AGENT: No documents found by RAG agent (fallback)")
                        
                    if self.verbose:
                        print(f"🔍 UNIVERSAL AGENT: RAG agent fallback result: {rag_result.get('answer', '')[:100]}...")
                except Exception as fallback_e:
                    print(f"❌ UNIVERSAL AGENT: Fallback RAG execution also failed: {fallback_e}")
                    # Create a minimal result structure
                    rag_result = {
                        "answer": f"Unable to process the attached files due to a technical error: {str(direct_e)}",
                        "references": []
                    }
        except Exception as e:
            if self.verbose:
                print(f"❌ UNIVERSAL AGENT: Error setting up RAG agent execution: {e}")
                import traceback
                print(f"❌ UNIVERSAL AGENT: Traceback: {traceback.format_exc()}")
            rag_result = {
                "answer": f"Error setting up RAG agent execution: {str(e)}",
                "references": []
            }
            
        return rag_result
        
    def _execute_web_search_agent(self, state: UniversalState) -> Dict[str, Any]:
        """
        Execute the Web Search agent.
        
        Args:
            state: The current state
            
        Returns:
            Web Search agent result
        """
        if self.verbose:
            print("\n🔍 UNIVERSAL AGENT: Executing Web Search agent")
            
        try:
            result = self.web_search_agent.graph.invoke({
                "messages": [],
                "query": state["standalone_query"],
                "search_results": [],
                "scraped_content": {},
                "previous_content": {},
                "response": {}
            })
            
            web_search_result = result.get("response", {})
            
            if self.verbose:
                print(f"✅ UNIVERSAL AGENT: Web Search agent result: {web_search_result.get('answer', '')[:100]}...")
                
            return web_search_result
        except Exception as e:
            if self.verbose:
                print(f"❌ UNIVERSAL AGENT: Error executing Web Search agent: {e}")
                
            return {
                "answer": f"I encountered an issue while searching the web: {str(e)}",
                "sources": {}
            }
            
    def _execute_gpt_agent(self, state: UniversalState) -> Dict[str, Any]:
        """
        Execute the GPT agent.
        
        Args:
            state: The current state
            
        Returns:
            GPT agent result
        """
        if self.verbose:
            print("\n🔍 UNIVERSAL AGENT: Executing GPT agent")
            
        try:
            result = self.gpt_agent.process_query(
                state["standalone_query"],
                state.get("conversation_history"),
                state.get("conversation_summary")
            )
            
            if self.verbose:
                print(f"✅ UNIVERSAL AGENT: GPT agent result: {result.get('answer', '')[:100]}...")
                
            return result
        except Exception as e:
            if self.verbose:
                print(f"❌ UNIVERSAL AGENT: Error executing GPT agent: {e}")
                
            return {
                "answer": f"I encountered an issue while processing your query: {str(e)}",
                "references": {}
            }
            
    def _is_result_sufficient(self, result: Dict[str, Any], agent_type: str, query: str) -> bool:
        """
        Evaluate if the result from an agent is sufficient to answer the query.
        
        Args:
            result: Agent result
            agent_type: Type of agent that produced the result
            query: The query being answered
            
        Returns:
            True if the result is sufficient, False otherwise
        """
        if not result or not result.get("answer"):
            return False
            
        answer = result.get("answer", "")
        
        # Check if the answer explicitly states it couldn't find information
        negative_indicators = [
            "couldn't find", "could not find", "no relevant", "no information",
            "don't have", "do not have", "unable to find", "not able to find"
        ]
        
        if any(indicator in answer.lower() for indicator in negative_indicators):
            if self.verbose:
                print(f"🔍 UNIVERSAL AGENT: {agent_type} result indicates no information found")
            return False
            
        # For RAG agent, check if we have references and a substantial answer
        if agent_type == "rag":
            has_references = len(result.get("references", [])) > 0
            is_substantial = len(answer.split()) >= 50  # At least 50 words
            
            if has_references and is_substantial:
                # SAFETY FIX: Don't use LLM to make routing decisions
                # Always continue to other agents to ensure comprehensive coverage
                if self.verbose:
                    print(f"🔍 UNIVERSAL AGENT: {agent_type} result has references and is substantial, but continuing to other agents for comprehensive coverage")
                return False  # Always continue to other agents for safety
            
        # For web search, check if we have sources and a substantial answer
        elif agent_type == "web_search":
            has_sources = len(result.get("sources", {})) > 0
            is_substantial = len(answer.split()) >= 75  # Web search answers should be more substantial
            
            if has_sources and is_substantial:
                if self.verbose:
                    print(f"🔍 UNIVERSAL AGENT: {agent_type} result has sources and is substantial")
                return True
                
        return False

    def synthesize_results(self, state: UniversalState) -> UniversalState:
        """
        Synthesize results from multiple agents into a coherent answer.

        Args:
            state: The current state

        Returns:
            Updated state with synthesized answer
        """
        if self.verbose:
            print("\n=== Starting synthesize_results node ===")
        
        # Collect results from each agent
        rag_result = state.get("rag_result")
        web_search_result = state.get("web_search_result")
        gpt_result = state.get("gpt_result")
        
        # Collect confidence scores
        agent_scores = state.get("agent_scores", {})
        rag_score = agent_scores.get("rag", 0)
        web_search_score = agent_scores.get("web_search", 0)
        gpt_score = agent_scores.get("gpt", 0)
        
        # Determine which agent results to use
        results_to_use = []
        
        if rag_result:
            results_to_use.append({
                "type": "rag",
                "result": rag_result,
                "score": rag_score
            })
        
        if web_search_result:
            results_to_use.append({
                "type": "web_search",
                "result": web_search_result,
                "score": web_search_score
            })
        
        if gpt_result:
            results_to_use.append({
                "type": "gpt",
                "result": gpt_result,
                "score": gpt_score
            })
        
        # Sort results by score (highest first)
        results_to_use.sort(key=lambda x: x["score"], reverse=True)
        
        # Prepare system prompt for synthesis
        system_prompt = """You are a helpful AI assistant tasked with synthesizing information from multiple sources to answer a user's question.

CRITICAL INSTRUCTION: NEVER include information that is not explicitly present in the provided sources. This is an enterprise system where accuracy is paramount.

Your task:
1. Analyze the user's query and the provided information from different sources
2. Synthesize a comprehensive, accurate answer that directly addresses the query
3. ONLY use information explicitly present in the provided sources
4. If the sources don't contain enough information to fully answer the query, acknowledge this limitation
5. NEVER add information from your own knowledge that isn't in the sources
6. NEVER make claims, provide examples, or include details not explicitly mentioned in the sources
7. Cite the sources appropriately in your answer

ENTERPRISE SAFETY GUIDELINES:
- This is an ENTERPRISE SYSTEM where hallucinations can cause SERIOUS BUSINESS IMPACT
- Users RELY on this system for ACCURATE information from their enterprise documents
- NEVER include information that isn't explicitly in the sources - this is a STRICT REQUIREMENT
- Even if a statement seems obviously true or common knowledge, it MUST be in the sources
- If you cannot answer the question with the provided information, say so clearly

If the sources contain conflicting information, note this and present both perspectives.
"""
        
        # Prepare human message with query and results
        human_message = f"Query: {state['query']}\n\n"
        
        if results_to_use:
            human_message += "Available information from different sources:\n\n"
            
            for i, result_info in enumerate(results_to_use):
                result_type = result_info["type"]
                result = result_info["result"]
                
                human_message += f"Source {i+1} ({result_type}):\n"
                
                if isinstance(result, dict):
                    if "answer" in result:
                        human_message += f"Answer: {result['answer']}\n"
                    
                    if "references" in result:
                        human_message += "References:\n"
                        
                        if isinstance(result["references"], dict):
                            for ref_key, ref_value in result["references"].items():
                                human_message += f"- {ref_key}: {ref_value}\n"
                        elif isinstance(result["references"], list):
                            for ref in result["references"]:
                                if isinstance(ref, str):
                                    human_message += f"- {ref}\n"
                                elif isinstance(ref, dict) and "content" in ref:
                                    human_message += f"- {ref.get('content', '')}\n"
                
                human_message += "\n"
            
            human_message += "\nPlease synthesize the above information to answer the user's query. ONLY include information explicitly present in the sources. If the sources don't contain enough information to fully answer the query, acknowledge this limitation."
        else:
            human_message += "No relevant information was found in the available sources. Please acknowledge this limitation in your response."
        
        # Create messages for the LLM
        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": human_message}
        ]
        
        # Get response from LLM
        response = self.llm.invoke(messages)
        
        if self.verbose:
            print(f"Synthesized answer: {response.content}")
        
        # Extract references from all results
        references = {}
        
        for result_info in results_to_use:
            result = result_info["result"]
            
            if isinstance(result, dict) and "references" in result:
                if isinstance(result["references"], dict):
                    references.update(result["references"])
                elif isinstance(result["references"], list):
                    for i, ref in enumerate(result["references"]):
                        if isinstance(ref, str):
                            references[f"ref_{i+1}"] = ref
                        elif isinstance(ref, dict):
                            ref_content = ref.get("content", "")
                            references[f"ref_{i+1}"] = ref_content
        
        # Update state
        return {
            **state,
            "final_answer": response.content,
            "references": references
        }

    def evaluate_answer(self, state: UniversalState) -> UniversalState:
        """
        Evaluate the synthesized answer for quality and completeness.

        Args:
            state: The current state

        Returns:
            Updated state with evaluation
        """
        if self.verbose:
            print("\n=== Starting evaluate_answer node ===")
        
        # For now, we'll just return the state as is
        # In a future version, we could use the QA agent to evaluate the answer
        
        if self.verbose:
            print("Answer evaluation complete")
            
        return state

    def process_query(
        self, 
        query: str,
        standalone_query: str = None,
        conversation_history: Optional[str] = None,
        conversation_summary: Optional[str] = None,
        selected_file_ids: Optional[List[int]] = None,
        blob_names: Optional[List[str]] = None,
        query_type: str = "all"
    ) -> Dict[str, Any]:
        """
        Process a query using the Universal agent.

        Args:
            query: User query
            standalone_query: Optional standalone query (if already provided by planner)
            conversation_history: Optional conversation history
            conversation_summary: Optional conversation summary
            selected_file_ids: Optional list of selected file IDs for RAG
            blob_names: Optional list of blob names for RAG
            query_type: Query type (all, filter, or selected)

        Returns:
            Dictionary with the response
        """
        if self.verbose:
            print(f"\n=== UNIVERSAL AGENT: Processing query ===")
            print(f"Query: {query}")
            print(f"Standalone query: {standalone_query}")
            if selected_file_ids:
                print(f"Selected file IDs: {selected_file_ids}")
            if blob_names:
                print(f"Blob names: {blob_names}")
            print(f"Query type: {query_type}")
            
        # Use the standalone query if provided, otherwise use the original query
        if standalone_query is None:
            standalone_query = query
            
        # Check if we need RAG and refresh the agent if needed
        has_files = (blob_names and len(blob_names) > 0) or (selected_file_ids and len(selected_file_ids) > 0)
        if has_files:
            if self.verbose:
                print(f"🔄 UNIVERSAL AGENT: Files detected, refreshing RAG agent to ensure latest version")
            self.refresh_rag_agent()
            self.refresh_rag_planner_agent()
            
        # Invoke the graph
        result = self.graph.invoke({
            "messages": [],
            "query": query,
            "standalone_query": standalone_query,
            "conversation_history": conversation_history,
            "conversation_summary": conversation_summary,
            "selected_file_ids": selected_file_ids or [],
            "blob_names": blob_names or [],
            "query_type": query_type,
            "rag_result": None,
            "web_search_result": None,
            "gpt_result": None,
            "combined_result": None,
            "final_answer": "",
            "references": {},
            "execution_plan": {},
            "agent_scores": {}
        })
        
        # Process references to ensure they're properly formatted
        processed_references = []
        raw_references = result.get("references", {})
        
        # Convert references to the expected format
        if isinstance(raw_references, dict):
            for key, value in raw_references.items():
                if isinstance(value, str):
                    # Simple string reference, convert to object
                    processed_references.append({
                        "chunk_id": value,
                        "title": f"Reference {key}",
                        "content": "",
                        "score": 0.8  # Default score
                    })
                elif isinstance(value, dict):
                    # Already an object, ensure it has required fields
                    processed_references.append({
                        "chunk_id": value.get("chunk_id", key),
                        "title": value.get("title", f"Reference {key}"),
                        "content": value.get("content", ""),
                        "score": value.get("score", 0.8)
                    })
        elif isinstance(raw_references, list):
            for i, ref in enumerate(raw_references):
                if isinstance(ref, str):
                    # Simple string reference, convert to object
                    processed_references.append({
                        "chunk_id": ref,
                        "title": f"Reference {i+1}",
                        "content": "",
                        "score": 0.8  # Default score
                    })
                elif isinstance(ref, dict):
                    # Already an object, ensure it has required fields
                    processed_references.append({
                        "chunk_id": ref.get("chunk_id", f"ref_{i+1}"),
                        "title": ref.get("title", f"Reference {i+1}"),
                        "content": ref.get("content", ""),
                        "score": ref.get("score", 0.8)
                    })
        
        if self.verbose:
            print(f"Processed {len(processed_references)} references")
            
        # Get the final answer
        final_answer = result["final_answer"]
            
        # Final QA check for hallucinations
        try:
            from agents.qa import create_qa_agent
            
            if self.verbose:
                print("🔍 UNIVERSAL AGENT: Performing final QA check for hallucinations")
                
            # Create QA agent with strict settings
            qa_agent = create_qa_agent(verbose=True, max_improvement_loops=1)
            
            # Convert processed references back to the format expected by QA agent
            qa_references = {}
            for i, ref in enumerate(processed_references):
                qa_references[f"ref_{i+1}"] = ref.get("chunk_id", f"reference_{i+1}")
                
            # Evaluate the answer
            qa_result = qa_agent.graph.invoke({
                "messages": [],
                "query": query,
                "conversation_history": conversation_history,
                "conversation_summary": conversation_summary,
                "answer": final_answer,
                "references": qa_references,
                "evaluation": {},
                "needs_improvement": False,
                "improvement_count": 0,
                "improvement_instructions": None
            })
            
            # Check for hallucinations
            hallucinations = qa_result.get("evaluation", {}).get("hallucinations_detected", [])
            source_adherence = qa_result.get("evaluation", {}).get("evaluation", {}).get("source_adherence", 5)
            
            if hallucinations and len(hallucinations) > 0:
                if self.verbose:
                    print(f"⚠️ UNIVERSAL AGENT: Detected {len(hallucinations)} potential hallucinations")
                    for i, h in enumerate(hallucinations):
                        print(f"  {i+1}. {h}")
                        
                # Add disclaimer to the answer
                disclaimer = "Based on the available references, I can only provide limited information on this topic. Some details may not be fully covered in the sources I have access to."
                final_answer = f"{disclaimer}\n\n{final_answer}"
                
                if self.verbose:
                    print("⚠️ UNIVERSAL AGENT: Added disclaimer to answer due to potential hallucinations")
            else:
                if self.verbose:
                    print("✅ UNIVERSAL AGENT: No hallucinations detected in final answer")
                    
        except Exception as e:
            if self.verbose:
                print(f"⚠️ UNIVERSAL AGENT: Error in final QA check: {e}")
                print("⚠️ UNIVERSAL AGENT: Proceeding with original answer")
        
        # Return the result
        return {
            "answer": final_answer,
            "references": processed_references,
            "agent_type": "universal",
            "execution_plan": result["execution_plan"],
            "agent_results": {
                "rag": result.get("rag_result"),
                "web_search": result.get("web_search_result"),
                "gpt": result.get("gpt_result")
            }
        }

def create_universal_agent(model=None, system_prompt=None, qa_system_prompt=None, verbose=True) -> UniversalAgent:
    """
    Create a Universal agent.

    Args:
        model: LLM to use
        system_prompt: System prompt for the agent
        qa_system_prompt: System prompt for the QA component
        verbose: Whether to print verbose output

    Returns:
        A Universal agent
    """
    return UniversalAgent(
        model=model,
        system_prompt=system_prompt,
        qa_system_prompt=qa_system_prompt,
        verbose=verbose
    ) 