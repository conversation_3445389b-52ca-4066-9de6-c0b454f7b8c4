"""
SQL Chat Session Manager

This service manages SQL chat sessions separately from document chat sessions.
Provides session creation, management, and conversation history tracking.
"""

import os
import sys
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta

# Add parent directories to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from models.models import db, SQLChatSession, QueryHistory, DatabaseConnection, UserDatabaseAccess

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class SQLSessionManager:
    """Service for managing SQL chat sessions"""
    
    def __init__(self, verbose: bool = True):
        """
        Initialize the SQL session manager
        
        Args:
            verbose: Whether to enable verbose logging
        """
        self.verbose = verbose
        
        if self.verbose:
            logger.info("Initialized SQL Session Manager")
    
    def create_session(self, user_id: int, database_id: int, session_name: Optional[str] = None,
                      session_description: Optional[str] = None) -> Dict[str, Any]:
        """
        Create a new SQL chat session
        
        Args:
            user_id: ID of the user
            database_id: ID of the database
            session_name: Optional name for the session
            session_description: Optional description
            
        Returns:
            Dictionary with session creation result
        """
        try:
            # Validate user has access to the database
            access = UserDatabaseAccess.query.filter_by(
                user_id=user_id,
                database_id=database_id,
                is_active=True
            ).first()
            
            if not access:
                return {
                    'status': 'error',
                    'error': 'User does not have access to this database'
                }
            
            # Get database info for default session name
            database = DatabaseConnection.query.get(database_id)
            if not database:
                return {
                    'status': 'error',
                    'error': 'Database not found'
                }
            
            # Generate default session name if not provided
            if not session_name:
                session_name = f"SQL Chat - {database.name} - {datetime.now().strftime('%Y-%m-%d %H:%M')}"
            
            # Create new session
            new_session = SQLChatSession(
                user_id=user_id,
                database_id=database_id,
                session_name=session_name,
                session_description=session_description
            )
            
            db.session.add(new_session)
            db.session.commit()
            
            if self.verbose:
                logger.info(f"Created SQL chat session {new_session.id} for user {user_id} on database {database_id}")
            
            return {
                'status': 'success',
                'session': {
                    'id': new_session.id,
                    'session_name': new_session.session_name,
                    'session_description': new_session.session_description,
                    'database_id': new_session.database_id,
                    'database_name': database.name,
                    'created_at': new_session.created_at.isoformat(),
                    'total_queries': 0,
                    'successful_queries': 0
                }
            }
            
        except Exception as e:
            db.session.rollback()
            logger.error(f"Error creating SQL session: {str(e)}")
            return {
                'status': 'error',
                'error': str(e)
            }
    
    def get_user_sessions(self, user_id: int, database_id: Optional[int] = None,
                         include_inactive: bool = False, limit: int = 50) -> List[Dict[str, Any]]:
        """
        Get SQL chat sessions for a user
        
        Args:
            user_id: ID of the user
            database_id: Optional database filter
            include_inactive: Whether to include inactive sessions
            limit: Maximum number of sessions to return
            
        Returns:
            List of session information
        """
        try:
            query = SQLChatSession.query.filter_by(user_id=user_id)
            
            if database_id:
                query = query.filter_by(database_id=database_id)
            
            if not include_inactive:
                query = query.filter_by(is_active=True)
            
            sessions = query.order_by(SQLChatSession.last_activity_at.desc()).limit(limit).all()
            
            session_list = []
            for session in sessions:
                session_data = {
                    'id': session.id,
                    'session_name': session.session_name,
                    'session_description': session.session_description,
                    'database_id': session.database_id,
                    'database_name': session.database.name if session.database else None,
                    'created_at': session.created_at.isoformat(),
                    'updated_at': session.updated_at.isoformat(),
                    'last_activity_at': session.last_activity_at.isoformat(),
                    'is_active': session.is_active,
                    'total_queries': session.total_queries,
                    'successful_queries': session.successful_queries,
                    'success_rate': (session.successful_queries / session.total_queries * 100) if session.total_queries > 0 else 0
                }
                session_list.append(session_data)
            
            return session_list
            
        except Exception as e:
            logger.error(f"Error getting user sessions: {str(e)}")
            return []
    
    def get_session(self, session_id: int, user_id: int) -> Optional[Dict[str, Any]]:
        """
        Get a specific SQL chat session
        
        Args:
            session_id: ID of the session
            user_id: ID of the user (for access control)
            
        Returns:
            Session information or None if not found/accessible
        """
        try:
            session = SQLChatSession.query.filter_by(
                id=session_id,
                user_id=user_id
            ).first()
            
            if not session:
                return None
            
            # Get recent queries for this session
            recent_queries = QueryHistory.query.filter_by(
                sql_session_id=session_id
            ).order_by(QueryHistory.created_at.desc()).limit(10).all()
            
            return {
                'id': session.id,
                'session_name': session.session_name,
                'session_description': session.session_description,
                'database_id': session.database_id,
                'database_name': session.database.name if session.database else None,
                'created_at': session.created_at.isoformat(),
                'updated_at': session.updated_at.isoformat(),
                'last_activity_at': session.last_activity_at.isoformat(),
                'is_active': session.is_active,
                'total_queries': session.total_queries,
                'successful_queries': session.successful_queries,
                'success_rate': (session.successful_queries / session.total_queries * 100) if session.total_queries > 0 else 0,
                'recent_queries': [
                    {
                        'id': q.id,
                        'natural_language_query': q.natural_language_query,
                        'was_successful': q.was_successful,
                        'row_count': q.row_count,
                        'execution_time_ms': q.execution_time_ms,
                        'created_at': q.created_at.isoformat(),
                        'query_intent': q.query_intent,
                        'requires_visualization': q.requires_visualization
                    } for q in recent_queries
                ]
            }
            
        except Exception as e:
            logger.error(f"Error getting session: {str(e)}")
            return None
    
    def update_session(self, session_id: int, user_id: int, updates: Dict[str, Any]) -> Dict[str, Any]:
        """
        Update a SQL chat session
        
        Args:
            session_id: ID of the session
            user_id: ID of the user (for access control)
            updates: Dictionary of fields to update
            
        Returns:
            Update result
        """
        try:
            session = SQLChatSession.query.filter_by(
                id=session_id,
                user_id=user_id
            ).first()
            
            if not session:
                return {
                    'status': 'error',
                    'error': 'Session not found or access denied'
                }
            
            # Update allowed fields
            if 'session_name' in updates:
                session.session_name = updates['session_name']
            if 'session_description' in updates:
                session.session_description = updates['session_description']
            if 'is_active' in updates:
                session.is_active = updates['is_active']
            
            session.updated_at = datetime.utcnow()
            db.session.commit()
            
            if self.verbose:
                logger.info(f"Updated SQL session {session_id}")
            
            return {
                'status': 'success',
                'message': 'Session updated successfully'
            }
            
        except Exception as e:
            db.session.rollback()
            logger.error(f"Error updating session: {str(e)}")
            return {
                'status': 'error',
                'error': str(e)
            }
    
    def delete_session(self, session_id: int, user_id: int) -> Dict[str, Any]:
        """
        Delete a SQL chat session and all associated queries
        
        Args:
            session_id: ID of the session
            user_id: ID of the user (for access control)
            
        Returns:
            Deletion result
        """
        try:
            session = SQLChatSession.query.filter_by(
                id=session_id,
                user_id=user_id
            ).first()
            
            if not session:
                return {
                    'status': 'error',
                    'error': 'Session not found or access denied'
                }
            
            # Delete associated queries
            QueryHistory.query.filter_by(sql_session_id=session_id).delete()
            
            # Delete the session
            db.session.delete(session)
            db.session.commit()
            
            if self.verbose:
                logger.info(f"Deleted SQL session {session_id}")
            
            return {
                'status': 'success',
                'message': 'Session deleted successfully'
            }
            
        except Exception as e:
            db.session.rollback()
            logger.error(f"Error deleting session: {str(e)}")
            return {
                'status': 'error',
                'error': str(e)
            }
    
    def update_session_activity(self, session_id: int, query_successful: bool = True) -> None:
        """
        Update session activity and statistics
        
        Args:
            session_id: ID of the session
            query_successful: Whether the query was successful
        """
        try:
            session = SQLChatSession.query.get(session_id)
            if session:
                session.total_queries += 1
                if query_successful:
                    session.successful_queries += 1
                session.last_activity_at = datetime.utcnow()
                session.updated_at = datetime.utcnow()
                db.session.commit()
                
        except Exception as e:
            logger.error(f"Error updating session activity: {str(e)}")
            db.session.rollback()
    
    def get_conversation_history(self, session_id: int, limit: int = 10) -> str:
        """
        Get formatted conversation history for a session
        
        Args:
            session_id: ID of the session
            limit: Maximum number of queries to include
            
        Returns:
            Formatted conversation history string
        """
        try:
            queries = QueryHistory.query.filter_by(
                sql_session_id=session_id
            ).order_by(QueryHistory.created_at.desc()).limit(limit).all()
            
            if not queries:
                return ""
            
            # Reverse to get chronological order
            queries = list(reversed(queries))
            
            history_parts = []
            for i, query in enumerate(queries, 1):
                history_parts.append(f"[Query {i}]")
                history_parts.append(f"User: {query.natural_language_query}")
                
                if query.was_successful and query.query_result_summary:
                    history_parts.append(f"Result: {query.query_result_summary}")
                elif query.error_message:
                    history_parts.append(f"Error: {query.error_message}")
                
                history_parts.append("")  # Empty line between queries
            
            return "\n".join(history_parts)
            
        except Exception as e:
            logger.error(f"Error getting conversation history: {str(e)}")
            return ""
    
    def cleanup_old_sessions(self, days_old: int = 30) -> Dict[str, Any]:
        """
        Clean up old inactive sessions
        
        Args:
            days_old: Number of days after which to consider sessions old
            
        Returns:
            Cleanup result
        """
        try:
            cutoff_date = datetime.utcnow() - timedelta(days=days_old)
            
            old_sessions = SQLChatSession.query.filter(
                SQLChatSession.last_activity_at < cutoff_date,
                SQLChatSession.is_active == False
            ).all()
            
            deleted_count = 0
            for session in old_sessions:
                # Delete associated queries
                QueryHistory.query.filter_by(sql_session_id=session.id).delete()
                db.session.delete(session)
                deleted_count += 1
            
            db.session.commit()
            
            if self.verbose:
                logger.info(f"Cleaned up {deleted_count} old SQL sessions")
            
            return {
                'status': 'success',
                'deleted_sessions': deleted_count,
                'cutoff_date': cutoff_date.isoformat()
            }
            
        except Exception as e:
            db.session.rollback()
            logger.error(f"Error cleaning up old sessions: {str(e)}")
            return {
                'status': 'error',
                'error': str(e)
            }
