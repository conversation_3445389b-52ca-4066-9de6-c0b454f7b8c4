"""
Visualization Agent for SQL Chat

This agent generates customizable HTML charts and visualizations based on
SQL query results and user preferences. It uses prompt-based customization
to create flexible, interactive charts.
"""

import os
import sys
import logging
from typing import Dict, List, Any, Optional
import json

# Add parent directories to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from config.settings import AZURE_OPENAI_KEY, AZURE_OPENAI_API_VERSION, AZURE_OPENAI_ENDPOINT, AZURE_OPENAI_DEPLOYMENT_NAME
from langchain_openai import AzureChatOpenAI
from langchain_core.messages import SystemMessage, HumanMessage

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class VisualizationAgent:
    """
    Agent for generating customizable HTML visualizations from SQL data
    """
    
    def __init__(self, verbose: bool = True):
        """
        Initialize the visualization agent
        
        Args:
            verbose: Whether to enable verbose logging
        """
        self.verbose = verbose
        
        # Initialize LLM
        self.llm = AzureChatOpenAI(
            azure_deployment=AZURE_OPENAI_DEPLOYMENT_NAME,
            openai_api_version=AZURE_OPENAI_API_VERSION,
            azure_endpoint=AZURE_OPENAI_ENDPOINT,
            openai_api_key=AZURE_OPENAI_KEY,
            temperature=0.1
        )
        
        if self.verbose:
            logger.info("Initialized Visualization Agent")
    
    def generate_chart(self, data: List[Dict[str, Any]], chart_config: Dict[str, Any]) -> Dict[str, Any]:
        """
        Generate an HTML chart from data and configuration
        
        Args:
            data: List of data points from SQL query results
            chart_config: Chart configuration including type, styling, etc.
            
        Returns:
            Dictionary with HTML chart and metadata
        """
        try:
            chart_type = chart_config.get('chart_type', 'bar')
            customizations = chart_config.get('customizations', {})
            
            if self.verbose:
                logger.info(f"Generating {chart_type} chart with {len(data)} data points")
            
            # Generate chart based on type
            if chart_type == 'bar':
                return self._generate_bar_chart(data, customizations)
            elif chart_type == 'line':
                return self._generate_line_chart(data, customizations)
            elif chart_type == 'pie':
                return self._generate_pie_chart(data, customizations)
            elif chart_type == 'scatter':
                return self._generate_scatter_chart(data, customizations)
            elif chart_type == 'table':
                return self._generate_table(data, customizations)
            else:
                return self._generate_custom_chart(data, chart_config)
        
        except Exception as e:
            logger.error(f"Error generating chart: {str(e)}")
            return {
                'status': 'error',
                'error': str(e),
                'html': '<div class="error">Failed to generate chart</div>'
            }
    
    def _generate_bar_chart(self, data: List[Dict[str, Any]], customizations: Dict[str, Any]) -> Dict[str, Any]:
        """Generate a customizable bar chart"""
        
        # Extract data for chart
        if not data:
            return {'status': 'error', 'error': 'No data provided'}
        
        # Auto-detect columns
        columns = list(data[0].keys())
        x_column = customizations.get('x_column', columns[0])
        y_column = customizations.get('y_column', columns[1] if len(columns) > 1 else columns[0])
        
        # Chart styling
        colors = customizations.get('colors', ['#3498db', '#e74c3c', '#2ecc71', '#f39c12', '#9b59b6'])
        title = customizations.get('title', 'Bar Chart')
        width = customizations.get('width', '100%')
        height = customizations.get('height', '400px')
        
        # Generate HTML with Chart.js
        html = f"""
        <div style="width: {width}; height: {height};">
            <canvas id="barChart_{id(data)}" width="400" height="200"></canvas>
        </div>
        
        <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
        <script>
        const ctx_{id(data)} = document.getElementById('barChart_{id(data)}').getContext('2d');
        const chart_{id(data)} = new Chart(ctx_{id(data)}, {{
            type: 'bar',
            data: {{
                labels: {json.dumps([str(row[x_column]) for row in data])},
                datasets: [{{
                    label: '{y_column}',
                    data: {json.dumps([float(row[y_column]) if isinstance(row[y_column], (int, float)) else 0 for row in data])},
                    backgroundColor: '{colors[0]}',
                    borderColor: '{colors[0]}',
                    borderWidth: 1
                }}]
            }},
            options: {{
                responsive: true,
                plugins: {{
                    title: {{
                        display: true,
                        text: '{title}'
                    }}
                }},
                scales: {{
                    y: {{
                        beginAtZero: true
                    }}
                }}
            }}
        }});
        </script>
        """
        
        return {
            'status': 'success',
            'html': html,
            'chart_type': 'bar',
            'data_points': len(data)
        }
    
    def _generate_line_chart(self, data: List[Dict[str, Any]], customizations: Dict[str, Any]) -> Dict[str, Any]:
        """Generate a customizable line chart"""
        
        if not data:
            return {'status': 'error', 'error': 'No data provided'}
        
        columns = list(data[0].keys())
        x_column = customizations.get('x_column', columns[0])
        y_column = customizations.get('y_column', columns[1] if len(columns) > 1 else columns[0])
        
        colors = customizations.get('colors', ['#3498db'])
        title = customizations.get('title', 'Line Chart')
        width = customizations.get('width', '100%')
        height = customizations.get('height', '400px')
        
        html = f"""
        <div style="width: {width}; height: {height};">
            <canvas id="lineChart_{id(data)}" width="400" height="200"></canvas>
        </div>
        
        <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
        <script>
        const ctx_{id(data)} = document.getElementById('lineChart_{id(data)}').getContext('2d');
        const chart_{id(data)} = new Chart(ctx_{id(data)}, {{
            type: 'line',
            data: {{
                labels: {json.dumps([str(row[x_column]) for row in data])},
                datasets: [{{
                    label: '{y_column}',
                    data: {json.dumps([float(row[y_column]) if isinstance(row[y_column], (int, float)) else 0 for row in data])},
                    borderColor: '{colors[0]}',
                    backgroundColor: '{colors[0]}20',
                    tension: 0.1
                }}]
            }},
            options: {{
                responsive: true,
                plugins: {{
                    title: {{
                        display: true,
                        text: '{title}'
                    }}
                }},
                scales: {{
                    y: {{
                        beginAtZero: true
                    }}
                }}
            }}
        }});
        </script>
        """
        
        return {
            'status': 'success',
            'html': html,
            'chart_type': 'line',
            'data_points': len(data)
        }
    
    def _generate_pie_chart(self, data: List[Dict[str, Any]], customizations: Dict[str, Any]) -> Dict[str, Any]:
        """Generate a customizable pie chart"""
        
        if not data:
            return {'status': 'error', 'error': 'No data provided'}
        
        columns = list(data[0].keys())
        label_column = customizations.get('label_column', columns[0])
        value_column = customizations.get('value_column', columns[1] if len(columns) > 1 else columns[0])
        
        colors = customizations.get('colors', ['#3498db', '#e74c3c', '#2ecc71', '#f39c12', '#9b59b6', '#1abc9c', '#34495e'])
        title = customizations.get('title', 'Pie Chart')
        width = customizations.get('width', '100%')
        height = customizations.get('height', '400px')
        
        html = f"""
        <div style="width: {width}; height: {height};">
            <canvas id="pieChart_{id(data)}" width="400" height="200"></canvas>
        </div>
        
        <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
        <script>
        const ctx_{id(data)} = document.getElementById('pieChart_{id(data)}').getContext('2d');
        const chart_{id(data)} = new Chart(ctx_{id(data)}, {{
            type: 'pie',
            data: {{
                labels: {json.dumps([str(row[label_column]) for row in data])},
                datasets: [{{
                    data: {json.dumps([float(row[value_column]) if isinstance(row[value_column], (int, float)) else 0 for row in data])},
                    backgroundColor: {json.dumps(colors[:len(data)])}
                }}]
            }},
            options: {{
                responsive: true,
                plugins: {{
                    title: {{
                        display: true,
                        text: '{title}'
                    }},
                    legend: {{
                        position: 'bottom'
                    }}
                }}
            }}
        }});
        </script>
        """
        
        return {
            'status': 'success',
            'html': html,
            'chart_type': 'pie',
            'data_points': len(data)
        }
    
    def _generate_scatter_chart(self, data: List[Dict[str, Any]], customizations: Dict[str, Any]) -> Dict[str, Any]:
        """Generate a customizable scatter chart"""
        
        if not data:
            return {'status': 'error', 'error': 'No data provided'}
        
        columns = list(data[0].keys())
        x_column = customizations.get('x_column', columns[0])
        y_column = customizations.get('y_column', columns[1] if len(columns) > 1 else columns[0])
        
        colors = customizations.get('colors', ['#3498db'])
        title = customizations.get('title', 'Scatter Chart')
        width = customizations.get('width', '100%')
        height = customizations.get('height', '400px')
        
        # Prepare scatter data
        scatter_data = []
        for row in data:
            x_val = row[x_column]
            y_val = row[y_column]
            if isinstance(x_val, (int, float)) and isinstance(y_val, (int, float)):
                scatter_data.append({'x': x_val, 'y': y_val})
        
        html = f"""
        <div style="width: {width}; height: {height};">
            <canvas id="scatterChart_{id(data)}" width="400" height="200"></canvas>
        </div>
        
        <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
        <script>
        const ctx_{id(data)} = document.getElementById('scatterChart_{id(data)}').getContext('2d');
        const chart_{id(data)} = new Chart(ctx_{id(data)}, {{
            type: 'scatter',
            data: {{
                datasets: [{{
                    label: '{y_column} vs {x_column}',
                    data: {json.dumps(scatter_data)},
                    backgroundColor: '{colors[0]}'
                }}]
            }},
            options: {{
                responsive: true,
                plugins: {{
                    title: {{
                        display: true,
                        text: '{title}'
                    }}
                }},
                scales: {{
                    x: {{
                        type: 'linear',
                        position: 'bottom',
                        title: {{
                            display: true,
                            text: '{x_column}'
                        }}
                    }},
                    y: {{
                        title: {{
                            display: true,
                            text: '{y_column}'
                        }}
                    }}
                }}
            }}
        }});
        </script>
        """
        
        return {
            'status': 'success',
            'html': html,
            'chart_type': 'scatter',
            'data_points': len(scatter_data)
        }
    
    def _generate_table(self, data: List[Dict[str, Any]], customizations: Dict[str, Any]) -> Dict[str, Any]:
        """Generate a customizable HTML table"""
        
        if not data:
            return {'status': 'error', 'error': 'No data provided'}
        
        title = customizations.get('title', 'Data Table')
        max_rows = customizations.get('max_rows', 100)
        styling = customizations.get('styling', 'default')
        
        # Limit data if needed
        display_data = data[:max_rows]
        
        # Generate table HTML
        columns = list(data[0].keys())
        
        table_style = """
        <style>
        .data-table {
            border-collapse: collapse;
            width: 100%;
            margin: 20px 0;
        }
        .data-table th, .data-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        .data-table th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        .data-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        </style>
        """
        
        html = f"""
        {table_style}
        <div>
            <h3>{title}</h3>
            <table class="data-table">
                <thead>
                    <tr>
                        {''.join(f'<th>{col}</th>' for col in columns)}
                    </tr>
                </thead>
                <tbody>
                    {''.join(
                        '<tr>' + ''.join(f'<td>{row[col]}</td>' for col in columns) + '</tr>'
                        for row in display_data
                    )}
                </tbody>
            </table>
            {f'<p><em>Showing {len(display_data)} of {len(data)} rows</em></p>' if len(data) > max_rows else ''}
        </div>
        """
        
        return {
            'status': 'success',
            'html': html,
            'chart_type': 'table',
            'data_points': len(display_data)
        }
    
    def _generate_custom_chart(self, data: List[Dict[str, Any]], chart_config: Dict[str, Any]) -> Dict[str, Any]:
        """Generate a custom chart using LLM for complex requirements"""
        
        system_prompt = """You are a data visualization expert. Generate HTML code for a chart based on the user's requirements.

Use Chart.js library for interactive charts. The HTML should be complete and self-contained.
Include proper styling and make the chart responsive.

Requirements:
1. Use Chart.js CDN: https://cdn.jsdelivr.net/npm/chart.js
2. Create unique IDs for chart elements
3. Make charts responsive and interactive
4. Use appropriate colors and styling
5. Include proper labels and titles

Return only the HTML code, no explanations."""
        
        user_prompt = f"""
Data: {json.dumps(data[:5])}... ({len(data)} total rows)

Chart Configuration: {json.dumps(chart_config)}

Generate HTML code for this visualization:"""
        
        try:
            messages = [
                SystemMessage(content=system_prompt),
                HumanMessage(content=user_prompt)
            ]
            
            response = self.llm.invoke(messages)
            html_content = response.content.strip()
            
            # Clean up the HTML (remove markdown formatting if present)
            html_content = html_content.replace('```html', '').replace('```', '').strip()
            
            return {
                'status': 'success',
                'html': html_content,
                'chart_type': 'custom',
                'data_points': len(data)
            }
            
        except Exception as e:
            logger.error(f"Error generating custom chart: {str(e)}")
            return {
                'status': 'error',
                'error': str(e),
                'html': '<div class="error">Failed to generate custom chart</div>'
            }
