"""
Role-Based Access Control Manager for SQL Chat

This service manages database roles, permissions, and user access controls.
"""

import os
import sys
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime

# Add parent directories to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from models.models import (
    db, DatabaseConnection, DatabaseRole, UserDatabaseAccess, 
    DatabaseSchema, DatabaseColumn
)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class RoleManager:
    """Service for managing database roles and access controls"""
    
    def __init__(self, verbose: bool = True):
        """
        Initialize the role manager
        
        Args:
            verbose: Whether to enable verbose logging
        """
        self.verbose = verbose
        
        if self.verbose:
            logger.info("Initialized RoleManager")
    
    def create_role(self, database_id: int, role_data: Dict[str, Any], created_by: int) -> Dict[str, Any]:
        """
        Create a new database role
        
        Args:
            database_id: ID of the database
            role_data: Role configuration data
            created_by: ID of the user creating the role
            
        Returns:
            Dictionary with creation result
        """
        try:
            # Validate database exists
            db_conn = DatabaseConnection.query.get(database_id)
            if not db_conn:
                return {'status': 'error', 'error': 'Database not found'}
            
            # Check if role name already exists for this database
            existing_role = DatabaseRole.query.filter_by(
                database_id=database_id,
                role_name=role_data['role_name']
            ).first()
            
            if existing_role:
                return {'status': 'error', 'error': 'Role name already exists for this database'}
            
            # Create new role
            new_role = DatabaseRole(
                database_id=database_id,
                role_name=role_data['role_name'],
                description=role_data.get('description'),
                row_predicate=role_data.get('row_predicate'),
                column_masks=role_data.get('column_masks', {}),
                allowed_tables=role_data.get('allowed_tables', []),
                denied_tables=role_data.get('denied_tables', []),
                created_by=created_by
            )
            
            db.session.add(new_role)
            db.session.commit()
            
            if self.verbose:
                logger.info(f"Created role '{role_data['role_name']}' for database {database_id}")
            
            return {
                'status': 'success',
                'role_id': new_role.id,
                'message': f"Role '{role_data['role_name']}' created successfully"
            }
            
        except Exception as e:
            db.session.rollback()
            logger.error(f"Error creating role: {str(e)}")
            return {'status': 'error', 'error': str(e)}
    
    def update_role(self, role_id: int, role_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Update an existing database role
        
        Args:
            role_id: ID of the role to update
            role_data: Updated role configuration data
            
        Returns:
            Dictionary with update result
        """
        try:
            role = DatabaseRole.query.get(role_id)
            if not role:
                return {'status': 'error', 'error': 'Role not found'}
            
            # Update role properties
            if 'description' in role_data:
                role.description = role_data['description']
            if 'row_predicate' in role_data:
                role.row_predicate = role_data['row_predicate']
            if 'column_masks' in role_data:
                role.column_masks = role_data['column_masks']
            if 'allowed_tables' in role_data:
                role.allowed_tables = role_data['allowed_tables']
            if 'denied_tables' in role_data:
                role.denied_tables = role_data['denied_tables']
            if 'is_active' in role_data:
                role.is_active = role_data['is_active']
            
            role.updated_at = datetime.utcnow()
            db.session.commit()
            
            if self.verbose:
                logger.info(f"Updated role {role_id}")
            
            return {
                'status': 'success',
                'message': f"Role '{role.role_name}' updated successfully"
            }
            
        except Exception as e:
            db.session.rollback()
            logger.error(f"Error updating role: {str(e)}")
            return {'status': 'error', 'error': str(e)}
    
    def delete_role(self, role_id: int) -> Dict[str, Any]:
        """
        Delete a database role
        
        Args:
            role_id: ID of the role to delete
            
        Returns:
            Dictionary with deletion result
        """
        try:
            role = DatabaseRole.query.get(role_id)
            if not role:
                return {'status': 'error', 'error': 'Role not found'}
            
            # Check if role is assigned to any users
            user_assignments = UserDatabaseAccess.query.filter_by(role_id=role_id).count()
            if user_assignments > 0:
                return {
                    'status': 'error', 
                    'error': f'Cannot delete role. It is assigned to {user_assignments} users.'
                }
            
            role_name = role.role_name
            db.session.delete(role)
            db.session.commit()
            
            if self.verbose:
                logger.info(f"Deleted role '{role_name}' (ID: {role_id})")
            
            return {
                'status': 'success',
                'message': f"Role '{role_name}' deleted successfully"
            }
            
        except Exception as e:
            db.session.rollback()
            logger.error(f"Error deleting role: {str(e)}")
            return {'status': 'error', 'error': str(e)}
    
    def grant_user_access(self, user_id: int, database_id: int, role_id: int, 
                         granted_by: int, expires_at: Optional[datetime] = None) -> Dict[str, Any]:
        """
        Grant database access to a user with a specific role
        
        Args:
            user_id: ID of the user
            database_id: ID of the database
            role_id: ID of the role
            granted_by: ID of the user granting access
            expires_at: Optional expiration date
            
        Returns:
            Dictionary with grant result
        """
        try:
            # Check if access already exists
            existing_access = UserDatabaseAccess.query.filter_by(
                user_id=user_id,
                database_id=database_id,
                role_id=role_id
            ).first()
            
            if existing_access:
                if existing_access.is_active:
                    return {'status': 'error', 'error': 'User already has this access'}
                else:
                    # Reactivate existing access
                    existing_access.is_active = True
                    existing_access.granted_by = granted_by
                    existing_access.granted_at = datetime.utcnow()
                    existing_access.expires_at = expires_at
                    db.session.commit()
                    
                    return {
                        'status': 'success',
                        'message': 'User access reactivated successfully'
                    }
            
            # Create new access record
            new_access = UserDatabaseAccess(
                user_id=user_id,
                database_id=database_id,
                role_id=role_id,
                granted_by=granted_by,
                expires_at=expires_at
            )
            
            db.session.add(new_access)
            db.session.commit()
            
            if self.verbose:
                logger.info(f"Granted database {database_id} access to user {user_id} with role {role_id}")
            
            return {
                'status': 'success',
                'access_id': new_access.id,
                'message': 'User access granted successfully'
            }
            
        except Exception as e:
            db.session.rollback()
            logger.error(f"Error granting user access: {str(e)}")
            return {'status': 'error', 'error': str(e)}
    
    def revoke_user_access(self, user_id: int, database_id: int, role_id: Optional[int] = None) -> Dict[str, Any]:
        """
        Revoke database access from a user
        
        Args:
            user_id: ID of the user
            database_id: ID of the database
            role_id: Optional specific role ID to revoke
            
        Returns:
            Dictionary with revoke result
        """
        try:
            query = UserDatabaseAccess.query.filter_by(
                user_id=user_id,
                database_id=database_id,
                is_active=True
            )
            
            if role_id:
                query = query.filter_by(role_id=role_id)
            
            access_records = query.all()
            
            if not access_records:
                return {'status': 'error', 'error': 'No active access found'}
            
            # Deactivate access records
            for access in access_records:
                access.is_active = False
            
            db.session.commit()
            
            if self.verbose:
                logger.info(f"Revoked database {database_id} access from user {user_id}")
            
            return {
                'status': 'success',
                'revoked_count': len(access_records),
                'message': f'Revoked {len(access_records)} access record(s) successfully'
            }
            
        except Exception as e:
            db.session.rollback()
            logger.error(f"Error revoking user access: {str(e)}")
            return {'status': 'error', 'error': str(e)}
    
    def get_user_roles(self, user_id: int, database_id: int) -> List[Dict[str, Any]]:
        """
        Get all active roles for a user on a specific database
        
        Args:
            user_id: ID of the user
            database_id: ID of the database
            
        Returns:
            List of role information dictionaries
        """
        try:
            access_records = UserDatabaseAccess.query.filter_by(
                user_id=user_id,
                database_id=database_id,
                is_active=True
            ).join(DatabaseRole).all()
            
            roles = []
            for access in access_records:
                # Check if access has expired
                if access.expires_at and access.expires_at < datetime.utcnow():
                    continue
                
                role = access.role
                roles.append({
                    'role_id': role.id,
                    'role_name': role.role_name,
                    'description': role.description,
                    'row_predicate': role.row_predicate,
                    'column_masks': role.column_masks,
                    'allowed_tables': role.allowed_tables,
                    'denied_tables': role.denied_tables,
                    'granted_at': access.granted_at.isoformat() if access.granted_at else None,
                    'expires_at': access.expires_at.isoformat() if access.expires_at else None
                })
            
            return roles
            
        except Exception as e:
            logger.error(f"Error getting user roles: {str(e)}")
            return []
    
    def get_database_roles(self, database_id: int) -> List[Dict[str, Any]]:
        """
        Get all roles for a specific database
        
        Args:
            database_id: ID of the database
            
        Returns:
            List of role information dictionaries
        """
        try:
            roles = DatabaseRole.query.filter_by(
                database_id=database_id,
                is_active=True
            ).all()
            
            role_list = []
            for role in roles:
                # Count users assigned to this role
                user_count = UserDatabaseAccess.query.filter_by(
                    role_id=role.id,
                    is_active=True
                ).count()
                
                role_list.append({
                    'role_id': role.id,
                    'role_name': role.role_name,
                    'description': role.description,
                    'row_predicate': role.row_predicate,
                    'column_masks': role.column_masks,
                    'allowed_tables': role.allowed_tables,
                    'denied_tables': role.denied_tables,
                    'user_count': user_count,
                    'created_at': role.created_at.isoformat() if role.created_at else None,
                    'updated_at': role.updated_at.isoformat() if role.updated_at else None
                })
            
            return role_list
            
        except Exception as e:
            logger.error(f"Error getting database roles: {str(e)}")
            return []
