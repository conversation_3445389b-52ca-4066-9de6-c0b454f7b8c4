"""
SQL Chat Agent

Main LangGraph-based agent for handling database chat interactions.
This agent orchestrates the entire flow from natural language query to SQL execution.
"""

import os
import sys
import logging
import uuid
from typing import Dict, List, Any, Optional
from datetime import datetime

# Add parent directories to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from langchain_core.messages import SystemMessage, HumanMessage, AIMessage
from langchain_openai import AzureChatOpenAI
from langgraph.graph import StateGraph, END

from config.settings import AZURE_OPENAI_KEY, AZURE_OPENAI_API_VERSION, AZURE_OPENAI_ENDPOINT, AZURE_OPENAI_DEPLOYMENT_NAME
from models.models import db, DatabaseConnection, QueryHistory
from .state import SQLChatState
from .db_ingestion import DatabaseIngestionService
from .schema_analyzer import DatabaseSchemaAnalyzer
from .query_generator import SQLQueryGenerator
from .training_service import TrainingService
from .sql_planner import <PERSON><PERSON><PERSON><PERSON><PERSON>
from .visualization_agent import VisualizationAgent
from .session_manager import SQLSessionManager

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class SQLChatAgent:
    """LangGraph-based SQL Chat Agent for database interactions"""
    
    def __init__(self, verbose: bool = True):
        """
        Initialize the SQL Chat Agent
        
        Args:
            verbose: Whether to enable verbose logging
        """
        self.verbose = verbose
        
        # Initialize LLM
        self.llm = AzureChatOpenAI(
            azure_deployment=AZURE_OPENAI_DEPLOYMENT_NAME,
            openai_api_version=AZURE_OPENAI_API_VERSION,
            azure_endpoint=AZURE_OPENAI_ENDPOINT,
            openai_api_key=AZURE_OPENAI_KEY,
            temperature=0.1
        )
        
        # Initialize services
        self.ingestion_service = DatabaseIngestionService(verbose=verbose)
        self.schema_analyzer = DatabaseSchemaAnalyzer(verbose=verbose)
        self.query_generator = SQLQueryGenerator(verbose=verbose)
        self.training_service = TrainingService(verbose=verbose)
        self.sql_planner = SQLPlanner(verbose=verbose)
        self.visualization_agent = VisualizationAgent(verbose=verbose)
        self.session_manager = SQLSessionManager(verbose=verbose)
        
        # Build the LangGraph
        self.graph = self._build_graph()
        
        if self.verbose:
            logger.info("Initialized SQLChatAgent with LangGraph orchestration")
    
    def _build_graph(self) -> StateGraph:
        """Build the LangGraph for SQL chat workflow"""
        graph = StateGraph(SQLChatState)
        
        # Add nodes
        graph.add_node("initialize", self._initialize_conversation)
        graph.add_node("plan_query", self._plan_query)
        graph.add_node("analyze_query", self._analyze_query)
        graph.add_node("check_access", self._check_access)
        graph.add_node("build_context", self._build_context)
        graph.add_node("generate_sql", self._generate_sql)
        graph.add_node("validate_sql", self._validate_sql)
        graph.add_node("execute_query", self._execute_query)
        graph.add_node("generate_visualization", self._generate_visualization)
        graph.add_node("qa_check", self._qa_check)
        graph.add_node("format_response", self._format_response)
        graph.add_node("handle_error", self._handle_error)
        graph.add_node("collect_feedback", self._collect_feedback)
        
        # Add edges
        graph.add_edge("initialize", "plan_query")
        graph.add_edge("plan_query", "analyze_query")
        graph.add_edge("analyze_query", "check_access")
        
        # Conditional routing from check_access
        graph.add_conditional_edges(
            "check_access",
            self._route_after_access_check,
            {
                "continue": "build_context",
                "error": "handle_error"
            }
        )
        
        graph.add_edge("build_context", "generate_sql")
        graph.add_edge("generate_sql", "validate_sql")
        
        # Conditional routing from validate_sql
        graph.add_conditional_edges(
            "validate_sql",
            self._route_after_validation,
            {
                "execute": "execute_query",
                "retry": "generate_sql",
                "error": "handle_error"
            }
        )
        
        graph.add_edge("execute_query", "generate_visualization")

        # Conditional routing from generate_visualization
        graph.add_conditional_edges(
            "generate_visualization",
            self._route_after_visualization,
            {
                "qa_check": "qa_check",
                "format_response": "format_response"
            }
        )
        
        # Conditional routing from qa_check
        graph.add_conditional_edges(
            "qa_check",
            self._route_after_qa,
            {
                "success": "format_response",
                "retry": "generate_sql",
                "feedback": "collect_feedback"
            }
        )
        
        graph.add_edge("format_response", END)
        graph.add_edge("handle_error", END)
        graph.add_edge("collect_feedback", "format_response")
        
        # Set entry point
        graph.set_entry_point("initialize")
        
        return graph.compile()
    
    def chat(self, user_id: int, database_id: int, query: str,
             sql_session_id: Optional[int] = None) -> Dict[str, Any]:
        """
        Main chat interface for SQL queries

        Args:
            user_id: ID of the user
            database_id: ID of the target database
            query: Natural language query
            sql_session_id: Optional SQL session ID for conversation tracking

        Returns:
            Dictionary with response and metadata
        """
        try:
            # Get or create session
            if sql_session_id:
                session_info = self.session_manager.get_session(sql_session_id, user_id)
                if not session_info:
                    return {
                        'status': 'error',
                        'error': 'Session not found or access denied'
                    }
            else:
                # Create a new session
                session_result = self.session_manager.create_session(
                    user_id=user_id,
                    database_id=database_id,
                    session_name=f"Query: {query[:50]}..."
                )
                if session_result['status'] != 'success':
                    return session_result

                session_info = session_result['session']
                sql_session_id = session_info['id']

            # Get conversation history
            conversation_history = self.session_manager.get_conversation_history(sql_session_id)

            # Initialize state
            initial_state = SQLChatState(
                messages=[HumanMessage(content=query)],
                user_id=user_id,
                database_id=database_id,
                session_id=str(sql_session_id),  # Convert to string for compatibility
                sql_session_id=sql_session_id,  # Add new field for proper session tracking
                natural_query=query,
                processed_query="",
                query_intent="",
                query_keywords=[],
                database_info=None,
                suggested_tables=[],
                table_relationships=[],
                semantic_mappings=[],
                user_roles=[],
                access_permissions={},
                generated_sql=None,
                final_sql=None,
                sql_explanation=None,
                estimated_rows=None,
                execution_result=None,
                result_data=None,
                execution_time=None,
                error_message=None,
                qa_score=None,
                qa_feedback=None,
                validation_errors=[],
                conversation_history=conversation_history,
                previous_queries=[],
                feedback_data=None,
                training_suggestions=[],
                requires_visualization=False,
                chart_type=None,
                chart_data=None,
                current_step="initialize",
                next_action="",
                retry_count=0,
                max_retries=3,
                has_error=False,
                error_type=None,
                recovery_suggestions=[]
            )
            
            # Run the graph
            final_state = self.graph.invoke(initial_state)

            # Update session activity
            if hasattr(final_state, 'sql_session_id') and final_state.get('sql_session_id'):
                query_successful = not final_state.get('has_error', False)
                self.session_manager.update_session_activity(
                    final_state['sql_session_id'],
                    query_successful
                )

            # Format final response
            response = self._format_final_response(final_state)
            response['sql_session_id'] = sql_session_id  # Include session ID in response
            return response
            
        except Exception as e:
            logger.error(f"Error in SQL chat: {str(e)}")
            return {
                'status': 'error',
                'error': str(e),
                'response': 'I encountered an error while processing your request. Please try again.',
                'sql': None,
                'data': None
            }
    
    def _initialize_conversation(self, state: SQLChatState) -> SQLChatState:
        """Initialize the conversation and load context"""
        if self.verbose:
            logger.info(f"Initializing conversation for user {state['user_id']} on database {state['database_id']}")
        
        # Load database information
        db_conn = DatabaseConnection.query.get(state['database_id'])
        if db_conn:
            state['database_info'] = {
                'name': db_conn.name,
                'description': db_conn.description,
                'type': db_conn.connection_type
            }
        
        # Load conversation history
        previous_queries = QueryHistory.query.filter_by(
            user_id=state['user_id'],
            database_id=state['database_id'],
            session_id=state['session_id']
        ).order_by(QueryHistory.created_at.desc()).limit(5).all()
        
        state['previous_queries'] = [
            {
                'query': q.natural_language_query,
                'sql': q.generated_sql,
                'timestamp': q.created_at.isoformat() if q.created_at else None
            }
            for q in previous_queries
        ]
        
        state['current_step'] = "plan_query"
        return state

    def _plan_query(self, state: SQLChatState) -> SQLChatState:
        """Plan the query execution strategy using SQL Planner"""
        if self.verbose:
            logger.info("Planning query execution strategy")

        # Use SQL Planner to analyze and plan the query
        planning_result = self.sql_planner.plan_query(
            user_query=state['natural_query'],
            database_id=state['database_id'],
            conversation_history=state.get('conversation_history'),
            qa_feedback=state.get('feedback_data'),
            user_roles=state.get('user_roles', [])
        )

        if planning_result['status'] == 'success':
            # Update state with planning results
            state['processed_query'] = planning_result['processed_query']
            state['query_intent'] = planning_result['query_intent']
            state['requires_visualization'] = planning_result['requires_visualization']
            state['chart_type'] = planning_result.get('chart_type')
            state['max_retries'] = planning_result.get('max_retries', 3)

            # Store planning metadata
            state['planning_metadata'] = {
                'query_complexity': planning_result.get('query_complexity'),
                'execution_strategy': planning_result.get('execution_strategy'),
                'estimated_tables': planning_result.get('estimated_tables', []),
                'visualization_type': planning_result.get('visualization_type'),
                'chart_config': planning_result.get('chart_config', {})
            }
        else:
            # Fallback if planning fails
            state['processed_query'] = state['natural_query']
            state['planning_metadata'] = {'execution_strategy': 'fallback'}

        state['current_step'] = "analyze_query"
        return state
    
    def _analyze_query(self, state: SQLChatState) -> SQLChatState:
        """Analyze the natural language query to understand intent"""
        if self.verbose:
            logger.info("Analyzing natural language query")
        
        # Extract keywords and intent
        query = state['natural_query']
        keywords = self.query_generator._extract_keywords(query)
        
        # Determine query intent using LLM
        intent = self._determine_query_intent(query)
        
        state['query_keywords'] = keywords
        state['query_intent'] = intent
        state['processed_query'] = query  # Could be enhanced with preprocessing
        state['current_step'] = "check_access"
        
        return state
    
    def _check_access(self, state: SQLChatState) -> SQLChatState:
        """Check user access permissions for the database"""
        if self.verbose:
            logger.info("Checking user access permissions")
        
        access_info = self.query_generator._check_user_access(
            state['user_id'], 
            state['database_id']
        )
        
        if not access_info or not access_info['has_access']:
            state['has_error'] = True
            state['error_type'] = 'access_denied'
            state['error_message'] = 'You do not have access to this database'
            state['current_step'] = "handle_error"
        else:
            state['user_roles'] = access_info['roles']
            state['access_permissions'] = access_info
            state['current_step'] = "build_context"
        
        return state
    
    def _build_context(self, state: SQLChatState) -> SQLChatState:
        """Build database context for SQL generation"""
        if self.verbose:
            logger.info("Building database context")
        
        # Get suggested tables
        suggested_tables = self.schema_analyzer.suggest_tables_for_query(
            state['database_id'], 
            state['query_keywords']
        )
        state['suggested_tables'] = suggested_tables
        
        # Get semantic mappings
        from models.models import SemanticMapping
        mappings = SemanticMapping.query.filter_by(database_id=state['database_id']).all()
        state['semantic_mappings'] = [
            {
                'name': m.name,
                'type': m.mapping_type,
                'sql_expression': m.sql_expression,
                'description': m.description
            }
            for m in mappings
        ]
        
        state['current_step'] = "generate_sql"
        return state
    
    def _generate_sql(self, state: SQLChatState) -> SQLChatState:
        """Generate SQL query from natural language"""
        if self.verbose:
            logger.info("Generating SQL query")
        
        result = self.query_generator.generate_sql(
            state['user_id'],
            state['database_id'],
            state['natural_query'],
            state['session_id']
        )
        
        if result['status'] == 'success':
            state['generated_sql'] = result['base_sql']
            state['final_sql'] = result['sql']
            state['sql_explanation'] = result['explanation']
            state['estimated_rows'] = result['estimated_rows']
            state['current_step'] = "validate_sql"
        else:
            state['has_error'] = True
            state['error_type'] = 'sql_generation'
            state['error_message'] = result['error']
            state['current_step'] = "handle_error"
        
        return state

    def _validate_sql(self, state: SQLChatState) -> SQLChatState:
        """Validate the generated SQL query"""
        if self.verbose:
            logger.info("Validating SQL query")

        validation_result = self.query_generator._validate_sql(state['final_sql'])

        if validation_result['valid']:
            state['current_step'] = "execute_query"
        else:
            state['validation_errors'].append(validation_result['error'])

            if state['retry_count'] < state['max_retries']:
                state['retry_count'] += 1
                state['current_step'] = "generate_sql"
                # Add validation error to context for retry
                state['messages'].append(AIMessage(content=f"SQL validation failed: {validation_result['error']}. Retrying..."))
            else:
                state['has_error'] = True
                state['error_type'] = 'validation_failed'
                state['error_message'] = f"SQL validation failed after {state['max_retries']} attempts: {validation_result['error']}"
                state['current_step'] = "handle_error"

        return state

    def _execute_query(self, state: SQLChatState) -> SQLChatState:
        """Execute the SQL query (simulation for now)"""
        if self.verbose:
            logger.info("Executing SQL query")

        # In a real implementation, you would execute the SQL against the actual database
        # For now, we'll simulate execution
        try:
            start_time = datetime.now()

            # Simulate query execution
            # In practice, you would:
            # 1. Connect to the database using the stored connection info
            # 2. Execute the SQL query
            # 3. Fetch results
            # 4. Apply any column masking based on user roles

            # Simulated result
            execution_result = {
                'status': 'success',
                'row_count': state['estimated_rows'] or 10,
                'columns': ['column1', 'column2', 'column3'],
                'execution_time_ms': 150
            }

            # Simulated data
            result_data = [
                {'column1': 'value1', 'column2': 'value2', 'column3': 'value3'},
                {'column1': 'value4', 'column2': 'value5', 'column3': 'value6'}
            ]

            end_time = datetime.now()
            execution_time = (end_time - start_time).total_seconds()

            state['execution_result'] = execution_result
            state['result_data'] = result_data
            state['execution_time'] = execution_time
            state['current_step'] = "qa_check"

            # Store query in history
            query_record = QueryHistory(
                user_id=state['user_id'],
                database_id=state['database_id'],
                sql_session_id=state.get('sql_session_id'),  # Use proper session FK
                session_id=state['session_id'],  # Keep for backward compatibility
                natural_language_query=state['natural_query'],
                generated_sql=state['generated_sql'],
                executed_sql=state['final_sql'],
                execution_time_ms=int(execution_time * 1000),
                row_count=execution_result['row_count'],
                was_successful=True,
                query_intent=state.get('query_intent'),
                requires_visualization=state.get('requires_visualization', False),
                chart_type=state.get('chart_type')
            )
            db.session.add(query_record)
            db.session.commit()

        except Exception as e:
            logger.error(f"Error executing SQL query: {str(e)}")
            state['has_error'] = True
            state['error_type'] = 'execution_failed'
            state['error_message'] = str(e)
            state['current_step'] = "handle_error"

        return state

    def _qa_check(self, state: SQLChatState) -> SQLChatState:
        """Perform quality assurance check on the results"""
        if self.verbose:
            logger.info("Performing QA check")

        # Check if results make sense
        qa_score = self._calculate_qa_score(state)
        state['qa_score'] = qa_score

        if qa_score >= 0.8:  # High confidence
            state['current_step'] = "format_response"
        elif qa_score >= 0.6:  # Medium confidence - proceed but note
            state['qa_feedback'] = "Results have medium confidence. Please verify."
            state['current_step'] = "format_response"
        else:  # Low confidence - might need feedback
            state['qa_feedback'] = "Results have low confidence. Consider providing feedback."
            state['current_step'] = "collect_feedback"

        return state

    def _format_response(self, state: SQLChatState) -> SQLChatState:
        """Format the final response for the user"""
        if self.verbose:
            logger.info("Formatting response")

        # Determine if visualization is needed
        state['requires_visualization'] = self._should_visualize(state)

        if state['requires_visualization']:
            chart_info = self._suggest_chart_type(state)
            state['chart_type'] = chart_info['type']
            state['chart_data'] = chart_info['data']

        # Add final AI message
        response_message = self._create_response_message(state)
        state['messages'].append(AIMessage(content=response_message))

        state['current_step'] = "complete"
        return state

    def _handle_error(self, state: SQLChatState) -> SQLChatState:
        """Handle errors and provide recovery suggestions"""
        if self.verbose:
            logger.info(f"Handling error: {state['error_type']}")

        recovery_suggestions = []

        if state['error_type'] == 'access_denied':
            recovery_suggestions = [
                "Contact your administrator to request access to this database",
                "Try a different database that you have access to"
            ]
        elif state['error_type'] == 'sql_generation':
            recovery_suggestions = [
                "Try rephrasing your question",
                "Be more specific about what data you're looking for",
                "Check if the table or column names are correct"
            ]
        elif state['error_type'] == 'validation_failed':
            recovery_suggestions = [
                "The query structure may be too complex",
                "Try breaking down your question into simpler parts",
                "Contact support if the issue persists"
            ]

        state['recovery_suggestions'] = recovery_suggestions

        # Create error response message
        error_message = f"I encountered an error: {state['error_message']}"
        if recovery_suggestions:
            error_message += "\n\nSuggestions:\n" + "\n".join(f"• {suggestion}" for suggestion in recovery_suggestions)

        state['messages'].append(AIMessage(content=error_message))
        state['current_step'] = "complete"

        return state

    def _collect_feedback(self, state: SQLChatState) -> SQLChatState:
        """Collect feedback for training purposes"""
        if self.verbose:
            logger.info("Collecting feedback")

        # In a real implementation, this might trigger a feedback UI
        # For now, we'll just note that feedback is needed

        feedback_message = (
            "I've generated a response, but I'm not entirely confident about it. "
            "Please let me know if the results look correct or if you'd like me to try a different approach."
        )

        state['messages'].append(AIMessage(content=feedback_message))
        state['current_step'] = "format_response"

        return state

    def _generate_visualization(self, state: SQLChatState) -> SQLChatState:
        """Generate visualization if required"""
        if self.verbose:
            logger.info("Generating visualization")

        if not state['requires_visualization'] or not state['result_data']:
            state['current_step'] = "qa_check"
            return state

        try:
            # Prepare chart configuration
            chart_config = {
                'chart_type': state.get('chart_type', 'bar'),
                'customizations': state.get('planning_metadata', {}).get('chart_config', {})
            }

            # Add default customizations if not provided
            if 'title' not in chart_config['customizations']:
                chart_config['customizations']['title'] = f"Results for: {state['natural_query'][:50]}..."

            # Generate visualization
            viz_result = self.visualization_agent.generate_chart(
                data=state['result_data'],
                chart_config=chart_config
            )

            if viz_result['status'] == 'success':
                state['chart_data'] = {
                    'html': viz_result['html'],
                    'chart_type': viz_result['chart_type'],
                    'data_points': viz_result['data_points']
                }

                if self.verbose:
                    logger.info(f"Generated {viz_result['chart_type']} chart with {viz_result['data_points']} data points")
            else:
                logger.warning(f"Visualization generation failed: {viz_result.get('error')}")
                state['chart_data'] = None

        except Exception as e:
            logger.error(f"Error generating visualization: {str(e)}")
            state['chart_data'] = None

        state['current_step'] = "qa_check"
        return state

    # Routing functions
    def _route_after_access_check(self, state: SQLChatState) -> str:
        """Route after access check"""
        return "error" if state['has_error'] else "continue"

    def _route_after_validation(self, state: SQLChatState) -> str:
        """Route after SQL validation"""
        if state['has_error']:
            return "error"
        elif state['validation_errors'] and state['retry_count'] < state['max_retries']:
            return "retry"
        else:
            return "execute"

    def _route_after_visualization(self, state: SQLChatState) -> str:
        """Route after visualization generation"""
        # Always proceed to QA check after visualization
        return "qa_check"

    def _route_after_qa(self, state: SQLChatState) -> str:
        """Route after QA check"""
        if state['qa_score'] and state['qa_score'] < 0.6:
            return "feedback"
        else:
            return "success"

    # Helper functions
    def _determine_query_intent(self, query: str) -> str:
        """Determine the intent of the natural language query"""
        query_lower = query.lower()

        if any(word in query_lower for word in ['show', 'list', 'display', 'get', 'find']):
            return 'retrieve'
        elif any(word in query_lower for word in ['count', 'how many', 'number of']):
            return 'count'
        elif any(word in query_lower for word in ['sum', 'total', 'average', 'mean', 'max', 'min']):
            return 'aggregate'
        elif any(word in query_lower for word in ['compare', 'difference', 'vs', 'versus']):
            return 'compare'
        elif any(word in query_lower for word in ['trend', 'over time', 'by month', 'by year']):
            return 'trend'
        else:
            return 'general'

    def _calculate_qa_score(self, state: SQLChatState) -> float:
        """Calculate quality assurance score for the results"""
        score = 1.0

        # Check if we have results
        if not state['result_data']:
            score -= 0.3

        # Check if estimated rows match actual rows
        if state['estimated_rows'] and state['execution_result']:
            actual_rows = state['execution_result'].get('row_count', 0)
            if state['estimated_rows'] > 0:
                ratio = min(actual_rows, state['estimated_rows']) / max(actual_rows, state['estimated_rows'])
                if ratio < 0.5:  # Very different from estimate
                    score -= 0.2

        # Check execution time (penalize very slow queries)
        if state['execution_time'] and state['execution_time'] > 10:  # More than 10 seconds
            score -= 0.1

        return max(0.0, score)

    def _should_visualize(self, state: SQLChatState) -> bool:
        """Determine if results should be visualized"""
        if not state['result_data']:
            return False

        # Check query intent
        if state['query_intent'] in ['trend', 'compare', 'aggregate']:
            return True

        # Check if we have numeric data
        if state['result_data']:
            first_row = state['result_data'][0]
            numeric_columns = sum(1 for value in first_row.values() if isinstance(value, (int, float)))
            return numeric_columns > 0

        return False

    def _suggest_chart_type(self, state: SQLChatState) -> Dict[str, Any]:
        """Suggest appropriate chart type for the data"""
        if state['query_intent'] == 'trend':
            return {'type': 'line', 'data': state['result_data']}
        elif state['query_intent'] == 'compare':
            return {'type': 'bar', 'data': state['result_data']}
        elif state['query_intent'] == 'aggregate':
            return {'type': 'pie', 'data': state['result_data']}
        else:
            return {'type': 'table', 'data': state['result_data']}

    def _create_response_message(self, state: SQLChatState) -> str:
        """Create the final response message"""
        message_parts = []

        # Add explanation
        if state['sql_explanation']:
            message_parts.append(state['sql_explanation'])

        # Add results summary
        if state['execution_result']:
            row_count = state['execution_result'].get('row_count', 0)
            message_parts.append(f"Found {row_count} results.")

        # Add QA feedback if any
        if state['qa_feedback']:
            message_parts.append(state['qa_feedback'])

        # Add visualization note
        if state['requires_visualization']:
            message_parts.append(f"I've prepared a {state['chart_type']} chart to visualize the results.")

        return "\n\n".join(message_parts)

    def _format_final_response(self, state: SQLChatState) -> Dict[str, Any]:
        """Format the final response dictionary"""
        if state['has_error']:
            return {
                'status': 'error',
                'error': state['error_message'],
                'error_type': state['error_type'],
                'recovery_suggestions': state.get('recovery_suggestions', []),
                'response': state['messages'][-1].content if state['messages'] else 'An error occurred',
                'sql': state.get('generated_sql'),
                'data': None
            }

        return {
            'status': 'success',
            'response': state['messages'][-1].content if state['messages'] else 'Query completed successfully',
            'sql': state['final_sql'],
            'base_sql': state['generated_sql'],
            'explanation': state['sql_explanation'],
            'data': state['result_data'],
            'execution_time': state['execution_time'],
            'row_count': state['execution_result'].get('row_count') if state['execution_result'] else 0,
            'qa_score': state['qa_score'],
            'requires_visualization': state['requires_visualization'],
            'chart_type': state['chart_type'],
            'chart_data': state['chart_data'],
            'session_id': state['session_id']
        }
