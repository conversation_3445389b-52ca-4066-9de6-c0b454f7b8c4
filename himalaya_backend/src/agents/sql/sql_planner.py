"""
SQL Planner Agent

This planner is specifically for SQL chat module. It analyzes user queries,
conversation history, QA feedback, and plans the next moves for SQL generation,
visualization needs, and response formatting.
"""

import os
import sys
import logging
from typing import Dict, List, Any, Optional, TypedDict, Annotated
import operator
from datetime import datetime

# Add parent directories to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from langchain_core.messages import AnyMessage, SystemMessage, HumanMessage
from langchain_openai import AzureChatOpenAI
from langgraph.graph import StateGraph, END

from config.settings import AZURE_OPENAI_KEY, AZURE_OPENAI_API_VERSION, AZURE_OPENAI_ENDPOINT, AZURE_OPENAI_DEPLOYMENT_NAME

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class SQLPlannerState(TypedDict):
    """State for the SQL planner agent"""
    messages: Annotated[List[AnyMessage], operator.add]
    user_query: str
    processed_query: str
    conversation_history: Optional[str]
    conversation_summary: Optional[str]
    qa_feedback: Optional[Dict[str, Any]]
    improvement_instructions: Optional[str]
    
    # Database context
    database_id: int
    database_info: Optional[Dict[str, Any]]
    user_roles: List[Dict[str, Any]]
    
    # Query analysis
    query_intent: str
    query_complexity: str  # simple, medium, complex
    requires_joins: bool
    estimated_tables: List[str]
    
    # Visualization planning
    requires_visualization: bool
    visualization_type: Optional[str]  # table, chart, both
    chart_type: Optional[str]  # bar, line, pie, scatter, etc.
    chart_config: Optional[Dict[str, Any]]
    
    # Execution planning
    execution_strategy: str
    retry_count: int
    max_retries: int
    
    # Quality control
    validation_required: bool
    explanation_level: str  # basic, detailed, technical


class SQLPlanner:
    """
    SQL-specific planner that analyzes queries and plans execution strategy
    """
    
    def __init__(self, verbose: bool = True):
        """
        Initialize the SQL planner
        
        Args:
            verbose: Whether to enable verbose logging
        """
        self.verbose = verbose
        
        # Initialize LLM
        self.llm = AzureChatOpenAI(
            azure_deployment=AZURE_OPENAI_DEPLOYMENT_NAME,
            openai_api_version=AZURE_OPENAI_API_VERSION,
            azure_endpoint=AZURE_OPENAI_ENDPOINT,
            openai_api_key=AZURE_OPENAI_KEY,
            temperature=0.1
        )
        
        # Build the planning graph
        self.graph = self._build_graph()
        
        if self.verbose:
            logger.info("Initialized SQL Planner with LangGraph")
    
    def _build_graph(self) -> StateGraph:
        """Build the LangGraph for SQL planning workflow"""
        graph = StateGraph(SQLPlannerState)
        
        # Add nodes
        graph.add_node("analyze_query", self._analyze_query)
        graph.add_node("plan_database_strategy", self._plan_database_strategy)
        graph.add_node("plan_visualization", self._plan_visualization)
        graph.add_node("plan_execution", self._plan_execution)
        graph.add_node("incorporate_feedback", self._incorporate_feedback)
        
        # Add edges
        graph.add_edge("analyze_query", "plan_database_strategy")
        graph.add_edge("plan_database_strategy", "plan_visualization")
        graph.add_edge("plan_visualization", "plan_execution")
        
        # Conditional routing for feedback
        graph.add_conditional_edges(
            "plan_execution",
            self._route_feedback,
            {
                "feedback": "incorporate_feedback",
                "complete": END
            }
        )
        
        graph.add_edge("incorporate_feedback", "plan_execution")
        
        # Set entry point
        graph.set_entry_point("analyze_query")
        
        return graph.compile()
    
    def plan_query(self, user_query: str, database_id: int, 
                   conversation_history: Optional[str] = None,
                   qa_feedback: Optional[Dict[str, Any]] = None,
                   user_roles: Optional[List[Dict[str, Any]]] = None) -> Dict[str, Any]:
        """
        Plan the execution strategy for a SQL query
        
        Args:
            user_query: Natural language query from user
            database_id: ID of the target database
            conversation_history: Previous conversation context
            qa_feedback: Quality assurance feedback from previous attempts
            user_roles: User's database roles and permissions
            
        Returns:
            Dictionary with planning results
        """
        try:
            # Initialize state
            initial_state = SQLPlannerState(
                messages=[HumanMessage(content=user_query)],
                user_query=user_query,
                processed_query="",
                conversation_history=conversation_history,
                conversation_summary="",
                qa_feedback=qa_feedback,
                improvement_instructions=None,
                database_id=database_id,
                database_info=None,
                user_roles=user_roles or [],
                query_intent="",
                query_complexity="medium",
                requires_joins=False,
                estimated_tables=[],
                requires_visualization=False,
                visualization_type=None,
                chart_type=None,
                chart_config=None,
                execution_strategy="standard",
                retry_count=0,
                max_retries=3,
                validation_required=True,
                explanation_level="detailed"
            )
            
            # Run the planning graph
            final_state = self.graph.invoke(initial_state)
            
            # Format planning results
            return self._format_planning_results(final_state)
            
        except Exception as e:
            logger.error(f"Error in SQL planning: {str(e)}")
            return {
                'status': 'error',
                'error': str(e),
                'execution_strategy': 'fallback'
            }
    
    def _analyze_query(self, state: SQLPlannerState) -> SQLPlannerState:
        """Analyze the user query to understand intent and complexity"""
        if self.verbose:
            logger.info("Analyzing SQL query")
        
        system_prompt = """You are a SQL query analyzer. Analyze the user's natural language query to understand:

1. Query Intent: What is the user trying to achieve?
2. Query Complexity: How complex is this query likely to be?
3. Data Requirements: What tables/data might be needed?
4. Join Requirements: Will this likely require joining multiple tables?

Consider the conversation history and any previous feedback to improve your analysis.

Return a JSON object with your analysis:
{
    "query_intent": "retrieve|count|aggregate|compare|trend|analyze",
    "query_complexity": "simple|medium|complex",
    "requires_joins": true/false,
    "estimated_tables": ["table1", "table2"],
    "processed_query": "cleaned and clarified version of the query",
    "reasoning": "explanation of your analysis"
}"""
        
        user_prompt = f"""
User Query: {state['user_query']}

Database ID: {state['database_id']}

Conversation History: {state['conversation_history'] or 'None'}

QA Feedback: {state['qa_feedback'] or 'None'}

Analyze this query:"""
        
        try:
            messages = [
                SystemMessage(content=system_prompt),
                HumanMessage(content=user_prompt)
            ]
            
            response = self.llm.invoke(messages)
            
            # Parse response (simplified - in practice you'd want more robust parsing)
            import json
            try:
                analysis = json.loads(response.content.strip())
                
                state['query_intent'] = analysis.get('query_intent', 'retrieve')
                state['query_complexity'] = analysis.get('query_complexity', 'medium')
                state['requires_joins'] = analysis.get('requires_joins', False)
                state['estimated_tables'] = analysis.get('estimated_tables', [])
                state['processed_query'] = analysis.get('processed_query', state['user_query'])
                
            except json.JSONDecodeError:
                # Fallback if JSON parsing fails
                state['processed_query'] = state['user_query']
                state['query_intent'] = 'retrieve'
                state['query_complexity'] = 'medium'
            
        except Exception as e:
            logger.error(f"Error analyzing query: {str(e)}")
            state['processed_query'] = state['user_query']
        
        return state
    
    def _plan_database_strategy(self, state: SQLPlannerState) -> SQLPlannerState:
        """Plan the database query strategy"""
        if self.verbose:
            logger.info("Planning database strategy")
        
        # Determine execution strategy based on complexity and user roles
        if state['query_complexity'] == 'simple':
            state['execution_strategy'] = 'direct'
            state['validation_required'] = False
        elif state['query_complexity'] == 'complex':
            state['execution_strategy'] = 'careful'
            state['validation_required'] = True
            state['explanation_level'] = 'technical'
        else:
            state['execution_strategy'] = 'standard'
        
        # Adjust strategy based on user roles
        if state['user_roles']:
            # If user has restricted roles, be more careful
            has_restrictions = any(
                role.get('row_predicate') or role.get('column_masks') 
                for role in state['user_roles']
            )
            if has_restrictions:
                state['execution_strategy'] = 'restricted'
                state['validation_required'] = True
        
        return state
    
    def _plan_visualization(self, state: SQLPlannerState) -> SQLPlannerState:
        """Plan visualization requirements"""
        if self.verbose:
            logger.info("Planning visualization")
        
        # Determine if visualization is needed based on query intent
        visualization_intents = ['aggregate', 'compare', 'trend', 'analyze']
        
        if state['query_intent'] in visualization_intents:
            state['requires_visualization'] = True
            
            # Determine chart type based on intent
            if state['query_intent'] == 'trend':
                state['chart_type'] = 'line'
                state['visualization_type'] = 'chart'
            elif state['query_intent'] == 'compare':
                state['chart_type'] = 'bar'
                state['visualization_type'] = 'chart'
            elif state['query_intent'] == 'aggregate':
                state['chart_type'] = 'pie'
                state['visualization_type'] = 'both'  # Table and chart
            else:
                state['chart_type'] = 'bar'
                state['visualization_type'] = 'chart'
            
            # Set basic chart configuration
            state['chart_config'] = {
                'responsive': True,
                'interactive': True,
                'theme': 'default',
                'colors': ['#3498db', '#e74c3c', '#2ecc71', '#f39c12', '#9b59b6']
            }
        else:
            state['requires_visualization'] = False
            state['visualization_type'] = 'table'
        
        return state
    
    def _plan_execution(self, state: SQLPlannerState) -> SQLPlannerState:
        """Plan the execution approach"""
        if self.verbose:
            logger.info("Planning execution")
        
        # Set retry strategy
        if state['execution_strategy'] == 'careful':
            state['max_retries'] = 5
        elif state['execution_strategy'] == 'restricted':
            state['max_retries'] = 3
        else:
            state['max_retries'] = 2
        
        # Set explanation level
        if state['query_complexity'] == 'complex':
            state['explanation_level'] = 'technical'
        elif state['query_complexity'] == 'simple':
            state['explanation_level'] = 'basic'
        else:
            state['explanation_level'] = 'detailed'
        
        return state
    
    def _incorporate_feedback(self, state: SQLPlannerState) -> SQLPlannerState:
        """Incorporate QA feedback into planning"""
        if self.verbose:
            logger.info("Incorporating feedback")
        
        if state['qa_feedback']:
            feedback = state['qa_feedback']
            
            # Adjust strategy based on feedback
            if feedback.get('accuracy_score', 1.0) < 0.7:
                state['execution_strategy'] = 'careful'
                state['validation_required'] = True
            
            if feedback.get('completeness_score', 1.0) < 0.8:
                state['explanation_level'] = 'technical'
            
            # Incorporate improvement instructions
            if 'improvement_suggestions' in feedback:
                state['improvement_instructions'] = feedback['improvement_suggestions']
        
        return state
    
    def _route_feedback(self, state: SQLPlannerState) -> str:
        """Route based on whether feedback needs to be incorporated"""
        if state['qa_feedback'] and state['retry_count'] == 0:
            return "feedback"
        return "complete"
    
    def _format_planning_results(self, state: SQLPlannerState) -> Dict[str, Any]:
        """Format the planning results"""
        return {
            'status': 'success',
            'processed_query': state['processed_query'],
            'query_intent': state['query_intent'],
            'query_complexity': state['query_complexity'],
            'execution_strategy': state['execution_strategy'],
            'requires_joins': state['requires_joins'],
            'estimated_tables': state['estimated_tables'],
            'requires_visualization': state['requires_visualization'],
            'visualization_type': state['visualization_type'],
            'chart_type': state['chart_type'],
            'chart_config': state['chart_config'],
            'validation_required': state['validation_required'],
            'explanation_level': state['explanation_level'],
            'max_retries': state['max_retries'],
            'improvement_instructions': state['improvement_instructions']
        }
