"""
SQL Query Generator

This service generates SQL queries from natural language using LLM
and applies role-based access controls.
"""

import os
import sys
import logging
import re
import sqlglot
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime

# Add parent directories to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from config.settings import AZURE_OPENAI_KEY, AZURE_OPENAI_API_VERSION, AZURE_OPENAI_ENDPOINT, AZURE_OPENAI_DEPLOYMENT_NAME
from models.models import (
    DatabaseConnection, DatabaseSchema, DatabaseColumn, 
    UserDatabaseAccess, DatabaseRole, SemanticMapping, QueryHistory
)
from langchain_openai import AzureChatOpenAI
from langchain_core.messages import SystemMessage, HumanMessage
from .schema_analyzer import DatabaseSchemaAnalyzer

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class SQLQueryGenerator:
    """Generates SQL queries from natural language with role-based access control"""
    
    def __init__(self, verbose: bool = True):
        """
        Initialize the SQL query generator
        
        Args:
            verbose: Whether to enable verbose logging
        """
        self.verbose = verbose
        self.llm = AzureChatOpenAI(
            azure_deployment=AZURE_OPENAI_DEPLOYMENT_NAME,
            openai_api_version=AZURE_OPENAI_API_VERSION,
            azure_endpoint=AZURE_OPENAI_ENDPOINT,
            openai_api_key=AZURE_OPENAI_KEY,
            temperature=0.1
        )
        self.schema_analyzer = DatabaseSchemaAnalyzer(verbose=verbose)
        
        if self.verbose:
            logger.info(f"Initialized SQLQueryGenerator with Azure OpenAI deployment: {AZURE_OPENAI_DEPLOYMENT_NAME}")
    
    def generate_sql(self, user_id: int, database_id: int, natural_query: str, 
                    session_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Generate SQL query from natural language with access control
        
        Args:
            user_id: ID of the user making the query
            database_id: ID of the target database
            natural_query: Natural language query
            session_id: Optional session ID for grouping queries
            
        Returns:
            Dictionary with generated SQL and metadata
        """
        try:
            # Check user access to database
            user_access = self._check_user_access(user_id, database_id)
            if not user_access:
                return {
                    'status': 'error',
                    'error': 'User does not have access to this database',
                    'sql': None
                }
            
            # Get database context
            db_context = self._build_database_context(database_id, natural_query)
            
            # Generate base SQL query
            base_sql = self._generate_base_sql(natural_query, db_context)
            
            if not base_sql:
                return {
                    'status': 'error',
                    'error': 'Could not generate SQL query from natural language',
                    'sql': None
                }
            
            # Apply role-based access controls
            final_sql = self._apply_access_controls(base_sql, user_access['roles'])
            
            # Validate SQL syntax and security
            validation_result = self._validate_sql(final_sql)
            if not validation_result['valid']:
                return {
                    'status': 'error',
                    'error': f"Generated SQL failed validation: {validation_result['error']}",
                    'sql': base_sql
                }
            
            # Store query history
            query_record = QueryHistory(
                user_id=user_id,
                database_id=database_id,
                session_id=session_id,
                natural_language_query=natural_query,
                generated_sql=base_sql,
                executed_sql=final_sql
            )
            
            return {
                'status': 'success',
                'sql': final_sql,
                'base_sql': base_sql,
                'explanation': self._explain_sql(final_sql, natural_query),
                'query_id': query_record.id if hasattr(query_record, 'id') else None,
                'tables_used': validation_result.get('tables_used', []),
                'estimated_rows': self._estimate_result_size(final_sql, database_id)
            }
            
        except Exception as e:
            logger.error(f"Error generating SQL query: {str(e)}")
            return {
                'status': 'error',
                'error': str(e),
                'sql': None
            }
    
    def _check_user_access(self, user_id: int, database_id: int) -> Optional[Dict[str, Any]]:
        """Check if user has access to the database and get their roles"""
        access_records = UserDatabaseAccess.query.filter_by(
            user_id=user_id,
            database_id=database_id,
            is_active=True
        ).all()
        
        if not access_records:
            return None
        
        roles = []
        for access in access_records:
            if access.role and access.role.is_active:
                roles.append({
                    'role_id': access.role.id,
                    'role_name': access.role.role_name,
                    'row_predicate': access.role.row_predicate,
                    'column_masks': access.role.column_masks,
                    'allowed_tables': access.role.allowed_tables,
                    'denied_tables': access.role.denied_tables
                })
        
        return {
            'has_access': len(roles) > 0,
            'roles': roles
        }
    
    def _build_database_context(self, database_id: int, natural_query: str) -> Dict[str, Any]:
        """Build context about the database for SQL generation"""
        # Extract keywords from natural query
        keywords = self._extract_keywords(natural_query)
        
        # Get suggested tables
        suggested_tables = self.schema_analyzer.suggest_tables_for_query(database_id, keywords)
        
        # Get detailed info for top suggested tables
        table_contexts = []
        for suggestion in suggested_tables[:5]:  # Limit to top 5 tables
            table_info = self.schema_analyzer.get_table_info(database_id, suggestion['table_name'])
            if table_info:
                table_contexts.append(table_info)
        
        # Get semantic mappings
        semantic_mappings = SemanticMapping.query.filter_by(database_id=database_id).all()
        
        mappings_context = []
        for mapping in semantic_mappings:
            if any(keyword.lower() in mapping.name.lower() for keyword in keywords):
                mappings_context.append({
                    'name': mapping.name,
                    'type': mapping.mapping_type,
                    'sql_expression': mapping.sql_expression,
                    'description': mapping.description
                })
        
        return {
            'suggested_tables': table_contexts,
            'semantic_mappings': mappings_context,
            'keywords': keywords
        }
    
    def _extract_keywords(self, natural_query: str) -> List[str]:
        """Extract meaningful keywords from natural language query"""
        # Remove common stop words and extract meaningful terms
        stop_words = {
            'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with',
            'by', 'from', 'up', 'about', 'into', 'through', 'during', 'before', 'after',
            'above', 'below', 'between', 'among', 'is', 'are', 'was', 'were', 'be', 'been',
            'being', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could',
            'should', 'may', 'might', 'must', 'can', 'show', 'get', 'find', 'give', 'tell'
        }
        
        # Extract words and filter
        words = re.findall(r'\b\w+\b', natural_query.lower())
        keywords = [word for word in words if word not in stop_words and len(word) > 2]
        
        return keywords
    
    def _generate_base_sql(self, natural_query: str, db_context: Dict[str, Any]) -> Optional[str]:
        """Generate base SQL query using LLM"""
        # Build context for LLM
        tables_context = ""
        for table in db_context['suggested_tables']:
            tables_context += f"\nTable: {table['table_name']}\n"
            tables_context += f"Description: {table['description']}\n"
            tables_context += f"Columns:\n"
            for col in table['columns']:
                tables_context += f"  - {col['name']} ({col['type']}): {col['description']}\n"
        
        mappings_context = ""
        for mapping in db_context['semantic_mappings']:
            mappings_context += f"\nBusiness Metric: {mapping['name']}\n"
            mappings_context += f"SQL: {mapping['sql_expression']}\n"
            mappings_context += f"Description: {mapping['description']}\n"
        
        system_prompt = """You are an expert SQL query generator. Generate a SQL query based on the natural language request and database schema provided.

IMPORTANT RULES:
1. Only use SELECT statements - no INSERT, UPDATE, DELETE, or DDL operations
2. Only use tables and columns that are provided in the schema
3. Use proper JOIN syntax when multiple tables are needed
4. Include appropriate WHERE clauses for filtering
5. Use aggregate functions (COUNT, SUM, AVG, etc.) when appropriate
6. Add ORDER BY and LIMIT clauses when relevant
7. Use semantic mappings when available for business metrics
8. Generate clean, readable SQL with proper formatting

Return only the SQL query without any explanation or markdown formatting."""
        
        user_prompt = f"""
Natural Language Query: {natural_query}

Available Tables and Columns:
{tables_context}

Available Business Metrics:
{mappings_context}

Generate a SQL query that answers the natural language question:"""
        
        try:
            messages = [
                SystemMessage(content=system_prompt),
                HumanMessage(content=user_prompt)
            ]
            
            response = self.llm.invoke(messages)
            sql_query = response.content.strip()
            
            # Clean up the SQL (remove markdown formatting if present)
            sql_query = re.sub(r'```sql\s*', '', sql_query)
            sql_query = re.sub(r'```\s*$', '', sql_query)
            sql_query = sql_query.strip()
            
            return sql_query
            
        except Exception as e:
            logger.error(f"Error generating base SQL: {str(e)}")
            return None
    
    def _apply_access_controls(self, base_sql: str, roles: List[Dict[str, Any]]) -> str:
        """Apply role-based access controls to the SQL query"""
        if not roles:
            return base_sql
        
        # For now, apply the most restrictive role
        # In a more sophisticated implementation, you might combine multiple roles
        primary_role = roles[0]
        
        try:
            # Parse SQL to understand structure
            parsed = sqlglot.parse_one(base_sql, dialect="postgres")
            
            # Apply row predicates
            if primary_role.get('row_predicate'):
                # This is a simplified implementation
                # In practice, you'd need more sophisticated SQL manipulation
                if 'WHERE' in base_sql.upper():
                    modified_sql = base_sql.replace(
                        'WHERE', 
                        f"WHERE ({primary_role['row_predicate']}) AND"
                    )
                else:
                    # Add WHERE clause
                    modified_sql = base_sql + f" WHERE {primary_role['row_predicate']}"
                
                return modified_sql
            
            return base_sql
            
        except Exception as e:
            logger.warning(f"Could not apply access controls: {str(e)}")
            return base_sql
    
    def _validate_sql(self, sql: str) -> Dict[str, Any]:
        """Validate SQL query for syntax and security"""
        try:
            # Parse SQL to check syntax
            parsed = sqlglot.parse_one(sql, dialect="postgres")
            
            if not parsed:
                return {'valid': False, 'error': 'Invalid SQL syntax'}
            
            # Check for forbidden operations
            forbidden_keywords = ['INSERT', 'UPDATE', 'DELETE', 'DROP', 'CREATE', 'ALTER', 'TRUNCATE']
            sql_upper = sql.upper()
            
            for keyword in forbidden_keywords:
                if keyword in sql_upper:
                    return {'valid': False, 'error': f'Forbidden operation: {keyword}'}
            
            # Extract table names
            tables_used = []
            if hasattr(parsed, 'find_all'):
                for table in parsed.find_all(sqlglot.expressions.Table):
                    tables_used.append(table.name)
            
            return {
                'valid': True,
                'tables_used': tables_used
            }
            
        except Exception as e:
            return {'valid': False, 'error': str(e)}
    
    def _explain_sql(self, sql: str, natural_query: str) -> str:
        """Generate explanation of the SQL query"""
        system_prompt = """You are a SQL expert. Explain what the given SQL query does in simple, business-friendly language. Focus on:
1. What data is being retrieved
2. From which tables
3. What filters or conditions are applied
4. How the results are organized

Keep the explanation concise and understandable to non-technical users."""
        
        user_prompt = f"""
Original Question: {natural_query}

SQL Query:
{sql}

Explain what this SQL query does:"""
        
        try:
            messages = [
                SystemMessage(content=system_prompt),
                HumanMessage(content=user_prompt)
            ]
            
            response = self.llm.invoke(messages)
            return response.content.strip()
            
        except Exception as e:
            logger.error(f"Error generating SQL explanation: {str(e)}")
            return "This query retrieves data from the database based on your request."
    
    def _estimate_result_size(self, sql: str, database_id: int) -> Optional[int]:
        """Estimate the number of rows the query might return"""
        # This is a simplified estimation
        # In practice, you might use EXPLAIN PLAN or other database-specific features
        try:
            # Extract main table from SQL
            parsed = sqlglot.parse_one(sql, dialect="postgres")
            
            if hasattr(parsed, 'find'):
                table_expr = parsed.find(sqlglot.expressions.Table)
                if table_expr:
                    table_name = table_expr.name
                    
                    # Get table row count
                    schema = DatabaseSchema.query.filter_by(
                        database_id=database_id,
                        table_name=table_name
                    ).first()
                    
                    if schema:
                        # Rough estimation: assume query returns 10% of table rows
                        return int(schema.row_count * 0.1) if schema.row_count else None
            
            return None
            
        except Exception as e:
            logger.warning(f"Could not estimate result size: {str(e)}")
            return None
