"""
State Management for SQL Chat Agent

This module defines the state structure for the LangGraph-based SQL chat agent.
"""

from typing import Dict, List, Any, Optional, TypedDict, Annotated
import operator
from langchain_core.messages import AnyMessage


class SQLChatState(TypedDict):
    """State for the SQL Chat Agent using LangGraph"""
    
    # Core conversation data
    messages: Annotated[List[AnyMessage], operator.add]
    user_id: int
    database_id: int
    session_id: Optional[str]
    
    # User query and processing
    natural_query: str
    processed_query: str
    query_intent: str
    query_keywords: List[str]
    
    # Database context
    database_info: Optional[Dict[str, Any]]
    suggested_tables: List[Dict[str, Any]]
    table_relationships: List[Dict[str, Any]]
    semantic_mappings: List[Dict[str, Any]]
    
    # User access and permissions
    user_roles: List[Dict[str, Any]]
    access_permissions: Dict[str, Any]
    
    # SQL generation
    generated_sql: Optional[str]
    final_sql: Optional[str]
    sql_explanation: Optional[str]
    estimated_rows: Optional[int]
    
    # Query execution
    execution_result: Optional[Dict[str, Any]]
    result_data: Optional[List[Dict[str, Any]]]
    execution_time: Optional[float]
    error_message: Optional[str]
    
    # Quality assurance
    qa_score: Optional[float]
    qa_feedback: Optional[str]
    validation_errors: List[str]
    
    # Conversation history and context
    conversation_history: Optional[str]
    previous_queries: List[Dict[str, Any]]
    
    # Training and feedback
    feedback_data: Optional[Dict[str, Any]]
    training_suggestions: List[str]
    
    # Visualization and output
    requires_visualization: bool
    chart_type: Optional[str]
    chart_data: Optional[Dict[str, Any]]
    
    # Agent flow control
    current_step: str
    next_action: str
    retry_count: int
    max_retries: int
    
    # Error handling
    has_error: bool
    error_type: Optional[str]
    recovery_suggestions: List[str]
