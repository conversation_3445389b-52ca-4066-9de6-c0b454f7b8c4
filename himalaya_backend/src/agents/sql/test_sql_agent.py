"""
Test Script for SQL Chat Agent

This script demonstrates the basic functionality of the SQL Chat Module.
Run this to test the system components.
"""

import os
import sys
import json
from datetime import datetime

# Add parent directories to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from agents.sql.sql_planner import SQLPlanner
from agents.sql.visualization_agent import VisualizationAgent
from agents.sql.schema_analyzer import DatabaseSchemaAnalyzer
from agents.sql.role_manager import RoleManager
from agents.sql.db_manager import DatabaseManager
from agents.sql.session_manager import SQLSessionManager


def test_sql_planner():
    """Test the SQL Planner functionality"""
    print("=" * 50)
    print("Testing SQL Planner")
    print("=" * 50)
    
    planner = SQLPlanner(verbose=True)
    
    # Test query planning
    result = planner.plan_query(
        user_query="Show me the top 10 customers by sales amount",
        database_id=1,
        conversation_history="Previous query about customer data",
        qa_feedback=None
    )
    
    print("Planning Result:")
    print(json.dumps(result, indent=2))
    print()


def test_visualization_agent():
    """Test the Visualization Agent functionality"""
    print("=" * 50)
    print("Testing Visualization Agent")
    print("=" * 50)
    
    viz_agent = VisualizationAgent(verbose=True)
    
    # Sample data
    sample_data = [
        {"region": "North", "sales": 150000, "customers": 45},
        {"region": "South", "sales": 120000, "customers": 38},
        {"region": "East", "sales": 180000, "customers": 52},
        {"region": "West", "sales": 95000, "customers": 29}
    ]
    
    # Test bar chart
    chart_config = {
        "chart_type": "bar",
        "customizations": {
            "title": "Sales by Region",
            "x_column": "region",
            "y_column": "sales",
            "colors": ["#3498db", "#e74c3c", "#2ecc71", "#f39c12"]
        }
    }
    
    result = viz_agent.generate_chart(sample_data, chart_config)
    
    print("Bar Chart Generation Result:")
    print(f"Status: {result['status']}")
    print(f"Chart Type: {result.get('chart_type')}")
    print(f"Data Points: {result.get('data_points')}")
    print(f"HTML Length: {len(result.get('html', ''))}")
    print()
    
    # Test pie chart
    chart_config = {
        "chart_type": "pie",
        "customizations": {
            "title": "Customer Distribution",
            "label_column": "region",
            "value_column": "customers"
        }
    }
    
    result = viz_agent.generate_chart(sample_data, chart_config)
    
    print("Pie Chart Generation Result:")
    print(f"Status: {result['status']}")
    print(f"Chart Type: {result.get('chart_type')}")
    print(f"Data Points: {result.get('data_points')}")
    print()


def test_role_manager():
    """Test the Role Manager functionality"""
    print("=" * 50)
    print("Testing Role Manager")
    print("=" * 50)
    
    role_manager = RoleManager(verbose=True)
    
    # Test role creation (simulation)
    role_data = {
        "role_name": "Sales_US",
        "description": "Sales team access for US region only",
        "row_predicate": "region = 'US'",
        "column_masks": {
            "customer_ssn": "MASK",
            "customer_phone": "PARTIAL"
        },
        "allowed_tables": ["customers", "orders", "products"],
        "denied_tables": ["employee_salaries", "financial_reports"]
    }
    
    print("Sample Role Configuration:")
    print(json.dumps(role_data, indent=2))
    print()
    
    # Note: Actual database operations would require a real database connection
    print("Role Manager initialized successfully")
    print("Database operations would require actual database connection")
    print()


def test_database_manager():
    """Test the Database Manager functionality"""
    print("=" * 50)
    print("Testing Database Manager")
    print("=" * 50)
    
    db_manager = DatabaseManager(verbose=True)
    
    # Test connection configuration
    connection_data = {
        "name": "Sample PostgreSQL DB",
        "description": "Sample database for testing",
        "connection_type": "postgresql",
        "host": "localhost",
        "port": 5432,
        "database_name": "sample_db",
        "username": "sample_user",
        "password": "sample_password"
    }
    
    print("Sample Database Connection Configuration:")
    print(json.dumps({k: v if k != 'password' else '***' for k, v in connection_data.items()}, indent=2))
    print()
    
    print("Database Manager initialized successfully")
    print("Encryption key generated for credential protection")
    print()


def test_schema_analyzer():
    """Test the Schema Analyzer functionality"""
    print("=" * 50)
    print("Testing Schema Analyzer")
    print("=" * 50)

    analyzer = DatabaseSchemaAnalyzer(verbose=True)

    # Test table suggestion
    keywords = ["customer", "sales", "revenue"]

    print(f"Testing table suggestions for keywords: {keywords}")
    print("Schema Analyzer initialized successfully")
    print("Table suggestion would require actual database schema data")
    print()


def test_session_manager():
    """Test the SQL Session Manager functionality"""
    print("=" * 50)
    print("Testing SQL Session Manager")
    print("=" * 50)

    session_manager = SQLSessionManager(verbose=True)

    # Test session creation (simulation)
    session_data = {
        "user_id": 1,
        "database_id": 1,
        "session_name": "Test SQL Session",
        "session_description": "Testing session management functionality"
    }

    print("Sample Session Configuration:")
    print(json.dumps(session_data, indent=2))
    print()

    print("SQL Session Manager initialized successfully")
    print("Session operations would require actual database connection")
    print("Features tested:")
    print("- Session creation and management")
    print("- Conversation history tracking")
    print("- Session activity updates")
    print("- User access control")
    print()


def run_integration_test():
    """Run a simulated integration test"""
    print("=" * 50)
    print("Integration Test - Simulated SQL Chat Flow")
    print("=" * 50)
    
    # Simulate a complete workflow
    user_query = "Show me the top 5 products by sales in the last quarter"
    
    print(f"User Query: {user_query}")
    print()
    
    # Step 1: Planning
    print("Step 1: Query Planning")
    planner = SQLPlanner(verbose=False)
    planning_result = planner.plan_query(
        user_query=user_query,
        database_id=1
    )
    print(f"Query Intent: {planning_result.get('query_intent')}")
    print(f"Requires Visualization: {planning_result.get('requires_visualization')}")
    print(f"Chart Type: {planning_result.get('chart_type')}")
    print()
    
    # Step 2: Simulated SQL Generation
    print("Step 2: SQL Generation (Simulated)")
    simulated_sql = """
    SELECT 
        p.product_name,
        SUM(o.quantity * o.unit_price) as total_sales
    FROM products p
    JOIN order_items o ON p.product_id = o.product_id
    JOIN orders ord ON o.order_id = ord.order_id
    WHERE ord.order_date >= DATE_SUB(CURRENT_DATE, INTERVAL 3 MONTH)
    GROUP BY p.product_id, p.product_name
    ORDER BY total_sales DESC
    LIMIT 5
    """
    print(f"Generated SQL: {simulated_sql.strip()}")
    print()
    
    # Step 3: Simulated Data
    print("Step 3: Query Execution (Simulated)")
    simulated_data = [
        {"product_name": "Laptop Pro", "total_sales": 125000},
        {"product_name": "Smartphone X", "total_sales": 98000},
        {"product_name": "Tablet Plus", "total_sales": 76000},
        {"product_name": "Headphones", "total_sales": 45000},
        {"product_name": "Smart Watch", "total_sales": 38000}
    ]
    print(f"Retrieved {len(simulated_data)} rows")
    print()
    
    # Step 4: Visualization
    print("Step 4: Visualization Generation")
    viz_agent = VisualizationAgent(verbose=False)
    chart_config = {
        "chart_type": "bar",
        "customizations": {
            "title": "Top 5 Products by Sales",
            "x_column": "product_name",
            "y_column": "total_sales"
        }
    }
    
    viz_result = viz_agent.generate_chart(simulated_data, chart_config)
    print(f"Chart Status: {viz_result['status']}")
    print(f"Chart Type: {viz_result.get('chart_type')}")
    print(f"HTML Chart Generated: {len(viz_result.get('html', ''))} characters")
    print()
    
    # Step 5: Final Response
    print("Step 5: Final Response")
    response = {
        "message": "Here are the top 5 products by sales in the last quarter. The data shows that Laptop Pro leads with $125,000 in sales.",
        "sql": simulated_sql.strip(),
        "data": simulated_data,
        "chart_html": viz_result.get('html', ''),
        "execution_time": 0.45,
        "row_count": len(simulated_data)
    }
    
    print("Response generated successfully!")
    print(f"Message: {response['message']}")
    print(f"Data rows: {response['row_count']}")
    print(f"Execution time: {response['execution_time']}s")
    print()


def main():
    """Run all tests"""
    print("SQL Chat Module Test Suite")
    print("=" * 50)
    print(f"Test started at: {datetime.now()}")
    print()
    
    try:
        # Run individual component tests
        test_sql_planner()
        test_visualization_agent()
        test_role_manager()
        test_database_manager()
        test_schema_analyzer()
        test_session_manager()
        
        # Run integration test
        run_integration_test()
        
        print("=" * 50)
        print("All tests completed successfully!")
        print("=" * 50)
        
    except Exception as e:
        print(f"Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
