"""
Database Memory Service

This service manages semantic summaries and memory for databases,
inspired by MemGPT's editable memory approach. It provides concise,
maintainable database understanding for SQL generation.
"""

import os
import sys
import logging
import json
from typing import Dict, List, Any, Optional
from datetime import datetime

# Add parent directories to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from config.settings import AZURE_OPENAI_KEY, AZURE_OPENAI_API_VERSION, AZURE_OPENAI_ENDPOINT, AZURE_OPENAI_DEPLOYMENT_NAME
from models.models import (
    db, DatabaseConnection, DatabaseSchema, DatabaseColumn, 
    DatabaseSemanticSummary, SemanticMapping
)
from langchain_openai import AzureChatOpenAI
from langchain_core.messages import SystemMessage, HumanMessage

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class DatabaseMemoryService:
    """Service for managing database semantic memory and summaries"""
    
    def __init__(self, verbose: bool = True):
        """
        Initialize the database memory service
        
        Args:
            verbose: Whether to enable verbose logging
        """
        self.verbose = verbose
        self.llm = AzureChatOpenAI(
            azure_deployment=AZURE_OPENAI_DEPLOYMENT_NAME,
            openai_api_version=AZURE_OPENAI_API_VERSION,
            azure_endpoint=AZURE_OPENAI_ENDPOINT,
            openai_api_key=AZURE_OPENAI_KEY,
            temperature=0.1
        )
        
        if self.verbose:
            logger.info("Initialized DatabaseMemoryService")
    
    def generate_database_summary(self, database_id: int, user_id: int) -> Dict[str, Any]:
        """
        Generate a concise semantic summary for a database (max 2000 tokens)
        
        Args:
            database_id: ID of the database
            user_id: ID of the user generating the summary
            
        Returns:
            Dictionary with summary generation results
        """
        try:
            # Get database connection info
            db_conn = DatabaseConnection.query.get(database_id)
            if not db_conn:
                raise ValueError(f"Database {database_id} not found")
            
            # Perform EDA and schema analysis
            eda_insights = self._perform_eda_analysis(database_id)
            schema_overview = self._generate_schema_overview(database_id)
            business_context = self._infer_business_context(database_id, eda_insights, schema_overview)
            
            # Generate concise summary using LLM
            summary_text = self._generate_llm_summary(
                db_conn.name, schema_overview, eda_insights, business_context
            )
            
            # Count tokens (approximate)
            token_count = len(summary_text.split()) * 1.3  # Rough estimate
            
            # Create or update semantic summary
            existing_summary = DatabaseSemanticSummary.query.filter_by(database_id=database_id).first()
            
            if existing_summary:
                existing_summary.summary_text = summary_text
                existing_summary.eda_insights = eda_insights
                existing_summary.schema_overview = schema_overview
                existing_summary.business_domain = business_context.get('domain')
                existing_summary.key_entities = business_context.get('entities', [])
                existing_summary.common_patterns = business_context.get('patterns', [])
                existing_summary.token_count = int(token_count)
                existing_summary.last_updated_by = user_id
                existing_summary.version += 1
                existing_summary.updated_at = datetime.utcnow()
            else:
                new_summary = DatabaseSemanticSummary(
                    database_id=database_id,
                    summary_text=summary_text,
                    eda_insights=eda_insights,
                    schema_overview=schema_overview,
                    business_domain=business_context.get('domain'),
                    key_entities=business_context.get('entities', []),
                    common_patterns=business_context.get('patterns', []),
                    token_count=int(token_count),
                    last_updated_by=user_id
                )
                db.session.add(new_summary)
            
            db.session.commit()
            
            if self.verbose:
                logger.info(f"Generated semantic summary for database {database_id} ({int(token_count)} tokens)")
            
            return {
                'status': 'success',
                'summary_text': summary_text,
                'token_count': int(token_count),
                'business_domain': business_context.get('domain'),
                'key_entities': business_context.get('entities', [])
            }
            
        except Exception as e:
            logger.error(f"Error generating database summary: {str(e)}")
            return {
                'status': 'error',
                'error': str(e)
            }
    
    def get_database_memory(self, database_id: int) -> Optional[Dict[str, Any]]:
        """
        Get the semantic memory for a database
        
        Args:
            database_id: ID of the database
            
        Returns:
            Dictionary with database memory or None if not found
        """
        summary = DatabaseSemanticSummary.query.filter_by(database_id=database_id).first()
        
        if not summary:
            return None
        
        return {
            'summary_text': summary.summary_text,
            'eda_insights': summary.eda_insights,
            'schema_overview': summary.schema_overview,
            'business_domain': summary.business_domain,
            'key_entities': summary.key_entities,
            'common_patterns': summary.common_patterns,
            'token_count': summary.token_count,
            'version': summary.version,
            'last_updated': summary.updated_at.isoformat() if summary.updated_at else None
        }
    
    def update_database_memory(self, database_id: int, updates: Dict[str, Any], user_id: int) -> Dict[str, Any]:
        """
        Update specific parts of database memory (MemGPT-style editing)
        
        Args:
            database_id: ID of the database
            updates: Dictionary with fields to update
            user_id: ID of the user making updates
            
        Returns:
            Dictionary with update results
        """
        try:
            summary = DatabaseSemanticSummary.query.filter_by(database_id=database_id).first()
            
            if not summary:
                return {
                    'status': 'error',
                    'error': 'Database summary not found. Generate summary first.'
                }
            
            # Update allowed fields
            updated_fields = []
            
            if 'business_domain' in updates:
                summary.business_domain = updates['business_domain']
                updated_fields.append('business_domain')
            
            if 'key_entities' in updates:
                summary.key_entities = updates['key_entities']
                updated_fields.append('key_entities')
            
            if 'common_patterns' in updates:
                summary.common_patterns = updates['common_patterns']
                updated_fields.append('common_patterns')
            
            if 'summary_text' in updates:
                summary.summary_text = updates['summary_text']
                summary.token_count = len(updates['summary_text'].split()) * 1.3
                updated_fields.append('summary_text')
            
            # Update metadata
            summary.last_updated_by = user_id
            summary.version += 1
            summary.updated_at = datetime.utcnow()
            
            db.session.commit()
            
            if self.verbose:
                logger.info(f"Updated database memory for {database_id}: {updated_fields}")
            
            return {
                'status': 'success',
                'updated_fields': updated_fields,
                'version': summary.version
            }
            
        except Exception as e:
            logger.error(f"Error updating database memory: {str(e)}")
            return {
                'status': 'error',
                'error': str(e)
            }
    
    def _perform_eda_analysis(self, database_id: int) -> Dict[str, Any]:
        """Perform exploratory data analysis on the database"""
        schemas = DatabaseSchema.query.filter_by(database_id=database_id).all()
        
        total_tables = len(schemas)
        total_rows = sum(schema.row_count or 0 for schema in schemas)
        
        # Analyze table sizes and types
        large_tables = []
        small_tables = []
        
        for schema in schemas:
            row_count = schema.row_count or 0
            if row_count > 100000:
                large_tables.append({'name': schema.table_name, 'rows': row_count})
            elif row_count < 1000:
                small_tables.append({'name': schema.table_name, 'rows': row_count})
        
        # Analyze column types across database
        column_type_distribution = {}
        for schema in schemas:
            columns = DatabaseColumn.query.filter_by(schema_id=schema.id).all()
            for column in columns:
                col_type = column.data_type.lower()
                column_type_distribution[col_type] = column_type_distribution.get(col_type, 0) + 1
        
        return {
            'total_tables': total_tables,
            'total_rows': total_rows,
            'large_tables': large_tables[:5],  # Top 5 largest
            'small_tables': small_tables[:5],  # Top 5 smallest
            'column_type_distribution': column_type_distribution,
            'avg_rows_per_table': total_rows / total_tables if total_tables > 0 else 0
        }
    
    def _generate_schema_overview(self, database_id: int) -> Dict[str, Any]:
        """Generate a simplified schema overview"""
        schemas = DatabaseSchema.query.filter_by(database_id=database_id).all()
        
        tables = []
        relationships = []
        
        for schema in schemas:
            columns = DatabaseColumn.query.filter_by(schema_id=schema.id).all()
            
            # Get key columns
            primary_keys = [col.column_name for col in columns if col.is_primary_key]
            foreign_keys = []
            
            for col in columns:
                if col.is_foreign_key:
                    foreign_keys.append({
                        'column': col.column_name,
                        'references': f"{col.foreign_key_table}.{col.foreign_key_column}"
                    })
                    relationships.append({
                        'from': schema.table_name,
                        'to': col.foreign_key_table,
                        'type': 'foreign_key'
                    })
            
            tables.append({
                'name': schema.table_name,
                'description': schema.llm_generated_description,
                'column_count': len(columns),
                'row_count': schema.row_count,
                'primary_keys': primary_keys,
                'foreign_keys': foreign_keys
            })
        
        return {
            'tables': tables,
            'relationships': relationships,
            'total_tables': len(tables),
            'total_relationships': len(relationships)
        }

    def _infer_business_context(self, database_id: int, eda_insights: Dict[str, Any],
                              schema_overview: Dict[str, Any]) -> Dict[str, Any]:
        """Infer business context from database structure"""
        table_names = [table['name'].lower() for table in schema_overview['tables']]

        # Domain inference based on table names
        domain_indicators = {
            'ecommerce': ['products', 'orders', 'customers', 'cart', 'inventory', 'payments'],
            'finance': ['accounts', 'transactions', 'payments', 'invoices', 'billing'],
            'healthcare': ['patients', 'doctors', 'appointments', 'medical', 'diagnosis'],
            'hr': ['employees', 'departments', 'payroll', 'attendance', 'performance'],
            'education': ['students', 'courses', 'grades', 'teachers', 'enrollment'],
            'crm': ['leads', 'contacts', 'opportunities', 'campaigns', 'sales']
        }

        domain_scores = {}
        for domain, indicators in domain_indicators.items():
            score = sum(1 for indicator in indicators if any(indicator in table for table in table_names))
            if score > 0:
                domain_scores[domain] = score

        # Determine primary domain
        primary_domain = max(domain_scores.keys(), key=lambda k: domain_scores[k]) if domain_scores else 'general'

        # Extract key entities (main business objects)
        key_entities = []
        entity_patterns = ['users', 'customers', 'products', 'orders', 'transactions', 'accounts']
        for pattern in entity_patterns:
            matching_tables = [table for table in table_names if pattern in table]
            if matching_tables:
                key_entities.extend(matching_tables[:2])  # Limit to 2 per pattern

        # Common query patterns based on domain
        common_patterns = self._get_domain_patterns(primary_domain)

        return {
            'domain': primary_domain,
            'domain_confidence': domain_scores.get(primary_domain, 0),
            'entities': key_entities[:10],  # Limit to top 10
            'patterns': common_patterns
        }

    def _get_domain_patterns(self, domain: str) -> List[str]:
        """Get common query patterns for a business domain"""
        patterns = {
            'ecommerce': [
                'Customer order history and purchase patterns',
                'Product sales performance and inventory levels',
                'Revenue analysis by time period and category',
                'Customer segmentation and lifetime value'
            ],
            'finance': [
                'Account balance and transaction history',
                'Payment processing and reconciliation',
                'Financial reporting and compliance',
                'Risk analysis and fraud detection'
            ],
            'healthcare': [
                'Patient medical history and treatment records',
                'Appointment scheduling and provider availability',
                'Billing and insurance claim processing',
                'Clinical outcomes and quality metrics'
            ],
            'hr': [
                'Employee performance and attendance tracking',
                'Payroll and compensation analysis',
                'Department staffing and resource allocation',
                'Training and development progress'
            ],
            'general': [
                'Data aggregation and summary statistics',
                'Time-based trend analysis',
                'Entity relationship exploration',
                'Performance and operational metrics'
            ]
        }

        return patterns.get(domain, patterns['general'])

    def _generate_llm_summary(self, db_name: str, schema_overview: Dict[str, Any],
                             eda_insights: Dict[str, Any], business_context: Dict[str, Any]) -> str:
        """Generate a concise LLM summary of the database"""

        system_prompt = """You are a database expert creating concise semantic summaries.
        Generate a comprehensive but concise summary (max 2000 tokens) that includes:

        1. Database purpose and business domain
        2. Key entities and their relationships
        3. Data scale and characteristics
        4. Common use cases and query patterns
        5. Important business rules or constraints

        Focus on information that would help an AI agent understand how to query this database effectively.
        Be specific about table relationships and business logic."""

        # Prepare context information
        table_info = []
        for table in schema_overview['tables'][:10]:  # Limit to top 10 tables
            table_info.append(f"- {table['name']}: {table.get('description', 'No description')} "
                            f"({table['column_count']} columns, {table.get('row_count', 0)} rows)")

        user_prompt = f"""
Database Name: {db_name}
Business Domain: {business_context['domain']} (confidence: {business_context['domain_confidence']})

Schema Overview:
- Total Tables: {schema_overview['total_tables']}
- Total Relationships: {schema_overview['total_relationships']}
- Total Rows: {eda_insights['total_rows']:,}

Key Tables:
{chr(10).join(table_info)}

Key Business Entities: {', '.join(business_context['entities'])}

Data Characteristics:
- Average rows per table: {eda_insights['avg_rows_per_table']:.0f}
- Large tables (>100K rows): {len(eda_insights['large_tables'])}
- Small tables (<1K rows): {len(eda_insights['small_tables'])}

Common Query Patterns for {business_context['domain']}:
{chr(10).join(f"- {pattern}" for pattern in business_context['patterns'])}

Generate a concise semantic summary that captures the essence of this database:"""

        try:
            messages = [
                SystemMessage(content=system_prompt),
                HumanMessage(content=user_prompt)
            ]

            response = self.llm.invoke(messages)
            return response.content.strip()

        except Exception as e:
            logger.error(f"Error generating LLM summary: {str(e)}")
            # Fallback to basic summary
            return self._generate_fallback_summary(db_name, schema_overview, eda_insights, business_context)

    def _generate_fallback_summary(self, db_name: str, schema_overview: Dict[str, Any],
                                  eda_insights: Dict[str, Any], business_context: Dict[str, Any]) -> str:
        """Generate a basic fallback summary if LLM fails"""

        summary_parts = [
            f"Database: {db_name}",
            f"Domain: {business_context['domain']}",
            f"Contains {schema_overview['total_tables']} tables with {eda_insights['total_rows']:,} total rows.",
            f"Key entities: {', '.join(business_context['entities'][:5])}.",
            f"Primary relationships: {schema_overview['total_relationships']} foreign key connections.",
            f"Supports typical {business_context['domain']} operations including data analysis and reporting."
        ]

        return " ".join(summary_parts)
