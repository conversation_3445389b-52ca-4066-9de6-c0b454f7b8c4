"""
Training Service for SQL Chat

This service handles the training and feedback loop for improving
SQL generation and database understanding.
"""

import os
import sys
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime

# Add parent directories to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from config.settings import AZURE_OPENAI_KEY, AZURE_OPENAI_API_VERSION, AZURE_OPENAI_ENDPOINT, AZURE_OPENAI_DEPLOYMENT_NAME
from models.models import (
    db, DatabaseConnection, DatabaseSchema, DatabaseColumn, 
    TrainingFeedback, QueryHistory, SemanticMapping
)
from langchain_openai import AzureChatOpenAI
from langchain_core.messages import SystemMessage, HumanMessage

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class TrainingService:
    """Service for handling training feedback and improving the system"""
    
    def __init__(self, verbose: bool = True):
        """
        Initialize the training service
        
        Args:
            verbose: Whether to enable verbose logging
        """
        self.verbose = verbose
        self.llm = AzureChatOpenAI(
            azure_deployment=AZURE_OPENAI_DEPLOYMENT_NAME,
            openai_api_version=AZURE_OPENAI_API_VERSION,
            azure_endpoint=AZURE_OPENAI_ENDPOINT,
            openai_api_key=AZURE_OPENAI_KEY,
            temperature=0.1
        )
        
        if self.verbose:
            logger.info(f"Initialized TrainingService with Azure OpenAI deployment: {AZURE_OPENAI_DEPLOYMENT_NAME}")
    
    def submit_feedback(self, user_id: int, database_id: int, feedback_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Submit training feedback for a query or general database understanding
        
        Args:
            user_id: ID of the user providing feedback
            database_id: ID of the database
            feedback_data: Dictionary containing feedback information
            
        Returns:
            Dictionary with feedback submission result
        """
        try:
            feedback_type = feedback_data.get('type', 'sql_correction')
            
            # Create feedback record
            feedback = TrainingFeedback(
                query_history_id=feedback_data.get('query_id'),
                database_id=database_id,
                user_id=user_id,
                feedback_type=feedback_type,
                original_query=feedback_data.get('original_query'),
                original_sql=feedback_data.get('original_sql'),
                corrected_sql=feedback_data.get('corrected_sql'),
                explanation=feedback_data.get('explanation'),
                semantic_updates=feedback_data.get('semantic_updates'),
                schema_updates=feedback_data.get('schema_updates')
            )
            
            db.session.add(feedback)
            db.session.commit()
            
            # Process feedback immediately if it's a simple correction
            if feedback_type in ['sql_correction', 'semantic_clarification']:
                self._process_feedback(feedback.id)
            
            if self.verbose:
                logger.info(f"Submitted feedback of type '{feedback_type}' for database {database_id}")
            
            return {
                'status': 'success',
                'feedback_id': feedback.id,
                'message': 'Feedback submitted successfully'
            }
            
        except Exception as e:
            logger.error(f"Error submitting feedback: {str(e)}")
            return {
                'status': 'error',
                'error': str(e)
            }
    
    def process_pending_feedback(self, database_id: Optional[int] = None) -> Dict[str, Any]:
        """
        Process all pending feedback for a database or all databases
        
        Args:
            database_id: Optional database ID to filter feedback
            
        Returns:
            Dictionary with processing results
        """
        try:
            # Get pending feedback
            query = TrainingFeedback.query.filter_by(is_processed=False)
            if database_id:
                query = query.filter_by(database_id=database_id)
            
            pending_feedback = query.all()
            
            processed_count = 0
            errors = []
            
            for feedback in pending_feedback:
                try:
                    self._process_feedback(feedback.id)
                    processed_count += 1
                except Exception as e:
                    errors.append(f"Error processing feedback {feedback.id}: {str(e)}")
            
            if self.verbose:
                logger.info(f"Processed {processed_count} feedback items with {len(errors)} errors")
            
            return {
                'status': 'success',
                'processed_count': processed_count,
                'errors': errors
            }
            
        except Exception as e:
            logger.error(f"Error processing pending feedback: {str(e)}")
            return {
                'status': 'error',
                'error': str(e)
            }
    
    def _process_feedback(self, feedback_id: int):
        """Process a specific feedback item"""
        feedback = TrainingFeedback.query.get(feedback_id)
        if not feedback:
            raise ValueError(f"Feedback {feedback_id} not found")
        
        if feedback.feedback_type == 'sql_correction':
            self._process_sql_correction(feedback)
        elif feedback.feedback_type == 'semantic_clarification':
            self._process_semantic_clarification(feedback)
        elif feedback.feedback_type == 'schema_update':
            self._process_schema_update(feedback)
        
        # Mark as processed
        feedback.is_processed = True
        feedback.processed_at = datetime.utcnow()
        db.session.commit()
    
    def _process_sql_correction(self, feedback: TrainingFeedback):
        """Process SQL correction feedback"""
        if not feedback.corrected_sql:
            return
        
        # Analyze the correction to understand what was wrong
        analysis = self._analyze_sql_correction(
            feedback.original_query,
            feedback.original_sql,
            feedback.corrected_sql,
            feedback.explanation
        )
        
        # Create or update semantic mappings based on the correction
        if analysis.get('create_mapping'):
            mapping_data = analysis['create_mapping']
            
            # Check if mapping already exists
            existing_mapping = SemanticMapping.query.filter_by(
                database_id=feedback.database_id,
                name=mapping_data['name']
            ).first()
            
            if existing_mapping:
                # Update existing mapping
                existing_mapping.sql_expression = mapping_data['sql_expression']
                existing_mapping.description = mapping_data['description']
                existing_mapping.version += 1
            else:
                # Create new mapping
                new_mapping = SemanticMapping(
                    database_id=feedback.database_id,
                    mapping_type='correction',
                    name=mapping_data['name'],
                    sql_expression=mapping_data['sql_expression'],
                    description=mapping_data['description'],
                    created_by=feedback.user_id
                )
                db.session.add(new_mapping)
        
        # Update column descriptions if needed
        if analysis.get('update_columns'):
            for column_update in analysis['update_columns']:
                self._update_column_description(
                    feedback.database_id,
                    column_update['table'],
                    column_update['column'],
                    column_update['description']
                )
    
    def _process_semantic_clarification(self, feedback: TrainingFeedback):
        """Process semantic clarification feedback"""
        if not feedback.semantic_updates:
            return
        
        for update in feedback.semantic_updates:
            if update['type'] == 'table_description':
                self._update_table_description(
                    feedback.database_id,
                    update['table_name'],
                    update['description']
                )
            elif update['type'] == 'column_description':
                self._update_column_description(
                    feedback.database_id,
                    update['table_name'],
                    update['column_name'],
                    update['description']
                )
            elif update['type'] == 'business_metric':
                self._create_business_metric(
                    feedback.database_id,
                    update['name'],
                    update['sql_expression'],
                    update['description'],
                    feedback.user_id
                )
    
    def _process_schema_update(self, feedback: TrainingFeedback):
        """Process schema update feedback"""
        if not feedback.schema_updates:
            return
        
        for update in feedback.schema_updates:
            if update['type'] == 'missing_relationship':
                self._add_missing_relationship(
                    feedback.database_id,
                    update['source_table'],
                    update['source_column'],
                    update['target_table'],
                    update['target_column']
                )
    
    def _analyze_sql_correction(self, original_query: str, original_sql: str, 
                              corrected_sql: str, explanation: str) -> Dict[str, Any]:
        """Analyze SQL correction to extract learning insights"""
        system_prompt = """You are a SQL expert analyzing corrections to learn from mistakes. 
        Analyze the original query, generated SQL, corrected SQL, and explanation to identify:
        1. What semantic mappings should be created
        2. What column descriptions need updating
        3. What table relationships were missed
        
        Return a JSON object with your analysis."""
        
        user_prompt = f"""
Original Natural Language Query: {original_query}

Original Generated SQL:
{original_sql}

Corrected SQL:
{corrected_sql}

Explanation: {explanation}

Analyze this correction and suggest improvements to the system's understanding:"""
        
        try:
            messages = [
                SystemMessage(content=system_prompt),
                HumanMessage(content=user_prompt)
            ]
            
            response = self.llm.invoke(messages)
            
            # Parse the response (simplified - in practice you'd want more robust parsing)
            analysis_text = response.content.strip()
            
            # For now, return a basic analysis structure
            # In practice, you'd parse the LLM response more carefully
            return {
                'analysis_text': analysis_text,
                'create_mapping': None,  # Would be extracted from LLM response
                'update_columns': [],    # Would be extracted from LLM response
                'missing_relationships': []  # Would be extracted from LLM response
            }
            
        except Exception as e:
            logger.error(f"Error analyzing SQL correction: {str(e)}")
            return {}
    
    def _update_table_description(self, database_id: int, table_name: str, description: str):
        """Update table description"""
        schema = DatabaseSchema.query.filter_by(
            database_id=database_id,
            table_name=table_name
        ).first()
        
        if schema:
            schema.table_description = description
            schema.updated_at = datetime.utcnow()
    
    def _update_column_description(self, database_id: int, table_name: str, 
                                 column_name: str, description: str):
        """Update column description"""
        schema = DatabaseSchema.query.filter_by(
            database_id=database_id,
            table_name=table_name
        ).first()
        
        if schema:
            column = DatabaseColumn.query.filter_by(
                schema_id=schema.id,
                column_name=column_name
            ).first()
            
            if column:
                column.column_description = description
                column.updated_at = datetime.utcnow()
    
    def _create_business_metric(self, database_id: int, name: str, sql_expression: str, 
                              description: str, user_id: int):
        """Create a new business metric mapping"""
        mapping = SemanticMapping(
            database_id=database_id,
            mapping_type='metric',
            name=name,
            sql_expression=sql_expression,
            description=description,
            created_by=user_id
        )
        db.session.add(mapping)
    
    def _add_missing_relationship(self, database_id: int, source_table: str, 
                                source_column: str, target_table: str, target_column: str):
        """Add a missing foreign key relationship"""
        # Get source schema and column
        source_schema = DatabaseSchema.query.filter_by(
            database_id=database_id,
            table_name=source_table
        ).first()
        
        if source_schema:
            source_col = DatabaseColumn.query.filter_by(
                schema_id=source_schema.id,
                column_name=source_column
            ).first()
            
            if source_col:
                source_col.is_foreign_key = True
                source_col.foreign_key_table = target_table
                source_col.foreign_key_column = target_column
                source_col.updated_at = datetime.utcnow()
    
    def get_training_statistics(self, database_id: int) -> Dict[str, Any]:
        """Get training statistics for a database"""
        total_feedback = TrainingFeedback.query.filter_by(database_id=database_id).count()
        processed_feedback = TrainingFeedback.query.filter_by(
            database_id=database_id,
            is_processed=True
        ).count()
        
        feedback_by_type = {}
        feedback_types = TrainingFeedback.query.filter_by(database_id=database_id).all()
        for feedback in feedback_types:
            feedback_by_type[feedback.feedback_type] = feedback_by_type.get(feedback.feedback_type, 0) + 1
        
        return {
            'total_feedback': total_feedback,
            'processed_feedback': processed_feedback,
            'pending_feedback': total_feedback - processed_feedback,
            'feedback_by_type': feedback_by_type
        }
