"""
Database Connection Manager for SQL Chat

This service manages database connections, encryption, and connection testing.
"""

import os
import sys
import logging
import base64
from cryptography.fernet import <PERSON><PERSON><PERSON>
from typing import Dict, List, Any, Optional
from datetime import datetime
import sqlalchemy as sa
from sqlalchemy import create_engine, text

# Add parent directories to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from models.models import db, DatabaseConnection
from config.settings import FLASK_CONFIG

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class DatabaseManager:
    """Service for managing database connections and encryption"""
    
    def __init__(self, verbose: bool = True):
        """
        Initialize the database manager
        
        Args:
            verbose: Whether to enable verbose logging
        """
        self.verbose = verbose
        
        # Initialize encryption key
        self.encryption_key = self._get_encryption_key()
        self.cipher_suite = Fernet(self.encryption_key)
        
        if self.verbose:
            logger.info("Initialized DatabaseManager with encryption")
    
    def _get_encryption_key(self) -> bytes:
        """Get or generate encryption key for database credentials"""
        # In production, this should be stored securely (e.g., Azure Key Vault)
        # For now, derive from FLASK_CONFIG SECRET_KEY
        secret_key = FLASK_CONFIG.get('SECRET_KEY', 'default-secret-key-for-local-testing')
        key_material = secret_key.encode()[:32].ljust(32, b'0')
        return base64.urlsafe_b64encode(key_material)
    
    def _encrypt_credential(self, credential: str) -> str:
        """Encrypt a credential string"""
        if not credential:
            return ""
        return self.cipher_suite.encrypt(credential.encode()).decode()
    
    def _decrypt_credential(self, encrypted_credential: str) -> str:
        """Decrypt a credential string"""
        if not encrypted_credential:
            return ""
        return self.cipher_suite.decrypt(encrypted_credential.encode()).decode()
    
    def create_database_connection(self, connection_data: Dict[str, Any], created_by: int) -> Dict[str, Any]:
        """
        Create a new database connection
        
        Args:
            connection_data: Database connection information
            created_by: ID of the user creating the connection
            
        Returns:
            Dictionary with creation result
        """
        try:
            # Validate required fields
            required_fields = ['name', 'connection_type']
            if not all(field in connection_data for field in required_fields):
                return {'status': 'error', 'error': f'Missing required fields: {required_fields}'}
            
            # Check if name already exists
            existing_conn = DatabaseConnection.query.filter_by(
                name=connection_data['name']
            ).first()
            
            if existing_conn:
                return {'status': 'error', 'error': 'Database connection name already exists'}
            
            # Encrypt sensitive information
            encrypted_password = ""
            encrypted_connection_string = ""
            
            if 'password' in connection_data and connection_data['password']:
                encrypted_password = self._encrypt_credential(connection_data['password'])
            
            if 'connection_string' in connection_data and connection_data['connection_string']:
                encrypted_connection_string = self._encrypt_credential(connection_data['connection_string'])
            
            # Create new connection record
            new_connection = DatabaseConnection(
                name=connection_data['name'],
                description=connection_data.get('description'),
                connection_type=connection_data['connection_type'],
                host=connection_data.get('host'),
                port=connection_data.get('port'),
                database_name=connection_data.get('database_name'),
                username=connection_data.get('username'),
                password_encrypted=encrypted_password,
                connection_string=encrypted_connection_string,
                created_by=created_by
            )
            
            db.session.add(new_connection)
            db.session.commit()
            
            if self.verbose:
                logger.info(f"Created database connection '{connection_data['name']}'")
            
            return {
                'status': 'success',
                'connection_id': new_connection.id,
                'message': f"Database connection '{connection_data['name']}' created successfully"
            }
            
        except Exception as e:
            db.session.rollback()
            logger.error(f"Error creating database connection: {str(e)}")
            return {'status': 'error', 'error': str(e)}
    
    def test_database_connection(self, connection_id: int) -> Dict[str, Any]:
        """
        Test a database connection
        
        Args:
            connection_id: ID of the database connection
            
        Returns:
            Dictionary with test result
        """
        try:
            db_conn = DatabaseConnection.query.get(connection_id)
            if not db_conn:
                return {'status': 'error', 'error': 'Database connection not found'}
            
            # Build connection URL
            connection_url = self._build_connection_url(db_conn)
            
            # Test connection
            engine = create_engine(connection_url, connect_args={'connect_timeout': 10})
            
            with engine.connect() as conn:
                # Simple test query
                result = conn.execute(text("SELECT 1"))
                result.fetchone()
            
            if self.verbose:
                logger.info(f"Successfully tested connection to '{db_conn.name}'")
            
            return {
                'status': 'success',
                'message': f"Connection to '{db_conn.name}' successful"
            }
            
        except Exception as e:
            logger.error(f"Error testing database connection: {str(e)}")
            return {
                'status': 'error',
                'error': f"Connection test failed: {str(e)}"
            }
    
    def _build_connection_url(self, db_conn: DatabaseConnection) -> str:
        """Build connection URL from database connection record"""
        if db_conn.connection_string:
            # Use stored connection string
            return self._decrypt_credential(db_conn.connection_string)
        
        # Build from components
        password = self._decrypt_credential(db_conn.password_encrypted) if db_conn.password_encrypted else ""
        
        if db_conn.connection_type.lower() == 'postgresql':
            return f"postgresql://{db_conn.username}:{password}@{db_conn.host}:{db_conn.port}/{db_conn.database_name}"
        elif db_conn.connection_type.lower() == 'mysql':
            return f"mysql+pymysql://{db_conn.username}:{password}@{db_conn.host}:{db_conn.port}/{db_conn.database_name}"
        elif db_conn.connection_type.lower() == 'sqlite':
            return f"sqlite:///{db_conn.database_name}"
        elif db_conn.connection_type.lower() == 'mssql':
            return f"mssql+pyodbc://{db_conn.username}:{password}@{db_conn.host}:{db_conn.port}/{db_conn.database_name}?driver=ODBC+Driver+17+for+SQL+Server"
        else:
            raise ValueError(f"Unsupported database type: {db_conn.connection_type}")
    
    def update_database_connection(self, connection_id: int, connection_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Update an existing database connection
        
        Args:
            connection_id: ID of the connection to update
            connection_data: Updated connection information
            
        Returns:
            Dictionary with update result
        """
        try:
            db_conn = DatabaseConnection.query.get(connection_id)
            if not db_conn:
                return {'status': 'error', 'error': 'Database connection not found'}
            
            # Update basic fields
            if 'description' in connection_data:
                db_conn.description = connection_data['description']
            if 'host' in connection_data:
                db_conn.host = connection_data['host']
            if 'port' in connection_data:
                db_conn.port = connection_data['port']
            if 'database_name' in connection_data:
                db_conn.database_name = connection_data['database_name']
            if 'username' in connection_data:
                db_conn.username = connection_data['username']
            if 'is_active' in connection_data:
                db_conn.is_active = connection_data['is_active']
            
            # Update encrypted fields
            if 'password' in connection_data:
                db_conn.password_encrypted = self._encrypt_credential(connection_data['password'])
            if 'connection_string' in connection_data:
                db_conn.connection_string = self._encrypt_credential(connection_data['connection_string'])
            
            db_conn.updated_at = datetime.utcnow()
            db.session.commit()
            
            if self.verbose:
                logger.info(f"Updated database connection '{db_conn.name}'")
            
            return {
                'status': 'success',
                'message': f"Database connection '{db_conn.name}' updated successfully"
            }
            
        except Exception as e:
            db.session.rollback()
            logger.error(f"Error updating database connection: {str(e)}")
            return {'status': 'error', 'error': str(e)}
    
    def delete_database_connection(self, connection_id: int) -> Dict[str, Any]:
        """
        Delete a database connection
        
        Args:
            connection_id: ID of the connection to delete
            
        Returns:
            Dictionary with deletion result
        """
        try:
            db_conn = DatabaseConnection.query.get(connection_id)
            if not db_conn:
                return {'status': 'error', 'error': 'Database connection not found'}
            
            # Check if connection has associated data
            from models.models import DatabaseSchema, UserDatabaseAccess
            
            schema_count = DatabaseSchema.query.filter_by(database_id=connection_id).count()
            access_count = UserDatabaseAccess.query.filter_by(database_id=connection_id).count()
            
            if schema_count > 0 or access_count > 0:
                return {
                    'status': 'error',
                    'error': f'Cannot delete connection. It has {schema_count} schemas and {access_count} user access records.'
                }
            
            connection_name = db_conn.name
            db.session.delete(db_conn)
            db.session.commit()
            
            if self.verbose:
                logger.info(f"Deleted database connection '{connection_name}'")
            
            return {
                'status': 'success',
                'message': f"Database connection '{connection_name}' deleted successfully"
            }
            
        except Exception as e:
            db.session.rollback()
            logger.error(f"Error deleting database connection: {str(e)}")
            return {'status': 'error', 'error': str(e)}
    
    def get_database_connections(self, user_id: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        Get database connections (optionally filtered by user access)
        
        Args:
            user_id: Optional user ID to filter by access
            
        Returns:
            List of database connection information
        """
        try:
            if user_id:
                # Get connections user has access to
                from models.models import UserDatabaseAccess
                
                connections = db.session.query(DatabaseConnection).join(
                    UserDatabaseAccess,
                    DatabaseConnection.id == UserDatabaseAccess.database_id
                ).filter(
                    UserDatabaseAccess.user_id == user_id,
                    UserDatabaseAccess.is_active == True,
                    DatabaseConnection.is_active == True
                ).all()
            else:
                # Get all active connections
                connections = DatabaseConnection.query.filter_by(is_active=True).all()
            
            connection_list = []
            for conn in connections:
                connection_list.append({
                    'id': conn.id,
                    'name': conn.name,
                    'description': conn.description,
                    'connection_type': conn.connection_type,
                    'host': conn.host,
                    'port': conn.port,
                    'database_name': conn.database_name,
                    'username': conn.username,
                    'created_at': conn.created_at.isoformat() if conn.created_at else None,
                    'last_ingestion_at': conn.last_ingestion_at.isoformat() if conn.last_ingestion_at else None
                })
            
            return connection_list
            
        except Exception as e:
            logger.error(f"Error getting database connections: {str(e)}")
            return []
