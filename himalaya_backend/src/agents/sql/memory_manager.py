"""
Memory Manager

This service coordinates database and user memory services,
providing a unified interface for MemGPT-style memory management
in the SQL chat system.
"""

import os
import sys
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime

# Add parent directories to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from .database_memory_service import DatabaseMemoryService
from .user_memory_service import UserMemoryService

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class MemoryManager:
    """Unified memory manager for SQL chat system"""
    
    def __init__(self, verbose: bool = True):
        """
        Initialize the memory manager
        
        Args:
            verbose: Whether to enable verbose logging
        """
        self.verbose = verbose
        self.db_memory = DatabaseMemoryService(verbose=verbose)
        self.user_memory = UserMemoryService(verbose=verbose)
        
        if self.verbose:
            logger.info("Initialized MemoryManager")
    
    def initialize_user(self, user_id: int) -> Dict[str, Any]:
        """
        Initialize memory for a new user
        
        Args:
            user_id: ID of the user
            
        Returns:
            Dictionary with initialization results
        """
        return self.user_memory.initialize_user_memory(user_id)
    
    def get_context_for_query(self, user_id: int, database_id: int, query: str) -> Dict[str, Any]:
        """
        Get comprehensive context for SQL query generation
        
        Args:
            user_id: ID of the user
            database_id: ID of the database
            query: Natural language query
            
        Returns:
            Dictionary with context information
        """
        try:
            # Get database memory
            db_memory = self.db_memory.get_database_memory(database_id)
            
            # Get user memory
            user_memory = self.user_memory.get_user_memory(user_id)
            
            # Combine context
            context = {
                'database_context': db_memory,
                'user_context': user_memory,
                'query': query,
                'context_quality': 'complete' if db_memory and user_memory else 'partial'
            }
            
            if self.verbose:
                logger.info(f"Built context for user {user_id}, database {database_id}")
            
            return context
            
        except Exception as e:
            logger.error(f"Error building context: {str(e)}")
            return {
                'database_context': None,
                'user_context': None,
                'query': query,
                'context_quality': 'error',
                'error': str(e)
            }
    
    def learn_from_interaction(self, user_id: int, database_id: int, query: str, 
                              sql_generated: str, feedback: Optional[str] = None,
                              execution_result: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Learn from user interaction and update both database and user memory
        
        Args:
            user_id: ID of the user
            database_id: ID of the database
            query: Natural language query
            sql_generated: Generated SQL
            feedback: Optional user feedback
            execution_result: Optional SQL execution result
            
        Returns:
            Dictionary with learning results
        """
        try:
            learning_results = {
                'user_memory_updates': [],
                'database_memory_updates': [],
                'insights': {}
            }
            
            # Learn from user interaction
            user_learning = self.user_memory.learn_from_interaction(
                user_id, query, sql_generated, feedback, database_id
            )
            
            if user_learning['status'] == 'success':
                learning_results['user_memory_updates'] = user_learning.get('updates_made', [])
                learning_results['insights']['user'] = user_learning.get('insights', {})
            
            # Update database memory if there's valuable feedback
            if feedback and any(word in feedback.lower() for word in ['wrong', 'incorrect', 'better', 'should']):
                # This could trigger database memory updates based on corrections
                db_updates = self._process_database_feedback(database_id, query, sql_generated, feedback)
                learning_results['database_memory_updates'] = db_updates
            
            if self.verbose and (learning_results['user_memory_updates'] or learning_results['database_memory_updates']):
                logger.info(f"Learning completed for user {user_id}: {learning_results}")
            
            return {
                'status': 'success',
                'learning_results': learning_results
            }
            
        except Exception as e:
            logger.error(f"Error in learning from interaction: {str(e)}")
            return {
                'status': 'error',
                'error': str(e)
            }
    
    def update_user_memory(self, user_id: int, memory_type: str, label: str, 
                          content: str, updater_email: str = None) -> Dict[str, Any]:
        """
        Update user memory block
        
        Args:
            user_id: ID of the user
            memory_type: Type of memory block
            label: Label for the memory block
            content: New content
            updater_email: Email of the updater (for trainer validation)
            
        Returns:
            Dictionary with update results
        """
        return self.user_memory.update_memory_block(user_id, memory_type, label, content, updater_email)
    
    def update_database_memory(self, database_id: int, updates: Dict[str, Any], user_id: int) -> Dict[str, Any]:
        """
        Update database memory
        
        Args:
            database_id: ID of the database
            updates: Dictionary with updates
            user_id: ID of the user making updates
            
        Returns:
            Dictionary with update results
        """
        return self.db_memory.update_database_memory(database_id, updates, user_id)
    
    def generate_database_summary(self, database_id: int, user_id: int) -> Dict[str, Any]:
        """
        Generate or regenerate database semantic summary
        
        Args:
            database_id: ID of the database
            user_id: ID of the user
            
        Returns:
            Dictionary with generation results
        """
        return self.db_memory.generate_database_summary(database_id, user_id)
    
    def get_memory_stats(self, user_id: int) -> Dict[str, Any]:
        """
        Get memory statistics for a user
        
        Args:
            user_id: ID of the user
            
        Returns:
            Dictionary with memory statistics
        """
        try:
            user_memory = self.user_memory.get_user_memory(user_id)
            
            stats = {
                'total_memory_blocks': user_memory.get('total_blocks', 0),
                'memory_types': list(user_memory.get('memory_blocks', {}).keys()),
                'memory_by_type': {}
            }
            
            for memory_type, blocks in user_memory.get('memory_blocks', {}).items():
                stats['memory_by_type'][memory_type] = {
                    'count': len(blocks),
                    'labels': [block['label'] for block in blocks]
                }
            
            return stats
            
        except Exception as e:
            logger.error(f"Error getting memory stats: {str(e)}")
            return {
                'error': str(e)
            }
    
    def _process_database_feedback(self, database_id: int, query: str, sql: str, feedback: str) -> List[str]:
        """
        Process feedback that might require database memory updates
        
        Args:
            database_id: ID of the database
            query: Original query
            sql: Generated SQL
            feedback: User feedback
            
        Returns:
            List of updates made
        """
        updates_made = []
        
        # This is a simplified implementation
        # In practice, you'd use LLM to analyze feedback and determine what to update
        
        feedback_lower = feedback.lower()
        
        # Check if feedback suggests business domain clarification
        if any(word in feedback_lower for word in ['business', 'domain', 'context', 'meaning']):
            # Could trigger business context update
            updates_made.append('business_context_clarification_needed')
        
        # Check if feedback suggests relationship issues
        if any(word in feedback_lower for word in ['join', 'relationship', 'connect', 'link']):
            updates_made.append('relationship_clarification_needed')
        
        # Check if feedback suggests metric definition issues
        if any(word in feedback_lower for word in ['metric', 'calculation', 'formula', 'definition']):
            updates_made.append('metric_definition_needed')
        
        return updates_made
    
    def export_user_memory(self, user_id: int) -> Dict[str, Any]:
        """
        Export user memory for backup or analysis
        
        Args:
            user_id: ID of the user
            
        Returns:
            Dictionary with exported memory
        """
        try:
            user_memory = self.user_memory.get_user_memory(user_id)
            
            export_data = {
                'user_id': user_id,
                'export_timestamp': str(datetime.utcnow()),
                'memory_blocks': user_memory.get('memory_blocks', {}),
                'total_blocks': user_memory.get('total_blocks', 0)
            }
            
            return {
                'status': 'success',
                'export_data': export_data
            }
            
        except Exception as e:
            logger.error(f"Error exporting user memory: {str(e)}")
            return {
                'status': 'error',
                'error': str(e)
            }
    
    def import_user_memory(self, user_id: int, import_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Import user memory from backup
        
        Args:
            user_id: ID of the user
            import_data: Memory data to import
            
        Returns:
            Dictionary with import results
        """
        try:
            # This would implement memory import logic
            # For now, return a placeholder
            return {
                'status': 'success',
                'message': 'Memory import functionality to be implemented'
            }
            
        except Exception as e:
            logger.error(f"Error importing user memory: {str(e)}")
            return {
                'status': 'error',
                'error': str(e)
            }
