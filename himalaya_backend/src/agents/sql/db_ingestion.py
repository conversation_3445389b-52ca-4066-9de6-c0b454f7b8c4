"""
Database Ingestion Service

This service handles the ingestion of database schemas, performs EDA,
and generates semantic descriptions using LLM.
"""

import os
import sys
import logging
import pandas as pd
import sqlalchemy as sa
from sqlalchemy import create_engine, inspect, text
from sqlalchemy.exc import SQLAlchemyError
from typing import Dict, List, Any, Optional, Tuple
import json
from datetime import datetime
import hashlib

# Add parent directories to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from config.settings import AZURE_OPENAI_KEY, AZURE_OPENAI_API_VERSION, AZURE_OPENAI_ENDPOINT, AZURE_OPENAI_DEPLOYMENT_NAME
from models.models import (
    db, DatabaseConnection, DatabaseSchema, DatabaseColumn, 
    SemanticMapping, DatabaseIngestionLog
)
from langchain_openai import AzureChatOpenAI
from langchain_core.messages import SystemMessage, HumanMessage

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class DatabaseIngestionService:
    """Service for ingesting database schemas and generating semantic descriptions"""
    
    def __init__(self, verbose: bool = True):
        """
        Initialize the database ingestion service
        
        Args:
            verbose: Whether to enable verbose logging
        """
        self.verbose = verbose
        self.llm = AzureChatOpenAI(
            azure_deployment=AZURE_OPENAI_DEPLOYMENT_NAME,
            openai_api_version=AZURE_OPENAI_API_VERSION,
            azure_endpoint=AZURE_OPENAI_ENDPOINT,
            openai_api_key=AZURE_OPENAI_KEY,
            temperature=0.1
        )
        
        if self.verbose:
            logger.info(f"Initialized DatabaseIngestionService with Azure OpenAI deployment: {AZURE_OPENAI_DEPLOYMENT_NAME}")
    
    def ingest_database(self, database_id: int, user_id: int, ingestion_type: str = 'full') -> Dict[str, Any]:
        """
        Ingest a database and extract its schema
        
        Args:
            database_id: ID of the database connection
            user_id: ID of the user performing ingestion
            ingestion_type: Type of ingestion ('full', 'incremental', 'schema_only')
            
        Returns:
            Dictionary with ingestion results
        """
        # Create ingestion log
        log = DatabaseIngestionLog(
            database_id=database_id,
            ingestion_type=ingestion_type,
            status='running',
            started_by=user_id
        )
        db.session.add(log)
        db.session.commit()
        
        try:
            # Get database connection
            db_conn = DatabaseConnection.query.get(database_id)
            if not db_conn:
                raise ValueError(f"Database connection {database_id} not found")
            
            if self.verbose:
                logger.info(f"Starting {ingestion_type} ingestion for database: {db_conn.name}")
            
            # Connect to the database
            engine = self._create_engine(db_conn)
            inspector = inspect(engine)
            
            # Extract schema information
            schema_info = self._extract_schema_info(inspector, engine)
            
            # Store schema information
            tables_processed, columns_processed = self._store_schema_info(
                database_id, schema_info, ingestion_type
            )
            
            # Generate semantic descriptions
            if ingestion_type in ['full', 'schema_only']:
                self._generate_semantic_descriptions(database_id, schema_info)
            
            # Update ingestion log
            log.status = 'completed'
            log.completed_at = datetime.utcnow()
            log.tables_processed = tables_processed
            log.columns_processed = columns_processed
            log.summary_report = {
                'tables_found': len(schema_info['tables']),
                'total_columns': sum(len(table['columns']) for table in schema_info['tables'].values()),
                'ingestion_type': ingestion_type
            }
            
            # Update database last ingestion time
            db_conn.last_ingestion_at = datetime.utcnow()
            
            db.session.commit()
            
            if self.verbose:
                logger.info(f"Successfully completed ingestion for database: {db_conn.name}")
            
            return {
                'status': 'success',
                'tables_processed': tables_processed,
                'columns_processed': columns_processed,
                'ingestion_log_id': log.id
            }
            
        except Exception as e:
            logger.error(f"Error during database ingestion: {str(e)}")
            
            # Update log with error
            log.status = 'failed'
            log.completed_at = datetime.utcnow()
            log.errors_encountered = [str(e)]
            db.session.commit()
            
            return {
                'status': 'error',
                'error': str(e),
                'ingestion_log_id': log.id
            }
    
    def _create_engine(self, db_conn: DatabaseConnection) -> sa.Engine:
        """Create SQLAlchemy engine from database connection"""
        # In a real implementation, you would decrypt the connection string
        # For now, we'll construct it from the components
        if db_conn.connection_string:
            # Use the stored connection string (should be decrypted)
            connection_url = db_conn.connection_string
        else:
            # Construct from components
            if db_conn.connection_type.lower() == 'postgresql':
                connection_url = f"postgresql://{db_conn.username}:{db_conn.password_encrypted}@{db_conn.host}:{db_conn.port}/{db_conn.database_name}"
            elif db_conn.connection_type.lower() == 'mysql':
                connection_url = f"mysql+pymysql://{db_conn.username}:{db_conn.password_encrypted}@{db_conn.host}:{db_conn.port}/{db_conn.database_name}"
            elif db_conn.connection_type.lower() == 'sqlite':
                connection_url = f"sqlite:///{db_conn.database_name}"
            else:
                raise ValueError(f"Unsupported database type: {db_conn.connection_type}")
        
        return create_engine(connection_url)
    
    def _extract_schema_info(self, inspector: sa.Inspector, engine: sa.Engine) -> Dict[str, Any]:
        """Extract comprehensive schema information from database"""
        schema_info = {
            'tables': {},
            'foreign_keys': [],
            'indexes': []
        }
        
        # Get all table names
        table_names = inspector.get_table_names()
        
        for table_name in table_names:
            if self.verbose:
                logger.info(f"Processing table: {table_name}")
            
            table_info = {
                'name': table_name,
                'columns': {},
                'primary_keys': [],
                'foreign_keys': [],
                'indexes': [],
                'row_count': 0
            }
            
            # Get column information
            columns = inspector.get_columns(table_name)
            for column in columns:
                column_info = {
                    'name': column['name'],
                    'type': str(column['type']),
                    'nullable': column['nullable'],
                    'default': column.get('default'),
                    'autoincrement': column.get('autoincrement', False)
                }
                
                # Perform column profiling
                column_profile = self._profile_column(engine, table_name, column['name'])
                column_info.update(column_profile)
                
                table_info['columns'][column['name']] = column_info
            
            # Get primary keys
            pk_constraint = inspector.get_pk_constraint(table_name)
            table_info['primary_keys'] = pk_constraint.get('constrained_columns', [])
            
            # Get foreign keys
            fk_constraints = inspector.get_foreign_keys(table_name)
            for fk in fk_constraints:
                fk_info = {
                    'constrained_columns': fk['constrained_columns'],
                    'referred_table': fk['referred_table'],
                    'referred_columns': fk['referred_columns']
                }
                table_info['foreign_keys'].append(fk_info)
                schema_info['foreign_keys'].append({
                    'source_table': table_name,
                    **fk_info
                })
            
            # Get indexes
            indexes = inspector.get_indexes(table_name)
            table_info['indexes'] = indexes
            
            # Get row count
            try:
                with engine.connect() as conn:
                    result = conn.execute(text(f"SELECT COUNT(*) FROM {table_name}"))
                    table_info['row_count'] = result.scalar()
            except Exception as e:
                logger.warning(f"Could not get row count for {table_name}: {str(e)}")
                table_info['row_count'] = 0
            
            schema_info['tables'][table_name] = table_info
        
        return schema_info
    
    def _profile_column(self, engine: sa.Engine, table_name: str, column_name: str) -> Dict[str, Any]:
        """Profile a column to get statistics and sample values"""
        profile = {
            'distinct_count': 0,
            'null_percentage': 0.0,
            'sample_values': [],
            'min_value': None,
            'max_value': None
        }
        
        try:
            with engine.connect() as conn:
                # Get basic statistics
                stats_query = text(f"""
                    SELECT 
                        COUNT(DISTINCT {column_name}) as distinct_count,
                        COUNT(*) as total_count,
                        COUNT({column_name}) as non_null_count
                    FROM {table_name}
                """)
                stats_result = conn.execute(stats_query).fetchone()
                
                if stats_result and stats_result.total_count > 0:
                    profile['distinct_count'] = stats_result.distinct_count
                    profile['null_percentage'] = (stats_result.total_count - stats_result.non_null_count) / stats_result.total_count * 100
                
                # Get sample values (top 10 most frequent)
                sample_query = text(f"""
                    SELECT {column_name}, COUNT(*) as frequency
                    FROM {table_name}
                    WHERE {column_name} IS NOT NULL
                    GROUP BY {column_name}
                    ORDER BY frequency DESC
                    LIMIT 10
                """)
                sample_result = conn.execute(sample_query).fetchall()
                profile['sample_values'] = [{'value': str(row[0]), 'frequency': row[1]} for row in sample_result]
                
                # Get min/max for numeric and date columns
                try:
                    minmax_query = text(f"SELECT MIN({column_name}), MAX({column_name}) FROM {table_name}")
                    minmax_result = conn.execute(minmax_query).fetchone()
                    if minmax_result:
                        profile['min_value'] = str(minmax_result[0]) if minmax_result[0] is not None else None
                        profile['max_value'] = str(minmax_result[1]) if minmax_result[1] is not None else None
                except:
                    # Skip min/max for non-comparable types
                    pass
                    
        except Exception as e:
            logger.warning(f"Could not profile column {table_name}.{column_name}: {str(e)}")
        
        return profile

    def _store_schema_info(self, database_id: int, schema_info: Dict[str, Any], ingestion_type: str) -> Tuple[int, int]:
        """Store extracted schema information in the database"""
        tables_processed = 0
        columns_processed = 0

        for table_name, table_info in schema_info['tables'].items():
            # Check if table already exists
            existing_schema = DatabaseSchema.query.filter_by(
                database_id=database_id,
                table_name=table_name
            ).first()

            if existing_schema and ingestion_type == 'incremental':
                # Update existing schema
                schema_record = existing_schema
                schema_record.row_count = table_info['row_count']
                schema_record.updated_at = datetime.utcnow()
            else:
                # Create new schema record
                schema_record = DatabaseSchema(
                    database_id=database_id,
                    table_name=table_name,
                    table_type='TABLE',
                    row_count=table_info['row_count']
                )
                db.session.add(schema_record)
                db.session.flush()  # Get the ID

            tables_processed += 1

            # Store column information
            for column_name, column_info in table_info['columns'].items():
                # Check if column already exists
                existing_column = DatabaseColumn.query.filter_by(
                    schema_id=schema_record.id,
                    column_name=column_name
                ).first()

                if existing_column and ingestion_type == 'incremental':
                    # Update existing column
                    column_record = existing_column
                else:
                    # Create new column record
                    column_record = DatabaseColumn(
                        schema_id=schema_record.id,
                        column_name=column_name
                    )
                    db.session.add(column_record)

                # Update column properties
                column_record.data_type = column_info['type']
                column_record.is_nullable = column_info['nullable']
                column_record.is_primary_key = column_name in table_info['primary_keys']
                column_record.sample_values = column_info.get('sample_values', [])
                column_record.distinct_count = column_info.get('distinct_count', 0)
                column_record.null_percentage = column_info.get('null_percentage', 0.0)
                column_record.min_value = column_info.get('min_value')
                column_record.max_value = column_info.get('max_value')
                column_record.updated_at = datetime.utcnow()

                # Check for foreign keys
                for fk in table_info['foreign_keys']:
                    if column_name in fk['constrained_columns']:
                        column_record.is_foreign_key = True
                        column_record.foreign_key_table = fk['referred_table']
                        idx = fk['constrained_columns'].index(column_name)
                        if idx < len(fk['referred_columns']):
                            column_record.foreign_key_column = fk['referred_columns'][idx]

                columns_processed += 1

        db.session.commit()
        return tables_processed, columns_processed

    def _generate_semantic_descriptions(self, database_id: int, schema_info: Dict[str, Any]):
        """Generate semantic descriptions for tables and columns using LLM"""
        if self.verbose:
            logger.info("Generating semantic descriptions using LLM")

        # Generate table descriptions
        for table_name, table_info in schema_info['tables'].items():
            table_description = self._generate_table_description(table_name, table_info)

            # Update the database schema record
            schema_record = DatabaseSchema.query.filter_by(
                database_id=database_id,
                table_name=table_name
            ).first()

            if schema_record:
                schema_record.llm_generated_description = table_description

                # Generate column descriptions
                for column_name, column_info in table_info['columns'].items():
                    column_description = self._generate_column_description(
                        table_name, column_name, column_info, table_info
                    )

                    column_record = DatabaseColumn.query.filter_by(
                        schema_id=schema_record.id,
                        column_name=column_name
                    ).first()

                    if column_record:
                        column_record.llm_generated_description = column_description

        db.session.commit()

    def _generate_table_description(self, table_name: str, table_info: Dict[str, Any]) -> str:
        """Generate semantic description for a table using LLM"""
        # Prepare table information for LLM
        columns_summary = []
        for col_name, col_info in table_info['columns'].items():
            col_summary = f"- {col_name} ({col_info['type']})"
            if col_name in table_info['primary_keys']:
                col_summary += " [PRIMARY KEY]"
            if not col_info['nullable']:
                col_summary += " [NOT NULL]"
            columns_summary.append(col_summary)

        foreign_keys_summary = []
        for fk in table_info['foreign_keys']:
            fk_summary = f"- {', '.join(fk['constrained_columns'])} -> {fk['referred_table']}.{', '.join(fk['referred_columns'])}"
            foreign_keys_summary.append(fk_summary)

        system_prompt = """You are a database expert. Generate a clear, concise description of what this database table represents and its business purpose. Focus on:
1. What business entity or concept this table represents
2. What kind of data it stores
3. Its likely role in the overall system
4. Any notable characteristics based on the schema

Keep the description under 200 words and make it understandable to business users."""

        user_prompt = f"""
Table Name: {table_name}
Row Count: {table_info['row_count']:,}

Columns:
{chr(10).join(columns_summary)}

Foreign Key Relationships:
{chr(10).join(foreign_keys_summary) if foreign_keys_summary else 'None'}

Generate a semantic description for this table:"""

        try:
            messages = [
                SystemMessage(content=system_prompt),
                HumanMessage(content=user_prompt)
            ]

            response = self.llm.invoke(messages)
            return response.content.strip()

        except Exception as e:
            logger.error(f"Error generating table description for {table_name}: {str(e)}")
            return f"Table storing {table_name.replace('_', ' ')} data with {len(table_info['columns'])} columns."

    def _generate_column_description(self, table_name: str, column_name: str,
                                   column_info: Dict[str, Any], table_info: Dict[str, Any]) -> str:
        """Generate semantic description for a column using LLM"""
        # Prepare column information for LLM
        sample_values_text = ""
        if column_info.get('sample_values'):
            sample_values = [item['value'] for item in column_info['sample_values'][:5]]
            sample_values_text = f"Sample values: {', '.join(sample_values)}"

        constraints = []
        if column_name in table_info['primary_keys']:
            constraints.append("PRIMARY KEY")
        if not column_info['nullable']:
            constraints.append("NOT NULL")
        if column_info.get('min_value') and column_info.get('max_value'):
            constraints.append(f"Range: {column_info['min_value']} to {column_info['max_value']}")

        system_prompt = """You are a database expert. Generate a brief, clear description of what this database column represents. Focus on:
1. What business concept or data this column stores
2. Its purpose and meaning in the context of the table
3. Any notable characteristics

Keep the description under 100 words and make it understandable to business users."""

        user_prompt = f"""
Table: {table_name}
Column: {column_name}
Data Type: {column_info['type']}
Nullable: {column_info['nullable']}
Distinct Values: {column_info.get('distinct_count', 'Unknown')}
Null Percentage: {column_info.get('null_percentage', 0):.1f}%
{sample_values_text}
Constraints: {', '.join(constraints) if constraints else 'None'}

Generate a semantic description for this column:"""

        try:
            messages = [
                SystemMessage(content=system_prompt),
                HumanMessage(content=user_prompt)
            ]

            response = self.llm.invoke(messages)
            return response.content.strip()

        except Exception as e:
            logger.error(f"Error generating column description for {table_name}.{column_name}: {str(e)}")
            return f"Column storing {column_name.replace('_', ' ')} data of type {column_info['type']}."

    def get_ingestion_status(self, database_id: int) -> Dict[str, Any]:
        """Get the status of the latest ingestion for a database"""
        latest_log = DatabaseIngestionLog.query.filter_by(
            database_id=database_id
        ).order_by(DatabaseIngestionLog.started_at.desc()).first()

        if not latest_log:
            return {'status': 'never_ingested'}

        return {
            'status': latest_log.status,
            'ingestion_type': latest_log.ingestion_type,
            'started_at': latest_log.started_at.isoformat() if latest_log.started_at else None,
            'completed_at': latest_log.completed_at.isoformat() if latest_log.completed_at else None,
            'tables_processed': latest_log.tables_processed,
            'columns_processed': latest_log.columns_processed,
            'errors': latest_log.errors_encountered,
            'summary': latest_log.summary_report
        }
