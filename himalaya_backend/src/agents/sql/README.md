# SQL Chat Module Documentation

## Overview

The SQL Chat Module is a comprehensive AI-powered database interaction system that allows users to query databases using natural language. It's built as a completely separate module from the document chat system, with its own planner, agents, and visualization capabilities.

## Architecture

### Core Components

1. **SQL Chat Agent** (`agent.py`) - Main orchestrator using LangGraph
2. **SQL Planner** (`sql_planner.py`) - Dedicated planner for SQL queries
3. **Database Ingestion Service** (`db_ingestion.py`) - Schema extraction and EDA
4. **Query Generator** (`query_generator.py`) - Natural language to SQL conversion
5. **Schema Analyzer** (`schema_analyzer.py`) - Database structure analysis
6. **Training Service** (`training_service.py`) - Feedback and learning system
7. **Role Manager** (`role_manager.py`) - Access control and permissions
8. **Database Manager** (`db_manager.py`) - Connection management
9. **Visualization Agent** (`visualization_agent.py`) - HTML chart generation
10. **Session Manager** (`session_manager.py`) - SQL chat session management

### Key Features

- **AI Agentic System**: Built with LangGraph for complex workflow orchestration
- **Role-Based Access Control**: Fine-grained permissions with row predicates and column masks
- **Multi-Database Support**: Connect to PostgreSQL, MySQL, SQLite, SQL Server
- **Intelligent Planning**: Dedicated SQL planner analyzes queries and plans execution
- **Custom Visualizations**: Prompt-based chart generation with HTML/Chart.js
- **Training & Feedback**: Human-in-the-loop learning system
- **Read-Only Safety**: All operations are SELECT-only for security

## Database Models

### Core Tables

- `database_connections` - Database connection information (encrypted)
- `database_schemas` - Table and schema metadata
- `database_columns` - Column information with profiling data
- `database_roles` - Access control roles
- `user_database_access` - User-role assignments
- `sql_chat_sessions` - SQL chat sessions (separate from document sessions)
- `query_history` - Query execution history with session tracking
- `semantic_mappings` - Business metrics and synonyms
- `training_feedback` - User feedback for system improvement
- `user_preferences` - User-specific SQL chat preferences
- `database_ingestion_logs` - Ingestion process tracking

## API Endpoints

### Core SQL Chat
- `POST /sql/chat` - Main chat interface for natural language queries
- `GET /sql/databases` - Get user's accessible databases
- `GET /sql/databases/{id}/schema` - Get database schema information
- `GET /sql/databases/{id}/history` - Get query history

### Database Management (Admin Only)
- `GET /sql/connections` - List all database connections
- `POST /sql/connections` - Create new database connection
- `PUT /sql/connections/{id}` - Update database connection
- `DELETE /sql/connections/{id}` - Delete database connection
- `POST /sql/connections/{id}/test` - Test database connection

### Database Ingestion
- `POST /sql/databases/{id}/ingest` - Trigger database ingestion
- `GET /sql/databases/{id}/training/stats` - Get training statistics
- `POST /sql/databases/{id}/training/process` - Process pending feedback

### Role-Based Access Control
- `GET /sql/databases/{id}/roles` - Get database roles
- `POST /sql/databases/{id}/roles` - Create new role
- `PUT /sql/roles/{id}` - Update role
- `DELETE /sql/roles/{id}` - Delete role
- `POST /sql/users/{id}/access` - Grant user access
- `DELETE /sql/users/{id}/access` - Revoke user access

### SQL Session Management
- `GET /sql/sessions` - Get user's SQL chat sessions
- `POST /sql/sessions` - Create new SQL chat session
- `GET /sql/sessions/{id}` - Get specific session with recent queries
- `PUT /sql/sessions/{id}` - Update session details
- `DELETE /sql/sessions/{id}` - Delete session and all queries
- `GET /sql/sessions/{id}/history` - Get conversation history

### Training & Feedback
- `POST /sql/feedback` - Submit training feedback
- `GET /sql/databases/{id}/tables/{table}` - Get table information
- `POST /sql/databases/{id}/suggest-tables` - Get table suggestions

## Workflow

### 1. Database Ingestion
```
1. Connect to database
2. Extract schema metadata (tables, columns, relationships)
3. Perform EDA (row counts, data profiling, sample values)
4. Generate semantic descriptions using LLM
5. Store in metadata tables
```

### 2. Query Processing
```
1. Initialize conversation context
2. Plan query using SQL Planner
   - Analyze intent and complexity
   - Plan visualization needs
   - Set execution strategy
3. Check user access permissions
4. Build database context
5. Generate SQL query
6. Validate SQL syntax and security
7. Execute query (with role-based filtering)
8. Generate visualizations if needed
9. Perform QA check
10. Format response
```

### 3. Visualization Generation
```
1. Determine chart type based on query intent
2. Prepare chart configuration
3. Generate HTML with Chart.js
4. Return interactive chart
```

## Security Features

### Access Control
- **Role-Based Permissions**: Users assigned to roles with specific database access
- **Row Predicates**: WHERE clauses automatically applied to filter data
- **Column Masks**: Sensitive data masking at the column level
- **Read-Only Access**: Only SELECT operations allowed

### Data Protection
- **Encrypted Credentials**: Database passwords encrypted at rest
- **SQL Validation**: AST parsing to prevent dangerous operations
- **Query Limits**: Configurable result set limits
- **Audit Logging**: All queries logged with user attribution

## Configuration

### Environment Variables
```
AZURE_OPENAI_KEY=your_key
AZURE_OPENAI_ENDPOINT=your_endpoint
AZURE_OPENAI_DEPLOYMENT_NAME=your_deployment
SECRET_KEY=your_secret_key
```

### Database Connection Types Supported
- PostgreSQL
- MySQL
- SQLite
- SQL Server (with ODBC driver)

## Usage Examples

### Basic Query
```python
# Natural language query
"How many customers do we have by region?"

# Generated SQL (with role-based filtering)
SELECT region, COUNT(*) as customer_count 
FROM customers 
WHERE region IN ('US', 'Canada')  -- Applied from user role
GROUP BY region
```

### Visualization
```python
# Query with chart generation
"Show me sales trends over the last 6 months"

# Returns:
{
    "response": "Here are the sales trends...",
    "sql": "SELECT month, SUM(sales) FROM...",
    "data": [...],
    "chart_data": {
        "html": "<canvas>...</canvas><script>...</script>",
        "chart_type": "line"
    }
}
```

### Role-Based Access
```python
# Role: Sales_US with row predicate: "region = 'US'"
# User query: "Show all customers"
# Executed SQL: "SELECT * FROM customers WHERE region = 'US'"
```

## Training & Improvement

### Feedback Loop
1. User provides feedback on query results
2. System analyzes corrections and improvements
3. Updates semantic mappings and descriptions
4. Improves future query generation

### Supported Feedback Types
- SQL corrections
- Semantic clarifications
- Schema updates
- Business metric definitions

## Visualization Capabilities

### Chart Types
- **Bar Charts**: Comparisons across categories
- **Line Charts**: Trends over time
- **Pie Charts**: Part-to-whole relationships
- **Scatter Plots**: Correlations between variables
- **Tables**: Detailed data display

### Customization
- Colors, themes, and styling
- Interactive features
- Responsive design
- Custom chart configurations via prompts

## Error Handling

### Graceful Degradation
- Fallback to simpler queries on complex failures
- Alternative visualization options
- Clear error messages with recovery suggestions
- Retry mechanisms with exponential backoff

### Common Error Scenarios
- Database connection failures
- Permission denied errors
- SQL syntax errors
- Data type mismatches
- Timeout handling

## Performance Considerations

### Optimization Strategies
- Query result caching
- Schema metadata caching
- Connection pooling
- Lazy loading of large result sets
- Efficient column profiling with sampling

### Monitoring
- Query execution time tracking
- Error rate monitoring
- User satisfaction metrics
- Resource usage tracking

## Future Enhancements

### Planned Features
- Advanced analytics functions
- Cross-database joins
- Real-time data streaming
- Advanced visualization types
- Natural language explanations of complex queries
- Automated insight generation

This SQL Chat Module provides a comprehensive, secure, and user-friendly way to interact with databases using natural language, while maintaining strict access controls and providing rich visualization capabilities.
