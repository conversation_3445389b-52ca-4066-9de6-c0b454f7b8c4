"""
User Memory Service

This service manages user memory blocks inspired by MemGPT/Letta,
tracking user preferences, domain knowledge, and conversation patterns
for personalized SQL assistance.
"""

import os
import sys
import logging
import json
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta

# Add parent directories to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from config.settings import AZURE_OPENAI_KEY, AZURE_OPENAI_API_VERSION, AZURE_OPENAI_ENDPOINT, AZURE_OPENAI_DEPLOYMENT_NAME
from models.models import db, User, UserMemory, QueryHistory
from langchain_openai import AzureChatOpenAI
from langchain_core.messages import SystemMessage, HumanMessage

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class UserMemoryService:
    """Service for managing user memory blocks and personalization"""
    
    def __init__(self, verbose: bool = True):
        """
        Initialize the user memory service
        
        Args:
            verbose: Whether to enable verbose logging
        """
        self.verbose = verbose
        self.llm = AzureChatOpenAI(
            azure_deployment=AZURE_OPENAI_DEPLOYMENT_NAME,
            openai_api_version=AZURE_OPENAI_API_VERSION,
            azure_endpoint=AZURE_OPENAI_ENDPOINT,
            openai_api_key=AZURE_OPENAI_KEY,
            temperature=0.1
        )
        
        # Config-driven trainers (email IDs that can update memory)
        self.trainer_emails = self._get_trainer_emails()
        
        if self.verbose:
            logger.info("Initialized UserMemoryService")
    
    def initialize_user_memory(self, user_id: int) -> Dict[str, Any]:
        """
        Initialize memory blocks for a new user
        
        Args:
            user_id: ID of the user
            
        Returns:
            Dictionary with initialization results
        """
        try:
            user = User.query.get(user_id)
            if not user:
                raise ValueError(f"User {user_id} not found")
            
            # Check if user already has memory blocks
            existing_memory = UserMemory.query.filter_by(user_id=user_id).first()
            if existing_memory:
                return {
                    'status': 'exists',
                    'message': 'User memory already initialized'
                }
            
            # Create initial memory blocks
            initial_blocks = [
                {
                    'memory_type': 'persona',
                    'label': 'user_identity',
                    'content': f"User email: {user.email}. This user is learning to work with databases and SQL queries.",
                    'priority': 10
                },
                {
                    'memory_type': 'preferences',
                    'label': 'query_style',
                    'content': "No specific query preferences established yet. User is open to learning different approaches.",
                    'priority': 8
                },
                {
                    'memory_type': 'domain_knowledge',
                    'label': 'sql_expertise',
                    'content': "SQL expertise level unknown. Will assess based on query complexity and feedback.",
                    'priority': 9
                },
                {
                    'memory_type': 'query_patterns',
                    'label': 'common_requests',
                    'content': "No established query patterns yet. Will learn from user interactions.",
                    'priority': 7
                }
            ]
            
            created_blocks = []
            for block_data in initial_blocks:
                memory_block = UserMemory(
                    user_id=user_id,
                    memory_type=block_data['memory_type'],
                    label=block_data['label'],
                    content=block_data['content'],
                    priority=block_data['priority'],
                    source='initialization'
                )
                db.session.add(memory_block)
                created_blocks.append(block_data['label'])
            
            db.session.commit()
            
            if self.verbose:
                logger.info(f"Initialized memory for user {user_id}: {created_blocks}")
            
            return {
                'status': 'success',
                'created_blocks': created_blocks,
                'message': f'Initialized {len(created_blocks)} memory blocks'
            }
            
        except Exception as e:
            logger.error(f"Error initializing user memory: {str(e)}")
            return {
                'status': 'error',
                'error': str(e)
            }
    
    def get_user_memory(self, user_id: int, memory_types: Optional[List[str]] = None) -> Dict[str, Any]:
        """
        Get user memory blocks
        
        Args:
            user_id: ID of the user
            memory_types: Optional list of memory types to filter by
            
        Returns:
            Dictionary with user memory blocks
        """
        query = UserMemory.query.filter_by(user_id=user_id, is_active=True)
        
        if memory_types:
            query = query.filter(UserMemory.memory_type.in_(memory_types))
        
        memory_blocks = query.order_by(UserMemory.priority.desc(), UserMemory.last_accessed.desc()).all()
        
        # Update access tracking
        for block in memory_blocks:
            block.last_accessed = datetime.utcnow()
            block.access_count += 1
        
        db.session.commit()
        
        # Format memory blocks
        formatted_blocks = {}
        for block in memory_blocks:
            if block.memory_type not in formatted_blocks:
                formatted_blocks[block.memory_type] = []
            
            formatted_blocks[block.memory_type].append({
                'id': block.id,
                'label': block.label,
                'content': block.content,
                'priority': block.priority,
                'confidence_score': block.confidence_score,
                'database_context': block.database_context,
                'last_accessed': block.last_accessed.isoformat() if block.last_accessed else None,
                'access_count': block.access_count
            })
        
        return {
            'user_id': user_id,
            'memory_blocks': formatted_blocks,
            'total_blocks': len(memory_blocks)
        }
    
    def update_memory_block(self, user_id: int, memory_type: str, label: str, 
                           content: str, updater_email: str = None) -> Dict[str, Any]:
        """
        Update or create a memory block (MemGPT-style editing)
        
        Args:
            user_id: ID of the user
            memory_type: Type of memory block
            label: Label for the memory block
            content: New content for the memory block
            updater_email: Email of the person updating (for trainer validation)
            
        Returns:
            Dictionary with update results
        """
        try:
            # Check if updater is authorized (for training scenarios)
            if updater_email and updater_email not in self.trainer_emails:
                return {
                    'status': 'unauthorized',
                    'error': 'Only authorized trainers can update user memory'
                }
            
            # Find existing memory block
            existing_block = UserMemory.query.filter_by(
                user_id=user_id,
                memory_type=memory_type,
                label=label
            ).first()
            
            if existing_block:
                # Update existing block
                existing_block.content = content
                existing_block.confidence_score = 1.0 if updater_email in self.trainer_emails else 0.8
                existing_block.source = 'training' if updater_email in self.trainer_emails else 'conversation'
                existing_block.updated_at = datetime.utcnow()
                action = 'updated'
            else:
                # Create new block
                new_block = UserMemory(
                    user_id=user_id,
                    memory_type=memory_type,
                    label=label,
                    content=content,
                    confidence_score=1.0 if updater_email in self.trainer_emails else 0.8,
                    source='training' if updater_email in self.trainer_emails else 'conversation',
                    priority=8 if memory_type == 'preferences' else 7
                )
                db.session.add(new_block)
                action = 'created'
            
            db.session.commit()
            
            if self.verbose:
                logger.info(f"{action.capitalize()} memory block for user {user_id}: {memory_type}.{label}")
            
            return {
                'status': 'success',
                'action': action,
                'memory_type': memory_type,
                'label': label
            }
            
        except Exception as e:
            logger.error(f"Error updating memory block: {str(e)}")
            return {
                'status': 'error',
                'error': str(e)
            }
    
    def learn_from_interaction(self, user_id: int, query: str, sql_generated: str, 
                              feedback: Optional[str] = None, database_id: Optional[int] = None) -> Dict[str, Any]:
        """
        Learn from user interaction and update memory accordingly
        
        Args:
            user_id: ID of the user
            query: Natural language query
            sql_generated: Generated SQL
            feedback: Optional user feedback
            database_id: Optional database context
            
        Returns:
            Dictionary with learning results
        """
        try:
            # Analyze the interaction to extract insights
            insights = self._analyze_interaction(query, sql_generated, feedback)
            
            # Update relevant memory blocks
            updates_made = []
            
            # Update query patterns
            if insights.get('query_pattern'):
                pattern_result = self._update_query_patterns(user_id, insights['query_pattern'], database_id)
                if pattern_result['status'] == 'success':
                    updates_made.append('query_patterns')
            
            # Update domain knowledge
            if insights.get('domain_knowledge'):
                domain_result = self._update_domain_knowledge(user_id, insights['domain_knowledge'])
                if domain_result['status'] == 'success':
                    updates_made.append('domain_knowledge')
            
            # Update preferences based on feedback
            if feedback and insights.get('preference_update'):
                pref_result = self._update_preferences(user_id, insights['preference_update'])
                if pref_result['status'] == 'success':
                    updates_made.append('preferences')
            
            if self.verbose and updates_made:
                logger.info(f"Updated memory for user {user_id}: {updates_made}")
            
            return {
                'status': 'success',
                'updates_made': updates_made,
                'insights': insights
            }
            
        except Exception as e:
            logger.error(f"Error learning from interaction: {str(e)}")
            return {
                'status': 'error',
                'error': str(e)
            }
    
    def _get_trainer_emails(self) -> List[str]:
        """Get list of authorized trainer emails from config"""
        # This would typically come from environment variables or config file
        # For now, return a default list
        return [
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>'
        ]

    def _analyze_interaction(self, query: str, sql_generated: str, feedback: Optional[str] = None) -> Dict[str, Any]:
        """Analyze user interaction to extract learning insights"""
        insights = {}

        # Analyze query complexity and type
        query_lower = query.lower()

        # Determine query type
        if any(word in query_lower for word in ['count', 'how many', 'total']):
            query_type = 'aggregation'
        elif any(word in query_lower for word in ['show', 'list', 'get', 'find']):
            query_type = 'retrieval'
        elif any(word in query_lower for word in ['compare', 'vs', 'versus', 'difference']):
            query_type = 'comparison'
        elif any(word in query_lower for word in ['trend', 'over time', 'monthly', 'yearly']):
            query_type = 'temporal'
        else:
            query_type = 'general'

        insights['query_pattern'] = {
            'type': query_type,
            'complexity': self._assess_query_complexity(sql_generated),
            'keywords': self._extract_keywords(query)
        }

        # Analyze SQL complexity for domain knowledge
        sql_features = self._analyze_sql_features(sql_generated)
        if sql_features:
            insights['domain_knowledge'] = {
                'sql_features_used': sql_features,
                'complexity_level': insights['query_pattern']['complexity']
            }

        # Analyze feedback for preferences
        if feedback:
            preference_insights = self._analyze_feedback(feedback)
            if preference_insights:
                insights['preference_update'] = preference_insights

        return insights

    def _assess_query_complexity(self, sql: str) -> str:
        """Assess the complexity of generated SQL"""
        sql_lower = sql.lower()

        complexity_indicators = {
            'simple': 0,
            'medium': 0,
            'complex': 0
        }

        # Simple indicators
        if 'select' in sql_lower and 'from' in sql_lower:
            complexity_indicators['simple'] += 1

        # Medium indicators
        if any(word in sql_lower for word in ['join', 'group by', 'having', 'order by']):
            complexity_indicators['medium'] += 1

        if any(word in sql_lower for word in ['sum', 'count', 'avg', 'max', 'min']):
            complexity_indicators['medium'] += 1

        # Complex indicators
        if any(word in sql_lower for word in ['subquery', 'with', 'case when', 'window']):
            complexity_indicators['complex'] += 2

        if sql_lower.count('select') > 1:  # Multiple selects
            complexity_indicators['complex'] += 1

        if sql_lower.count('join') > 2:  # Multiple joins
            complexity_indicators['complex'] += 1

        # Determine overall complexity
        if complexity_indicators['complex'] > 0:
            return 'complex'
        elif complexity_indicators['medium'] > 1:
            return 'medium'
        else:
            return 'simple'

    def _extract_keywords(self, query: str) -> List[str]:
        """Extract meaningful keywords from query"""
        # Simple keyword extraction (could be enhanced with NLP)
        stop_words = {'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might', 'can', 'show', 'get', 'find', 'list'}

        words = query.lower().split()
        keywords = [word.strip('.,!?') for word in words if word.strip('.,!?') not in stop_words and len(word) > 2]

        return keywords[:10]  # Limit to top 10 keywords

    def _analyze_sql_features(self, sql: str) -> List[str]:
        """Analyze SQL features used in the query"""
        sql_lower = sql.lower()
        features = []

        feature_patterns = {
            'joins': ['join', 'inner join', 'left join', 'right join', 'full join'],
            'aggregation': ['sum', 'count', 'avg', 'max', 'min', 'group by'],
            'filtering': ['where', 'having'],
            'sorting': ['order by'],
            'subqueries': ['select' if sql_lower.count('select') > 1 else None],
            'window_functions': ['over', 'partition by'],
            'case_statements': ['case when'],
            'date_functions': ['date', 'year', 'month', 'day']
        }

        for feature_name, patterns in feature_patterns.items():
            if any(pattern and pattern in sql_lower for pattern in patterns):
                features.append(feature_name)

        return features

    def _analyze_feedback(self, feedback: str) -> Optional[Dict[str, Any]]:
        """Analyze user feedback to extract preference insights"""
        feedback_lower = feedback.lower()

        preferences = {}

        # Analyze feedback sentiment and content
        if any(word in feedback_lower for word in ['good', 'great', 'perfect', 'correct', 'right']):
            preferences['feedback_sentiment'] = 'positive'
        elif any(word in feedback_lower for word in ['wrong', 'incorrect', 'bad', 'error']):
            preferences['feedback_sentiment'] = 'negative'

        # Analyze specific preferences
        if any(word in feedback_lower for word in ['simple', 'simpler', 'basic']):
            preferences['complexity_preference'] = 'simple'
        elif any(word in feedback_lower for word in ['detailed', 'complex', 'advanced']):
            preferences['complexity_preference'] = 'complex'

        if any(word in feedback_lower for word in ['explain', 'explanation', 'why']):
            preferences['wants_explanations'] = True

        return preferences if preferences else None

    def _update_query_patterns(self, user_id: int, pattern_info: Dict[str, Any], database_id: Optional[int] = None) -> Dict[str, Any]:
        """Update user's query patterns memory"""
        try:
            # Get existing query patterns
            existing_block = UserMemory.query.filter_by(
                user_id=user_id,
                memory_type='query_patterns',
                label='common_requests'
            ).first()

            if existing_block:
                # Parse existing patterns
                try:
                    existing_patterns = json.loads(existing_block.content) if existing_block.content.startswith('{') else {'patterns': []}
                except:
                    existing_patterns = {'patterns': []}

                # Add new pattern
                new_pattern = {
                    'type': pattern_info['type'],
                    'complexity': pattern_info['complexity'],
                    'keywords': pattern_info['keywords'],
                    'database_id': database_id,
                    'timestamp': datetime.utcnow().isoformat()
                }

                existing_patterns['patterns'].append(new_pattern)

                # Keep only last 20 patterns
                existing_patterns['patterns'] = existing_patterns['patterns'][-20:]

                existing_block.content = json.dumps(existing_patterns)
                existing_block.updated_at = datetime.utcnow()

            db.session.commit()
            return {'status': 'success'}

        except Exception as e:
            logger.error(f"Error updating query patterns: {str(e)}")
            return {'status': 'error', 'error': str(e)}

    def _update_domain_knowledge(self, user_id: int, knowledge_info: Dict[str, Any]) -> Dict[str, Any]:
        """Update user's domain knowledge memory"""
        try:
            existing_block = UserMemory.query.filter_by(
                user_id=user_id,
                memory_type='domain_knowledge',
                label='sql_expertise'
            ).first()

            if existing_block:
                # Update SQL expertise assessment
                features_used = knowledge_info.get('sql_features_used', [])
                complexity = knowledge_info.get('complexity_level', 'simple')

                # Simple expertise assessment
                if complexity == 'complex' or len(features_used) > 3:
                    expertise_level = 'advanced'
                elif complexity == 'medium' or len(features_used) > 1:
                    expertise_level = 'intermediate'
                else:
                    expertise_level = 'beginner'

                existing_block.content = f"SQL expertise level: {expertise_level}. " \
                                       f"Comfortable with: {', '.join(features_used)}. " \
                                       f"Typically works with {complexity} queries."
                existing_block.updated_at = datetime.utcnow()

            db.session.commit()
            return {'status': 'success'}

        except Exception as e:
            logger.error(f"Error updating domain knowledge: {str(e)}")
            return {'status': 'error', 'error': str(e)}

    def _update_preferences(self, user_id: int, preference_info: Dict[str, Any]) -> Dict[str, Any]:
        """Update user's preferences memory"""
        try:
            existing_block = UserMemory.query.filter_by(
                user_id=user_id,
                memory_type='preferences',
                label='query_style'
            ).first()

            if existing_block:
                # Parse existing preferences
                try:
                    existing_prefs = json.loads(existing_block.content) if existing_block.content.startswith('{') else {}
                except:
                    existing_prefs = {}

                # Update with new preferences
                existing_prefs.update(preference_info)

                existing_block.content = json.dumps(existing_prefs)
                existing_block.updated_at = datetime.utcnow()

            db.session.commit()
            return {'status': 'success'}

        except Exception as e:
            logger.error(f"Error updating preferences: {str(e)}")
            return {'status': 'error', 'error': str(e)}
