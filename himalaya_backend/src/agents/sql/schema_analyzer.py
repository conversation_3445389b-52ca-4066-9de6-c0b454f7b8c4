"""
Database Schema Analyzer

This service analyzes database schemas to understand relationships,
build join graphs, and provide semantic understanding for query generation.
"""

import os
import sys
import logging
from typing import Dict, List, Any, Optional, Set, Tuple
import networkx as nx
from collections import defaultdict

# Add parent directories to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from models.models import (
    DatabaseConnection, DatabaseSchema, DatabaseColumn, 
    SemanticMapping, UserDatabaseAccess, DatabaseRole
)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class DatabaseSchemaAnalyzer:
    """Analyzes database schemas to understand structure and relationships"""
    
    def __init__(self, verbose: bool = True):
        """
        Initialize the schema analyzer
        
        Args:
            verbose: Whether to enable verbose logging
        """
        self.verbose = verbose
        if self.verbose:
            logger.info("Initialized DatabaseSchemaAnalyzer")
    
    def build_join_graph(self, database_id: int) -> nx.DiGraph:
        """
        Build a directed graph representing table relationships
        
        Args:
            database_id: ID of the database
            
        Returns:
            NetworkX directed graph with tables as nodes and foreign keys as edges
        """
        graph = nx.DiGraph()
        
        # Get all tables for the database
        schemas = DatabaseSchema.query.filter_by(database_id=database_id).all()
        
        # Add nodes (tables)
        for schema in schemas:
            graph.add_node(schema.table_name, 
                          schema_id=schema.id,
                          row_count=schema.row_count,
                          description=schema.llm_generated_description)
        
        # Add edges (foreign key relationships)
        for schema in schemas:
            columns = DatabaseColumn.query.filter_by(schema_id=schema.id).all()
            
            for column in columns:
                if column.is_foreign_key and column.foreign_key_table:
                    # Add edge from current table to referenced table
                    graph.add_edge(
                        schema.table_name,
                        column.foreign_key_table,
                        column=column.column_name,
                        foreign_column=column.foreign_key_column,
                        relationship_type='foreign_key'
                    )
        
        if self.verbose:
            logger.info(f"Built join graph with {graph.number_of_nodes()} tables and {graph.number_of_edges()} relationships")
        
        return graph
    
    def find_join_path(self, database_id: int, source_table: str, target_table: str) -> List[Dict[str, Any]]:
        """
        Find the shortest join path between two tables
        
        Args:
            database_id: ID of the database
            source_table: Source table name
            target_table: Target table name
            
        Returns:
            List of join steps with table and column information
        """
        graph = self.build_join_graph(database_id)
        
        try:
            # Find shortest path (undirected for flexibility)
            undirected_graph = graph.to_undirected()
            path = nx.shortest_path(undirected_graph, source_table, target_table)
            
            # Build join steps
            join_steps = []
            for i in range(len(path) - 1):
                current_table = path[i]
                next_table = path[i + 1]
                
                # Get edge data (check both directions)
                edge_data = None
                if graph.has_edge(current_table, next_table):
                    edge_data = graph[current_table][next_table]
                    join_direction = 'forward'
                elif graph.has_edge(next_table, current_table):
                    edge_data = graph[next_table][current_table]
                    join_direction = 'reverse'
                
                if edge_data:
                    if join_direction == 'forward':
                        join_step = {
                            'from_table': current_table,
                            'to_table': next_table,
                            'from_column': edge_data['column'],
                            'to_column': edge_data['foreign_column'],
                            'join_type': 'INNER'  # Default, can be customized
                        }
                    else:
                        join_step = {
                            'from_table': current_table,
                            'to_table': next_table,
                            'from_column': edge_data['foreign_column'],
                            'to_column': edge_data['column'],
                            'join_type': 'INNER'
                        }
                    
                    join_steps.append(join_step)
            
            return join_steps
            
        except nx.NetworkXNoPath:
            if self.verbose:
                logger.warning(f"No join path found between {source_table} and {target_table}")
            return []
    
    def get_table_info(self, database_id: int, table_name: str) -> Optional[Dict[str, Any]]:
        """
        Get comprehensive information about a table
        
        Args:
            database_id: ID of the database
            table_name: Name of the table
            
        Returns:
            Dictionary with table information
        """
        schema = DatabaseSchema.query.filter_by(
            database_id=database_id,
            table_name=table_name
        ).first()
        
        if not schema:
            return None
        
        columns = DatabaseColumn.query.filter_by(schema_id=schema.id).all()
        
        column_info = []
        primary_keys = []
        foreign_keys = []
        
        for column in columns:
            col_info = {
                'name': column.column_name,
                'type': column.data_type,
                'nullable': column.is_nullable,
                'description': column.llm_generated_description,
                'sample_values': column.sample_values,
                'distinct_count': column.distinct_count,
                'null_percentage': column.null_percentage
            }
            
            if column.is_primary_key:
                primary_keys.append(column.column_name)
            
            if column.is_foreign_key:
                foreign_keys.append({
                    'column': column.column_name,
                    'references_table': column.foreign_key_table,
                    'references_column': column.foreign_key_column
                })
            
            column_info.append(col_info)
        
        return {
            'table_name': table_name,
            'description': schema.llm_generated_description,
            'row_count': schema.row_count,
            'columns': column_info,
            'primary_keys': primary_keys,
            'foreign_keys': foreign_keys
        }
    
    def get_related_tables(self, database_id: int, table_name: str, max_depth: int = 2) -> List[str]:
        """
        Get tables related to the given table within specified depth
        
        Args:
            database_id: ID of the database
            table_name: Name of the source table
            max_depth: Maximum relationship depth to explore
            
        Returns:
            List of related table names
        """
        graph = self.build_join_graph(database_id)
        
        if table_name not in graph:
            return []
        
        # Use BFS to find related tables within max_depth
        related_tables = set()
        visited = set()
        queue = [(table_name, 0)]
        
        while queue:
            current_table, depth = queue.pop(0)
            
            if current_table in visited or depth > max_depth:
                continue
            
            visited.add(current_table)
            if depth > 0:  # Don't include the source table itself
                related_tables.add(current_table)
            
            # Add neighbors to queue
            for neighbor in graph.neighbors(current_table):
                if neighbor not in visited:
                    queue.append((neighbor, depth + 1))
            
            # Also check predecessors (reverse relationships)
            for predecessor in graph.predecessors(current_table):
                if predecessor not in visited:
                    queue.append((predecessor, depth + 1))
        
        return list(related_tables)
    
    def suggest_tables_for_query(self, database_id: int, query_keywords: List[str]) -> List[Dict[str, Any]]:
        """
        Suggest relevant tables based on query keywords
        
        Args:
            database_id: ID of the database
            query_keywords: List of keywords from the user query
            
        Returns:
            List of suggested tables with relevance scores
        """
        schemas = DatabaseSchema.query.filter_by(database_id=database_id).all()
        suggestions = []
        
        for schema in schemas:
            relevance_score = 0
            matching_reasons = []
            
            # Check table name
            table_name_lower = schema.table_name.lower()
            for keyword in query_keywords:
                keyword_lower = keyword.lower()
                if keyword_lower in table_name_lower:
                    relevance_score += 10
                    matching_reasons.append(f"Table name contains '{keyword}'")
            
            # Check table description
            if schema.llm_generated_description:
                description_lower = schema.llm_generated_description.lower()
                for keyword in query_keywords:
                    keyword_lower = keyword.lower()
                    if keyword_lower in description_lower:
                        relevance_score += 5
                        matching_reasons.append(f"Description mentions '{keyword}'")
            
            # Check column names and descriptions
            columns = DatabaseColumn.query.filter_by(schema_id=schema.id).all()
            for column in columns:
                column_name_lower = column.column_name.lower()
                for keyword in query_keywords:
                    keyword_lower = keyword.lower()
                    if keyword_lower in column_name_lower:
                        relevance_score += 3
                        matching_reasons.append(f"Column '{column.column_name}' contains '{keyword}'")
                
                if column.llm_generated_description:
                    col_desc_lower = column.llm_generated_description.lower()
                    for keyword in query_keywords:
                        keyword_lower = keyword.lower()
                        if keyword_lower in col_desc_lower:
                            relevance_score += 2
                            matching_reasons.append(f"Column '{column.column_name}' description mentions '{keyword}'")
            
            if relevance_score > 0:
                suggestions.append({
                    'table_name': schema.table_name,
                    'relevance_score': relevance_score,
                    'matching_reasons': matching_reasons,
                    'description': schema.llm_generated_description,
                    'row_count': schema.row_count
                })
        
        # Sort by relevance score
        suggestions.sort(key=lambda x: x['relevance_score'], reverse=True)
        
        return suggestions
    
    def get_database_summary(self, database_id: int) -> Dict[str, Any]:
        """
        Get a comprehensive summary of the database structure
        
        Args:
            database_id: ID of the database
            
        Returns:
            Dictionary with database summary information
        """
        schemas = DatabaseSchema.query.filter_by(database_id=database_id).all()
        
        total_tables = len(schemas)
        total_columns = 0
        total_rows = 0
        table_summaries = []
        
        for schema in schemas:
            columns = DatabaseColumn.query.filter_by(schema_id=schema.id).all()
            column_count = len(columns)
            total_columns += column_count
            total_rows += schema.row_count or 0
            
            table_summaries.append({
                'name': schema.table_name,
                'description': schema.llm_generated_description,
                'column_count': column_count,
                'row_count': schema.row_count
            })
        
        # Build relationship summary
        graph = self.build_join_graph(database_id)
        relationship_count = graph.number_of_edges()
        
        return {
            'total_tables': total_tables,
            'total_columns': total_columns,
            'total_rows': total_rows,
            'total_relationships': relationship_count,
            'tables': table_summaries
        }
