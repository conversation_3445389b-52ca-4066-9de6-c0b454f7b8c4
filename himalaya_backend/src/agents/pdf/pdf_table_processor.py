"""
PDF Table Processor Agent

This agent handles PDF-specific queries and table processing:
- Processes tables extracted from PDFs
- Provides PDF-specific analysis capabilities
- Integrates with the agentic system architecture
"""

import os
import sys
from pathlib import Path
import pandas as pd
from typing import Optional, Dict, Any, List
import logging

# Add the src directory to the path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from models.models import db, File, DocumentTable, CSVFile
from agents.csv.excel_langchain_rag import ExcelLangChainRAG

logger = logging.getLogger(__name__)

class PDFTableProcessor:
    """
    PDF Table Processor Agent for handling PDF-specific queries and table analysis.
    """
    
    def __init__(self, data_dir="data", verbose=True):
        """
        Initialize the PDF Table Processor.
        
        Args:
            data_dir: Directory for data storage
            verbose: Whether to print verbose output
        """
        self.data_dir = data_dir
        self.verbose = verbose
        
        # Initialize CSV agent for table processing
        self.csv_agent = ExcelLangChainRAG(data_dir=data_dir, verbose=verbose)
        
        if self.verbose:
            print("PDF Table Processor initialized")
    
    def process_pdf_query(self, query: str, pdf_documents: List[Dict[str, Any]], 
                          conversation_history: str = None, conversation_summary: str = None,
                          user_context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Process a query related to PDF documents using intelligent context-aware decision making.
        
        Args:
            query: The user query
            pdf_documents: List of PDF documents from search results
            conversation_history: Previous conversation context
            conversation_summary: Summary of conversation
            user_context: Additional user context (preferences, patterns, etc.)
            
        Returns:
            Processing result with answer and metadata
        """
        if self.verbose:
            print(f"\n=== PDF Agent Processing Query ===")
            print(f"Query: {query}")
            print(f"PDF Documents: {len(pdf_documents)}")
        
        try:
            # Extract file IDs from PDF documents
            pdf_file_ids = []
            for doc in pdf_documents:
                file_id = self._extract_file_id_from_document(doc)
                if file_id:
                    pdf_file_ids.append(file_id)
            
            if not pdf_file_ids:
                return {
                    "answer": "No valid PDF files found for processing.",
                    "sources": [],
                    "metadata": {"agent": "pdf", "status": "no_files"}
                }
            
            # Get available table context
            extracted_tables = self._find_extracted_tables(pdf_file_ids)
            
            # Perform intelligent context-aware analysis
            processing_decision = self._intelligent_processing_decision(
                query=query,
                pdf_documents=pdf_documents,
                available_tables=extracted_tables,
                conversation_history=conversation_history,
                conversation_summary=conversation_summary,
                user_context=user_context or {}
            )
            
            if self.verbose:
                print(f"🧠 PDF AGENT: Intelligent Decision Analysis:")
                print(f"   Processing Type: {processing_decision['processing_type']}")
                print(f"   Confidence: {processing_decision['confidence']:.2f}")
                print(f"   Reasoning: {processing_decision['reasoning']}")
                if processing_decision.get('context_factors'):
                    print(f"   Key Factors: {', '.join(processing_decision['context_factors'])}")
            
            needs_table_analysis = processing_decision['processing_type'] == 'TABLE'
            
            if not needs_table_analysis:
                # Query is asking for document summary/overview, use text-based processing
                if self.verbose:
                    print("🔍 PDF AGENT: Routing to text-based processing (summary/overview query)")
                result = self._process_text_based_query(query, pdf_documents)
                # Add the intelligent decision information to metadata
                result["metadata"]["processing_decision"] = processing_decision
                result["metadata"]["processing_type"] = processing_decision['processing_type']
                return result
            
            # Query needs table analysis, check if tables are available
            if not extracted_tables:
                # No extracted tables available, fall back to text-based processing
                if self.verbose:
                    print("🔍 PDF AGENT: No tables found, falling back to text-based processing")
                return self._process_text_based_query(query, pdf_documents)
            
            # Process tables using CSV agent
            if self.verbose:
                print(f"🔍 PDF AGENT: Routing to table-based processing with {len(extracted_tables)} tables")
            result = self._process_table_based_query(query, extracted_tables, pdf_documents)
            # Add the intelligent decision information to metadata
            result["metadata"]["processing_decision"] = processing_decision
            result["metadata"]["processing_type"] = processing_decision['processing_type']
            return result
            
        except Exception as e:
            logger.error(f"Error in PDF agent processing: {str(e)}")
            return {
                "answer": f"Error processing PDF query: {str(e)}",
                "sources": [],
                "metadata": {"agent": "pdf", "status": "error", "error": str(e)}
            }
    
    def _intelligent_processing_decision(self, query: str, pdf_documents: List[Dict[str, Any]], 
                                       available_tables: List[Dict[str, Any]], 
                                       conversation_history: str = None,
                                       conversation_summary: str = None,
                                       user_context: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Make an intelligent, context-aware decision about processing approach using comprehensive analysis.
        
        Args:
            query: The user query
            pdf_documents: Available PDF documents
            available_tables: Available extracted tables
            conversation_history: Previous conversation context
            conversation_summary: Summary of conversation
            user_context: Additional user context
            
        Returns:
            Dict with processing_type, confidence, reasoning, and context_factors
        """
        try:
            from openai import AzureOpenAI
            from config.settings import (
                AZURE_OPENAI_KEY, AZURE_OPENAI_ENDPOINT, 
                AZURE_OPENAI_API_VERSION, AZURE_OPENAI_DEPLOYMENT_NAME
            )
            
            client = AzureOpenAI(
                api_key=AZURE_OPENAI_KEY,
                api_version=AZURE_OPENAI_API_VERSION,
                azure_endpoint=AZURE_OPENAI_ENDPOINT
            )
            
            # Build comprehensive context for analysis
            context_info = self._build_analysis_context(
                query, pdf_documents, available_tables, 
                conversation_history, conversation_summary, user_context
            )
            
            intelligent_analysis_prompt = f"""
            You are an expert AI system that makes intelligent decisions about document processing approaches based on comprehensive context analysis.

            **USER'S ORIGINAL INTENT**: "{query}"
            
            **CONTEXT FOR INTENT UNDERSTANDING**:
            - This query represents the user's specific information need
            - Your decision should directly support fulfilling this intent
            - Consider what type of response would best serve the user's goal

            **AVAILABLE CONTEXT**:
            {context_info}

            **YOUR TASK**: Analyze ALL available context to determine the optimal processing approach.

            **PROCESSING OPTIONS**:
            1. **TABLE**: Analyze structured data from extracted tables
            2. **DOCUMENT**: Analyze document content and narrative

            **DECISION FACTORS TO CONSIDER**:
            1. **Query Intent**: What is the user actually trying to accomplish?
            2. **Content Availability**: What data/content is actually available?
            3. **Conversation Context**: What has been discussed before?
            4. **User Patterns**: What are the user's typical information needs?
            5. **Data Relevance**: How relevant are available tables to the query?
            6. **Complexity Match**: Does the query complexity match available data?

            **INTELLIGENT DECISION RULES**:
            - If query seeks specific calculations/data AND relevant tables exist → TABLE
            - If query is exploratory/conceptual regardless of table availability → DOCUMENT  
            - If conversation history shows data focus AND tables are relevant → TABLE
            - If user typically needs summaries/overviews → DOCUMENT
            - If query is follow-up to previous table analysis → Consider TABLE
            - If query is about document themes/concepts → DOCUMENT
            - If format preference (tabular presentation) but content focus → DOCUMENT

            **RESPONSE FORMAT** (JSON):
            {{
                "processing_type": "TABLE" or "DOCUMENT",
                "confidence": 0.0-1.0,
                "reasoning": "Detailed explanation of decision logic",
                "context_factors": ["list", "of", "key", "factors", "considered"],
                "table_relevance_score": 0.0-1.0,
                "conversation_influence": 0.0-1.0
            }}

            Make your decision based on comprehensive analysis, not simple keyword matching.
            """
            
            response = client.chat.completions.create(
                model=AZURE_OPENAI_DEPLOYMENT_NAME,
                messages=[
                    {"role": "system", "content": "You are an expert AI decision system. Analyze all context comprehensively and respond with valid JSON only."},
                    {"role": "user", "content": intelligent_analysis_prompt}
                ],
                temperature=0.2,
                max_tokens=500
            )
            
            # Parse the JSON response
            import json
            decision_data = json.loads(response.choices[0].message.content.strip())
            
            # Validate and set defaults
            decision = {
                'processing_type': decision_data.get('processing_type', 'DOCUMENT').upper(),
                'confidence': float(decision_data.get('confidence', 0.5)),
                'reasoning': decision_data.get('reasoning', 'Default decision based on available context'),
                'context_factors': decision_data.get('context_factors', []),
                'table_relevance_score': float(decision_data.get('table_relevance_score', 0.0)),
                'conversation_influence': float(decision_data.get('conversation_influence', 0.0))
            }
            
            return decision
            
        except Exception as e:
            logger.warning(f"Intelligent decision analysis failed, using enhanced fallback: {str(e)}")
            return self._enhanced_fallback_decision(query, available_tables, conversation_history)

    def _build_analysis_context(self, query: str, pdf_documents: List[Dict[str, Any]], 
                               available_tables: List[Dict[str, Any]], 
                               conversation_history: str = None,
                               conversation_summary: str = None,
                               user_context: Dict[str, Any] = None) -> str:
        """
        Build comprehensive context information for intelligent analysis.
        """
        context_parts = []
        
        # Document context
        context_parts.append(f"**DOCUMENTS AVAILABLE**: {len(pdf_documents)} PDF documents")
        if pdf_documents:
            doc_titles = [doc.get('metadata', {}).get('title', 'Unknown') for doc in pdf_documents[:3]]
            context_parts.append(f"Sample documents: {', '.join(doc_titles)}")
        
        # Table context
        context_parts.append(f"**TABLES AVAILABLE**: {len(available_tables)} extracted tables")
        if available_tables:
            table_summaries = []
            for table in available_tables[:3]:
                summary = table.get('semantic_summary', 'No summary')[:100]
                rows = table.get('row_count', 0)
                cols = table.get('column_count', 0)
                table_summaries.append(f"Table ({rows}x{cols}): {summary}")
            context_parts.append("Sample tables:\n" + "\n".join(table_summaries))
        
        # Conversation context
        if conversation_history:
            context_parts.append(f"**CONVERSATION HISTORY**: {conversation_history[-500:]}")
        if conversation_summary:
            context_parts.append(f"**CONVERSATION SUMMARY**: {conversation_summary}")
        
        # User context
        if user_context:
            context_parts.append(f"**USER CONTEXT**: {user_context}")
        
        return "\n\n".join(context_parts)

    def _enhanced_fallback_decision(self, query: str, available_tables: List[Dict[str, Any]], 
                                   conversation_history: str = None) -> Dict[str, Any]:
        """
        Enhanced fallback decision logic when AI analysis fails.
        """
        query_lower = query.lower()
        
        # Analyze query patterns
        calculation_patterns = ['calculate', 'sum', 'total', 'average', 'count', 'statistics', 'compare values', 'data analysis']
        format_patterns = ['tabular form', 'table format', 'in a table', 'as a table', 'show in table']
        summary_patterns = ['summary', 'overview', 'explain', 'describe', 'what is', 'tell me about', 'summarize']
        
        has_calculation_intent = any(pattern in query_lower for pattern in calculation_patterns)
        has_format_preference = any(pattern in query_lower for pattern in format_patterns)
        has_summary_intent = any(pattern in query_lower for pattern in summary_patterns)
        
        # Check table relevance
        table_relevance = 0.0
        if available_tables:
            # Simple relevance check based on table summaries
            for table in available_tables:
                summary = table.get('semantic_summary', '').lower()
                if any(word in summary for word in query_lower.split()):
                    table_relevance += 0.3
            table_relevance = min(table_relevance, 1.0)
        
        # Decision logic
        if has_calculation_intent and table_relevance > 0.3:
            return {
                'processing_type': 'TABLE',
                'confidence': 0.7,
                'reasoning': 'Calculation intent detected with relevant tables available',
                'context_factors': ['calculation_intent', 'relevant_tables'],
                'table_relevance_score': table_relevance,
                'conversation_influence': 0.0
            }
        elif has_summary_intent or has_format_preference:
            return {
                'processing_type': 'DOCUMENT',
                'confidence': 0.8,
                'reasoning': 'Summary intent or format preference detected',
                'context_factors': ['summary_intent' if has_summary_intent else 'format_preference'],
                'table_relevance_score': table_relevance,
                'conversation_influence': 0.0
            }
        else:
            return {
                'processing_type': 'DOCUMENT',
                'confidence': 0.6,
                'reasoning': 'Default to document processing for ambiguous queries',
                'context_factors': ['ambiguous_query'],
                'table_relevance_score': table_relevance,
                'conversation_influence': 0.0
            }

    def _extract_file_id_from_document(self, document: Dict[str, Any]) -> Optional[int]:
        """
        Extract file ID from a document search result.
        
        Args:
            document: Document from search results
            
        Returns:
            File ID if found, None otherwise
        """
        try:
            # Try different ways to extract file ID
            if 'file_id' in document:
                return document['file_id']
            
            if 'metadata' in document:
                metadata = document['metadata']
                if 'file_id' in metadata:
                    return metadata['file_id']
                
                # Extract from chunk_id (format: chunk_<file_id>_<chunk_index>_<hash>)
                chunk_id = metadata.get('chunk_id', '')
                if chunk_id.startswith('chunk_'):
                    parts = chunk_id.split('_')
                    if len(parts) >= 2:
                        try:
                            return int(parts[1])
                        except ValueError:
                            pass
            
            return None
            
        except Exception as e:
            logger.warning(f"Error extracting file ID from document: {str(e)}")
            return None
    
    def _find_extracted_tables(self, pdf_file_ids: List[int]) -> List[Dict[str, Any]]:
        """
        Find extracted tables for the given PDF file IDs.
        
        Args:
            pdf_file_ids: List of PDF file IDs
            
        Returns:
            List of extracted table information
        """
        try:
            extracted_tables = []
            seen_csv_ids = set()  # Track unique CSV IDs to prevent duplicates
            
            # Check DocumentTable for extracted tables
            doc_tables = DocumentTable.query.filter(DocumentTable.file_id.in_(pdf_file_ids)).all()
            
            for doc_table in doc_tables:
                # Find corresponding CSV file in agentic tables
                csv_files = CSVFile.query.filter(
                    CSVFile.original_file_id == doc_table.file_id
                ).all()
                
                for csv_file in csv_files:
                    # Skip if we've already processed this CSV file
                    if csv_file.csv_id in seen_csv_ids:
                        if self.verbose:
                            print(f"Skipping duplicate CSV file: {csv_file.csv_id}")
                        continue
                        
                    seen_csv_ids.add(csv_file.csv_id)
                    
                    extracted_tables.append({
                        "document_table_id": doc_table.id,
                        "csv_file_id": csv_file.id,
                        "csv_id": csv_file.csv_id,
                        "file_path": csv_file.file_path,
                        "azure_url": csv_file.azure_url,
                        "sheet_name": csv_file.sheet_name,
                        "row_count": csv_file.row_count,
                        "column_count": csv_file.column_count,
                        "semantic_summary": csv_file.semantic_summary,
                        "original_file_id": doc_table.file_id,
                        "table_index": doc_table.table_index,
                        "page_number": doc_table.page_number
                    })
            
            if self.verbose:
                print(f"Found {len(extracted_tables)} unique extracted tables for PDF files")
                if len(seen_csv_ids) != len(extracted_tables):
                    print(f"Deduplicated {len(doc_tables) * len(csv_files) - len(extracted_tables)} duplicate tables")
            
            return extracted_tables
            
        except Exception as e:
            logger.error(f"Error finding extracted tables: {str(e)}")
            return []
    
    def _process_table_based_query(self, query: str, extracted_tables: List[Dict[str, Any]], pdf_documents: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Process query using extracted tables via CSV agent.
        
        Args:
            query: User query
            extracted_tables: List of extracted table information
            pdf_documents: Original PDF documents
            
        Returns:
            Processing result
        """
        if self.verbose:
            print(f"📊 PDF AGENT: Processing table-based query with {len(extracted_tables)} tables")
        
        try:
            # Prepare CSV documents for the CSV agent
            csv_documents = []
            for table in extracted_tables:
                csv_doc = {
                    "csv_id": table["csv_id"],
                    "file_path": table["azure_url"] if table["azure_url"] else table["file_path"],
                    "sheet_name": table["sheet_name"],
                    "semantic_summary": table["semantic_summary"],
                    "row_count": table["row_count"],
                    "column_count": table["column_count"],
                    "metadata": {
                        "original_file_id": table["original_file_id"],
                        "table_index": table["table_index"],
                        "page_number": table["page_number"]
                    }
                }
                csv_documents.append(csv_doc)
            
            # Use CSV agent to process the query
            csv_result = self.csv_agent.process_query_with_documents(query, csv_documents)
            
            # Enhance the result with PDF-specific metadata
            enhanced_result = {
                "answer": csv_result.get("answer", "No answer generated"),
                "sources": csv_result.get("sources", []),
                "metadata": {
                    "agent": "pdf",
                    "status": "success",
                    "processing_type": "table_based",
                    "tables_processed": len(extracted_tables),
                    "csv_agent_metadata": csv_result.get("metadata", {}),
                    "pdf_files": [doc.get('file_id') for doc in pdf_documents if doc.get('file_id')]
                }
            }
            
            return enhanced_result
            
        except Exception as e:
            logger.error(f"Error in table-based query processing: {str(e)}")
            return {
                "answer": f"Error processing tables: {str(e)}",
                "sources": [],
                "metadata": {"agent": "pdf", "status": "error", "error": str(e)}
            }
    
    def _process_text_based_query(self, query: str, pdf_documents: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Process query using text content from PDF documents with LLM analysis.
        
        Args:
            query: User query
            pdf_documents: PDF documents from search results
            
        Returns:
            Processing result
        """
        if self.verbose:
            print(f"📄 PDF AGENT: Processing text-based query with {len(pdf_documents)} PDF documents")
        
        try:
            # Combine text content from PDF documents
            combined_content = ""
            sources = []
            document_titles = set()
            
            for doc in pdf_documents:
                content = doc.get('content', doc.get('chunk', ''))
                if content:
                    combined_content += f"{content}\n\n"
                
                # Extract source information
                metadata = doc.get('metadata', {})
                title = metadata.get('title', 'Unknown PDF')
                document_titles.add(title)
                
                source_info = {
                    "chunk_id": metadata.get('chunk_id', ''),
                    "title": title,
                    "file_id": self._extract_file_id_from_document(doc)
                }
                sources.append(source_info)
            
            if not combined_content.strip():
                return {
                    "answer": "No readable content found in the PDF documents.",
                    "sources": sources,
                    "metadata": {"agent": "pdf", "status": "no_content"}
                }
            
            # Use LLM to generate intelligent response based on query and content
            document_list = ", ".join(document_titles) if document_titles else "PDF documents"
            
            prompt = f"""
            Based on the following PDF document content, please answer the user's query comprehensively and clearly.

            User Query: "{query}"

            Document Content:
            {combined_content[:4000]}  # Limit content to avoid token limits

            Instructions:
            1. Provide a direct, comprehensive answer to the user's query
            2. Focus on the most relevant information from the document(s)
            3. If the query asks for a summary or overview, provide key points and main themes
            4. Use clear, natural language without mentioning technical processing details
            5. If multiple documents are involved, synthesize information appropriately
            6. Be concise but thorough in your response

            Document(s) analyzed: {document_list}
            """
            
            try:
                from openai import AzureOpenAI
                from config.settings import (
                    AZURE_OPENAI_KEY, AZURE_OPENAI_ENDPOINT, 
                    AZURE_OPENAI_API_VERSION, AZURE_OPENAI_DEPLOYMENT_NAME
                )
                
                client = AzureOpenAI(
                    api_key=AZURE_OPENAI_KEY,
                    api_version=AZURE_OPENAI_API_VERSION,
                    azure_endpoint=AZURE_OPENAI_ENDPOINT
                )
                
                response = client.chat.completions.create(
                    model=AZURE_OPENAI_DEPLOYMENT_NAME,
                    messages=[
                        {"role": "system", "content": "You are a helpful AI assistant that analyzes PDF documents and provides clear, comprehensive answers based on their content."},
                        {"role": "user", "content": prompt}
                    ],
                    temperature=0.3,
                    max_tokens=1000
                )
                
                answer = response.choices[0].message.content.strip()
                
                if self.verbose:
                    print(f"📄 PDF AGENT: Generated LLM-based answer of {len(answer)} characters")
                
            except Exception as llm_error:
                logger.warning(f"LLM processing failed, falling back to content excerpt: {str(llm_error)}")
                # Fallback to content-based response
                answer = f"Based on the PDF document analysis:\n\n{combined_content[:800]}..."
            
            return {
                "answer": answer,
                "sources": sources,
                "metadata": {
                    "agent": "pdf",
                    "status": "success",
                    "processing_type": "text_based",
                    "content_length": len(combined_content),
                    "documents_processed": len(pdf_documents),
                    "document_titles": list(document_titles)
                }
            }
            
        except Exception as e:
            logger.error(f"Error in text-based query processing: {str(e)}")
            return {
                "answer": f"Error processing PDF content: {str(e)}",
                "sources": [],
                "metadata": {"agent": "pdf", "status": "error", "error": str(e)}
            }

def create_pdf_agent(data_dir="data", verbose=True) -> PDFTableProcessor:
    """
    Create a PDF Table Processor agent.
    
    Args:
        data_dir: Directory for data storage
        verbose: Whether to print verbose output
        
    Returns:
        PDFTableProcessor instance
    """
    return PDFTableProcessor(data_dir=data_dir, verbose=verbose) 