"""
Universal New Agent

The main orchestrating agent that combines sophisticated planning, multi-agent execution,
context management, validation, and QA feedback loops for comprehensive query processing.
"""

import os
import sys
import time
import asyncio
from typing import Dict, List, Any, Optional

# LangGraph import with fallback
try:
    from langgraph.graph import StateGraph, END
    LANGGRAPH_AVAILABLE = True
except ImportError:
    # Fallback when LangGraph is not available
    LANGGRAPH_AVAILABLE = False
    StateGraph = None
    END = None

# Import configuration
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from .state import (
    UniversalNewState, UniversalNewConfig, WorkflowStep,
    ValidationResult, create_initial_state
)
from .planner import EnhancedUniversalPlanner
from .orchestrator import AgentOrchestrator
from .multi_step_orchestrator import MultiStepOrchestrator
from .context_manager import ContextManager
from .reference_collector import ReferenceCollector
from .synthesizer import AnswerSynthesizer
from .validator import UniversalValidator
from .qa_feedback import QAFeedbackSystem


class UniversalNewAgent:
    """
    Universal New Agent - A sophisticated multi-agent orchestration system.
    
    This agent implements advanced workflow coordination with:
    - Intelligent universal planner for agent selection and orchestration
    - Support for parallel and sequential agent execution
    - Context passing between agents in sequential workflows
    - Comprehensive reference collection and deduplication
    - Answer synthesis from multiple agent results
    - Validation system for hallucination detection
    - QA feedback loop for iterative improvement
    
    Example usage scenarios:
    1. Resume updating with web research: "Update my resume with latest skills from web research"
    2. R&D report validation: "Check if everything in this R&D report is correct"
    3. Multi-source information gathering: "Find Rama's resume and update it for senior positions"
    """
    
    def __init__(
        self,
        config: UniversalNewConfig = None,
        verbose: bool = True,
        use_enhanced_workflow: bool = False
    ):
        """
        Initialize the Universal New Agent.
        
        Args:
            config: Configuration for the agent
            verbose: Whether to print verbose output
            use_enhanced_workflow: Whether to use the enhanced workflow (default: True)
                If True, uses more sophisticated planning and multi-step execution
                If False, falls back to simpler execution mode
        """
        self.verbose = verbose
        self.config = config or UniversalNewConfig(verbose=verbose)
        self.use_enhanced_workflow = True  # Always use enhanced workflow

        if self.verbose:
            print(f"🚀 UNIVERSAL NEW AGENT: Initializing Enhanced Multi-Step multi-agent system")

        try:
            # Enhanced workflow components
            self.enhanced_planner = EnhancedUniversalPlanner(
                model=self._get_model(),
                config=self.config,
                verbose=verbose
            )

            self.multi_step_orchestrator = MultiStepOrchestrator(
                planner=self.enhanced_planner,
                verbose=verbose
            )

            if self.verbose:
                print("✅ Enhanced workflow components initialized")

        except Exception as e:
            if self.verbose:
                print(f"⚠️ Enhanced workflow initialization failed: {e}")

            # Even in case of error, keep the enhanced workflow flag
            # (the planner will use internal fallback mechanisms)
            self.enhanced_planner = None
            self.multi_step_orchestrator = None

        # Common components
        self.context_manager = ContextManager(config=self.config.context_config, verbose=verbose)
        self.reference_collector = ReferenceCollector(config=self.config.reference_config, verbose=verbose)
        self.synthesizer = AnswerSynthesizer(config=self.config, verbose=verbose)
        self.validator = UniversalValidator(config=self.config.validation_config, verbose=verbose)
        self.qa_feedback = QAFeedbackSystem(config=self.config, verbose=verbose)

        # Create the processing graph
        self.graph = self._create_processing_graph()

        if self.verbose:
            print("✅ UNIVERSAL NEW AGENT: Initialization complete")

    def enable_enhanced_workflow(self, rag_agent=None, web_search_agent=None, gpt_agent=None, deep_search_agent=None):
        """
        Enable the enhanced workflow with proper agent instances.

        Args:
            rag_agent: RAG agent instance
            web_search_agent: Web search agent instance
            gpt_agent: GPT agent instance
            deep_search_agent: Deep search agent instance
        """
        try:
            if not hasattr(self, 'enhanced_planner'):
                self.enhanced_planner = EnhancedUniversalPlanner(
                    model=self._get_model(),
                    config=self.config,
                    verbose=self.verbose
                )

            self.multi_step_orchestrator = MultiStepOrchestrator(
                rag_agent=rag_agent,
                web_search_agent=web_search_agent,
                gpt_agent=gpt_agent,
                deep_search_agent=deep_search_agent,
                planner=self.enhanced_planner,
                verbose=self.verbose
            )

            self.use_enhanced_workflow = True

            if self.verbose:
                print("✅ Enhanced workflow enabled with proper agent instances")

        except Exception as e:
            if self.verbose:
                print(f"❌ Failed to enable enhanced workflow: {e}")
            self.use_enhanced_workflow = False

    def _get_model(self):
        """Get the language model for planning."""
        try:
            from langchain_openai import AzureChatOpenAI

            # Import Azure OpenAI configuration from settings (same as other agents)
            import sys
            import os
            sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
            from config.settings import (
                AZURE_OPENAI_KEY, AZURE_OPENAI_API_VERSION, AZURE_OPENAI_ENDPOINT,
                AZURE_OPENAI_DEPLOYMENT_NAME
            )

            if self.verbose:
                print("✅ UNIVERSAL NEW AGENT: Using Azure OpenAI for enhanced planning")

            return AzureChatOpenAI(
                azure_deployment=AZURE_OPENAI_DEPLOYMENT_NAME,
                api_version=AZURE_OPENAI_API_VERSION,
                azure_endpoint=AZURE_OPENAI_ENDPOINT,
                api_key=AZURE_OPENAI_KEY,
                temperature=0.1
            )

        except (ImportError, ValueError) as e:
            if self.verbose:
                print(f"⚠️ UNIVERSAL NEW AGENT: Using fallback model due to: {e}")

            # Fallback to an enhanced mock model for the enhanced planner
            class EnhancedMockModel:
                def invoke(self, messages):
                    class MockResponse:
                        def __init__(self, content):
                            self.content = content

                    # Analyze the message content to provide appropriate responses
                    message_content = str(messages).lower()

                    if "workflow" in message_content and "analysis" in message_content:
                        # Workflow analysis request
                        return MockResponse('''{
                            "workflow_type": "content_creation",
                            "complexity": "moderate",
                            "estimated_steps": 2,
                            "first_step_focus": "Analyze current content and gather improvement suggestions",
                            "key_information_needed": ["current content analysis", "improvement recommendations"],
                            "reasoning": "For content improvement, we need to first understand what exists, then enhance it",
                            "estimated_time": 90.0
                        }''')

                    elif "agent_tasks" in message_content:
                        # Agent task planning request - generic for any query
                        return MockResponse('''{
                            "agent_tasks": [
                                {
                                    "agent_type": "deep_search",
                                    "targeted_query": "Analyze the provided files and extract relevant information for the user query",
                                    "reasoning": "Deep search to understand file content and extract relevant information",
                                    "expected_output": "Summary of file content relevant to the query",
                                    "priority": 1
                                }
                            ]
                        }''')

                    elif "progress" in message_content:
                        # Progress analysis request - generic for any query
                        return MockResponse('''{
                            "is_complete": false,
                            "completion_confidence": 0.6,
                            "missing_information": ["content generation"],
                            "suggested_next_step": "Generate comprehensive response based on file analysis",
                            "reasoning": "File analysis completed, now need GPT to generate response based on the content"
                        }''')

                    else:
                        # Default legacy response
                        return MockResponse('''{
                            "analysis": {"query_type": "information_gathering", "complexity": "moderate"},
                            "execution_plan": {"strategy": "parallel", "agents": []}
                        }''')

            return EnhancedMockModel()
    
    def _create_processing_graph(self):
        """
        Create the processing workflow.

        Returns:
            Compiled workflow (StateGraph if available, otherwise None)
        """
        if LANGGRAPH_AVAILABLE:
            # Use LangGraph for sophisticated workflow
            graph = StateGraph(UniversalNewState)

            # Add workflow nodes
            graph.add_node("planning", self._planning_node)
            graph.add_node("agent_execution", self._agent_execution_node)
            graph.add_node("answer_synthesis", self._answer_synthesis_node)
            graph.add_node("validation", self._validation_node)
            graph.add_node("qa_feedback_analysis", self._qa_feedback_node)
            graph.add_node("iteration_check", self._iteration_check_node)
            graph.add_node("finalization", self._finalization_node)

            # Define workflow edges
            graph.add_edge("planning", "agent_execution")
            graph.add_edge("agent_execution", "answer_synthesis")
            graph.add_edge("answer_synthesis", "validation")
            graph.add_edge("validation", "qa_feedback_analysis")
            graph.add_edge("qa_feedback_analysis", "iteration_check")

            # Conditional edges from iteration_check
            graph.add_conditional_edges(
                "iteration_check",
                self._should_continue_iteration,
                {
                    "continue": "planning",  # Go back to planning for next iteration
                    "finalize": "finalization"
                }
            )

            graph.add_edge("finalization", END)

            # Set entry point
            graph.set_entry_point("planning")

            return graph.compile()
        else:
            # Fallback: No graph, use direct method calls
            if self.verbose:
                print("⚠️ UNIVERSAL NEW AGENT: LangGraph not available, using direct workflow execution")
            return None
    
    async def process_query(
        self,
        query: str,
        standalone_query: str = None,
        conversation_history: Optional[str] = None,
        conversation_summary: Optional[str] = None,
        selected_file_ids: Optional[List[int]] = None,
        blob_names: Optional[List[str]] = None,
        user_context: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Process a query using the sophisticated multi-agent system.
        
        Args:
            query: User query
            standalone_query: Optional standalone query (if already processed by main planner)
            conversation_history: Optional conversation history
            conversation_summary: Optional conversation summary
            selected_file_ids: Optional list of selected file IDs
            blob_names: Optional list of blob names
            user_context: Optional additional user context
            
        Returns:
            Comprehensive processing result
        """
        start_time = time.time()
        
        if self.verbose:
            print(f"\n🚀 UNIVERSAL NEW AGENT: Processing query")
            print(f"   Query: {query[:100]}...")
            print(f"   Files: {len(selected_file_ids) if selected_file_ids else 0} selected")
            print(f"   Has conversation: {bool(conversation_history)}")
        
        try:
            # Create initial state
            initial_state = create_initial_state(
                query=query,
                standalone_query=standalone_query,
                conversation_history=conversation_history,
                conversation_summary=conversation_summary,
                selected_file_ids=selected_file_ids,
                blob_names=blob_names,
                user_context=user_context or {}
            )

            # Use enhanced multi-step workflow
            final_state = await self._process_enhanced_workflow(initial_state)

            # Calculate total processing time
            total_time = time.time() - start_time
            final_state['total_execution_time'] = total_time

            # Prepare final result
            result = self._prepare_final_result(final_state, total_time)
            
            if self.verbose:
                print(f"✅ UNIVERSAL NEW AGENT: Processing completed in {total_time:.2f}s")
                print(f"   Final confidence: {result.get('confidence_score', 0):.2f}")
                print(f"   Iterations: {final_state['iteration_count']}")
                print(f"   Agents used: {len(final_state['agent_results'])}")
            
            return result

        except Exception as e:
            processing_time = time.time() - start_time
            if self.verbose:
                print(f"❌ UNIVERSAL NEW AGENT: Error during processing: {e}")

            return {
                "answer": f"I encountered an error while processing your request: {str(e)}",
                "confidence_score": 0.0,
                "references": {},
                "metadata": {
                    "agent_type": "universal_new",
                    "error_occurred": True,
                    "processing_time": processing_time
                }
            }

    async def _process_enhanced_workflow(self, state: UniversalNewState) -> UniversalNewState:
        """
        Process query using the enhanced multi-step workflow.

        Args:
            state: Initial state

        Returns:
            Final state after enhanced processing
        """
        if self.verbose:
            print(f"\n🔄 UNIVERSAL NEW AGENT: Using Enhanced Multi-Step Workflow")

        try:
            # Check if enhanced_planner and multi_step_orchestrator were properly initialized
            if self.enhanced_planner is None or self.multi_step_orchestrator is None:
                if self.verbose:
                    print("⚠️ ENHANCED WORKFLOW: Components not available, using simplified processing")
                
                # Create minimal components for basic processing
                self.enhanced_planner = EnhancedUniversalPlanner(
                    model=self._get_model(),
                    config=self.config,
                    verbose=self.verbose
                )
                
                self.multi_step_orchestrator = MultiStepOrchestrator(
                    planner=self.enhanced_planner,
                    verbose=self.verbose
                )
            
            # Step 1: Create multi-step workflow plan
            state = await self.enhanced_planner.create_multi_step_workflow(state)

            # Step 2: Execute the multi-step workflow
            state = await self.multi_step_orchestrator.execute_multi_step_workflow(state)

            # Step 2.5: Collect references from all step results
            state = await self._collect_enhanced_references(state)

            # Step 3: Synthesize final answer from all steps
            state = await self._synthesize_enhanced_final_answer(state)

            # Step 4: Final validation
            state = await self._validate_enhanced_final_answer(state)

            return state

        except Exception as e:
            if self.verbose:
                print(f"❌ ENHANCED WORKFLOW: Error processing: {e}")
                import traceback
                traceback.print_exc()

            state['error_messages'].append(f"Enhanced workflow error: {str(e)}")
            state['final_answer'] = "I encountered an error while processing your request using the enhanced workflow."
            return state

    async def _synthesize_enhanced_final_answer(self, state: UniversalNewState) -> UniversalNewState:
        """
        Synthesize the final answer from all completed steps.

        Args:
            state: State with completed steps

        Returns:
            State with synthesized final answer
        """
        if not state['step_results']:
            state['final_answer'] = "No results were generated from the workflow steps."
            return state

        # Get the last step result (step_results is a dictionary, not a list)
        step_ids = list(state['step_results'].keys())
        if step_ids:
            last_step_id = step_ids[-1]
            last_step_result = state['step_results'][last_step_id]
            if last_step_result.success and last_step_result.synthesized_answer:
                state['final_answer'] = last_step_result.synthesized_answer
            else:
                state['final_answer'] = "The final step did not produce a valid result."
        else:
            state['final_answer'] = "No step results were found."

        return state

    async def _collect_enhanced_references(self, state: UniversalNewState) -> UniversalNewState:
        """
        Collect references from all step results in the enhanced workflow.

        Args:
            state: State with completed step results

        Returns:
            State with collected references
        """
        if self.verbose:
            print(f"\n📚 ENHANCED WORKFLOW: Collecting references from {len(state['step_results'])} steps")

        try:
            # Collect references from each step result
            for step_id, step_result in state['step_results'].items():
                if step_result.success and step_result.agent_results:
                    for agent_type, agent_result in step_result.agent_results.items():
                        if agent_result.success:
                            if self.verbose:
                                print(f"   Processing references from {agent_type.value} in {step_id}")
                                ref_structure = "None" if agent_result.references is None else (
                                    f"List[{len(agent_result.references)}]" if isinstance(agent_result.references, list) else 
                                    f"Dict with keys: {list(agent_result.references.keys())}" if isinstance(agent_result.references, dict) else
                                    f"Unexpected type: {type(agent_result.references)}"
                                )
                                print(f"   Reference structure: {ref_structure}")
                                
                            # Check if references exist and process them
                            if agent_result.references:
                                try:
                                    self.reference_collector.collect_references_from_result(agent_type, agent_result)
                                except Exception as collection_error:
                                    if self.verbose:
                                        print(f"   ❌ Error collecting references from {agent_type.value}: {collection_error}")
                                        import traceback
                                        traceback.print_exc()

            # Get final references
            final_references = self.reference_collector.get_final_references()
            state['final_references'] = final_references

            if self.verbose:
                total_refs = sum(len(refs) for refs in final_references.values()) if final_references else 0
                print(f"📚 ENHANCED WORKFLOW: Collected {total_refs} total references")
                if final_references:
                    for ref_type, refs in final_references.items():
                        print(f"   {ref_type}: {len(refs)} references")
                else:
                    print(f"   ⚠️ No references collected")

        except Exception as e:
            if self.verbose:
                print(f"❌ ENHANCED WORKFLOW: Error collecting references: {e}")
                import traceback
                traceback.print_exc()
            state['final_references'] = {}

        return state

    async def _validate_enhanced_final_answer(self, state: UniversalNewState) -> UniversalNewState:
        """
        Validate the final answer from enhanced workflow.

        Args:
            state: State with final answer

        Returns:
            State with validation results
        """
        try:
            if self.validator and state['final_answer']:
                validation_result = self.validator.validate_answer(
                    state=state,
                    synthesized_answer=state['final_answer'],
                    references=state.get('final_references', {})
                )

                state['validation_results'].append(validation_result)
                state['confidence_score'] = validation_result.confidence_score

                if self.verbose:
                    print(f"✅ ENHANCED VALIDATION: Confidence {validation_result.confidence_score:.2f}")
            else:
                # Default confidence if no validation
                state['confidence_score'] = 0.8

        except Exception as e:
            if self.verbose:
                print(f"⚠️ ENHANCED VALIDATION: Error validating: {e}")
            state['confidence_score'] = 0.7

        return state
    
    def _planning_node(self, state: UniversalNewState) -> UniversalNewState:
        """Planning node for LangGraph workflow."""
        state["workflow_step"] = WorkflowStep.PLANNING
        
        # Create or update execution plan
        state = self.enhanced_planner.create_multi_step_workflow(state)
        
        return state
    
    async def _agent_execution_node(self, state: UniversalNewState) -> UniversalNewState:
        """Agent execution node for LangGraph workflow."""
        state["workflow_step"] = WorkflowStep.AGENT_EXECUTION
        
        # Execute agents according to plan
        state = await self.multi_step_orchestrator.execute_multi_step_workflow(state)
        
        # Collect references from all agent results
        for agent_type, result in state["agent_results"].items():
            if result.success and result.references:
                self.reference_collector.collect_references_from_result(agent_type, result)
                
        return state
    
    def _answer_synthesis_node(self, state: UniversalNewState) -> UniversalNewState:
        """Answer synthesis workflow node"""
        state['workflow_step'] = WorkflowStep.CONTEXT_SYNTHESIS
        
        if self.verbose:
            print(f"\n🔄 WORKFLOW: Answer Synthesis")
        
        # Synthesize final answer from agent results
        synthesized_answer = self.synthesizer.synthesize_final_answer(state)
        state['final_answer'] = synthesized_answer
        
        # Calculate confidence score
        confidence_score = self.synthesizer.calculate_confidence_score(state)
        state['confidence_score'] = confidence_score
        
        # Collect final references
        final_references = self.reference_collector.get_final_references()
        state['final_references'] = final_references
        
        return state
    
    def _validation_node(self, state: UniversalNewState) -> UniversalNewState:
        """
        Validation workflow node with intelligent validation based on agents used.

        Logic:
        - If GPT or Web Search agents were used → Lenient validation (external knowledge expected)
        - If only RAG/Deep Search agents used → Strict validation (must stick to file content)
        """
        state['workflow_step'] = WorkflowStep.VALIDATION

        if self.verbose:
            print(f"\n✅ WORKFLOW: Validation")

        try:
            # Determine validation strictness based on agents used
            # Convert AgentType enums to strings for comparison
            agents_used = [str(agent_type).split('.')[-1].lower() for agent_type in state['agent_results'].keys()]

            # If GPT or Web Search agents were used, use lenient validation
            # because external knowledge is expected and valid
            has_external_agents = any(agent in ['gpt', 'web_search'] for agent in agents_used)

            if has_external_agents:
                if self.verbose:
                    print(f"✅ VALIDATOR: Skipping validation (external agents involved: {[a for a in agents_used if a in ['gpt', 'web_search']]})")
                    print(f"   Reason: GPT/Web Search agents provide external knowledge - validation not applicable")

                # Skip validation entirely for external knowledge agents
                validation_result = ValidationResult(
                    is_valid=True,
                    confidence_score=0.95,  # High confidence - external knowledge is expected and valid
                    hallucinations_detected=[],
                    missing_information=[],
                    source_adherence_score=1.0,  # Perfect score - external knowledge is valid
                    improvement_suggestions=[]
                )
            else:
                if self.verbose:
                    print(f"✅ VALIDATOR: Using strict validation (file-only agents: {agents_used})")

                # Use strict validation for RAG/Deep Search only responses
                validation_result = self.validator.validate_answer(
                    state, state['final_answer'], state['final_references']
                )

            state['validation_results'].append(validation_result)

            if self.verbose:
                print(f"✅ VALIDATOR: Validation completed")
                print(f"   Valid: {validation_result.is_valid}")
                print(f"   Confidence: {validation_result.confidence_score}")
                print(f"   Hallucinations: {len(validation_result.hallucinations_detected)}")
                print(f"   Missing info: {len(validation_result.missing_information)}")

            return state

        except Exception as e:
            if self.verbose:
                print(f"❌ VALIDATOR: Validation failed: {e}")

            # Create fallback validation result with correct field names
            fallback_validation = ValidationResult(
                is_valid=True,  # Default to valid on error
                confidence_score=0.7,
                hallucinations_detected=[],
                missing_information=[],
                source_adherence_score=0.7,
                improvement_suggestions=[f"Validation error occurred: {str(e)}"]
            )
            state['validation_results'].append(fallback_validation)

            return state
    
    def _qa_feedback_node(self, state: UniversalNewState) -> UniversalNewState:
        """QA feedback workflow node"""
        state['workflow_step'] = WorkflowStep.QA_FEEDBACK
        
        if self.verbose:
            print(f"\n🔄 WORKFLOW: QA Feedback")
        
        # Get the latest validation result
        latest_validation = state['validation_results'][-1] if state['validation_results'] else None
        
        if latest_validation:
            # Generate feedback for potential iteration
            feedback = self.qa_feedback.generate_feedback(
                state, latest_validation, state['final_answer']
            )
            state['qa_feedback_result'] = feedback
        
        return state
    
    def _iteration_check_node(self, state: UniversalNewState) -> UniversalNewState:
        """Iteration check workflow node"""
        if self.verbose:
            print(f"\n🔍 WORKFLOW: Iteration Check")
        
        # Determine if we should continue iterating
        if state['qa_feedback_result']:
            should_continue = self.qa_feedback.should_continue_iteration(state, state['qa_feedback_result'])
            state['needs_iteration'] = should_continue
            
            if should_continue:
                state['iteration_count'] += 1
                if self.verbose:
                    print(f"   Continuing to iteration {state['iteration_count'] + 1}")
            else:
                if self.verbose:
                    print(f"   Stopping iteration, finalizing answer")
        else:
            state['needs_iteration'] = False
        
        return state
    
    def _finalization_node(self, state: UniversalNewState) -> UniversalNewState:
        """Finalization workflow node"""
        state['workflow_step'] = WorkflowStep.FINAL_SYNTHESIS
        
        if self.verbose:
            print(f"\n🏁 WORKFLOW: Finalization")
        
        # Final processing and cleanup
        # The state already contains the final answer and references
        
        return state
    
    def _should_continue_iteration(self, state: UniversalNewState) -> str:
        """Determine if iteration should continue"""
        return "continue" if state['needs_iteration'] else "finalize"
    
    def _prepare_final_result(self, state: UniversalNewState, processing_time: float) -> Dict[str, Any]:
        """
        Prepare the final result dictionary.
        
        Args:
            state: Final state
            processing_time: Total processing time
            
        Returns:
            Final result dictionary
        """
        # Get final validation result
        final_validation = state['validation_results'][-1] if state['validation_results'] else None
        
        return {
            "answer": state['final_answer'],
            "references": state['final_references'],
            "confidence_score": state['confidence_score'],
            "success": True,
            "processing_time": processing_time,
            "metadata": {
                "agent_type": "universal_new",
                "execution_strategy": state['execution_strategy'].value,
                "agents_used": [agent_type.value for agent_type in state['agent_results'].keys()],
                "iterations_completed": state['iteration_count'],
                "validation_passed": final_validation.is_valid if final_validation else False,
                "planner_reasoning": state['planner_reasoning'],
                "total_execution_time": state['total_execution_time'],
                "workflow_completed": True,
                "reference_stats": self.reference_collector.get_collection_stats(),
                "context_summary": self.context_manager.get_context_summary()
            }
        }


def create_universal_new_agent(config: UniversalNewConfig = None, verbose: bool = True, use_enhanced_workflow: bool = False) -> UniversalNewAgent:
    """
    Create a Universal New Agent instance.

    Args:
        config: Optional configuration
        verbose: Whether to enable verbose logging
        use_enhanced_workflow: Whether to use the enhanced multi-step workflow (requires proper agent setup)

    Returns:
        Configured Universal New Agent
    """
    return UniversalNewAgent(config=config, verbose=verbose, use_enhanced_workflow=use_enhanced_workflow)
