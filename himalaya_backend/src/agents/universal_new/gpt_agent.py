"""
Universal GPT Agent for Enhanced Multi-Step Workflow

This agent is specifically designed for the Universal Agent's enhanced workflow.
It can process targeted queries with document context and analysis results.
"""

import os
import sys
import datetime
from typing import Dict, Any, Optional, List

from langchain_core.messages import SystemMessage, HumanMessage
from langchain_openai import AzureChatOpenAI

# Import configuration
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
from config.settings import AZURE_OPENAI_KEY, AZURE_OPENAI_API_VERSION, AZURE_OPENAI_ENDPOINT, AZURE_OPENAI_DEPLOYMENT_NAME
from config.logging_config import get_logger

# Configure logging
logger = get_logger(__name__)

class UniversalGPTAgent:
    """
    Specialized GPT agent for Universal Agent's enhanced workflow.
    
    This agent can process targeted queries with document context,
    analysis results, and other information gathered by previous agents.
    """

    def __init__(self, model=None, verbose=True):
        """
        Initialize the Universal GPT agent.

        Args:
            model: LLM to use for responses
            verbose: Whether to print verbose output
        """
        self.verbose = verbose
        self.today_date = datetime.datetime.now().strftime("%Y-%m-%d")

        # Initialize the model
        if model:
            self.model = model
        else:
            self.model = AzureChatOpenAI(
                azure_deployment=AZURE_OPENAI_DEPLOYMENT_NAME,
                api_version=AZURE_OPENAI_API_VERSION,
                azure_endpoint=AZURE_OPENAI_ENDPOINT,
                api_key=AZURE_OPENAI_KEY,
                temperature=0.7  # Balanced temperature for creative yet accurate responses
            )
            if self.verbose:
                print(f"🤖 UNIVERSAL GPT: Using Azure OpenAI with deployment {AZURE_OPENAI_DEPLOYMENT_NAME}")

    def _get_system_prompt(self) -> str:
        """
        Get the system prompt for the Universal GPT agent.

        Returns:
            System prompt optimized for document-based responses
        """
        return f"""You are an expert assistant specialized in analyzing and responding based on provided information.

Your role is to process targeted queries using the information and analysis results provided to you from document analysis, web search, or other sources.

Key Guidelines:
1. **Use Provided Information**: Always base your response on the information provided in the query
2. **Be Specific**: Reference specific details, data, and insights from the provided content
3. **Be Comprehensive**: Provide thorough, well-structured responses that fully address the query
4. **Maintain Context**: Consider the original user intent while focusing on the targeted task
5. **Professional Tone**: Maintain a professional, helpful, and clear communication style
6. **Actionable Output**: When appropriate, provide actionable recommendations or next steps

When you receive information from document analysis:
- Extract and use specific details from the documents
- Reference key sections, data points, or insights
- Build upon the analysis to provide enhanced value
- Do not claim inability to access information that has been provided to you

When processing targeted queries:
- Focus on the specific task requested
- Use all relevant information provided
- Synthesize information from multiple sources when available
- Provide structured, organized responses

Today's date is {self.today_date}.

Remember: You have access to the information provided in each query. Use it effectively to deliver valuable, specific responses."""

    def process_targeted_query(
        self,
        targeted_query: str,
        context_information: Optional[str] = None,
        original_query: Optional[str] = None,
        conversation_history: Optional[str] = None,
        conversation_summary: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Process a targeted query with provided context information.

        Args:
            targeted_query: The specific task/query for this GPT execution
            context_information: Information gathered from previous agents (documents, web search, etc.)
            original_query: The original user query for context
            metadata: Additional metadata that might be useful

        Returns:
            Dictionary with the response
        """
        if self.verbose:
            print(f"🤖 UNIVERSAL GPT: Processing targeted query")
            print(f"   Targeted Query: {targeted_query[:100]}...")
            if context_information:
                print(f"   Context Length: {len(context_information)} characters")

        # Build the complete query with conversation history
        query_parts = []

        # Add conversation history if available - preserve the full conversation history
        # instead of truncating arbitrarily
        if conversation_history and conversation_history.strip():
            # Check if the conversation history already has the formatted [RECENT EXCHANGE] format
            if "[RECENT EXCHANGE" in conversation_history:
                # It's already in the correct format, use it without truncation
                query_parts.append(f"CONVERSATION HISTORY:\n{conversation_history}")
                if self.verbose:
                    print(f"   Using pre-formatted conversation history: {len(conversation_history)} chars")
            else:
                # Try to extract QA pairs in a structured way instead of truncating
                query_parts.append(f"CONVERSATION HISTORY:\n{conversation_history}")
                if self.verbose:
                    print(f"   Using full conversation history: {len(conversation_history)} chars")

        # Add context information if available
        if context_information and context_information.strip():
            query_parts.append(f"CURRENT CONTEXT:\n{context_information}")

        # Add the main task
        query_parts.append(f"TASK: {targeted_query}")

        # Add original query context if different
        if original_query and original_query != targeted_query:
            query_parts.append(f"ORIGINAL USER REQUEST: {original_query}")

        # Combine all parts
        if query_parts:
            complete_query = "\n\n".join(query_parts)
            if conversation_history:
                complete_query += "\n\nIMPORTANT: Use the conversation history to understand the context and build upon previous discussions. If this is a follow-up request, reference and build upon the previous conversation."
        else:
            complete_query = targeted_query

        # Create messages
        messages = [
            SystemMessage(content=self._get_system_prompt()),
            HumanMessage(content=complete_query)
        ]
        
        # Generate response
        try:
            response = self.model.invoke(messages)
            
            if self.verbose:
                print(f"🤖 UNIVERSAL GPT: Generated response ({len(response.content)} chars)")
                print(f"   Preview: {response.content[:150]}...")
            
            # Return the result
            return {
                "answer": response.content,
                "metadata": {
                    "agent_type": "universal_gpt",
                    "targeted_query": targeted_query,
                    "context_provided": bool(context_information and context_information.strip()),
                    "context_length": len(context_information) if context_information else 0,
                    "response_length": len(response.content)
                },
                "references": {
                    "gpt_knowledge": {
                        "source_type": "knowledge",
                        "title": "GPT Knowledge",
                        "content": "Information from GPT's knowledge base",
                        "confidence_score": 0.85,  # High confidence when context is provided
                        "knowledge_source": "gpt",
                        "icon": "ai",
                        "metadata": {
                            "agent_type": "universal_gpt",
                            "targeted_query": targeted_query,
                            "context_provided": bool(context_information and context_information.strip()),
                            "context_length": len(context_information) if context_information else 0,
                            "response_length": len(response.content)
                        }
                    }
                },
                "confidence_score": 0.85,  # High confidence when context is provided
                "success": True
            }
            
        except Exception as e:
            if self.verbose:
                print(f"❌ UNIVERSAL GPT: Error processing query: {e}")
            
            return {
                "answer": f"I encountered an error while processing your request: {str(e)}",
                "metadata": {"error": str(e)},
                "references": {},
                "confidence_score": 0.0,
                "success": False
            }

    def process_synthesis_query(
        self,
        synthesis_task: str,
        agent_results: Dict[str, str],
        original_query: str
    ) -> Dict[str, Any]:
        """
        Process a synthesis query that combines results from multiple agents.

        Args:
            synthesis_task: The synthesis task to perform
            agent_results: Results from different agents (key: agent_name, value: result)
            original_query: The original user query

        Returns:
            Dictionary with the synthesized response
        """
        if self.verbose:
            print(f"🤖 UNIVERSAL GPT: Processing synthesis query")
            print(f"   Agent results: {len(agent_results)} sources")

        # Format agent results
        context_parts = []
        for agent_name, result in agent_results.items():
            if result and result.strip():
                context_parts.append(f"**{agent_name.title()} Results:**\n{result}")

        context_information = "\n\n".join(context_parts) if context_parts else ""

        # Build synthesis query
        synthesis_query = f"""Based on the following information from multiple sources, {synthesis_task}:

{context_information}

Original user query: {original_query}

Please synthesize this information to provide a comprehensive response."""

        return self.process_targeted_query(
            targeted_query=synthesis_query,
            context_information="",  # Context already included in query
            original_query=original_query
        )


def create_universal_gpt_agent(model=None, verbose=True) -> UniversalGPTAgent:
    """
    Create a Universal GPT agent.

    Args:
        model: LLM to use for responses
        verbose: Whether to print verbose output

    Returns:
        A Universal GPT agent
    """
    return UniversalGPTAgent(model=model, verbose=verbose)
