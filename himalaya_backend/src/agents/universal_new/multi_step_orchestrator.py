"""
Multi-Step Workflow Orchestrator for Universal New Agent

This orchestrator implements advanced multi-step workflow execution similar to 
DeepSeek, O3, and Genspark, where each step builds on previous results and 
agents receive targeted queries specific to their capabilities.
"""

import asyncio
import time
from typing import Dict, List, Any, Optional

from .state import (
    UniversalNewState, AgentTask, ExecutionStep, StepResult, AgentResult, 
    AgentType, ExecutionStrategy, ValidationResult
)
from .planner import EnhancedUniversalPlanner


class MultiStepOrchestrator:
    """
    Multi-step workflow orchestrator that executes workflows step by step,
    accumulating context and planning next steps dynamically.
    """
    
    def __init__(self,
                 rag_agent=None,
                 web_search_agent=None,
                 gpt_agent=None,
                 deep_search_agent=None,
                 planner: EnhancedUniversalPlanner = None,
                 validator=None,
                 verbose: bool = True):
        """
        Initialize the multi-step orchestrator.

        Args:
            rag_agent: RAG agent instance (optional, will be lazy loaded)
            web_search_agent: Web search agent instance (optional, will be lazy loaded)
            gpt_agent: GPT agent instance (optional, will be lazy loaded)
            deep_search_agent: Deep search agent instance (optional, will be lazy loaded)
            planner: Enhanced planner for dynamic planning
            validator: Validator for step validation
            verbose: Whether to print verbose output
        """
        self._rag_agent = rag_agent
        self._web_search_agent = web_search_agent
        self._gpt_agent = gpt_agent
        self._deep_search_agent = deep_search_agent
        self.planner = planner
        self.validator = validator
        self.verbose = verbose

    @property
    def rag_agent(self):
        """Lazy load RAG agent"""
        if self._rag_agent is None:
            from agents.rag import create_rag_agent
            self._rag_agent = create_rag_agent(verbose=self.verbose)
            if self.verbose:
                print("🎭 MULTI-STEP ORCHESTRATOR: Initialized RAG agent")
        return self._rag_agent

    @property
    def web_search_agent(self):
        """Lazy load Web Search agent"""
        if self._web_search_agent is None:
            from agents.web import create_web_search_agent
            self._web_search_agent = create_web_search_agent(verbose=self.verbose)
            if self.verbose:
                print("🎭 MULTI-STEP ORCHESTRATOR: Initialized Web Search agent")
        return self._web_search_agent

    @property
    def gpt_agent(self):
        """Lazy load Universal GPT agent"""
        if self._gpt_agent is None:
            from .gpt_agent import create_universal_gpt_agent
            self._gpt_agent = create_universal_gpt_agent(verbose=self.verbose)
            if self.verbose:
                print("🎭 MULTI-STEP ORCHESTRATOR: Initialized Universal GPT agent")
        return self._gpt_agent

    @property
    def deep_search_agent(self):
        """Lazy load Deep Search agent"""
        if self._deep_search_agent is None:
            from agents.deep_search_new import DeepSearchOrchestrator
            self._deep_search_agent = DeepSearchOrchestrator(verbose=self.verbose)
            if self.verbose:
                print("🎭 MULTI-STEP ORCHESTRATOR: Initialized Deep Search agent")
        return self._deep_search_agent
    
    async def execute_multi_step_workflow(self, state: UniversalNewState) -> UniversalNewState:
        """
        Execute the multi-step workflow.
        
        Args:
            state: Initial state
            
        Returns:
            Updated state
        """
        start_time = time.time()
        step_count = 0
        max_steps = 5  # Limit maximum steps to avoid infinite loops
        
        if self.verbose:
            print(f"\n🎭 MULTI-STEP ORCHESTRATOR: Starting workflow execution")
            print(f"   Total steps planned: {len(state['workflow_steps'])}")
            
        # Initialize accumulated context if not present
        if 'accumulated_context' not in state:
            state['accumulated_context'] = {}
            
        # Initialize step_results if not present
        if 'step_results' not in state:
            state['step_results'] = {}
            
        # Track previous answers for context continuity
        previous_answers = []
            
        while state['current_step_index'] < len(state['workflow_steps']) and step_count < max_steps:
            current_step = state['workflow_steps'][state['current_step_index']]

            if self.verbose:
                print(f"\n🔄 ORCHESTRATOR: Executing {current_step.step_name}")
                print(f"   Current accumulated context keys: {list(state['accumulated_context'].keys())}")
                print(f"   Current step results: {list(state['step_results'].keys())}")

            # Execute the current step
            step_result = await self._execute_step(current_step, state)

            # Store the step result
            state['step_results'][current_step.step_id] = step_result
            
            # Update previous answers for context continuity
            if step_result.success and step_result.synthesized_answer:
                previous_answers.append({
                    'step_id': current_step.step_id,
                    'answer': step_result.synthesized_answer
                })
                # Limit to last 3 answers to avoid context explosion
                previous_answers = previous_answers[-3:]
                
            # Add previous answers to accumulated context for better continuity
            if previous_answers:
                state['accumulated_context']['previous_answers'] = previous_answers

            # Update accumulated context with detailed step context
            if step_result.accumulated_context:
                state['accumulated_context'].update(step_result.accumulated_context)
                
                if self.verbose:
                    print(f"   Updated accumulated context with {len(step_result.accumulated_context)} new keys")
                    print(f"   Total accumulated context keys: {len(state['accumulated_context'])}")
                    print(f"   Key accumulated context items: {list(state['accumulated_context'].keys())[:5]}")

            # Mark step as completed
            current_step.completed = True

            # Move to next step
            state['current_step_index'] += 1
            step_count += 1

            if self.verbose:
                status = "✅ Success" if step_result.success else "❌ Failed"
                print(f"   {status}: {current_step.step_name} ({step_result.execution_time:.2f}s)")

            # Check if we need to plan more steps
            if state['current_step_index'] >= len(state['workflow_steps']):
                # Plan next step based on progress
                old_step_count = len(state['workflow_steps'])
                state = await self.planner.plan_next_step(state)
                new_step_count = len(state['workflow_steps'])

                # If no new steps were added, we're done
                if new_step_count == old_step_count:
                    if self.verbose:
                        print(f"✅ ORCHESTRATOR: No more steps planned, workflow complete")
                    break

        if step_count >= max_steps:
            if self.verbose:
                print(f"⚠️ ORCHESTRATOR: Reached maximum steps ({max_steps}), stopping workflow")
        
        # Calculate total execution time
        total_time = time.time() - start_time
        
        if self.verbose:
            print(f"✅ MULTI-STEP ORCHESTRATOR: Workflow completed in {total_time:.2f}s")
            print(f"   Total steps executed: {step_count}")
        
        return state
    
    async def execute_plan(self, state: UniversalNewState) -> UniversalNewState:
        """
        Execute the plan (compatibility method that calls execute_multi_step_workflow).
        
        Args:
            state: State with execution plan
            
        Returns:
            Updated state with execution results
        """
        if self.verbose:
            print(f"🔄 MULTI-STEP ORCHESTRATOR: execute_plan called (compatibility method)")
            
        return await self.execute_multi_step_workflow(state)
    
    async def _execute_step(self, step: ExecutionStep, state: UniversalNewState) -> StepResult:
        """
        Execute a single workflow step.
        
        Args:
            step: Step to execute
            state: Current state
            
        Returns:
            Result of step execution
        """
        start_time = time.time()
        
        try:
            # Enhance each task with context from previous steps
            self._enhance_tasks_with_context(step.agent_tasks, state)
            
            # Track what information has already been processed
            already_processed_files = any(
                result.agent_results.get(AgentType.DEEP_SEARCH) is not None and 
                result.agent_results.get(AgentType.DEEP_SEARCH).success 
                for result in state['step_results'].values()
            )
            
            already_generated_content = any(
                result.agent_results.get(AgentType.GPT) is not None and 
                result.agent_results.get(AgentType.GPT).success 
                for result in state['step_results'].values()
            )
            
            if self.verbose:
                print(f"🔄 STEP EXECUTION STATE:")
                print(f"   Already processed files: {already_processed_files}")
                print(f"   Already generated content: {already_generated_content}")
                print(f"   Accumulated context keys: {list(state['accumulated_context'].keys())}")
            
            # Always execute tasks sequentially to ensure proper context passing
            # Force sequential execution regardless of step strategy
            agent_results = await self._execute_tasks_sequential(step.agent_tasks, state)
            
            # Synthesize results from all agents in this step
            synthesized_answer = self._synthesize_step_results(agent_results, step, state)
            
            # Create accumulated context for next steps
            accumulated_context = self._create_step_context(agent_results, synthesized_answer, step)
            
            # Add progressive state tracking
            accumulated_context["step_sequence"] = state.get("step_sequence", []) + [step.step_id]
            accumulated_context["processed_files"] = already_processed_files or any(
                agent_type == AgentType.DEEP_SEARCH and result.success 
                for agent_type, result in agent_results.items()
            )
            accumulated_context["generated_content"] = already_generated_content or any(
                agent_type == AgentType.GPT and result.success 
                for agent_type, result in agent_results.items()
            )
            
            # Validate step results if required
            validation_result = None
            if step.validation_required and self.validator:
                validation_result = await self._validate_step_results(agent_results, synthesized_answer, state)
            
            execution_time = time.time() - start_time
            
            return StepResult(
                step_id=step.step_id,
                agent_results=agent_results,
                synthesized_answer=synthesized_answer,
                accumulated_context=accumulated_context,
                validation_result=validation_result,
                execution_time=execution_time,
                success=True
            )
            
        except Exception as e:
            execution_time = time.time() - start_time
            
            return StepResult(
                step_id=step.step_id,
                agent_results={},
                execution_time=execution_time,
                success=False,
                error_message=str(e)
            )
            
    def _enhance_tasks_with_context(self, agent_tasks: List[AgentTask], state: UniversalNewState) -> None:
        """
        Enhance tasks with relevant context from previous steps.
        
        Args:
            agent_tasks: List of agent tasks to enhance
            state: Current state
        """
        # Create a state summary to add to each task
        state_summary = self._build_state_summary(state)
        
        for task in agent_tasks:
            # Check if the task already has context set from the planner
            if not hasattr(task, 'context') or not task.context:
                task.context = state_summary
            elif not state_summary in task.context:
                # If it does but doesn't have our summary, add it
                task.context = f"{task.context}\n\n{state_summary}"
                
            # Modify task queries to reflect what's already been done
            if task.agent_type == AgentType.DEEP_SEARCH and "processed_files" in state['accumulated_context']:
                if state['accumulated_context']["processed_files"] and "focus" not in task.targeted_query.lower():
                    task.targeted_query = f"Focus only on new aspects not already analyzed: {task.targeted_query}"
                    
            if task.agent_type == AgentType.GPT and "generated_content" in state['accumulated_context']:
                if state['accumulated_context']["generated_content"] and "build upon" not in task.targeted_query.lower():
                    task.targeted_query = f"Building upon previous analysis, {task.targeted_query}"
                    
    def _build_state_summary(self, state: UniversalNewState) -> str:
        """
        Build a summary of the current state for context.
        
        Args:
            state: Current state
            
        Returns:
            State summary string
        """
        parts = []
        
        # Summarize completed steps
        if state['step_results']:
            steps_summary = []
            step_ids = sorted(state['step_results'].keys(), 
                             key=lambda x: int(x.split('_')[-1]) if x.split('_')[-1].isdigit() else 0)
            
            for step_id in step_ids:
                step_result = state['step_results'][step_id]
                if step_result.success:
                    agent_types = [at.value for at in step_result.agent_results.keys()]
                    steps_summary.append(f"- Step {step_id} ({', '.join(agent_types)}): {step_result.synthesized_answer[:100]}...")
            
            if steps_summary:
                parts.append("COMPLETED STEPS:\n" + "\n".join(steps_summary))
        
        # Add information about what's been processed
        processed_info = []
        if state['accumulated_context'].get("processed_files", False):
            processed_info.append("- Files have already been analyzed")
        if state['accumulated_context'].get("generated_content", False):
            processed_info.append("- Content has already been generated")
        if state.get("step_sequence"):
            processed_info.append(f"- Step sequence so far: {', '.join(state['step_sequence'])}")
            
        if processed_info:
            parts.append("PROCESSING STATE:\n" + "\n".join(processed_info))
        
        if parts:
            return "\n\n".join(parts)
        return ""
    
    async def _execute_tasks_parallel(self, tasks: List[AgentTask], state: UniversalNewState) -> Dict[AgentType, AgentResult]:
        """
        Execute agent tasks in parallel.
        
        Args:
            tasks: List of agent tasks to execute
            state: Current state
            
        Returns:
            Dictionary of agent results
        """
        if self.verbose:
            print(f"⚡ ORCHESTRATOR: Executing {len(tasks)} tasks in parallel")
        
        # Create tasks for parallel execution
        async_tasks = []
        for task in tasks:
            async_task = self._execute_single_task(task, state)
            async_tasks.append((task.agent_type, async_task))
        
        # Execute all tasks concurrently
        results = await asyncio.gather(*[task for _, task in async_tasks], return_exceptions=True)
        
        # Collect results
        agent_results = {}
        for i, (agent_type, result) in enumerate(zip([t[0] for t in async_tasks], results)):
            if isinstance(result, Exception):
                agent_results[agent_type] = AgentResult(
                    agent_type=agent_type,
                    success=False,
                    error_message=str(result)
                )
            else:
                agent_results[agent_type] = result
        
        return agent_results
    
    async def _execute_tasks_sequential(self, tasks: List[AgentTask], state: UniversalNewState) -> Dict[AgentType, AgentResult]:
        """
        Execute agent tasks sequentially.
        
        Args:
            tasks: List of agent tasks to execute
            state: Current state
            
        Returns:
            Dictionary of agent results
        """
        if self.verbose:
            print(f"🔄 ORCHESTRATOR: Executing {len(tasks)} tasks sequentially")
        
        agent_results = {}
        accumulated_context = state['accumulated_context'].copy()
        
        for task in sorted(tasks, key=lambda t: t.priority):
            if self.verbose:
                print(f"   Executing: {task.agent_type.value}")
            
            # Update task context with accumulated results
            task_state = state.copy()
            task_state['accumulated_context'] = accumulated_context
            
            # Execute the task
            result = await self._execute_single_task(task, task_state)
            agent_results[task.agent_type] = result
            
            # Update accumulated context with this result
            if result.success and result.context_for_next:
                accumulated_context.update(result.context_for_next)
        
        return agent_results

    async def _execute_single_task(self, task: AgentTask, state: UniversalNewState) -> AgentResult:
        """
        Execute a single agent task with its targeted query.

        Args:
            task: Agent task to execute
            state: Current state

        Returns:
            Result from agent execution
        """
        start_time = time.time()

        try:
            if task.agent_type == AgentType.RAG:
                result = await self._execute_rag_task(task, state)
            elif task.agent_type == AgentType.WEB_SEARCH:
                result = await self._execute_web_search_task(task, state)
            elif task.agent_type == AgentType.GPT:
                result = await self._execute_gpt_task(task, state)
            elif task.agent_type == AgentType.DEEP_SEARCH:
                result = await self._execute_deep_search_task(task, state)
            else:
                raise ValueError(f"Unknown agent type: {task.agent_type}")

            execution_time = time.time() - start_time
            result.execution_time = execution_time

            return result

        except Exception as e:
            execution_time = time.time() - start_time

            return AgentResult(
                agent_type=task.agent_type,
                success=False,
                error_message=str(e),
                execution_time=execution_time
            )

    async def _execute_rag_task(self, task: AgentTask, state: UniversalNewState) -> AgentResult:
        """Execute RAG agent with targeted query"""
        try:
            result = self.rag_agent.process_query(
                query=state['query'],  # Original query for context
                standalone_query=task.targeted_query,  # Targeted query for this task
                blob_names=state.get('blob_names'),
                conversation_history=state.get('conversation_history'),
                conversation_summary=state.get('conversation_summary'),
                user_context=state.get('user_context', {})
            )

            return AgentResult(
                agent_type=AgentType.RAG,
                success=True,
                answer=result.get('answer', ''),
                references=result.get('references', {}),
                metadata=result.get('metadata', {}),
                confidence_score=result.get('confidence_score', 0.0)
            )

        except Exception as e:
            return AgentResult(
                agent_type=AgentType.RAG,
                success=False,
                error_message=str(e)
            )

    async def _execute_web_search_task(self, task: AgentTask, state: UniversalNewState) -> AgentResult:
        """Execute Web Search agent with targeted query"""
        try:
            result = self.web_search_agent.graph.invoke({
                "messages": [],
                "query": task.targeted_query,  # Use targeted query
                "search_results": [],
                "scraped_content": {},
                "final_answer": ""
            })

            return AgentResult(
                agent_type=AgentType.WEB_SEARCH,
                success=True,
                answer=result.get('final_answer', ''),
                references=result.get('web_sources', {}),
                metadata={"search_results": result.get('search_results', [])},
                confidence_score=0.8  # Default confidence for web search
            )

        except Exception as e:
            return AgentResult(
                agent_type=AgentType.WEB_SEARCH,
                success=False,
                error_message=str(e)
            )

    async def _execute_gpt_task(self, task: AgentTask, state: UniversalNewState) -> AgentResult:
        """Execute Universal GPT agent with targeted query and context"""
        try:
            # Prepare comprehensive context information
            context_parts = []

            # Add conversation history if available - preserve the full formatted history
            if state.get('conversation_history'):
                # Don't truncate the conversation history, it's already formatted as QA pairs
                context_parts.append(f"**Conversation History:**\n{state['conversation_history']}")
                if self.verbose:
                    qa_pairs = state['conversation_history'].count("[RECENT EXCHANGE")
                    print(f"   Conversation history: {len(state['conversation_history'])} chars, {qa_pairs} QA pairs")

            # Add previous step results in chronological order for better context continuity
            if state.get('step_results'):
                # Get steps in order
                step_ids = sorted(state['step_results'].keys(), key=lambda x: int(x.split('_')[-1]) if x.split('_')[-1].isdigit() else 0)
                for step_id in step_ids:
                    step_result = state['step_results'][step_id]
                    if step_result.success and step_result.synthesized_answer:
                        context_parts.append(f"**Previous {step_id} Results:**\n{step_result.synthesized_answer}")
                        
                        if self.verbose:
                            print(f"   Added {step_id} result: {len(step_result.synthesized_answer)} chars")

            # Add accumulated context from current workflow with better organization
            if state.get('accumulated_context'):
                # First, add previous answers for continuity if available
                if 'previous_answers' in state['accumulated_context']:
                    prev_answers = state['accumulated_context']['previous_answers']
                    for pa in prev_answers:
                        context_parts.append(f"**Previous Step {pa['step_id']} Answer:**\n{pa['answer'][:1000]}...")
                
                # Then add other relevant context, filtering out less important keys
                for key, value in state['accumulated_context'].items():
                    # Skip previous_answers since we've already added them
                    if key == 'previous_answers':
                        continue
                    
                    # Only add important context that provides valuable information
                    if (key.endswith('_result') or key.endswith('_answer')) and value and isinstance(value, str):
                        agent_name = key.replace('_result', '').replace('_answer', '')
                        context_parts.append(f"**{agent_name.title()}:**\n{value[:1000]}...")
            
            # Add task-specific context if available
            if getattr(task, 'context', None):
                context_parts.append(f"**Task-Specific Context:**\n{task.context}")

            # Format context information
            context_information = "\n\n".join(context_parts) if context_parts else None

            # Enhanced logging for debugging
            if self.verbose:
                print(f"🎯 UNIVERSAL GPT EXECUTION:")
                print(f"   Targeted Query: {task.targeted_query[:100]}...")
                print(f"   Context Available: {len(context_parts)} items")
                for i, part in enumerate(context_parts):
                    print(f"     {i+1}. {part[:100]}...")
                if context_information:
                    print(f"   Context Length: {len(context_information)} chars")

            # Use the Universal GPT agent's specialized method
            result = self.gpt_agent.process_targeted_query(
                targeted_query=task.targeted_query,
                context_information=context_information,
                original_query=state.get('query'),
                conversation_history=state.get('conversation_history'),
                conversation_summary=state.get('conversation_summary'),
                metadata={
                    'step_context': state.get('accumulated_context', {}),
                    'file_count': len(state.get('selected_file_ids', [])),
                    'blob_names': state.get('blob_names', []),
                    'has_conversation_history': bool(state.get('conversation_history')),
                    'step_results_count': len(state.get('step_results', {}))
                }
            )

            if self.verbose:
                print(f"   Universal GPT Response: {result.get('answer', '')[:150]}...")

            return AgentResult(
                agent_type=AgentType.GPT,
                success=result.get('success', True),
                answer=result.get('answer', ''),
                references=result.get('references', {}),
                metadata=result.get('metadata', {}),
                confidence_score=result.get('confidence_score', 0.85)
            )

        except Exception as e:
            if self.verbose:
                print(f"❌ UNIVERSAL GPT: Error executing task: {e}")

            return AgentResult(
                agent_type=AgentType.GPT,
                success=False,
                error_message=str(e)
            )

    async def _execute_deep_search_task(self, task: AgentTask, state: UniversalNewState) -> AgentResult:
        """Execute Deep Search agent with targeted query"""
        try:
            if not state.get('selected_file_ids'):
                return AgentResult(
                    agent_type=AgentType.DEEP_SEARCH,
                    success=False,
                    error_message="No files selected for deep search"
                )

            # Debug: Check what we're passing to Deep Search
            if self.verbose:
                print(f"🔍 DEEP SEARCH INPUT:")
                print(f"   Selected file IDs: {state.get('selected_file_ids')}")
                print(f"   Blob names: {state.get('blob_names')}")
                print(f"   Targeted query: {task.targeted_query}")

            # Prepare user context with blob names for Deep Search
            user_context = state.get('user_context', {}).copy()
            if state.get('blob_names'):
                user_context['blob_names'] = state['blob_names']

            # Configure Deep Search for Universal Agent workflow
            user_context['multi_agent_mode'] = True
            user_context['validation_mode'] = 'lenient'  # Be less strict with validation
            user_context['purpose'] = 'content_extraction'  # Focus on extracting content for other agents
            user_context['skip_strict_validation'] = True  # Skip strict context-only validation

            # Deep Search agent works better with blob_names for file processing
            # Try blob_names first, fallback to selected_file_ids
            try:
                if state.get('blob_names'):
                    if self.verbose:
                        print(f"   Calling Deep Search with blob_names: {state['blob_names']}")
                    result = await self.deep_search_agent.process_deep_search_query(
                        query=task.targeted_query,  # Use targeted query
                        selected_file_ids=state.get('selected_file_ids', []),  # Still need file IDs
                        conversation_history=state.get('conversation_history'),
                        conversation_summary=state.get('conversation_summary'),
                        user_context=user_context
                    )
                else:
                    if self.verbose:
                        print(f"   Calling Deep Search with selected_file_ids: {state['selected_file_ids']}")
                    result = await self.deep_search_agent.process_deep_search_query(
                        query=task.targeted_query,  # Use targeted query
                        selected_file_ids=state['selected_file_ids'],
                        conversation_history=state.get('conversation_history'),
                        conversation_summary=state.get('conversation_summary'),
                        user_context=user_context
                    )
            except Exception as e:
                if self.verbose:
                    print(f"   ❌ Deep Search agent threw exception: {e}")
                    import traceback
                    traceback.print_exc()

                return AgentResult(
                    agent_type=AgentType.DEEP_SEARCH,
                    success=False,
                    error_message=f"Deep Search agent error: {str(e)}"
                )

            if self.verbose:
                print(f"🔍 DEEP SEARCH RESULT:")
                print(f"   Success: {result.get('success', 'Unknown')}")
                print(f"   Answer length: {len(result.get('answer', ''))}")
                print(f"   Answer preview: {result.get('answer', '')[:200]}...")
                print(f"   References: {len(result.get('references', {}))}")
                print(f"   Metadata: {result.get('metadata', {})}")

                # Check for errors
                if 'error' in result:
                    print(f"   ❌ Error: {result['error']}")
                if not result.get('success', True):
                    print(f"   ❌ Deep Search failed - checking for error details")
                    print(f"   Full result keys: {list(result.keys())}")

            answer = result.get('answer', '')
            success = bool(answer and answer.strip())  # Only successful if we have actual content

            return AgentResult(
                agent_type=AgentType.DEEP_SEARCH,
                success=success,
                answer=answer,
                references=result.get('references', {}),
                metadata=result.get('metadata', {}),
                confidence_score=result.get('confidence_score', 0.9) if success else 0.1
            )

        except Exception as e:
            return AgentResult(
                agent_type=AgentType.DEEP_SEARCH,
                success=False,
                error_message=str(e)
            )

    def _synthesize_step_results(self, agent_results: Dict[AgentType, AgentResult],
                                step: ExecutionStep, state: UniversalNewState) -> str:
        """
        Synthesize results from all agents in a step.

        Args:
            agent_results: Results from all agents in this step
            step: The executed step
            state: Current state

        Returns:
            Synthesized answer for this step
        """
        successful_results = {
            agent_type: result for agent_type, result in agent_results.items()
            if result.success and result.answer
        }

        if self.verbose:
            print(f"🔄 STEP SYNTHESIS:")
            print(f"   Total agent results: {len(agent_results)}")
            for agent_type, result in agent_results.items():
                print(f"   {agent_type.value}: success={result.success}, answer_len={len(result.answer) if result.answer else 0}")
            print(f"   Successful results: {len(successful_results)}")

        if not successful_results:
            return "No successful results from this step."

        if len(successful_results) == 1:
            # Single result, return it directly
            return list(successful_results.values())[0].answer

        # Multiple results, combine them intelligently
        combined_answer = f"Results from {step.step_name}:\n\n"

        for agent_type, result in successful_results.items():
            combined_answer += f"**{agent_type.value.replace('_', ' ').title()} Analysis:**\n"
            combined_answer += f"{result.answer}\n\n"

        return combined_answer.strip()

    def _create_step_context(self, agent_results: Dict[AgentType, AgentResult],
                           synthesized_answer: str, step: ExecutionStep) -> Dict[str, Any]:
        """
        Create context from step results for use in next steps.

        Args:
            agent_results: Results from all agents in this step
            synthesized_answer: Synthesized answer from this step
            step: The executed step

        Returns:
            Context dictionary for next steps
        """
        # Create a new context with well-organized structure
        context = {
            # Store step results in a consistent, easy-to-access format
            f"step_{step.step_id}_answer": synthesized_answer,
            f"step_{step.step_id}_agents_used": [agent.value for agent in agent_results.keys()],
            f"step_{step.step_id}_execution_time": step.execution_time if hasattr(step, 'execution_time') else 0.0
        }

        # Add detailed results from each agent with clear naming convention
        for agent_type, result in agent_results.items():
            if result.success:
                # Store agent results with a consistent naming pattern
                agent_key = f"{step.step_id}_{agent_type.value}"
                context[f"{agent_key}_result"] = result.answer
                
                # Store metadata for better context awareness
                if result.metadata:
                    # Only store key metadata fields to avoid bloat
                    important_metadata = {
                        k: v for k, v in result.metadata.items() 
                        if k in ["confidence_score", "source_count", "has_references"]
                    }
                    context[f"{agent_key}_metadata"] = important_metadata

        # Add a summary section that's easy for models to understand
        context["latest_step_summary"] = {
            "step_id": step.step_id,
            "step_name": step.step_name,
            "synthesized_answer": synthesized_answer[:500] + "..." if len(synthesized_answer) > 500 else synthesized_answer,
            "agents_used": [agent.value for agent in agent_results.keys()],
            "success": all(result.success for result in agent_results.values())
        }
        
        if self.verbose:
            print(f"   Step context created with {len(context)} keys")
            print(f"   Key context items: {list(context.keys())[:5]}")

        return context

    async def _validate_step_results(self, agent_results: Dict[AgentType, AgentResult],
                                   synthesized_answer: str, state: UniversalNewState) -> ValidationResult:
        """
        Validate the results from a step.

        Args:
            agent_results: Results from all agents in this step
            synthesized_answer: Synthesized answer from this step
            state: Current state

        Returns:
            Validation result
        """
        if not self.validator:
            return ValidationResult(
                is_valid=True,
                confidence_score=0.8,
                source_adherence_score=0.8
            )

        try:
            # Create a temporary state for validation
            temp_state = state.copy()
            temp_state['final_answer'] = synthesized_answer

            # Use the validator to check the synthesized answer
            validation = self.validator.validate_answer(
                state=temp_state,
                synthesized_answer=synthesized_answer,
                references={}  # Step-level validation doesn't have final references yet
            )

            return validation

        except Exception as e:
            return ValidationResult(
                is_valid=False,
                confidence_score=0.0,
                source_adherence_score=0.0,
                improvement_suggestions=[f"Validation error: {str(e)}"]
            )
