"""
Universal New Agent Module

A sophisticated multi-agent orchestration system that intelligently plans and executes
queries using multiple specialized agents (RAG, Web, GPT, DeepSearch) with advanced
workflow coordination, context management, and validation.

Key Features:
- Intelligent universal planner for agent selection and orchestration
- Support for parallel and sequential agent execution
- Context passing between agents in sequential workflows
- Comprehensive reference collection and deduplication
- Answer synthesis from multiple agent results
- Validation system for hallucination detection
- QA feedback loop for iterative improvement

Usage:
    from agents.universal_new import UniversalNewAgent
    
    agent = UniversalNewAgent(verbose=True)
    result = await agent.process_query(
        query="Update my resume with latest skills from web research",
        selected_file_ids=[1, 2, 3],
        conversation_history="Previous discussion about career goals"
    )
"""

from .agent import create_universal_new_agent, UniversalNewAgent
from .state import (
    AgentType, ExecutionStrategy, UniversalNewState, AgentExecutionPlan,
    AgentResult, ValidationResult, QAFeedback, UniversalNewConfig
)
from .planner import EnhancedUniversalPlanner
from .context_manager import ContextManager
from .reference_collector import ReferenceCollector
from .validator import UniversalValidator
from .qa_feedback import QAFeedbackSystem
from .synthesizer import AnswerSynthesizer
from .orchestrator import AgentOrchestrator
from .multi_step_orchestrator import MultiStepOrchestrator


__all__ = [
    'create_universal_new_agent',
    'UniversalNewAgent',
    'AgentType',
    'ExecutionStrategy',
    'UniversalNewState',
    'AgentExecutionPlan',
    'AgentResult',
    'ValidationResult',
    'QAFeedback',
    'UniversalNewConfig',
    'EnhancedUniversalPlanner',
    'ContextManager',
    'ReferenceCollector',
    'UniversalValidator',
    'QAFeedbackSystem',
    'AnswerSynthesizer',
    'AgentOrchestrator',
    'MultiStepOrchestrator'
]

__version__ = "1.0.0"
