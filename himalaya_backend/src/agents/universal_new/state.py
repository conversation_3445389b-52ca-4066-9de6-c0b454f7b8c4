"""
State management for Universal New Agent

Defines all state structures for the sophisticated multi-agent orchestration system.
"""

from typing import Dict, List, Any, Optional, TypedDict, Union
from enum import Enum
import time
from dataclasses import dataclass, field


class AgentType(Enum):
    """Available agent types for orchestration"""
    RAG = "rag"
    WEB_SEARCH = "web_search"
    GPT = "gpt"
    DEEP_SEARCH = "deep_search"


class ExecutionStrategy(Enum):
    """Execution strategies for agent orchestration"""
    SEQUENTIAL = "sequential"
    PARALLEL = "parallel"
    HYBRID = "hybrid"  # Mix of sequential and parallel


class WorkflowStep(Enum):
    """Workflow steps in the universal agent processing"""
    PLANNING = "planning"
    AGENT_EXECUTION = "agent_execution"
    CONTEXT_SYNTHESIS = "context_synthesis"
    VALIDATION = "validation"
    QA_FEEDBACK = "qa_feedback_analysis"
    FINAL_SYNTHESIS = "final_synthesis"


class WorkflowStepType(Enum):
    """Types of workflow steps for multi-step orchestration"""
    INITIAL_PLANNING = "initial_planning"
    STEP_EXECUTION = "step_execution"
    STEP_SYNTHESIS = "step_synthesis"
    STEP_VALIDATION = "step_validation"
    DYNAMIC_PLANNING = "dynamic_planning"
    FINAL_SYNTHESIS = "final_synthesis"
    FINAL_VALIDATION = "final_validation"
    FINALIZATION = "finalization"


@dataclass
class AgentTask:
    """Individual task for a specific agent with targeted query"""
    agent_type: AgentType
    targeted_query: str  # Specific query tailored for this agent
    reasoning: str
    parameters: Dict[str, Any] = field(default_factory=dict)
    context_dependencies: List[str] = field(default_factory=list)
    expected_output: str = ""
    priority: int = 1  # For ordering within parallel execution
    context: str = ""  # Context about previous steps and state


@dataclass
class ExecutionStep:
    """A step in the multi-step workflow"""
    step_id: str
    step_name: str
    step_description: str
    agent_tasks: List[AgentTask]
    execution_strategy: ExecutionStrategy
    depends_on_steps: List[str] = field(default_factory=list)
    validation_required: bool = True
    completed: bool = False


@dataclass
class StepResult:
    """Result from executing a workflow step"""
    step_id: str
    agent_results: Dict[AgentType, 'AgentResult']
    synthesized_answer: str = ""
    accumulated_context: Dict[str, Any] = field(default_factory=dict)
    validation_result: Optional['ValidationResult'] = None
    execution_time: float = 0.0
    success: bool = True
    error_message: Optional[str] = None


@dataclass
class AgentExecutionPlan:
    """Plan for executing a specific agent (legacy compatibility)"""
    agent_type: AgentType
    execution_order: int
    parallel_group: Optional[int] = None  # For parallel execution grouping
    context_dependencies: List[AgentType] = field(default_factory=list)
    parameters: Dict[str, Any] = field(default_factory=dict)
    reasoning: str = ""
    expected_output_type: str = "answer"  # answer, data, analysis, etc.


@dataclass
class AgentResult:
    """Result from executing an agent"""
    agent_type: AgentType
    success: bool
    answer: str = ""
    references: Dict[str, Any] = field(default_factory=dict)
    metadata: Dict[str, Any] = field(default_factory=dict)
    execution_time: float = 0.0
    error_message: Optional[str] = None
    confidence_score: float = 0.0
    context_for_next: Dict[str, Any] = field(default_factory=dict)


@dataclass
class ValidationResult:
    """Result from validation process"""
    is_valid: bool
    confidence_score: float
    hallucinations_detected: List[str] = field(default_factory=list)
    missing_information: List[str] = field(default_factory=list)
    source_adherence_score: float = 0.0
    improvement_suggestions: List[str] = field(default_factory=list)


@dataclass
class QAFeedback:
    """Feedback from QA system to planner"""
    needs_additional_agents: bool
    suggested_agents: List[AgentType] = field(default_factory=list)
    missing_information: List[str] = field(default_factory=list)
    improvement_instructions: str = ""
    confidence_in_current_answer: float = 0.0


class UniversalNewState(TypedDict):
    """Main state for Universal New Agent with multi-step workflow support"""

    # Input parameters
    query: str
    standalone_query: str
    conversation_history: Optional[str]
    conversation_summary: Optional[str]
    selected_file_ids: Optional[List[int]]
    blob_names: Optional[List[str]]
    user_context: Dict[str, Any]

    # Multi-step workflow planning
    workflow_steps: List[ExecutionStep]
    current_step_index: int
    step_results: Dict[str, StepResult]
    accumulated_context: Dict[str, Any]  # Context accumulated across all steps

    # Legacy planning phase (for backward compatibility)
    execution_plan: List[AgentExecutionPlan]
    execution_strategy: ExecutionStrategy
    planner_reasoning: str
    estimated_execution_time: float

    # Execution phase
    agent_results: Dict[AgentType, AgentResult]
    current_context: Dict[str, Any]  # Context passed between sequential agents
    accumulated_answer: str  # Progressive answer building
    accumulated_references: Dict[str, Any]  # Collected references

    # Validation and QA
    validation_results: List[ValidationResult]
    qa_feedback_result: Optional[QAFeedback]
    needs_iteration: bool
    iteration_count: int

    # Final results
    final_answer: str
    final_references: Dict[str, Any]
    confidence_score: float

    # Metadata
    workflow_step: WorkflowStep
    total_execution_time: float
    error_messages: List[str]
    debug_info: Dict[str, Any]
    processing_metadata: Dict[str, Any]


@dataclass
class ContextPassingConfig:
    """Configuration for context passing between agents"""
    include_previous_answers: bool = True
    include_references: bool = True
    include_metadata: bool = False
    max_context_length: int = 4000
    context_summary_threshold: int = 2000


@dataclass
class ReferenceCollectionConfig:
    """Configuration for reference collection and deduplication"""
    deduplicate_by_source: bool = True
    deduplicate_by_content: bool = True
    max_references_per_agent: int = 50
    include_confidence_scores: bool = True
    resolve_file_names: bool = True  # Use DB for clean file names


@dataclass
class ValidationConfig:
    """Configuration for validation system"""
    enable_hallucination_detection: bool = True
    enable_source_adherence_check: bool = True
    enable_completeness_check: bool = True
    min_confidence_threshold: float = 0.7
    max_validation_iterations: int = 3


@dataclass
class UniversalNewConfig:
    """Overall configuration for Universal New Agent"""
    max_agents_per_query: int = 4
    max_iterations: int = 3
    enable_parallel_execution: bool = True
    enable_context_passing: bool = True
    enable_validation: bool = True
    enable_qa_feedback: bool = True
    
    context_config: ContextPassingConfig = field(default_factory=ContextPassingConfig)
    reference_config: ReferenceCollectionConfig = field(default_factory=ReferenceCollectionConfig)
    validation_config: ValidationConfig = field(default_factory=ValidationConfig)
    
    # Agent-specific configurations
    agent_configs: Dict[AgentType, Dict[str, Any]] = field(default_factory=dict)
    
    # Timeouts and limits
    max_total_execution_time: float = 300.0  # 5 minutes
    max_agent_execution_time: float = 60.0   # 1 minute per agent
    
    verbose: bool = True


# Helper functions for state management
def create_initial_state(
    query: str,
    standalone_query: str = None,
    conversation_history: str = None,
    conversation_summary: str = None,
    selected_file_ids: List[int] = None,
    blob_names: List[str] = None,
    user_context: Dict[str, Any] = None
) -> UniversalNewState:
    """Create initial state for Universal New Agent processing"""
    
    return UniversalNewState(
        # Input
        query=query,
        standalone_query=standalone_query or query,
        conversation_history=conversation_history,
        conversation_summary=conversation_summary,
        selected_file_ids=selected_file_ids or [],
        blob_names=blob_names or [],
        user_context=user_context or {},

        # Multi-step workflow
        workflow_steps=[],
        current_step_index=0,
        step_results={},
        accumulated_context={},

        # Legacy planning (for backward compatibility)
        execution_plan=[],
        execution_strategy=ExecutionStrategy.SEQUENTIAL,
        planner_reasoning="",
        estimated_execution_time=0.0,

        # Execution
        agent_results={},
        current_context={},
        accumulated_answer="",
        accumulated_references={},

        # Validation
        validation_results=[],
        qa_feedback_result=None,
        needs_iteration=False,
        iteration_count=0,

        # Final
        final_answer="",
        final_references={},
        confidence_score=0.0,

        # Metadata
        workflow_step=WorkflowStep.PLANNING,
        total_execution_time=0.0,
        error_messages=[],
        debug_info={},
        processing_metadata={}
    )


def update_state_with_agent_result(
    state: UniversalNewState,
    agent_type: AgentType,
    result: AgentResult
) -> UniversalNewState:
    """Update state with result from an agent execution"""
    
    # Update agent results
    state["agent_results"][agent_type] = result
    
    # Update accumulated answer if sequential
    if state["execution_strategy"] == ExecutionStrategy.SEQUENTIAL and result.success:
        if state["accumulated_answer"]:
            state["accumulated_answer"] += f"\n\n{result.answer}"
        else:
            state["accumulated_answer"] = result.answer
    
    # Update accumulated references
    if result.references:
        state["accumulated_references"].update(result.references)
    
    # Update context for next agents
    if result.context_for_next:
        state["current_context"].update(result.context_for_next)
    
    return state
