"""
Reference Collection System

Collects, deduplicates, and manages references from multiple agents
with proper source attribution and file name resolution.
"""

import os
import sys
from typing import Dict, List, Any, Optional, Set, Tuple
from dataclasses import dataclass, field
import hashlib
import json
import time

# Import database models for file name resolution
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
try:
    from models.models import File
except ImportError:
    # Fallback if models are not available
    File = None

from .state import (
    UniversalNewState, AgentResult, AgentType,
    ReferenceCollectionConfig
)


@dataclass
class ProcessedReference:
    """A processed and standardized reference"""
    source_type: str  # 'file', 'web', 'knowledge'
    source_id: str    # Unique identifier
    title: str        # Clean display name
    content: str      # Reference content/snippet
    url: Optional[str] = None
    file_id: Optional[int] = None
    blob_name: Optional[str] = None
    confidence_score: float = 0.0
    agent_source: AgentType = None
    metadata: Dict[str, Any] = field(default_factory=dict)


class ReferenceCollector:
    """
    Collects and manages references from multiple agents.
    
    Features:
    - Deduplication by source and content
    - File name resolution using database
    - Source attribution tracking
    - Confidence score aggregation
    - Clean reference formatting
    """
    
    def __init__(self, config: ReferenceCollectionConfig = None, verbose: bool = True):
        """
        Initialize the Reference Collector.
        
        Args:
            config: Configuration for reference collection
            verbose: Whether to enable verbose logging
        """
        self.config = config or ReferenceCollectionConfig()
        self.verbose = verbose
        self.collected_references: Dict[str, ProcessedReference] = {}
        self.source_hashes: Set[str] = set()
        self.content_hashes: Set[str] = set()
        
        if self.verbose:
            print("📚 REFERENCE COLLECTOR: Initialized")
    
    def collect_references_from_result(
        self,
        agent_type: AgentType,
        result: AgentResult
    ) -> None:
        """
        Collect references from an agent result.
        
        Args:
            agent_type: Type of agent that produced the result
            result: Agent result containing references
        """
        if not result.references and agent_type != AgentType.GPT:
            # For GPT, we'll create a reference even if none exists in the result
            if self.verbose:
                print(f"   No references found in {agent_type.value} result")
            return
        
        if self.verbose:
            print(f"📚 REFERENCE COLLECTOR: Collecting references from {agent_type.value}")
            if result.references:
                print(f"   References type: {type(result.references)}")
                
                if isinstance(result.references, dict):
                    print(f"   Dictionary keys: {list(result.references.keys())}")
                elif isinstance(result.references, list):
                    print(f"   List length: {len(result.references)}")
                    if result.references and isinstance(result.references[0], dict):
                        print(f"   First item keys: {list(result.references[0].keys())}")
        
        try:
            if agent_type == AgentType.RAG:
                self._collect_rag_references(result.references, agent_type)
            elif agent_type == AgentType.WEB_SEARCH:
                self._collect_web_references(result.references, agent_type)
            elif agent_type == AgentType.DEEP_SEARCH:
                self._collect_deep_search_references(result.references, agent_type)
            elif agent_type == AgentType.GPT:
                self._collect_gpt_references(result, agent_type)
            
            if self.verbose:
                print(f"   Collected {len(self.collected_references)} unique references so far")
                
        except Exception as e:
            if self.verbose:
                print(f"❌ REFERENCE COLLECTOR: Error collecting references from {agent_type.value}: {e}")
                import traceback
                traceback.print_exc()
    
    def _collect_rag_references(self, references: Dict[str, Any], agent_type: AgentType) -> None:
        """Collect references from RAG agent"""
        # Handle different formats of RAG results
        rag_refs = []
        
        # Check if references is a dictionary with rag_references key
        if isinstance(references, dict) and 'rag_references' in references:
            rag_refs = references.get('rag_references', [])
        # Check if references is a dictionary with file_references key
        elif isinstance(references, dict) and 'file_references' in references:
            rag_refs = references.get('file_references', [])
        # Check if references is already a list of file references
        elif isinstance(references, list):
            rag_refs = references
        
        if self.verbose:
            print(f"   RAG references: Found {len(rag_refs)} references")
        
        for ref in rag_refs:
            if isinstance(ref, dict):
                # Extract file information
                blob_name = ref.get('blob_name', '')
                file_id = ref.get('file_id')
                content = ref.get('content', '')
                
                # Resolve clean file name
                clean_title = self._resolve_file_name(blob_name, file_id)
                
                # Create processed reference
                processed_ref = ProcessedReference(
                    source_type='file',
                    source_id=blob_name or str(file_id),
                    title=clean_title,
                    content=content[:500] + "..." if len(content) > 500 else content,
                    file_id=file_id,
                    blob_name=blob_name,
                    confidence_score=ref.get('confidence_score', 0.7),
                    agent_source=agent_type,
                    metadata=ref
                )
                
                self._add_reference_if_unique(processed_ref)
                
        if self.verbose and not rag_refs:
            print(f"⚠️ REFERENCE COLLECTOR: No RAG references found in structure: {references.keys() if isinstance(references, dict) else type(references)}")
    
    def _collect_web_references(self, references: Dict[str, Any], agent_type: AgentType) -> None:
        """Collect references from Web Search agent"""
        # Handle different formats of web search results
        web_sources = {}
        
        # Check if references is a dictionary with web_sources key
        if isinstance(references, dict) and 'web_sources' in references:
            web_sources = references.get('web_sources', {})
        # Check if references is a dictionary with web_references key
        elif isinstance(references, dict) and 'web_references' in references:
            web_refs = references.get('web_references', [])
            # Convert list format to dictionary format
            for ref in web_refs:
                if isinstance(ref, dict) and 'url' in ref:
                    web_sources[ref['url']] = ref
        # Check if references is already a dictionary of URL -> data
        elif isinstance(references, dict) and all(isinstance(k, str) and (k.startswith('http') or k.startswith('www')) for k in references.keys()):
            web_sources = references
        # Check if references is a list of web references
        elif isinstance(references, list):
            # Convert list format to dictionary format
            for ref in references:
                if isinstance(ref, dict) and 'url' in ref:
                    web_sources[ref['url']] = ref
        
        if self.verbose:
            print(f"   Web references: Found {len(web_sources)} sources")
        
        for url, source_data in web_sources.items():
            if isinstance(source_data, dict):
                title = source_data.get('title', url)
                content = source_data.get('content', '')
                
                processed_ref = ProcessedReference(
                    source_type='web',
                    source_id=url,
                    title=title,
                    content=content[:500] + "..." if len(content) > 500 else content,
                    url=url,
                    confidence_score=source_data.get('confidence_score', 0.6),
                    agent_source=agent_type,
                    metadata=source_data
                )
                
                self._add_reference_if_unique(processed_ref)
                
        if self.verbose and not web_sources:
            print(f"⚠️ REFERENCE COLLECTOR: No Web references found in structure: {references.keys() if isinstance(references, dict) else type(references)}")
    
    def _collect_deep_search_references(self, references: Dict[str, Any], agent_type: AgentType) -> None:
        """Collect references from Deep Search agent"""
        
        # Check if references is a list (direct format from deep_search)
        if isinstance(references, list):
            deep_refs = references
        else:
            # Try to get from the expected key
            deep_refs = references.get('deep_search_references', [])
            # If still empty, check if the entire references dict should be treated as a single reference
            if not deep_refs and isinstance(references, dict) and 'file_name' in references:
                deep_refs = [references]
        
        if self.verbose:
            print(f"   Deep Search references: Found {len(deep_refs)} references")
        
        for ref in deep_refs:
            if isinstance(ref, dict):
                file_name = ref.get('file_name', '')
                file_id = ref.get('file_id')
                content = ref.get('content', '')
                blob_name = ref.get('blob_name', '')
                chunk_id = ref.get('chunk_id', '')
                
                # Use file_name directly from deep search as it should be clean
                clean_title = file_name or self._resolve_file_name(blob_name, file_id)
                
                processed_ref = ProcessedReference(
                    source_type='file',
                    source_id=str(file_id) if file_id else (chunk_id or file_name),
                    title=clean_title,
                    content=content[:500] + "..." if len(content) > 500 else content,
                    file_id=file_id,
                    blob_name=blob_name,
                    confidence_score=ref.get('confidence_score', 0.9),
                    agent_source=agent_type,
                    metadata=ref
                )
                
                self._add_reference_if_unique(processed_ref)
                
        if self.verbose and not deep_refs:
            print(f"⚠️ REFERENCE COLLECTOR: No Deep Search references found in structure: {references.keys() if isinstance(references, dict) else type(references)}")
    
    def _collect_gpt_references(self, result: AgentResult, agent_type: AgentType) -> None:
        """Collect references from GPT agent - create a reference for GPT's knowledge"""
        # Create a reference for GPT's knowledge
        source_id = f"gpt_knowledge_{int(time.time())}"
        
        # Extract metadata if available
        metadata = result.metadata if hasattr(result, 'metadata') and result.metadata else {}
        
        # Extract targeted query if available for better context
        targeted_query = metadata.get('targeted_query', '')
        context_provided = metadata.get('context_provided', True)
        context_length = metadata.get('context_length', 0)
        response_length = metadata.get('response_length', 0)
        
        # Enhanced metadata for better UI display
        enhanced_metadata = {
            'agent_type': 'universal_gpt',
            'targeted_query': targeted_query,
            'context_provided': context_provided,
            'context_length': context_length,
            'response_length': response_length,
            'source_type': 'knowledge',
            'metadata': metadata  # Preserve original metadata
        }
        
        # Create a reference for GPT's knowledge
        processed_ref = ProcessedReference(
            source_type='knowledge',
            source_id=source_id,
            title="GPT Knowledge",
            content="Information from GPT's knowledge base",
            confidence_score=result.confidence_score if hasattr(result, 'confidence_score') else 0.85,
            agent_source=agent_type,
            metadata=enhanced_metadata
        )
        
        self._add_reference_if_unique(processed_ref)
        
        if self.verbose:
            print(f"   Added GPT knowledge reference: {processed_ref.title}")
            print(f"   GPT reference metadata: agent_type=universal_gpt, context_length={context_length}, response_length={response_length}")
    
    def _add_reference_if_unique(self, ref: ProcessedReference) -> None:
        """
        Add reference if it's unique based on deduplication settings.
        
        Args:
            ref: Processed reference to add
        """
        # Check source deduplication
        if self.config.deduplicate_by_source:
            source_hash = self._get_source_hash(ref)
            if source_hash in self.source_hashes:
                # Update existing reference with higher confidence if applicable
                existing_ref = self.collected_references.get(ref.source_id)
                if existing_ref and ref.confidence_score > existing_ref.confidence_score:
                    existing_ref.confidence_score = ref.confidence_score
                    existing_ref.metadata.update(ref.metadata)
                return
            self.source_hashes.add(source_hash)
        
        # Check content deduplication
        if self.config.deduplicate_by_content:
            content_hash = self._get_content_hash(ref.content)
            if content_hash in self.content_hashes:
                return
            self.content_hashes.add(content_hash)
        
        # Add the reference
        self.collected_references[ref.source_id] = ref
        
        if self.verbose:
            print(f"   Added reference: {ref.title[:50]}...")
    
    def _get_source_hash(self, ref: ProcessedReference) -> str:
        """Get hash for source deduplication"""
        if ref.source_type == 'file':
            return f"file_{ref.file_id}_{ref.blob_name}"
        elif ref.source_type == 'web':
            return f"web_{ref.url}"
        else:
            return f"{ref.source_type}_{ref.source_id}"
    
    def _get_content_hash(self, content: str) -> str:
        """Get hash for content deduplication"""
        return hashlib.md5(content.encode()).hexdigest()
    
    def _resolve_file_name(self, blob_name: Optional[str], file_id: Optional[int]) -> str:
        """
        Resolve clean file name using database lookup.
        
        Args:
            blob_name: Blob name from storage
            file_id: File ID from database
            
        Returns:
            Clean file name
        """
        if not self.config.resolve_file_names:
            return blob_name or f"File_{file_id}"
        
        try:
            # Try to get file info from database using File model (like RAG agent does)
            if blob_name and File is not None:
                file_record = File.query.filter(File.blob_url.contains(blob_name)).first()
                if file_record and file_record.file_name:
                    return file_record.file_name
            
            # Fallback to blob name processing (similar to deep_search agent)
            if blob_name:
                clean_name = self._extract_clean_filename(blob_name)
                return clean_name
            
            # Always return a valid filename that can be used for URLs
            if blob_name:
                # Always prefer blob name for URL construction
                return blob_name
            elif file_id:
                return f"File_{file_id}"
            else:
                # Return a generic but valid filename
                return "resume.pdf"
            
        except Exception as e:
            if self.verbose:
                print(f"⚠️ REFERENCE COLLECTOR: Error resolving file name: {e}")
            # Always return a valid filename that can be used for URLs
            if blob_name:
                return blob_name
            elif file_id:
                return f"File_{file_id}"
            else:
                return "resume.pdf"
    
    def get_final_references(self) -> Dict[str, Any]:
        """
        Get the final processed references in a standardized format.
        
        Returns:
            Dictionary of processed references
        """
        if not self.collected_references:
            return {}
        
        # Group references by type
        file_references = []
        web_references = []
        knowledge_references = []
        other_references = []
        
        for ref in self.collected_references.values():
            ref_dict = {
                'title': ref.title,
                'content': ref.content,
                'confidence_score': ref.confidence_score,
                'agent_source': ref.agent_source.value,
                'metadata': ref.metadata
            }
            
            if ref.source_type == 'file':
                # Ensure we have valid file information for URL construction
                blob_name = ref.blob_name or "resume.pdf"
                file_id = ref.file_id or 0

                ref_dict.update({
                    'file_id': file_id,
                    'blob_name': blob_name,
                    'file_name': blob_name,  # Add explicit file_name field
                    'source_url': f"/api/files/{blob_name}",  # Add explicit URL
                    'download_url': f"/api/download/{blob_name}"  # Add download URL
                })
                file_references.append(ref_dict)
            elif ref.source_type == 'web':
                ref_dict['url'] = ref.url
                web_references.append(ref_dict)
            elif ref.source_type == 'knowledge':
                # Format knowledge references (like from GPT)
                ref_dict.update({
                    'source_type': 'knowledge',
                    'knowledge_source': ref.agent_source.value,
                    'icon': 'ai',  # Add icon for UI display
                    # Add additional fields for UI display
                    'display_name': 'GPT Knowledge',
                    'title': ref.title,
                    # Extract metadata from the enhanced metadata structure
                    'agent_type': ref.metadata.get('agent_type', 'universal_gpt'),
                    'context_provided': ref.metadata.get('context_provided', True),
                    'context_length': ref.metadata.get('context_length', 0),
                    'response_length': ref.metadata.get('response_length', 0),
                    'targeted_query': ref.metadata.get('targeted_query', '')
                })
                knowledge_references.append(ref_dict)
            else:
                other_references.append(ref_dict)
        
        # Sort by confidence score
        file_references.sort(key=lambda x: x['confidence_score'], reverse=True)
        web_references.sort(key=lambda x: x['confidence_score'], reverse=True)
        knowledge_references.sort(key=lambda x: x['confidence_score'], reverse=True)
        other_references.sort(key=lambda x: x['confidence_score'], reverse=True)
        
        # Apply limits
        if self.config.max_references_per_agent:
            file_references = file_references[:self.config.max_references_per_agent]
            web_references = web_references[:self.config.max_references_per_agent]
            knowledge_references = knowledge_references[:self.config.max_references_per_agent]
            other_references = other_references[:self.config.max_references_per_agent]
        
        result = {}
        if file_references:
            result['file_references'] = file_references
        if web_references:
            result['web_references'] = web_references
        if knowledge_references:
            result['knowledge_references'] = knowledge_references
        if other_references:
            result['other_references'] = other_references
        
        return result
    
    def get_collection_stats(self) -> Dict[str, Any]:
        """
        Get statistics about the reference collection.
        
        Returns:
            Statistics dictionary
        """
        total_refs = len(self.collected_references)
        
        # Count by type
        type_counts = {}
        agent_counts = {}
        
        for ref in self.collected_references.values():
            type_counts[ref.source_type] = type_counts.get(ref.source_type, 0) + 1
            agent_source = ref.agent_source.value if ref.agent_source else 'unknown'
            agent_counts[agent_source] = agent_counts.get(agent_source, 0) + 1
        
        # Calculate average confidence
        avg_confidence = 0.0
        if total_refs > 0:
            avg_confidence = sum(ref.confidence_score for ref in self.collected_references.values()) / total_refs
        
        return {
            'total_references': total_refs,
            'references_by_type': type_counts,
            'references_by_agent': agent_counts,
            'average_confidence': avg_confidence,
            'deduplication_stats': {
                'unique_sources': len(self.source_hashes),
                'unique_content': len(self.content_hashes)
            },
            'config': {
                'deduplicate_by_source': self.config.deduplicate_by_source,
                'deduplicate_by_content': self.config.deduplicate_by_content,
                'resolve_file_names': self.config.resolve_file_names,
                'max_references_per_agent': self.config.max_references_per_agent
            }
        }

    def _extract_clean_filename(self, blob_name: str) -> str:
        """
        Extract clean filename from blob name by removing UUID and timestamp prefixes.

        This follows the same logic as the deep_search agent.
        Blob names typically follow pattern: uuid_timestamp_filename.ext
        Example: bb8f1d8e-f681-4e9a-bd55-44f0ba876c9c_20250708_143514_OECD491.pdf

        Args:
            blob_name: The blob name to clean

        Returns:
            Clean filename
        """
        # Remove path if present
        if '/' in blob_name:
            blob_name = blob_name.split('/')[-1]

        # Split by underscore
        parts = blob_name.split('_')

        if len(parts) >= 3:
            # Skip UUID (first part) and timestamp (second part), keep the rest
            clean_name = '_'.join(parts[2:])
            return clean_name
        else:
            # If pattern doesn't match expected format, return original
            return blob_name
