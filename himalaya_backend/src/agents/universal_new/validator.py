"""
Validation System

Validates synthesized answers for hallucinations, source adherence,
and completeness similar to the deep_search validation system.
"""

import os
import sys
import json
from typing import Dict, List, Any, Optional
import datetime

from langchain_core.messages import SystemMessage, HumanMessage
from langchain_openai import AzureChatOpenAI

# Import configuration
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
from config.settings import AZURE_OPENAI_KEY, AZURE_OPENAI_API_VERSION, AZURE_OPENAI_ENDPOINT, AZURE_OPENAI_DEPLOYMENT_NAME

from .state import (
    UniversalNewState, ValidationResult, ValidationConfig,
    UniversalNewConfig
)


class UniversalValidator:
    """
    Validates synthesized answers from the Universal New Agent.
    
    Features:
    - Hallucination detection
    - Source adherence validation
    - Completeness assessment
    - Context-only validation
    - Multi-agent result cross-validation
    """
    
    def __init__(self, model=None, config: ValidationConfig = None, verbose: bool = True):
        """
        Initialize the Universal Validator.
        
        Args:
            model: LLM model for validation
            config: Validation configuration
            verbose: Whether to enable verbose logging
        """
        self.verbose = verbose
        self.config = config or ValidationConfig()
        self.today_date = datetime.datetime.now().strftime("%Y-%m-%d")
        
        # Initialize the model
        if model:
            self.model = model
        else:
            self.model = AzureChatOpenAI(
                azure_deployment=AZURE_OPENAI_DEPLOYMENT_NAME,
                api_version=AZURE_OPENAI_API_VERSION,
                azure_endpoint=AZURE_OPENAI_ENDPOINT,
                api_key=AZURE_OPENAI_KEY,
                temperature=0.1  # Very low temperature for consistent validation
            )
            if self.verbose:
                print(f"✅ UNIVERSAL VALIDATOR: Using Azure OpenAI with deployment {AZURE_OPENAI_DEPLOYMENT_NAME}")
    
    def validate_answer(
        self,
        state: UniversalNewState,
        synthesized_answer: str,
        references: Dict[str, Any]
    ) -> ValidationResult:
        """
        Validate the synthesized answer against sources and context.
        
        Args:
            state: Current state with agent results
            synthesized_answer: The synthesized final answer
            references: Collected references from all agents
            
        Returns:
            Validation result
        """
        if self.verbose:
            print(f"\n✅ UNIVERSAL VALIDATOR: Validating synthesized answer")
            print(f"   Answer length: {len(synthesized_answer)} characters")
            print(f"   References: {len(references)} sources")
        
        try:
            # Perform comprehensive validation
            validation_result = self._perform_comprehensive_validation(
                state, synthesized_answer, references
            )
            
            if self.verbose:
                print(f"✅ VALIDATOR: Validation completed")
                print(f"   Valid: {validation_result.is_valid}")
                print(f"   Confidence: {validation_result.confidence_score:.2f}")
                print(f"   Hallucinations: {len(validation_result.hallucinations_detected)}")
                print(f"   Missing info: {len(validation_result.missing_information)}")
            
            return validation_result
            
        except Exception as e:
            if self.verbose:
                print(f"❌ UNIVERSAL VALIDATOR: Error during validation: {e}")
            
            # Return failed validation
            return ValidationResult(
                is_valid=False,
                confidence_score=0.0,
                hallucinations_detected=[f"Validation error: {str(e)}"],
                missing_information=["Unable to complete validation"],
                source_adherence_score=0.0,
                improvement_suggestions=["Please retry the query"]
            )
    
    def _perform_comprehensive_validation(
        self,
        state: UniversalNewState,
        answer: str,
        references: Dict[str, Any]
    ) -> ValidationResult:
        """
        Perform comprehensive validation of the answer.
        
        Args:
            state: Current state
            answer: Answer to validate
            references: References to validate against
            
        Returns:
            Validation result
        """
        # Prepare validation context
        validation_context = self._prepare_validation_context(state, references)
        
        # Get validation response from LLM
        validation_response = self._get_validation_response(
            state['query'], answer, validation_context
        )
        
        # Parse validation response
        return self._parse_validation_response(validation_response)
    
    def _prepare_validation_context(
        self,
        state: UniversalNewState,
        references: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Prepare context for validation.
        
        Args:
            state: Current state
            references: References from all agents
            
        Returns:
            Validation context
        """
        context = {
            'query': state['query'],
            'agent_results': {},
            'references': references,
            'execution_strategy': state['execution_strategy'].value
        }
        
        # Add agent results for cross-validation
        for agent_type, result in state['agent_results'].items():
            if result.success:
                context['agent_results'][agent_type.value] = {
                    'answer': result.answer,
                    'confidence': result.confidence_score,
                    'references': result.references
                }
        
        return context
    
    def _get_validation_response(
        self,
        query: str,
        answer: str,
        context: Dict[str, Any]
    ) -> str:
        """
        Get validation response from LLM.
        
        Args:
            query: Original query
            answer: Answer to validate
            context: Validation context
            
        Returns:
            Validation response
        """
        system_prompt = self._get_validation_system_prompt()
        
        # Format agent results
        agent_results_text = ""
        for agent_name, result in context['agent_results'].items():
            agent_results_text += f"\n{agent_name.upper()} RESULT:\n{result['answer'][:500]}...\n"
        
        # Format references
        references_text = ""
        for ref_type, refs in context['references'].items():
            references_text += f"\n{ref_type.upper()}:\n"
            if isinstance(refs, list):
                for ref in refs[:5]:  # Limit to first 5
                    if isinstance(ref, dict):
                        references_text += f"- {ref.get('title', 'Unknown')}: {ref.get('content', '')[:200]}...\n"
            references_text += "\n"
        
        user_prompt = f"""
        ORIGINAL QUERY: {query}
        
        ANSWER TO VALIDATE:
        {answer}
        
        AGENT RESULTS FOR CROSS-VALIDATION:
        {agent_results_text}
        
        AVAILABLE REFERENCES:
        {references_text}
        
        EXECUTION STRATEGY: {context['execution_strategy']}
        
        Please validate this answer according to the criteria in your instructions and provide a detailed JSON response.
        """
        
        messages = [
            SystemMessage(content=system_prompt),
            HumanMessage(content=user_prompt)
        ]
        
        response = self.model.invoke(messages)
        return response.content
    
    def _get_validation_system_prompt(self) -> str:
        """Get system prompt for validation."""
        return f"""You are an expert answer validator for a multi-agent system.

Your role is to validate synthesized answers from multiple agents (RAG, Web Search, GPT, Deep Search) to ensure they are accurate, complete, and grounded in the provided sources.

VALIDATION CRITERIA:

1. SOURCE ADHERENCE (Critical):
   - Answer must be grounded in the provided references
   - No information should be added that isn't supported by sources
   - Web sources, internal documents, and agent results are all valid sources
   - Rate adherence from 1-5 (5 = perfect adherence)

2. HALLUCINATION DETECTION (Critical):
   - Identify any claims not supported by the sources
   - Look for fabricated facts, dates, numbers, or quotes
   - Check for inconsistencies between agent results
   - Flag any unsupported generalizations

3. COMPLETENESS ASSESSMENT:
   - Does the answer fully address the original query?
   - Are there important aspects missing?
   - Could the answer be more comprehensive given available sources?

4. CROSS-AGENT CONSISTENCY:
   - Do results from different agents contradict each other?
   - Are there conflicts that need resolution?
   - Is the synthesis coherent across all sources?

5. FACTUAL ACCURACY:
   - Are facts, dates, and numbers accurate?
   - Are quotes and references properly attributed?
   - Is the information current and relevant?

RESPONSE FORMAT (JSON):
{{
    "validation_result": {{
        "is_valid": true/false,
        "confidence_score": 0.85,
        "source_adherence_score": 4.2,
        "hallucinations_detected": [
            "Specific claim not supported by sources",
            "Another unsupported statement"
        ],
        "missing_information": [
            "Important aspect not covered",
            "Key detail that could be added"
        ],
        "improvement_suggestions": [
            "Specific suggestion for improvement",
            "Another actionable recommendation"
        ],
        "cross_agent_conflicts": [
            "Description of any conflicts between agent results"
        ],
        "validation_notes": "Additional notes about the validation"
    }}
}}

VALIDATION STANDARDS:
- Be strict about source adherence
- Flag even minor unsupported claims
- Consider the query context and user intent
- Validate against ALL available sources
- Check for internal consistency

Today's date: {self.today_date}

Provide thorough, accurate validation focused on ensuring the answer is trustworthy and complete."""
    
    def _parse_validation_response(self, response: str) -> ValidationResult:
        """
        Parse validation response from LLM.
        
        Args:
            response: Raw response from LLM
            
        Returns:
            Parsed validation result
        """
        try:
            # Extract JSON from response
            start_idx = response.find('{')
            end_idx = response.rfind('}') + 1
            
            if start_idx != -1 and end_idx != -1:
                json_str = response[start_idx:end_idx]
                validation_data = json.loads(json_str)
                
                result_data = validation_data.get('validation_result', {})
                
                return ValidationResult(
                    is_valid=result_data.get('is_valid', False),
                    confidence_score=result_data.get('confidence_score', 0.0),
                    hallucinations_detected=result_data.get('hallucinations_detected', []),
                    missing_information=result_data.get('missing_information', []),
                    source_adherence_score=result_data.get('source_adherence_score', 0.0),
                    improvement_suggestions=result_data.get('improvement_suggestions', [])
                )
            else:
                raise ValueError("No JSON found in validation response")
                
        except Exception as e:
            if self.verbose:
                print(f"⚠️ VALIDATOR: Error parsing validation response: {e}")
                print(f"   Response: {response[:200]}...")
            
            # Return conservative validation result
            return ValidationResult(
                is_valid=False,
                confidence_score=0.3,
                hallucinations_detected=["Unable to parse validation response"],
                missing_information=["Validation parsing failed"],
                source_adherence_score=1.0,
                improvement_suggestions=["Please retry validation"]
            )
    
    def should_pass_validation(self, validation_result: ValidationResult) -> bool:
        """
        Determine if the validation result should pass.
        
        Args:
            validation_result: Validation result to evaluate
            
        Returns:
            True if validation passes
        """
        # Check minimum confidence threshold
        if validation_result.confidence_score < self.config.min_confidence_threshold:
            return False
        
        # Check for critical hallucinations
        if self.config.enable_hallucination_detection and validation_result.hallucinations_detected:
            return False
        
        # Check source adherence
        if self.config.enable_source_adherence_check and validation_result.source_adherence_score < 3.0:
            return False
        
        # Must be marked as valid
        return validation_result.is_valid
