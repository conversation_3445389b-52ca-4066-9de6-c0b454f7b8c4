"""
QA Feedback Loop System

Provides intelligent feedback to the universal planner for identifying
missing information and planning next steps for iterative improvement.
"""

import os
import sys
import json
from typing import Dict, List, Any, Optional
import datetime

from langchain_core.messages import SystemMessage, HumanMessage
from langchain_openai import AzureChatOpenAI

# Import configuration
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
from config.settings import AZURE_OPENAI_KEY, AZURE_OPENAI_API_VERSION, AZURE_OPENAI_ENDPOINT, AZURE_OPENAI_DEPLOYMENT_NAME

from .state import (
    UniversalNewState, ValidationResult, QAFeedback, AgentType,
    UniversalNewConfig
)


class QAFeedbackSystem:
    """
    QA Feedback System that analyzes validation results and provides
    intelligent feedback to the universal planner for iterative improvement.
    
    Features:
    - Gap analysis and missing information identification
    - Agent recommendation for additional processing
    - Improvement instruction generation
    - Confidence assessment and iteration planning
    """
    
    def __init__(self, model=None, config: UniversalNewConfig = None, verbose: bool = True):
        """
        Initialize the QA Feedback System.
        
        Args:
            model: LLM model for feedback generation
            config: Configuration
            verbose: Whether to enable verbose logging
        """
        self.verbose = verbose
        self.config = config or UniversalNewConfig()
        self.today_date = datetime.datetime.now().strftime("%Y-%m-%d")
        
        # Initialize the model
        if model:
            self.model = model
        else:
            self.model = AzureChatOpenAI(
                azure_deployment=AZURE_OPENAI_DEPLOYMENT_NAME,
                api_version=AZURE_OPENAI_API_VERSION,
                azure_endpoint=AZURE_OPENAI_ENDPOINT,
                api_key=AZURE_OPENAI_KEY,
                temperature=0.2  # Low temperature for consistent feedback
            )
            if self.verbose:
                print(f"🔄 QA FEEDBACK: Using Azure OpenAI with deployment {AZURE_OPENAI_DEPLOYMENT_NAME}")
    
    def generate_feedback(
        self,
        state: UniversalNewState,
        validation_result: ValidationResult,
        current_answer: str
    ) -> QAFeedback:
        """
        Generate feedback based on validation results and current state.
        
        Args:
            state: Current state
            validation_result: Results from validation
            current_answer: Current synthesized answer
            
        Returns:
            QA feedback for the planner
        """
        if self.verbose:
            print(f"\n🔄 QA FEEDBACK: Generating feedback")
            print(f"   Validation passed: {validation_result.is_valid}")
            print(f"   Confidence: {validation_result.confidence_score:.2f}")
            print(f"   Iteration: {state['iteration_count']}")
        
        try:
            # Generate comprehensive feedback
            feedback = self._generate_comprehensive_feedback(
                state, validation_result, current_answer
            )
            
            if self.verbose:
                print(f"✅ QA FEEDBACK: Generated feedback")
                print(f"   Needs additional agents: {feedback.needs_additional_agents}")
                print(f"   Suggested agents: {[a.value for a in feedback.suggested_agents]}")
                print(f"   Missing info items: {len(feedback.missing_information)}")
            
            return feedback
            
        except Exception as e:
            if self.verbose:
                print(f"❌ QA FEEDBACK: Error generating feedback: {e}")
            
            # Return minimal feedback
            return QAFeedback(
                needs_additional_agents=False,
                suggested_agents=[],
                missing_information=[],
                improvement_instructions="Unable to generate feedback due to error",
                confidence_in_current_answer=validation_result.confidence_score
            )
    
    def _generate_comprehensive_feedback(
        self,
        state: UniversalNewState,
        validation_result: ValidationResult,
        current_answer: str
    ) -> QAFeedback:
        """
        Generate comprehensive feedback using LLM analysis.
        
        Args:
            state: Current state
            validation_result: Validation results
            current_answer: Current answer
            
        Returns:
            QA feedback
        """
        # Get feedback response from LLM
        feedback_response = self._get_feedback_response(
            state, validation_result, current_answer
        )
        
        # Parse feedback response
        return self._parse_feedback_response(feedback_response, validation_result)
    
    def _get_feedback_response(
        self,
        state: UniversalNewState,
        validation_result: ValidationResult,
        current_answer: str
    ) -> str:
        """
        Get feedback response from LLM.
        
        Args:
            state: Current state
            validation_result: Validation results
            current_answer: Current answer
            
        Returns:
            Feedback response
        """
        system_prompt = self._get_feedback_system_prompt()
        
        # Prepare execution summary
        executed_agents = list(state['agent_results'].keys())
        execution_summary = f"Executed agents: {[a.value for a in executed_agents]}"
        
        # Prepare validation summary
        validation_summary = f"""
        Validation Results:
        - Valid: {validation_result.is_valid}
        - Confidence: {validation_result.confidence_score:.2f}
        - Source adherence: {validation_result.source_adherence_score:.1f}/5.0
        - Hallucinations: {len(validation_result.hallucinations_detected)}
        - Missing information: {len(validation_result.missing_information)}
        """
        
        # Prepare missing information details
        missing_info_text = ""
        if validation_result.missing_information:
            missing_info_text = "Missing Information Identified:\n"
            for i, info in enumerate(validation_result.missing_information):
                missing_info_text += f"{i+1}. {info}\n"
        
        # Prepare hallucination details
        hallucination_text = ""
        if validation_result.hallucinations_detected:
            hallucination_text = "Hallucinations Detected:\n"
            for i, hall in enumerate(validation_result.hallucinations_detected):
                hallucination_text += f"{i+1}. {hall}\n"
        
        user_prompt = f"""
        ORIGINAL QUERY: {state['query']}
        
        CURRENT EXECUTION STATE:
        - {execution_summary}
        - Strategy: {state['execution_strategy'].value}
        - Iteration: {state['iteration_count']}/{self.config.max_iterations}
        
        {validation_summary}
        
        CURRENT ANSWER:
        {current_answer[:1000]}...
        
        {missing_info_text}
        
        {hallucination_text}
        
        IMPROVEMENT SUGGESTIONS:
        {chr(10).join(validation_result.improvement_suggestions)}
        
        Please analyze this situation and provide feedback according to your instructions.
        """
        
        messages = [
            SystemMessage(content=system_prompt),
            HumanMessage(content=user_prompt)
        ]
        
        response = self.model.invoke(messages)
        return response.content
    
    def _get_feedback_system_prompt(self) -> str:
        """Get system prompt for feedback generation."""
        return f"""You are a QA Feedback Analyst for a sophisticated multi-agent system.

Your role is to analyze validation results and provide intelligent feedback to improve the system's response through additional agent execution or refinement.

AVAILABLE AGENTS:
- RAG Agent: Internal documents and knowledge base
- Web Search Agent: Current internet information  
- GPT Agent: General knowledge and analysis
- Deep Search Agent: Detailed file analysis (requires selected files)

FEEDBACK ANALYSIS FRAMEWORK:

1. GAP ANALYSIS:
   - What information is missing from the current answer?
   - Which aspects of the query are not fully addressed?
   - Are there contradictions that need resolution?

2. AGENT RECOMMENDATION:
   - Which agents could provide the missing information?
   - Should agents be run in sequence or parallel?
   - Are there specific parameters needed for agents?

3. ITERATION STRATEGY:
   - Is another iteration likely to improve the answer significantly?
   - What are the diminishing returns considerations?
   - Should we stop iterating and accept current answer?

4. IMPROVEMENT PRIORITIZATION:
   - Which improvements would have the highest impact?
   - What are the most critical gaps to address?
   - How can we best use remaining iteration budget?

DECISION CRITERIA:
- Recommend additional agents only if they can meaningfully improve the answer
- Consider the iteration count and avoid infinite loops
- Prioritize fixing hallucinations over adding more information
- Balance comprehensiveness with efficiency

RESPONSE FORMAT (JSON):
{{
    "feedback_analysis": {{
        "needs_additional_agents": true/false,
        "suggested_agents": ["rag", "web_search", "gpt", "deep_search"],
        "execution_strategy": "sequential|parallel",
        "agent_parameters": {{
            "rag": {{"deep_search": false}},
            "web_search": {{"focus": "recent information"}}
        }},
        "missing_information": [
            "Specific information gap 1",
            "Specific information gap 2"
        ],
        "improvement_instructions": "Detailed instructions for the planner",
        "confidence_in_current_answer": 0.75,
        "should_continue_iteration": true/false,
        "priority_improvements": [
            "High priority improvement 1",
            "High priority improvement 2"
        ],
        "reasoning": "Detailed reasoning for recommendations"
    }}
}}

GUIDELINES:
- Be specific about what information is missing
- Recommend agents that can actually provide the missing information
- Consider the cost/benefit of additional iterations
- Provide actionable improvement instructions
- Be realistic about what can be achieved

Today's date: {self.today_date}

Analyze the situation and provide strategic feedback for improvement."""
    
    def _parse_feedback_response(
        self,
        response: str,
        validation_result: ValidationResult
    ) -> QAFeedback:
        """
        Parse feedback response from LLM.
        
        Args:
            response: Raw response from LLM
            validation_result: Original validation result
            
        Returns:
            Parsed QA feedback
        """
        try:
            # Extract JSON from response
            start_idx = response.find('{')
            end_idx = response.rfind('}') + 1
            
            if start_idx != -1 and end_idx != -1:
                json_str = response[start_idx:end_idx]
                feedback_data = json.loads(json_str)
                
                analysis = feedback_data.get('feedback_analysis', {})
                
                # Parse suggested agents
                suggested_agents = []
                for agent_str in analysis.get('suggested_agents', []):
                    try:
                        suggested_agents.append(AgentType(agent_str))
                    except ValueError:
                        if self.verbose:
                            print(f"⚠️ QA FEEDBACK: Unknown agent type: {agent_str}")
                
                return QAFeedback(
                    needs_additional_agents=analysis.get('needs_additional_agents', False),
                    suggested_agents=suggested_agents,
                    missing_information=analysis.get('missing_information', []),
                    improvement_instructions=analysis.get('improvement_instructions', ''),
                    confidence_in_current_answer=analysis.get('confidence_in_current_answer', validation_result.confidence_score)
                )
            else:
                raise ValueError("No JSON found in feedback response")
                
        except Exception as e:
            if self.verbose:
                print(f"⚠️ QA FEEDBACK: Error parsing feedback response: {e}")
                print(f"   Response: {response[:200]}...")
            
            # Return fallback feedback based on validation result
            return self._create_fallback_feedback(validation_result)
    
    def _create_fallback_feedback(self, validation_result: ValidationResult) -> QAFeedback:
        """
        Create fallback feedback when parsing fails.
        
        Args:
            validation_result: Validation result
            
        Returns:
            Fallback QA feedback
        """
        # Simple heuristic-based feedback
        needs_additional = not validation_result.is_valid or validation_result.confidence_score < 0.7
        
        suggested_agents = []
        if validation_result.missing_information:
            # Suggest RAG for internal info, Web for external info
            suggested_agents = [AgentType.RAG, AgentType.WEB_SEARCH]
        
        return QAFeedback(
            needs_additional_agents=needs_additional,
            suggested_agents=suggested_agents,
            missing_information=validation_result.missing_information,
            improvement_instructions="Fallback feedback: Consider additional information sources",
            confidence_in_current_answer=validation_result.confidence_score
        )
    
    def should_continue_iteration(
        self,
        state: UniversalNewState,
        feedback: QAFeedback
    ) -> bool:
        """
        Determine if iteration should continue based on feedback and state.
        
        Args:
            state: Current state
            feedback: QA feedback
            
        Returns:
            True if iteration should continue
        """
        # Check iteration limit
        if state['iteration_count'] >= self.config.max_iterations:
            if self.verbose:
                print("🔄 QA FEEDBACK: Max iterations reached, stopping")
            return False
        
        # Check if additional agents are needed and beneficial
        if not feedback.needs_additional_agents:
            if self.verbose:
                print("🔄 QA FEEDBACK: No additional agents needed, stopping")
            return False
        
        # Check confidence threshold
        if feedback.confidence_in_current_answer >= 0.9:
            if self.verbose:
                print("🔄 QA FEEDBACK: High confidence achieved, stopping")
            return False
        
        # Check if we have meaningful improvements to make
        if not feedback.missing_information and not feedback.suggested_agents:
            if self.verbose:
                print("🔄 QA FEEDBACK: No clear improvements identified, stopping")
            return False
        
        if self.verbose:
            print("🔄 QA FEEDBACK: Continuing iteration for improvement")
        
        return True
