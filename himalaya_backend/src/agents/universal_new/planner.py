"""
Universal Planner Agent

The brain of the Universal New Agent system. This planner analyzes queries and files
to create sophisticated execution plans that determine which agents to use, in what
sequence, and how to orchestrate the entire workflow.
"""

import os
import sys
import json
from typing import Dict, List, Any, Optional
import datetime

from langchain_core.messages import SystemMessage, HumanMessage
from langchain_openai import AzureChatOpenAI

# Import configuration
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
from config.settings import AZURE_OPENAI_KEY, AZURE_OPENAI_API_VERSION, AZURE_OPENAI_ENDPOINT, AZURE_OPENAI_DEPLOYMENT_NAME

from .state import (
    UniversalNewState, AgentExecutionPlan, AgentType, ExecutionStrategy,
    UniversalNewConfig, AgentTask, ExecutionStep, WorkflowStepType
)


class EnhancedUniversalPlanner:
    """
    Enhanced Universal Planner that creates multi-step workflows with targeted queries.

    This planner implements advanced orchestration similar to DeepSeek, O3, and Genspark:
    1. Analyzes the query and creates a multi-step workflow plan
    2. Generates targeted, specific queries for each agent based on their capabilities
    3. Plans dynamic step-by-step execution with context accumulation
    4. Supports re-planning after each step based on accumulated information
    5. Implements proper departmental workflow where each agent gets specific tasks
    """

    def __init__(self, model, config: UniversalNewConfig, verbose: bool = True):
        """
        Initialize the Enhanced Universal Planner.

        Args:
            model: Language model for planning
            config: Configuration for the planner
            verbose: Whether to print verbose output
        """
        self.model = model
        self.config = config
        self.verbose = verbose
        self.today_date = datetime.datetime.now().strftime("%Y-%m-%d")

    async def create_multi_step_workflow(self, state: UniversalNewState) -> UniversalNewState:
        """
        Create a multi-step workflow with targeted queries for each agent.

        Args:
            state: Current state

        Returns:
            Updated state with workflow steps
        """
        if self.verbose:
            print(f"\n🧠 ENHANCED PLANNER: Creating multi-step workflow")
            print(f"   Query: {state['query'][:100]}...")
            print(f"   Files: {len(state['selected_file_ids'])} selected")
            print(f"   Has history: {bool(state['conversation_history'])}")

        try:
            # Analyze the query and create initial workflow plan
            workflow_analysis = self._analyze_query_for_workflow(state)

            # Create the first step based on analysis
            first_step = self._create_initial_step(workflow_analysis, state)

            # Update state with the workflow
            state["workflow_steps"] = [first_step]
            state["current_step_index"] = 0
            state["planner_reasoning"] = workflow_analysis["reasoning"]
            state["estimated_execution_time"] = workflow_analysis["estimated_time"]

            if self.verbose:
                print(f"✅ ENHANCED PLANNER: Created workflow with {len(state['workflow_steps'])} initial steps")
                print(f"   First step: {first_step.step_name}")
                print(f"   Agent tasks: {len(first_step.agent_tasks)}")

            return state

        except Exception as e:
            if self.verbose:
                print(f"❌ ENHANCED PLANNER: Error creating workflow: {e}")

            # Fallback to legacy planning
            return await self._fallback_to_legacy_planning(state)

    async def plan_next_step(self, state: UniversalNewState) -> UniversalNewState:
        """
        Plan the next step based on accumulated context and results.

        Args:
            state: Current state with completed steps

        Returns:
            Updated state with next step planned
        """
        if self.verbose:
            print(f"\n🧠 ENHANCED PLANNER: Planning next step")
            print(f"   Completed steps: {len(state['step_results'])}")
            print(f"   Current step index: {state['current_step_index']}")

        try:
            # Analyze what has been accomplished so far
            progress_analysis = self._analyze_progress(state)

            # Determine if more steps are needed
            if progress_analysis.get("is_complete", False):
                if self.verbose:
                    print("✅ ENHANCED PLANNER: Workflow is complete, no more steps needed")
                return state

            # Create the next step
            next_step = self._create_next_step(progress_analysis, state)

            # If no next step was created, workflow is complete
            if next_step is None:
                if self.verbose:
                    print(f"✅ ENHANCED PLANNER: Workflow is complete, no more steps needed")
                return state

            # Add the step to the workflow
            state["workflow_steps"].append(next_step)

            if self.verbose:
                print(f"✅ ENHANCED PLANNER: Created next step: {next_step.step_name}")
                print(f"   Agent tasks: {len(next_step.agent_tasks)}")

            return state

        except Exception as e:
            if self.verbose:
                print(f"❌ ENHANCED PLANNER: Error planning next step: {e}")
                import traceback
                traceback.print_exc()

            # Mark workflow as complete if planning fails
            return state

    def _analyze_query_for_workflow(self, state: UniversalNewState) -> Dict[str, Any]:
        """
        Analyze the query to understand what kind of workflow is needed.

        Args:
            state: Current state

        Returns:
            Analysis results for workflow planning
        """
        system_prompt = """You are an expert workflow analyst for a multi-agent system. Your job is to analyze user queries and determine what kind of multi-step workflow is needed.

You have access to these agents:
1. RAG Agent: Searches internal documents and knowledge base
2. Web Search Agent: Searches internet for current information
3. GPT Agent: Uses LLM knowledge for general questions and content generation
4. Deep Search Agent: Comprehensive analysis of selected files

Your task is to analyze the query and determine:
1. What type of workflow is needed (information gathering, analysis, content creation, etc.)
2. What the first step should be
3. Estimate how many total steps might be needed
4. Identify the key information that needs to be gathered

IMPORTANT: Think like a department manager - break down complex tasks into logical steps where each step builds on the previous ones."""

        # Include conversation history if available
        conversation_context = ""
        if state['conversation_history']:
            conversation_context = f"""

        CONVERSATION HISTORY:
        {state['conversation_history'][-3000:]}  # Last 2000 chars to avoid token limits

        IMPORTANT: Consider the conversation history when planning. If this is a follow-up question or modification request (like "make it more concise", "improve this", "change the tone"), you should build upon the previous conversation rather than starting from scratch."""

        user_prompt = f"""
        Query: {state['query']}
        Selected Files: {len(state['selected_file_ids'])} files
        Has Conversation History: {bool(state['conversation_history'])}{conversation_context}

        Please analyze this query and provide a workflow analysis in JSON format:
        {{
            "workflow_type": "information_gathering|analysis|content_creation|comparison|validation",
            "complexity": "simple|moderate|complex",
            "estimated_steps": 2,
            "first_step_focus": "What should the first step accomplish",
            "key_information_needed": ["list", "of", "key", "info"],
            "reasoning": "Detailed explanation of the workflow approach",
            "estimated_time": 60.0
        }}
        """

        messages = [
            SystemMessage(content=system_prompt),
            HumanMessage(content=user_prompt)
        ]

        response = self.model.invoke(messages)

        try:
            # Extract JSON from response
            start_idx = response.content.find('{')
            end_idx = response.content.rfind('}') + 1

            if start_idx != -1 and end_idx != -1:
                json_str = response.content[start_idx:end_idx]
                analysis = json.loads(json_str)
                return analysis
            else:
                raise ValueError("No JSON found in response")

        except Exception as e:
            if self.verbose:
                print(f"⚠️ ENHANCED PLANNER: Error parsing workflow analysis: {e}")

            # Return fallback analysis
            return {
                "workflow_type": "information_gathering",
                "complexity": "moderate",
                "estimated_steps": 2,
                "first_step_focus": "Gather relevant information",
                "key_information_needed": ["relevant context"],
                "reasoning": "Fallback analysis due to parsing error",
                "estimated_time": 60.0
            }

    def _create_initial_step(self, workflow_analysis: Dict[str, Any], state: UniversalNewState) -> ExecutionStep:
        """
        Create the first step of the workflow with targeted agent tasks.

        Args:
            workflow_analysis: Analysis of what workflow is needed
            state: Current state

        Returns:
            First execution step
        """
        if self.verbose:
            print(f"🔧 ENHANCED PLANNER: Creating initial step")
            print(f"   Workflow type: {workflow_analysis.get('workflow_type', 'unknown')}")
            print(f"   First step focus: {workflow_analysis.get('first_step_focus', 'unknown')}")

        # Determine which agents are needed for the first step
        agent_tasks = self._plan_first_step_agents(workflow_analysis, state)

        if self.verbose:
            print(f"   Agent tasks created: {len(agent_tasks)}")

        # If no agent tasks were created, use fallback
        if not agent_tasks:
            if self.verbose:
                print(f"⚠️ ENHANCED PLANNER: No agent tasks created, using fallback")
            agent_tasks = self._create_fallback_first_step_tasks(state)

        # Let the LLM decide on the best agents and execution strategy
        # No forced limitations - trust the AI planning
        execution_strategy = ExecutionStrategy.SEQUENTIAL  # Default to sequential for context passing

        return ExecutionStep(
            step_id="step_1",
            step_name=f"Initial {workflow_analysis.get('workflow_type', 'Processing').replace('_', ' ').title()}",
            step_description=workflow_analysis.get('first_step_focus', 'Process the user query'),
            agent_tasks=agent_tasks,
            execution_strategy=execution_strategy,
            depends_on_steps=[],
            validation_required=True,
            completed=False
        )

    def _plan_first_step_agents(self, workflow_analysis: Dict[str, Any], state: UniversalNewState) -> List[AgentTask]:
        """
        Plan which agents to use in the first step with targeted queries.

        Args:
            workflow_analysis: Analysis of workflow needs
            state: Current state

        Returns:
            List of agent tasks with targeted queries
        """
        system_prompt = """You are an expert task coordinator for a multi-agent system. Your job is to create specific, targeted tasks for different agents based on the workflow needs.

Available Agents and Their Strengths:
1. RAG Agent:
   - Searches internal documents and knowledge base
   - Best for: Finding specific information in company documents, policies, existing data
   - Give it queries like: "Find information about X in the documents", "What does the policy say about Y"

2. Web Search Agent:
   - Searches internet for current information
   - Best for: Latest trends, current events, public information, best practices
   - Give it queries like: "What are the latest trends in X", "Current best practices for Y", "Recent developments in Z"

3. GPT Agent:
   - Uses LLM knowledge for general questions and content generation
   - Best for: Analysis, synthesis, content creation, explanations, FOLLOW-UP REQUESTS
   - Give it queries like: "Analyze this information and provide insights", "Create content based on this data"
   - PERFECT for: "make it more concise", "improve this", "rewrite with different tone", "give me the final version"

4. Deep Search Agent:
   - Comprehensive analysis of selected files
   - Best for: Detailed file analysis, extracting specific information from documents (FIRST TIME ONLY)
   - Give it queries like: "Analyze the resume and extract key skills", "Summarize the main points in these documents"
   - AVOID for follow-up questions when files were already analyzed

CONVERSATION CONTEXT RULES:
- If there's conversation history and the query is a follow-up/modification request → Use GPT Agent ONLY
- If it's a new query with files → Consider Deep Search Agent
- If it's asking for updates/changes to previous results → Use GPT Agent with conversation context
- Examples of follow-up queries: "make it shorter", "add more details", "change the format", "give me the final version"

CRITICAL: Create SPECIFIC, TARGETED queries for each agent. Don't give the same generic query to all agents."""

        # Include conversation history if available
        conversation_context = ""
        if state['conversation_history']:
            conversation_context = f"""

        CONVERSATION HISTORY:
        {state['conversation_history']}  # Use the complete conversation history

        CRITICAL DECISION RULES:
        1. If this query is asking for modifications to previous results (like "make it more concise", "improve this", "change the tone", "add more details", "give me the final version") → Use ONLY GPT agent
        2. If this query references "these updates", "the changes", "final version" → Use ONLY GPT agent
        3. If this is clearly a follow-up building on previous conversation → Use ONLY GPT agent
        4. DO NOT use deep_search again if files were already analyzed in the conversation history
        5. Only use deep_search for completely new file analysis requests"""

        user_prompt = f"""
        Original Query: {state['query']}
        Workflow Type: {workflow_analysis['workflow_type']}
        First Step Focus: {workflow_analysis['first_step_focus']}
        Selected Files: {len(state['selected_file_ids'])} files{conversation_context}

        Create targeted tasks for the first step. Each agent should get a SPECIFIC query tailored to their capabilities.

        Respond in JSON format:
        {{
            "agent_tasks": [
                {{
                    "agent_type": "rag|web_search|gpt|deep_search",
                    "targeted_query": "Specific query for this agent",
                    "reasoning": "Why this agent with this specific query",
                    "expected_output": "What this agent should provide",
                    "priority": 1
                }}
            ]
        }}
        """

        messages = [
            SystemMessage(content=system_prompt),
            HumanMessage(content=user_prompt)
        ]

        response = self.model.invoke(messages)

        try:
            # Extract JSON from response
            start_idx = response.content.find('{')
            end_idx = response.content.rfind('}') + 1

            if self.verbose:
                print(f"🔍 ENHANCED PLANNER: Parsing agent task response")
                print(f"   Response content: {response.content[:300]}...")
                print(f"   JSON indices: start={start_idx}, end={end_idx}")

            if start_idx != -1 and end_idx != -1:
                json_str = response.content[start_idx:end_idx]
                task_plan = json.loads(json_str)

                if self.verbose:
                    print(f"   Parsed task plan: {task_plan}")

                # Convert to AgentTask objects
                agent_tasks = []
                for task_data in task_plan.get("agent_tasks", []):
                    agent_type = AgentType(task_data["agent_type"])

                    # Skip deep search if no files selected
                    if agent_type == AgentType.DEEP_SEARCH and not state['selected_file_ids']:
                        if self.verbose:
                            print(f"   Skipping deep_search - no files selected")
                        continue

                    agent_task = AgentTask(
                        agent_type=agent_type,
                        targeted_query=task_data["targeted_query"],
                        reasoning=task_data["reasoning"],
                        expected_output=task_data["expected_output"],
                        priority=task_data.get("priority", 1)
                    )
                    agent_tasks.append(agent_task)

                    if self.verbose:
                        print(f"   Created task: {agent_type.value} - {task_data['targeted_query'][:50]}...")

                return agent_tasks

        except Exception as e:
            if self.verbose:
                print(f"⚠️ ENHANCED PLANNER: Error parsing agent tasks: {e}")

        # Fallback to basic task creation
        if self.verbose:
            print(f"⚠️ ENHANCED PLANNER: Using fallback task creation")
        return self._create_fallback_first_step_tasks(state)

    def _create_fallback_first_step_tasks(self, state: UniversalNewState) -> List[AgentTask]:
        """
        Create fallback tasks for the first step if planning fails.

        Args:
            state: Current state

        Returns:
            List of basic agent tasks
        """
        tasks = []

        if self.verbose:
            print(f"🔧 ENHANCED PLANNER: Creating fallback tasks")
            print(f"   Query: {state['query']}")
            print(f"   Selected files: {len(state['selected_file_ids'])}")

        # Intelligent fallback based on conversation context
        if state['conversation_history']:
            # If there's conversation history, likely a follow-up - use GPT
            gpt_task = AgentTask(
                agent_type=AgentType.GPT,
                targeted_query=f"Based on our previous conversation, provide a comprehensive response to: {state['query']}",
                reasoning="GPT agent for follow-up queries with conversation context",
                expected_output="Response building on previous conversation"
            )
            tasks.append(gpt_task)
            if self.verbose:
                print(f"   Added gpt task for follow-up: {gpt_task.targeted_query[:50]}...")
        elif state['selected_file_ids']:
            # New conversation with files - use deep search
            task = AgentTask(
                agent_type=AgentType.DEEP_SEARCH,
                targeted_query=f"Analyze the selected files to understand their content and extract relevant information for: {state['query']}",
                reasoning="Deep search to understand file content for new query",
                expected_output="Summary and key information from files relevant to the query"
            )
            tasks.append(task)
            if self.verbose:
                print(f"   Added deep_search task: {task.targeted_query[:50]}...")
        else:
            # No files, no history - use GPT for general response
            gpt_task = AgentTask(
                agent_type=AgentType.GPT,
                targeted_query=f"Provide a comprehensive response to: {state['query']}",
                reasoning="GPT agent for general response when no files available",
                expected_output="Comprehensive response to the query"
            )
            tasks.append(gpt_task)
            if self.verbose:
                print(f"   Added gpt task: {gpt_task.targeted_query[:50]}...")

        if self.verbose:
            print(f"   Total fallback tasks created: {len(tasks)}")

        return tasks

    def _analyze_progress(self, state: UniversalNewState) -> Dict[str, Any]:
        """
        Analyze the progress made so far to determine next steps.

        Args:
            state: Current state with completed steps

        Returns:
            Progress analysis
        """
        # Track what information has already been collected or analyzed
        collected_information = set()
        analyzed_files = set()
        generated_content_types = set()
        previous_analyses = []
        completed_steps_info = {}
        
        # Get the results from completed steps in order of execution
        completed_results = []
        step_ids = sorted(state['step_results'].keys(), 
                         key=lambda x: int(x.split('_')[-1]) if x.split('_')[-1].isdigit() else 0)
        
        for step_id in step_ids:
            step_result = state['step_results'][step_id]
            if step_result.success:
                # Track what's been completed
                completed_steps_info[step_id] = {
                    "answer": step_result.synthesized_answer[:200] + "..." if len(step_result.synthesized_answer) > 200 else step_result.synthesized_answer,
                    "agent_types": [agent_type.value for agent_type in step_result.agent_results.keys()]
                }
                
                # Extract information based on agent types used
                for agent_type, result in step_result.agent_results.items():
                    if agent_type == AgentType.DEEP_SEARCH and result.success:
                        analyzed_files.add("file_analysis")
                    if agent_type == AgentType.WEB_SEARCH and result.success:
                        collected_information.add("web_information")
                    if agent_type == AgentType.GPT and result.success:
                        generated_content_types.add("content_generation")
                        
                # Add to the list of previous analyses
                previous_analyses.append({
                    "step_id": step_id,
                    "answer_summary": step_result.synthesized_answer[:300]
                })
                
                # Track this for the detailed progress analysis
                completed_results.append({
                    "step_id": step_id,
                    "answer": step_result.synthesized_answer,
                    "agent_types": [agent_type.value for agent_type in step_result.agent_results.keys()],
                    "context": {k: v for k, v in step_result.accumulated_context.items() if k.endswith('_result')}
                })
                
        if self.verbose:
            print(f"🔍 PROGRESS TRACKING:")
            print(f"   Completed steps: {list(completed_steps_info.keys())}")
            print(f"   Analyzed files: {bool(analyzed_files)}")
            print(f"   Generated content: {bool(generated_content_types)}")
            print(f"   Collected information: {list(collected_information)}")

        system_prompt = """You are an expert workflow coordinator. Analyze the progress made so far and determine if the workflow is complete or if more steps are needed.

Consider:
1. Has the original query been fully answered?
2. Is there missing information that needs to be gathered?
3. Does the answer need refinement or additional analysis?
4. Would additional agents provide value?
5. If there's conversation history, is this a follow-up request that can be completed with the current results?

IMPORTANT: Do not recommend gathering information that has already been collected. Focus on what's ACTUALLY missing."""

        # Include conversation history if available
        conversation_context = ""
        if state['conversation_history']:
            conversation_context = f"""

        CONVERSATION HISTORY:
        {state['conversation_history']}  # Use complete conversation history

        IMPORTANT: If this is a follow-up question or modification request based on previous conversation, consider whether the current results are sufficient to answer the query without additional file analysis."""

        user_prompt = f"""
        Original Query: {state['query']}

        Already Completed:
        - File Analysis: {bool(analyzed_files)}
        - Web Search: {"web_information" in collected_information}
        - Content Generation: {bool(generated_content_types)}
        
        Previous Step Results:
        {json.dumps(completed_steps_info, indent=2)}
        
        {conversation_context}

        Progress So Far:
        {json.dumps(completed_results, indent=2)}

        Accumulated Context Summary:
        {json.dumps({k: v[:100] + "..." if isinstance(v, str) and len(v) > 100 else v 
                   for k, v in state['accumulated_context'].items() if k.endswith('_result') or k.endswith('_answer')}, indent=2)}

        Analyze the progress and determine next steps in JSON format:
        {{
            "is_complete": true/false,
            "completion_confidence": 0.8,
            "missing_information": ["list", "of", "missing", "info"],
            "suggested_next_step": "What should be done next",
            "already_addressed": ["list", "of", "topics", "already", "covered"],
            "reasoning": "Detailed explanation that considers what's already been done"
        }}
        
        Remember: Don't suggest repeating work that has already been done. If a file has already been analyzed, don't recommend analyzing it again.
        """

        messages = [
            SystemMessage(content=system_prompt),
            HumanMessage(content=user_prompt)
        ]

        response = self.model.invoke(messages)

        try:
            # Extract JSON from response
            start_idx = response.content.find('{')
            end_idx = response.content.rfind('}') + 1

            if start_idx != -1 and end_idx != -1:
                json_str = response.content[start_idx:end_idx]
                analysis = json.loads(json_str)
                return analysis

        except Exception as e:
            if self.verbose:
                print(f"⚠️ ENHANCED PLANNER: Error parsing progress analysis: {e}")

        # Fallback analysis - check if we have both deep search and GPT
        # Check if we have any successful results
        has_successful_results = any(
            step_result.success and step_result.synthesized_answer
            for step_result in state['step_results'].values()
        )

        # For follow-up queries with conversation history, one good result might be enough
        if state['conversation_history'] and has_successful_results:
            is_complete = True
        else:
            # For new queries, check if we have meaningful content
            is_complete = has_successful_results

        return {
            "is_complete": is_complete,
            "completion_confidence": 0.9 if is_complete else 0.5,
            "missing_information": [] if is_complete else ["meaningful response"],
            "suggested_next_step": "Finalize answer" if is_complete else "Generate meaningful response",
            "reasoning": "Intelligent completion analysis based on results quality and conversation context"
        }

    def _create_next_step(self, progress_analysis: Dict[str, Any], state: UniversalNewState) -> ExecutionStep:
        """
        Create the next step based on progress analysis.

        Args:
            progress_analysis: Analysis of current progress
            state: Current state

        Returns:
            Next execution step
        """
        step_number = len(state['workflow_steps']) + 1
        suggested_next_step = progress_analysis.get('suggested_next_step', 'Continue processing')

        # Create targeted tasks for the next step
        agent_tasks = self._plan_next_step_agents(progress_analysis, state)

        # If no agent tasks, don't create a step (workflow is complete)
        if not agent_tasks:
            if self.verbose:
                print(f"✅ ENHANCED PLANNER: No agent tasks for next step, workflow complete")
            return None

        # Always use sequential execution to ensure proper context passing
        execution_strategy = ExecutionStrategy.SEQUENTIAL

        return ExecutionStep(
            step_id=f"step_{step_number}",
            step_name=f"Step {step_number}: {suggested_next_step}",
            step_description=suggested_next_step,
            agent_tasks=agent_tasks,
            execution_strategy=execution_strategy,
            depends_on_steps=[f"step_{i}" for i in range(1, step_number)],
            validation_required=True,
            completed=False
        )

    def _plan_next_step_agents(self, progress_analysis: Dict[str, Any], state: UniversalNewState) -> List[AgentTask]:
        """
        Plan agents for the next step based on what's missing.

        Args:
            progress_analysis: Analysis of what's needed next
            state: Current state

        Returns:
            List of agent tasks for next step
        """
        if self.verbose:
            print(f"🔧 ENHANCED PLANNER: Planning next step agents")
            print(f"   Progress analysis: {progress_analysis}")
            print(f"   Completed steps: {len(state['step_results'])}")

        # Intelligent next step planning based on progress analysis
        if progress_analysis.get("is_complete", False):
            if self.verbose:
                print(f"   Progress analysis indicates workflow is complete")
            return []

        # Track what has already been done to avoid redundancy
        already_addressed = progress_analysis.get("already_addressed", [])
        missing_information = progress_analysis.get("missing_information", [])
        suggested_next_step = progress_analysis.get("suggested_next_step", "")
        
        # Check what agents have been used in previous steps
        completed_agents = set()
        for step_result in state['step_results'].values():
            if step_result.success:
                completed_agents.update(step_result.agent_results.keys())
                
        if self.verbose:
            print(f"   Already addressed topics: {already_addressed}")
            print(f"   Missing information: {missing_information}")
            print(f"   Completed agents: {[agent.value for agent in completed_agents]}")

        # Build context for next agent that includes a summary of what's been done
        previous_steps_context = self._build_previous_steps_context(state)
        
        # Choose agent based on the specific missing information and suggested next step
        if "file" in " ".join(missing_information).lower() and AgentType.DEEP_SEARCH not in completed_agents and state['selected_file_ids']:
            # Need to analyze files and haven't done so yet
            return [AgentTask(
                agent_type=AgentType.DEEP_SEARCH,
                targeted_query=f"Based on previous analysis, analyze the selected files focusing specifically on: {', '.join(missing_information)}",
                reasoning="Deep search focusing on specific missing information from files",
                expected_output="Targeted file analysis for missing information",
                context=previous_steps_context
            )]
        
        if "web" in " ".join(missing_information).lower() and AgentType.WEB_SEARCH not in completed_agents:
            # Need web information and haven't gathered it yet
            return [AgentTask(
                agent_type=AgentType.WEB_SEARCH,
                targeted_query=f"Based on previous analysis, search the web specifically for: {', '.join(missing_information)}",
                reasoning="Web search focused on specific missing information",
                expected_output="Targeted web search results for missing information",
                context=previous_steps_context
            )]
            
        # For synthesis, refinement, or finalizing content based on what we already have
        if any(keyword in suggested_next_step.lower() for keyword in ["synthesize", "summarize", "conclude", "finalize", "combine"]):
            return [AgentTask(
                agent_type=AgentType.GPT,
                targeted_query=f"Based on all previous analyses, synthesize a comprehensive response to the original query: {state['query']}. Focus on creating a coherent answer that addresses all aspects.",
                reasoning="Final synthesis of all information gathered",
                expected_output="Comprehensive synthesized response",
                context=previous_steps_context,
                priority=1  # High priority for synthesis
            )]
            
        # For content generation or answering the query based on gathered information
        if any(keyword in suggested_next_step.lower() for keyword in ["generate", "create", "write", "produce"]):
            return [AgentTask(
                agent_type=AgentType.GPT,
                targeted_query=f"Based on the information gathered so far, generate a comprehensive response to: {state['query']}. Focus specifically on addressing: {', '.join(missing_information)}",
                reasoning="Content generation based on gathered information, addressing specific missing aspects",
                expected_output="Targeted content generation for missing information",
                context=previous_steps_context
            )]
            
        # Default: use GPT to address specific missing information
        return [AgentTask(
            agent_type=AgentType.GPT,
            targeted_query=f"Based on the analysis so far, address these specific missing points in relation to the query '{state['query']}': {', '.join(missing_information)}",
            reasoning="GPT focused on specific missing information",
            expected_output="Targeted response for missing information",
            context=previous_steps_context
        )]
        
    def _build_previous_steps_context(self, state: UniversalNewState) -> str:
        """
        Build context summary from previous steps for next agents.
        
        Args:
            state: Current state with step results
            
        Returns:
            Context summary string
        """
        context_parts = []
        
        # Get steps in chronological order
        step_ids = sorted(state['step_results'].keys(), 
                         key=lambda x: int(x.split('_')[-1]) if x.split('_')[-1].isdigit() else 0)
        
        for step_id in step_ids:
            step_result = state['step_results'][step_id]
            if step_result.success:
                # Add a summary of what this step accomplished
                agents_used = [agent_type.value for agent_type in step_result.agent_results.keys()]
                context_parts.append(f"Step {step_id} ({', '.join(agents_used)}): {step_result.synthesized_answer[:300]}...")
        
        if context_parts:
            return "Previous steps summary:\n" + "\n\n".join(context_parts)
        return ""
        
    async def _fallback_to_legacy_planning(self, state: UniversalNewState) -> UniversalNewState:
        """
        Fallback to simple planning if enhanced planning fails.
        
        Args:
            state: Current state
            
        Returns:
            State with simple execution plan
        """
        if self.verbose:
            print("⚠️ ENHANCED PLANNER: Using simple fallback planning")

        # Create a simple workflow step
        first_step = self._create_simple_fallback_step(state)
        
        # Update state with the workflow
        state["workflow_steps"] = [first_step]
        state["current_step_index"] = 0
        state["planner_reasoning"] = "Simple fallback planning due to error in enhanced planning"
        state["estimated_execution_time"] = 60.0  # Default estimate
        
        if self.verbose:
            print(f"✅ ENHANCED PLANNER: Created fallback workflow with 1 step")
            print(f"   First step: {first_step.step_name}")
            print(f"   Agent tasks: {len(first_step.agent_tasks)}")
        
        return state
        
    def _create_simple_fallback_step(self, state: UniversalNewState) -> ExecutionStep:
        """
        Create a simple fallback step for the workflow.
        
        Args:
            state: Current state
            
        Returns:
            Simple fallback step
        """
        agent_tasks = []
        
        # Determine which agent to use based on the context
        if state['selected_file_ids']:
            # If we have files, use deep search
            deep_search_task = AgentTask(
                agent_type=AgentType.DEEP_SEARCH,
                targeted_query=f"Analyze the selected files to extract relevant information for: {state['query']}",
                reasoning="Fallback deep search for file analysis",
                expected_output="Extracted information from files"
            )
            agent_tasks.append(deep_search_task)
            
        # Always add GPT for response generation
        gpt_task = AgentTask(
            agent_type=AgentType.GPT,
            targeted_query=f"Provide a comprehensive response to: {state['query']}",
            reasoning="Fallback GPT for response generation",
            expected_output="Comprehensive response"
        )
        agent_tasks.append(gpt_task)
        
        return ExecutionStep(
            step_id="step_1",
            step_name="Fallback Processing",
            step_description="Process the query with fallback agents",
            agent_tasks=agent_tasks,
            execution_strategy=ExecutionStrategy.SEQUENTIAL,
            depends_on_steps=[],
            validation_required=True,
            completed=False
        )
