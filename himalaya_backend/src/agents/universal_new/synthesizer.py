"""
Answer Synthesis Engine

Intelligently combines results from multiple agents based on execution strategy,
confidence scores, and content relevance to create comprehensive final answers.
"""

import os
import sys
from typing import Dict, List, Any, Optional
import datetime

from langchain_core.messages import SystemMessage, HumanMessage
from langchain_openai import AzureChatOpenAI

# Import configuration
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
from config.settings import AZURE_OPENAI_KEY, AZURE_OPENAI_API_VERSION, AZURE_OPENAI_ENDPOINT, AZURE_OPENAI_DEPLOYMENT_NAME

from .state import (
    UniversalNewState, AgentResult, AgentType, ExecutionStrategy,
    UniversalNewConfig
)


class AnswerSynthesizer:
    """
    Synthesizes answers from multiple agents into a coherent final response.
    
    Features:
    - Strategy-aware synthesis (sequential vs parallel)
    - Confidence-weighted combination
    - Content deduplication and organization
    - Comprehensive answer structuring
    """
    
    def __init__(self, model=None, config: UniversalNewConfig = None, verbose: bool = True):
        """
        Initialize the Answer Synthesizer.
        
        Args:
            model: LLM model for synthesis
            config: Configuration
            verbose: Whether to enable verbose logging
        """
        self.verbose = verbose
        self.config = config or UniversalNewConfig()
        self.today_date = datetime.datetime.now().strftime("%Y-%m-%d")
        
        # Initialize the model
        if model:
            self.model = model
        else:
            self.model = AzureChatOpenAI(
                azure_deployment=AZURE_OPENAI_DEPLOYMENT_NAME,
                api_version=AZURE_OPENAI_API_VERSION,
                azure_endpoint=AZURE_OPENAI_ENDPOINT,
                api_key=AZURE_OPENAI_KEY,
                temperature=0.2  # Low temperature for consistent synthesis
            )
            if self.verbose:
                print(f"🔄 ANSWER SYNTHESIZER: Using Azure OpenAI with deployment {AZURE_OPENAI_DEPLOYMENT_NAME}")
    
    def synthesize_final_answer(self, state: UniversalNewState) -> str:
        """
        Synthesize the final answer from all agent results.
        
        Args:
            state: Current state with agent results
            
        Returns:
            Synthesized final answer
        """
        if self.verbose:
            print(f"\n🔄 ANSWER SYNTHESIZER: Synthesizing final answer")
            print(f"   Strategy: {state['execution_strategy'].value}")
            print(f"   Agent results: {len(state['agent_results'])}")
        
        try:
            if state['execution_strategy'] == ExecutionStrategy.SEQUENTIAL:
                return self._synthesize_sequential_answer(state)
            elif state['execution_strategy'] == ExecutionStrategy.PARALLEL:
                return self._synthesize_parallel_answer(state)
            elif state['execution_strategy'] == ExecutionStrategy.HYBRID:
                return self._synthesize_hybrid_answer(state)
            else:
                return self._synthesize_fallback_answer(state)
                
        except Exception as e:
            if self.verbose:
                print(f"❌ ANSWER SYNTHESIZER: Error during synthesis: {e}")
            return self._synthesize_fallback_answer(state)
    
    def _synthesize_sequential_answer(self, state: UniversalNewState) -> str:
        """
        Synthesize answer for sequential execution strategy.
        
        For sequential execution, we already have accumulated answers,
        but we need to create a coherent final synthesis.
        """
        if self.verbose:
            print("🔄 SYNTHESIZER: Sequential synthesis")
        
        # Use accumulated answer as base
        if state['accumulated_answer']:
            return self._refine_accumulated_answer(state)
        
        # Fallback to combining individual results
        return self._combine_agent_results(state)
    
    def _synthesize_parallel_answer(self, state: UniversalNewState) -> str:
        """
        Synthesize answer for parallel execution strategy.
        
        For parallel execution, we need to intelligently combine
        independent results from multiple agents.
        """
        if self.verbose:
            print("🔄 SYNTHESIZER: Parallel synthesis")
        
        return self._combine_agent_results(state)
    
    def _synthesize_hybrid_answer(self, state: UniversalNewState) -> str:
        """
        Synthesize answer for hybrid execution strategy.
        
        Combines both sequential and parallel synthesis approaches.
        """
        if self.verbose:
            print("🔄 SYNTHESIZER: Hybrid synthesis")
        
        # Use accumulated answer if available, otherwise combine results
        if state['accumulated_answer']:
            return self._refine_accumulated_answer(state)
        else:
            return self._combine_agent_results(state)
    
    def _refine_accumulated_answer(self, state: UniversalNewState) -> str:
        """
        Refine the accumulated answer from sequential processing.
        
        Args:
            state: Current state
            
        Returns:
            Refined answer
        """
        system_prompt = self._get_refinement_system_prompt()
        
        # Prepare agent summaries
        agent_summaries = []
        for agent_type, result in state['agent_results'].items():
            if result.success:
                agent_summaries.append(f"{agent_type.value}: {result.answer[:200]}...")
        
        user_prompt = f"""
        ORIGINAL QUERY: {state['query']}
        
        ACCUMULATED ANSWER FROM SEQUENTIAL PROCESSING:
        {state['accumulated_answer']}
        
        AGENT SUMMARIES:
        {chr(10).join(agent_summaries)}
        
        Please refine this accumulated answer into a coherent, comprehensive response that:
        1. Directly addresses the original query
        2. Integrates insights from all agents seamlessly
        3. Removes redundancy and improves flow
        4. Maintains all important information
        5. Provides a clear, actionable conclusion
        """
        
        messages = [
            SystemMessage(content=system_prompt),
            HumanMessage(content=user_prompt)
        ]
        
        response = self.model.invoke(messages)
        return response.content
    
    def _combine_agent_results(self, state: UniversalNewState) -> str:
        """
        Combine individual agent results into a synthesized answer.
        
        Args:
            state: Current state
            
        Returns:
            Combined answer
        """
        system_prompt = self._get_combination_system_prompt()
        
        # Prepare agent results
        agent_results = []
        for agent_type, result in state['agent_results'].items():
            if result.success:
                agent_results.append({
                    'agent': agent_type.value,
                    'answer': result.answer,
                    'confidence': result.confidence_score,
                    'has_references': bool(result.references)
                })
        
        # Sort by confidence score
        agent_results.sort(key=lambda x: x['confidence'], reverse=True)
        
        # Format agent results for prompt
        formatted_results = []
        for i, result in enumerate(agent_results):
            formatted_results.append(f"""
        AGENT {i+1}: {result['agent'].upper()} (Confidence: {result['confidence']:.2f})
        {result['answer']}
        """)
        
        user_prompt = f"""
        ORIGINAL QUERY: {state['query']}
        
        AGENT RESULTS TO COMBINE:
        {''.join(formatted_results)}
        
        Please synthesize these results into a comprehensive answer that:
        1. Directly addresses the original query
        2. Integrates the most relevant information from each agent
        3. Prioritizes higher-confidence results
        4. Resolves any conflicts or contradictions
        5. Provides a clear, well-structured response
        6. Acknowledges the sources of information appropriately
        """
        
        messages = [
            SystemMessage(content=system_prompt),
            HumanMessage(content=user_prompt)
        ]
        
        response = self.model.invoke(messages)
        return response.content
    
    def _synthesize_fallback_answer(self, state: UniversalNewState) -> str:
        """
        Create a fallback answer when synthesis fails.
        
        Args:
            state: Current state
            
        Returns:
            Fallback answer
        """
        if self.verbose:
            print("🔄 SYNTHESIZER: Using fallback synthesis")
        
        # Collect successful results
        successful_results = [
            result for result in state['agent_results'].values()
            if result.success and result.answer
        ]
        
        if not successful_results:
            return "I apologize, but I wasn't able to generate a comprehensive answer to your query. Please try rephrasing your question or providing more context."
        
        # Simple concatenation with headers
        answer_parts = [f"Based on my analysis of your query: {state['query']}\n"]
        
        for result in successful_results:
            agent_name = result.agent_type.value.replace('_', ' ').title()
            answer_parts.append(f"\n**{agent_name} Analysis:**")
            answer_parts.append(result.answer)
        
        return '\n'.join(answer_parts)
    
    def _get_refinement_system_prompt(self) -> str:
        """Get system prompt for answer refinement."""
        return f"""You are an expert answer synthesizer that refines accumulated answers from sequential agent processing.

Your role is to take a comprehensive but potentially redundant accumulated answer and refine it into a polished, coherent response.

REFINEMENT GUIDELINES:
1. Maintain all important information and insights
2. Remove redundancy and repetition
3. Improve flow and readability
4. Ensure the answer directly addresses the query
5. Organize information logically
6. Provide clear conclusions and actionable insights
7. Maintain professional tone

STRUCTURE YOUR REFINED ANSWER:
- Start with a direct response to the query
- Organize supporting information logically
- Use clear headings or bullet points when appropriate
- End with conclusions or recommendations
- Keep the response comprehensive but concise

Today's date: {self.today_date}

Focus on creating a polished, professional response that fully addresses the user's needs."""
    
    def _get_combination_system_prompt(self) -> str:
        """Get system prompt for combining agent results."""
        return f"""You are an expert answer synthesizer that combines results from multiple specialized agents.

Your role is to intelligently merge information from different sources into a comprehensive, coherent answer.

AVAILABLE AGENT TYPES:
- RAG Agent: Internal documents and knowledge base
- Web Search Agent: Current internet information
- GPT Agent: General knowledge and analysis
- Deep Search Agent: Detailed file analysis

SYNTHESIS GUIDELINES:
1. Prioritize information by confidence scores
2. Resolve conflicts by favoring more reliable sources
3. Integrate complementary information seamlessly
4. Acknowledge different perspectives when appropriate
5. Maintain factual accuracy
6. Provide comprehensive coverage of the topic
7. Structure the answer logically

CONFLICT RESOLUTION:
- Internal documents (RAG/Deep Search) > Web sources > General knowledge
- Recent information > Older information
- Specific data > General statements
- Higher confidence scores > Lower confidence scores

Today's date: {self.today_date}

Create a well-structured, comprehensive answer that leverages the strengths of each agent."""
    
    def calculate_confidence_score(self, state: UniversalNewState) -> float:
        """
        Calculate overall confidence score for the synthesized answer.
        
        Args:
            state: Current state
            
        Returns:
            Overall confidence score (0.0 to 1.0)
        """
        successful_results = [
            result for result in state['agent_results'].values()
            if result.success
        ]
        
        if not successful_results:
            return 0.0
        
        # Weighted average based on agent reliability
        agent_weights = {
            AgentType.DEEP_SEARCH: 0.9,
            AgentType.RAG: 0.8,
            AgentType.WEB_SEARCH: 0.7,
            AgentType.GPT: 0.6
        }
        
        total_weighted_score = 0.0
        total_weight = 0.0
        
        for result in successful_results:
            weight = agent_weights.get(result.agent_type, 0.5)
            total_weighted_score += result.confidence_score * weight
            total_weight += weight
        
        if total_weight == 0:
            return 0.0
        
        base_confidence = total_weighted_score / total_weight
        
        # Boost confidence if multiple agents agree
        if len(successful_results) > 1:
            collaboration_boost = min(0.1 * (len(successful_results) - 1), 0.2)
            base_confidence = min(1.0, base_confidence + collaboration_boost)
        
        return base_confidence
