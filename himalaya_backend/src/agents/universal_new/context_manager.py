"""
Context Management System

Handles context passing between agents, conversation history management,
and progressive answer accumulation for the Universal New Agent.
"""

import json
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, field

from .state import (
    UniversalNewState, AgentResult, AgentType, ExecutionStrategy,
    ContextPassingConfig
)


@dataclass
class ContextSnapshot:
    """Snapshot of context at a specific point in execution"""
    timestamp: float
    agent_type: AgentType
    answer: str
    references: Dict[str, Any]
    metadata: Dict[str, Any]
    confidence_score: float


class ContextManager:
    """
    Manages context passing between agents and progressive answer building.
    
    Responsibilities:
    - Context summarization and compression
    - Progressive answer accumulation
    - Conversation history management
    - Context dependency resolution
    """
    
    def __init__(self, config: ContextPassingConfig = None, verbose: bool = True):
        """
        Initialize the Context Manager.
        
        Args:
            config: Configuration for context passing
            verbose: Whether to enable verbose logging
        """
        self.config = config or ContextPassingConfig()
        self.verbose = verbose
        self.context_history: List[ContextSnapshot] = []
        
        if self.verbose:
            print("🧠 CONTEXT MANAGER: Initialized")
    
    def prepare_context_for_agent(
        self,
        state: UniversalNewState,
        target_agent: AgentType,
        dependencies: List[AgentType]
    ) -> Dict[str, Any]:
        """
        Prepare context for a specific agent based on its dependencies.
        
        Args:
            state: Current state
            target_agent: Agent that will receive the context
            dependencies: List of agent types this agent depends on
            
        Returns:
            Prepared context dictionary
        """
        if self.verbose:
            print(f"🧠 CONTEXT MANAGER: Preparing context for {target_agent.value}")
            print(f"   Dependencies: {[dep.value for dep in dependencies]}")
        
        context = {
            'query': state['query'],
            'standalone_query': state['standalone_query'],
            'conversation_history': state['conversation_history'],
            'conversation_summary': state['conversation_summary'],
            'selected_file_ids': state['selected_file_ids'],
            'blob_names': state['blob_names'],
            'user_context': state['user_context']
        }
        
        # Add dependency context
        if dependencies:
            dependency_context = self._build_dependency_context(state, dependencies)
            context['dependency_context'] = dependency_context
        
        # Add accumulated context for sequential processing
        if state['execution_strategy'] == ExecutionStrategy.SEQUENTIAL:
            sequential_context = self._build_sequential_context(state)
            context['sequential_context'] = sequential_context
        
        # Add conversation context
        conversation_context = self._build_conversation_context(state)
        if conversation_context:
            context['conversation_context'] = conversation_context
        
        # Compress context if too large
        context = self._compress_context_if_needed(context)
        
        return context
    
    def _build_dependency_context(
        self,
        state: UniversalNewState,
        dependencies: List[AgentType]
    ) -> Dict[str, Any]:
        """
        Build context from dependency agents.
        
        Args:
            state: Current state
            dependencies: List of dependency agent types
            
        Returns:
            Dependency context dictionary
        """
        dependency_context = {}
        
        for dep_agent in dependencies:
            if dep_agent in state['agent_results']:
                result = state['agent_results'][dep_agent]
                if result.success:
                    dep_key = f"{dep_agent.value}_result"
                    dependency_context[dep_key] = {
                        'answer': result.answer,
                        'confidence': result.confidence_score,
                        'summary': self._summarize_text(result.answer, 200)
                    }
                    
                    if self.config.include_references and result.references:
                        dependency_context[f"{dep_agent.value}_references"] = result.references
                    
                    if self.config.include_metadata and result.metadata:
                        dependency_context[f"{dep_agent.value}_metadata"] = result.metadata
        
        return dependency_context
    
    def _build_sequential_context(self, state: UniversalNewState) -> Dict[str, Any]:
        """
        Build context for sequential processing.
        
        Args:
            state: Current state
            
        Returns:
            Sequential context dictionary
        """
        sequential_context = {}
        
        if self.config.include_previous_answers and state['accumulated_answer']:
            # Summarize if too long
            accumulated_summary = self._summarize_text(
                state['accumulated_answer'],
                self.config.context_summary_threshold
            )
            sequential_context['previous_answers'] = accumulated_summary
        
        if state['current_context']:
            sequential_context['current_context'] = state['current_context']
        
        # Add execution progress
        completed_agents = [
            agent_type.value for agent_type, result in state['agent_results'].items()
            if result.success
        ]
        sequential_context['completed_agents'] = completed_agents
        
        return sequential_context
    
    def _build_conversation_context(self, state: UniversalNewState) -> Optional[Dict[str, Any]]:
        """
        Build conversation context.
        
        Args:
            state: Current state
            
        Returns:
            Conversation context dictionary or None
        """
        if not state['conversation_history'] and not state['conversation_summary']:
            return None
        
        conversation_context = {}
        
        if state['conversation_history']:
            # Summarize conversation history if too long
            history_summary = self._summarize_text(
                state['conversation_history'],
                self.config.context_summary_threshold // 2
            )
            conversation_context['history'] = history_summary
        
        if state['conversation_summary']:
            conversation_context['summary'] = state['conversation_summary']
        
        return conversation_context
    
    def _compress_context_if_needed(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Compress context if it exceeds the maximum length.
        
        Args:
            context: Context dictionary
            
        Returns:
            Compressed context dictionary
        """
        # Estimate context size
        context_str = json.dumps(context, default=str)
        context_length = len(context_str)
        
        if context_length <= self.config.max_context_length:
            return context
        
        if self.verbose:
            print(f"🧠 CONTEXT MANAGER: Compressing context ({context_length} -> {self.config.max_context_length} chars)")
        
        # Compress by summarizing large text fields
        compressed_context = context.copy()
        
        # Compress dependency context
        if 'dependency_context' in compressed_context:
            for key, value in compressed_context['dependency_context'].items():
                if isinstance(value, dict) and 'answer' in value:
                    value['answer'] = self._summarize_text(value['answer'], 150)
        
        # Compress sequential context
        if 'sequential_context' in compressed_context:
            seq_ctx = compressed_context['sequential_context']
            if 'previous_answers' in seq_ctx:
                seq_ctx['previous_answers'] = self._summarize_text(
                    seq_ctx['previous_answers'], 200
                )
        
        # Compress conversation context
        if 'conversation_context' in compressed_context:
            conv_ctx = compressed_context['conversation_context']
            if 'history' in conv_ctx:
                conv_ctx['history'] = self._summarize_text(conv_ctx['history'], 150)
        
        return compressed_context
    
    def _summarize_text(self, text: str, max_length: int) -> str:
        """
        Summarize text to fit within max_length.
        
        Args:
            text: Text to summarize
            max_length: Maximum length for summary
            
        Returns:
            Summarized text
        """
        if len(text) <= max_length:
            return text
        
        # Simple truncation with ellipsis for now
        # In a more sophisticated implementation, this could use an LLM for summarization
        return text[:max_length-3] + "..."
    
    def update_context_with_result(
        self,
        state: UniversalNewState,
        agent_type: AgentType,
        result: AgentResult
    ) -> UniversalNewState:
        """
        Update state context with a new agent result.
        
        Args:
            state: Current state
            agent_type: Type of agent that produced the result
            result: Result from the agent
            
        Returns:
            Updated state
        """
        if self.verbose:
            print(f"🧠 CONTEXT MANAGER: Updating context with {agent_type.value} result")
        
        # Add to context history
        snapshot = ContextSnapshot(
            timestamp=result.execution_time,
            agent_type=agent_type,
            answer=result.answer,
            references=result.references,
            metadata=result.metadata,
            confidence_score=result.confidence_score
        )
        self.context_history.append(snapshot)
        
        # Update current context
        if result.context_for_next:
            state['current_context'].update(result.context_for_next)
        
        # Update accumulated answer for sequential processing
        if state['execution_strategy'] == ExecutionStrategy.SEQUENTIAL and result.success:
            if state['accumulated_answer']:
                # Add separator and new answer
                state['accumulated_answer'] += f"\n\n--- {agent_type.value.upper()} ANALYSIS ---\n{result.answer}"
            else:
                state['accumulated_answer'] = f"--- {agent_type.value.upper()} ANALYSIS ---\n{result.answer}"
        
        return state
    
    def get_context_summary(self) -> Dict[str, Any]:
        """
        Get a summary of the current context state.
        
        Returns:
            Context summary dictionary
        """
        return {
            'total_snapshots': len(self.context_history),
            'agents_executed': [snapshot.agent_type.value for snapshot in self.context_history],
            'average_confidence': sum(s.confidence_score for s in self.context_history) / len(self.context_history) if self.context_history else 0.0,
            'total_answer_length': sum(len(s.answer) for s in self.context_history),
            'config': {
                'max_context_length': self.config.max_context_length,
                'include_previous_answers': self.config.include_previous_answers,
                'include_references': self.config.include_references,
                'include_metadata': self.config.include_metadata
            }
        }
