"""
Agent Orchestrator

Handles the execution of multiple agents according to the execution plan.
Supports both sequential and parallel execution with context passing.
"""

import os
import sys
import asyncio
import time
from typing import Dict, List, Any, Optional, Tuple
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading

# Import configuration
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

# Import specialized agents
from agents.rag import create_rag_agent
from agents.web import create_web_search_agent
from agents.gpt import create_gpt_agent
from agents.deep_search_new import DeepSearchOrchestrator

from .state import (
    UniversalNewState, AgentExecutionPlan, AgentResult, AgentType,
    ExecutionStrategy, UniversalNewConfig, update_state_with_agent_result
)


class AgentOrchestrator:
    """
    Orchestrates the execution of multiple agents according to the execution plan.
    
    Handles:
    - Sequential execution with context passing
    - Parallel execution for independent agents
    - Hybrid execution strategies
    - Error handling and recovery
    - Result collection and synthesis
    """
    
    def __init__(self, config: UniversalNewConfig = None, verbose: bool = True):
        """
        Initialize the Agent Orchestrator.
        
        Args:
            config: Configuration for orchestration
            verbose: Whether to enable verbose logging
        """
        self.verbose = verbose
        self.config = config or UniversalNewConfig()
        
        # Initialize agents (lazy loading)
        self._rag_agent = None
        self._web_search_agent = None
        self._gpt_agent = None
        self._deep_search_orchestrator = None
        
        # Thread pool for parallel execution
        self._thread_pool = ThreadPoolExecutor(max_workers=4)
        
        if self.verbose:
            print("🎭 AGENT ORCHESTRATOR: Initialized with lazy loading")
    
    @property
    def rag_agent(self):
        """Lazy load RAG agent"""
        if self._rag_agent is None:
            self._rag_agent = create_rag_agent(verbose=self.verbose)
            if self.verbose:
                print("🎭 ORCHESTRATOR: Initialized RAG agent")
        return self._rag_agent
    
    @property
    def web_search_agent(self):
        """Lazy load Web Search agent"""
        if self._web_search_agent is None:
            self._web_search_agent = create_web_search_agent(verbose=self.verbose)
            if self.verbose:
                print("🎭 ORCHESTRATOR: Initialized Web Search agent")
        return self._web_search_agent
    
    @property
    def gpt_agent(self):
        """Lazy load GPT agent"""
        if self._gpt_agent is None:
            from agents.gpt import create_gpt_agent
            self._gpt_agent = create_gpt_agent(verbose=self.verbose)
            if self.verbose:
                print("🎭 ORCHESTRATOR: Initialized GPT agent")
        return self._gpt_agent
    
    @property
    def deep_search_orchestrator(self):
        """Lazy load Deep Search orchestrator"""
        if self._deep_search_orchestrator is None:
            self._deep_search_orchestrator = DeepSearchOrchestrator(verbose=self.verbose)
            if self.verbose:
                print("🎭 ORCHESTRATOR: Initialized Deep Search orchestrator")
        return self._deep_search_orchestrator
    
    async def execute_plan(self, state: UniversalNewState) -> UniversalNewState:
        """
        Execute the agent plan according to the execution strategy.
        
        Args:
            state: Current state with execution plan
            
        Returns:
            Updated state with agent results
        """
        if self.verbose:
            print(f"\n🎭 AGENT ORCHESTRATOR: Executing plan with {len(state['execution_plan'])} agents")
            print(f"   Strategy: {state['execution_strategy'].value}")
        
        start_time = time.time()
        
        try:
            if state['execution_strategy'] == ExecutionStrategy.SEQUENTIAL:
                state = await self._execute_sequential(state)
            elif state['execution_strategy'] == ExecutionStrategy.PARALLEL:
                state = await self._execute_parallel(state)
            elif state['execution_strategy'] == ExecutionStrategy.HYBRID:
                state = await self._execute_hybrid(state)
            else:
                raise ValueError(f"Unknown execution strategy: {state['execution_strategy']}")
            
            execution_time = time.time() - start_time
            state['total_execution_time'] = execution_time
            
            if self.verbose:
                print(f"✅ AGENT ORCHESTRATOR: Execution completed in {execution_time:.2f}s")
                print(f"   Successful agents: {len([r for r in state['agent_results'].values() if r.success])}")
                print(f"   Failed agents: {len([r for r in state['agent_results'].values() if not r.success])}")
            
            return state
            
        except Exception as e:
            if self.verbose:
                print(f"❌ AGENT ORCHESTRATOR: Error during execution: {e}")
            
            state['error_messages'].append(f"Orchestration error: {str(e)}")
            return state
    
    async def _execute_sequential(self, state: UniversalNewState) -> UniversalNewState:
        """
        Execute agents sequentially with context passing.
        
        Args:
            state: Current state
            
        Returns:
            Updated state
        """
        if self.verbose:
            print("🔄 ORCHESTRATOR: Starting sequential execution")
        
        for i, agent_plan in enumerate(state['execution_plan']):
            if self.verbose:
                print(f"\n🔄 ORCHESTRATOR: Executing agent {i+1}/{len(state['execution_plan'])}: {agent_plan.agent_type.value}")
            
            # Prepare context for this agent
            agent_context = self._prepare_agent_context(state, agent_plan)
            
            # Execute the agent
            result = await self._execute_single_agent(agent_plan, state, agent_context)
            
            # Update state with result
            state = update_state_with_agent_result(state, agent_plan.agent_type, result)
            
            # Update current context for next agents
            if result.success and result.context_for_next:
                state['current_context'].update(result.context_for_next)
            
            if self.verbose:
                status = "✅ Success" if result.success else "❌ Failed"
                print(f"   {status}: {agent_plan.agent_type.value} ({result.execution_time:.2f}s)")
                if not result.success and result.error_message:
                    print(f"   Error: {result.error_message}")
        
        return state
    
    async def _execute_parallel(self, state: UniversalNewState) -> UniversalNewState:
        """
        Execute agents in parallel.
        
        Args:
            state: Current state
            
        Returns:
            Updated state
        """
        if self.verbose:
            print("⚡ ORCHESTRATOR: Starting parallel execution")
        
        # Create tasks for all agents
        tasks = []
        for agent_plan in state['execution_plan']:
            agent_context = self._prepare_agent_context(state, agent_plan)
            task = self._execute_single_agent(agent_plan, state, agent_context)
            tasks.append((agent_plan.agent_type, task))
        
        # Execute all tasks concurrently
        results = await asyncio.gather(*[task for _, task in tasks], return_exceptions=True)
        
        # Process results
        for (agent_type, _), result in zip(tasks, results):
            if isinstance(result, Exception):
                # Handle exception
                error_result = AgentResult(
                    agent_type=agent_type,
                    success=False,
                    error_message=str(result),
                    execution_time=0.0
                )
                state = update_state_with_agent_result(state, agent_type, error_result)
                if self.verbose:
                    print(f"❌ ORCHESTRATOR: {agent_type.value} failed with exception: {result}")
            else:
                # Handle successful result
                state = update_state_with_agent_result(state, agent_type, result)
                if self.verbose:
                    status = "✅ Success" if result.success else "❌ Failed"
                    print(f"   {status}: {agent_type.value} ({result.execution_time:.2f}s)")
        
        return state
    
    async def _execute_hybrid(self, state: UniversalNewState) -> UniversalNewState:
        """
        Execute agents using hybrid strategy (mix of sequential and parallel).
        
        Args:
            state: Current state
            
        Returns:
            Updated state
        """
        if self.verbose:
            print("🔀 ORCHESTRATOR: Starting hybrid execution")
        
        # Group agents by parallel groups
        parallel_groups = {}
        sequential_agents = []
        
        for agent_plan in state['execution_plan']:
            if agent_plan.parallel_group is not None:
                if agent_plan.parallel_group not in parallel_groups:
                    parallel_groups[agent_plan.parallel_group] = []
                parallel_groups[agent_plan.parallel_group].append(agent_plan)
            else:
                sequential_agents.append(agent_plan)
        
        # Execute in order: sequential agents and parallel groups
        all_items = []
        
        # Add sequential agents
        for agent_plan in sequential_agents:
            all_items.append(('sequential', agent_plan))
        
        # Add parallel groups
        for group_id, group_agents in parallel_groups.items():
            all_items.append(('parallel', group_agents))
        
        # Sort by execution order
        all_items.sort(key=lambda x: x[1].execution_order if x[0] == 'sequential' else min(a.execution_order for a in x[1]))
        
        # Execute each item
        for item_type, item_data in all_items:
            if item_type == 'sequential':
                # Execute single agent
                agent_plan = item_data
                agent_context = self._prepare_agent_context(state, agent_plan)
                result = await self._execute_single_agent(agent_plan, state, agent_context)
                state = update_state_with_agent_result(state, agent_plan.agent_type, result)
                
                if self.verbose:
                    status = "✅ Success" if result.success else "❌ Failed"
                    print(f"   Sequential {status}: {agent_plan.agent_type.value}")
                    
            elif item_type == 'parallel':
                # Execute parallel group
                group_agents = item_data
                tasks = []
                for agent_plan in group_agents:
                    agent_context = self._prepare_agent_context(state, agent_plan)
                    task = self._execute_single_agent(agent_plan, state, agent_context)
                    tasks.append((agent_plan.agent_type, task))
                
                # Execute group concurrently
                results = await asyncio.gather(*[task for _, task in tasks], return_exceptions=True)
                
                # Process group results
                for (agent_type, _), result in zip(tasks, results):
                    if isinstance(result, Exception):
                        error_result = AgentResult(
                            agent_type=agent_type,
                            success=False,
                            error_message=str(result),
                            execution_time=0.0
                        )
                        state = update_state_with_agent_result(state, agent_type, error_result)
                    else:
                        state = update_state_with_agent_result(state, agent_type, result)
                
                if self.verbose:
                    print(f"   Parallel group completed: {[a.agent_type.value for a in group_agents]}")
        
        return state
    
    def _prepare_agent_context(self, state: UniversalNewState, agent_plan: AgentExecutionPlan) -> Dict[str, Any]:
        """
        Prepare context for an agent based on dependencies and current state.
        
        Args:
            state: Current state
            agent_plan: Plan for the agent
            
        Returns:
            Context dictionary for the agent
        """
        context = {
            'query': state['query'],
            'standalone_query': state['standalone_query'],
            'conversation_history': state['conversation_history'],
            'conversation_summary': state['conversation_summary'],
            'selected_file_ids': state['selected_file_ids'],
            'blob_names': state['blob_names'],
            'user_context': state['user_context']
        }
        
        # Add context from dependencies
        if agent_plan.context_dependencies:
            dependency_context = {}
            for dep_agent_type in agent_plan.context_dependencies:
                if dep_agent_type in state['agent_results']:
                    dep_result = state['agent_results'][dep_agent_type]
                    if dep_result.success:
                        dependency_context[f'{dep_agent_type.value}_answer'] = dep_result.answer
                        dependency_context[f'{dep_agent_type.value}_references'] = dep_result.references
            
            context['dependency_context'] = dependency_context
        
        # Add current accumulated context
        if state['current_context']:
            context['accumulated_context'] = state['current_context']
        
        # Add accumulated answer for sequential processing
        if state['accumulated_answer'] and state['execution_strategy'] == ExecutionStrategy.SEQUENTIAL:
            context['previous_answers'] = state['accumulated_answer']
        
        return context

    async def _execute_single_agent(self, agent_plan: AgentExecutionPlan, state: UniversalNewState, context: Dict[str, Any]) -> AgentResult:
        """
        Execute a single agent with the given context.

        Args:
            agent_plan: Plan for the agent
            state: Current state
            context: Context for the agent

        Returns:
            Result from agent execution
        """
        start_time = time.time()

        try:
            if agent_plan.agent_type == AgentType.RAG:
                result = await self._execute_rag_agent(context)
            elif agent_plan.agent_type == AgentType.WEB_SEARCH:
                result = await self._execute_web_search_agent(context)
            elif agent_plan.agent_type == AgentType.GPT:
                result = await self._execute_gpt_agent(context)
            elif agent_plan.agent_type == AgentType.DEEP_SEARCH:
                result = await self._execute_deep_search_agent(context)
            else:
                raise ValueError(f"Unknown agent type: {agent_plan.agent_type}")

            execution_time = time.time() - start_time
            result.execution_time = execution_time

            return result

        except Exception as e:
            execution_time = time.time() - start_time
            if self.verbose:
                print(f"❌ ORCHESTRATOR: Error executing {agent_plan.agent_type.value}: {e}")

            return AgentResult(
                agent_type=agent_plan.agent_type,
                success=False,
                error_message=str(e),
                execution_time=execution_time
            )

    async def _execute_rag_agent(self, context: Dict[str, Any]) -> AgentResult:
        """Execute RAG agent"""
        try:
            # Prepare enhanced query with context
            enhanced_query = self._enhance_query_with_context(context, "rag")

            result = self.rag_agent.process_query(
                query=context['query'],
                standalone_query=enhanced_query,
                blob_names=context.get('blob_names'),
                deep_search=False
            )

            # Extract answer from RAG result
            answer = ""
            references = {}

            if result.get('csv_documents'):
                answer += "CSV Data Analysis:\n"
                for doc in result['csv_documents'][:3]:  # Limit to first 3
                    answer += f"- {doc.get('content', '')[:200]}...\n"

            if result.get('text_documents'):
                answer += "\nDocument Information:\n"
                for doc in result['text_documents'][:3]:  # Limit to first 3
                    answer += f"- {doc.get('content', '')[:200]}...\n"

            if result.get('references'):
                references = {'rag_references': result['references']}

            if not answer:
                answer = "No relevant information found in internal documents."

            return AgentResult(
                agent_type=AgentType.RAG,
                success=True,
                answer=answer.strip(),
                references=references,
                metadata={'rag_result': result},
                confidence_score=0.8,
                context_for_next={'rag_data': result}
            )

        except Exception as e:
            return AgentResult(
                agent_type=AgentType.RAG,
                success=False,
                error_message=str(e)
            )

    async def _execute_web_search_agent(self, context: Dict[str, Any]) -> AgentResult:
        """Execute Web Search agent"""
        try:
            # Prepare enhanced query with context
            enhanced_query = self._enhance_query_with_context(context, "web_search")

            result = self.web_search_agent.graph.invoke({
                "messages": [],
                "query": enhanced_query,
                "search_results": [],
                "scraped_content": {},
                "previous_content": {},
                "response": {}
            })

            web_result = result.get("response", {})
            answer = web_result.get("answer", "No web search results found.")
            sources = web_result.get("sources", {})

            return AgentResult(
                agent_type=AgentType.WEB_SEARCH,
                success=True,
                answer=answer,
                references={'web_sources': sources},
                metadata={'web_result': web_result},
                confidence_score=0.7,
                context_for_next={'web_data': web_result}
            )

        except Exception as e:
            return AgentResult(
                agent_type=AgentType.WEB_SEARCH,
                success=False,
                error_message=str(e)
            )

    async def _execute_gpt_agent(self, context: Dict[str, Any]) -> AgentResult:
        """Execute GPT agent"""
        try:
            # Prepare enhanced query with context
            enhanced_query = self._enhance_query_with_context(context, "gpt")

            # Enhanced logging for debugging
            if self.verbose:
                print(f"🔍 GPT AGENT EXECUTION:")
                print(f"   Original Query: {context.get('query', 'N/A')}")
                print(f"   Enhanced Query: {enhanced_query[:200]}...")
                if context.get('dependency_context'):
                    print(f"   Has Dependency Context: {len(context['dependency_context'])} items")
                    for key, value in context['dependency_context'].items():
                        if key.endswith('_answer'):
                            agent_name = key.replace('_answer', '')
                            print(f"     - {agent_name}: {str(value)[:100]}...")
                if context.get('previous_answers'):
                    print(f"   Has Previous Answers: {len(context['previous_answers'])} chars")

            result = self.gpt_agent.process_query(
                query=enhanced_query,
                conversation_history=context.get('conversation_history'),
                conversation_summary=context.get('conversation_summary')
            )

            answer = result.get("answer", "No response from GPT agent.")

            if self.verbose:
                print(f"   GPT Response: {answer[:150]}...")

            return AgentResult(
                agent_type=AgentType.GPT,
                success=True,
                answer=answer,
                references={},
                metadata={'gpt_result': result},
                confidence_score=0.6,
                context_for_next={'gpt_analysis': answer}
            )

        except Exception as e:
            return AgentResult(
                agent_type=AgentType.GPT,
                success=False,
                error_message=str(e)
            )

    async def _execute_deep_search_agent(self, context: Dict[str, Any]) -> AgentResult:
        """Execute Deep Search agent"""
        try:
            if not context.get('selected_file_ids'):
                return AgentResult(
                    agent_type=AgentType.DEEP_SEARCH,
                    success=False,
                    error_message="No files selected for deep search"
                )

            # Prepare enhanced query with context
            enhanced_query = self._enhance_query_with_context(context, "deep_search")

            # Prepare user context with blob names for Deep Search
            user_context = context.get('user_context', {}).copy()
            if context.get('blob_names'):
                user_context['blob_names'] = context['blob_names']

            # Configure Deep Search to be lenient when part of multi-agent workflow
            # Deep Search should focus on content extraction, not strict validation
            user_context['multi_agent_mode'] = True
            user_context['validation_mode'] = 'lenient'  # Tell Deep Search to be less strict
            user_context['purpose'] = 'content_extraction'  # Focus on extracting content for other agents

            result = await self.deep_search_orchestrator.process_deep_search_query(
                query=enhanced_query,
                selected_file_ids=context['selected_file_ids'],
                conversation_history=context.get('conversation_history'),
                conversation_summary=context.get('conversation_summary'),
                user_context=user_context
            )

            answer = result.get("answer", "No results from deep search.")
            references = result.get("references", [])

            return AgentResult(
                agent_type=AgentType.DEEP_SEARCH,
                success=result.get("success", False),
                answer=answer,
                references={'deep_search_references': references},
                metadata={'deep_search_result': result},
                confidence_score=0.9,
                context_for_next={'deep_search_data': result}
            )

        except Exception as e:
            return AgentResult(
                agent_type=AgentType.DEEP_SEARCH,
                success=False,
                error_message=str(e)
            )

    def _enhance_query_with_context(self, context: Dict[str, Any], agent_type: str) -> str:
        """
        Enhance the query with relevant context for the specific agent.

        Args:
            context: Context dictionary
            agent_type: Type of agent being executed

        Returns:
            Enhanced query string
        """
        base_query = context['standalone_query']

        # Special handling for GPT agent to make context more explicit
        if agent_type == "gpt":
            context_info = []

            # Add context from previous agents if available
            if context.get('dependency_context'):
                for key, value in context['dependency_context'].items():
                    if key.endswith('_answer') and value:
                        agent_name = key.replace('_answer', '')
                        if agent_name == 'deep_search':
                            context_info.append(f"Document Analysis Results:\n{value}")
                        elif agent_name == 'web_search':
                            context_info.append(f"Web Research Results:\n{value}")
                        elif agent_name == 'rag':
                            context_info.append(f"Knowledge Base Results:\n{value}")
                        else:
                            context_info.append(f"{agent_name.title()} Results:\n{value}")

            # Add accumulated context for sequential processing
            if context.get('previous_answers'):
                context_info.append(f"Previous Analysis:\n{context['previous_answers']}")

            if context_info:
                enhanced_query = f"""Based on the following information, please {base_query}:

{chr(10).join(context_info)}

Please use this information to provide a comprehensive response to: {base_query}"""
                return enhanced_query
        else:
            # Standard context enhancement for other agents
            if context.get('dependency_context'):
                context_parts = []
                for key, value in context['dependency_context'].items():
                    if key.endswith('_answer') and value:
                        agent_name = key.replace('_answer', '')
                        context_parts.append(f"Previous {agent_name} result: {value[:300]}...")

                if context_parts:
                    enhanced_query = f"{base_query}\n\nContext from previous agents:\n" + "\n".join(context_parts)
                    return enhanced_query

            # Add accumulated context for sequential processing
            if context.get('previous_answers'):
                enhanced_query = f"{base_query}\n\nPrevious analysis: {context['previous_answers'][:500]}..."
                return enhanced_query

        return base_query
