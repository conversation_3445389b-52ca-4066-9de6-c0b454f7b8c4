#!/usr/bin/env python3
"""
Test script to verify Excel processing fixes in Deep Search New
"""

import asyncio
import sys
import os

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))

from agents.deep_search_new.file_content_service import FileContentService
from agents.deep_search_new.excel_processor import ExcelProcessor
from agents.deep_search_new.state import FileInfo, FileType, PlannerDecision, QueryType, ProcessingStrategy


async def test_csv_links_population():
    """Test CSV links population for different file types"""
    print("🧪 Testing CSV links population...")
    
    # Initialize file content service
    file_service = FileContentService(verbose=True)
    
    # Test with mock blob names (you can replace with real ones)
    test_blob_names = [
        "sample_excel.xlsx",
        "sample_csv.csv", 
        "sample_pdf.pdf"
    ]
    
    try:
        excel_files, non_excel_files = await file_service.organize_files_from_blob_names(test_blob_names)
        
        print(f"\n📊 Excel files found: {len(excel_files)}")
        for file_info in excel_files:
            print(f"   File: {file_info['file_name']}")
            print(f"   Type: {file_info['file_type']}")
            print(f"   CSV Links: {file_info.get('csv_links', [])}")
            print(f"   Blob URL: {file_info.get('blob_url', 'N/A')}")
            print()
        
        print(f"📄 Non-Excel files found: {len(non_excel_files)}")
        for file_info in non_excel_files:
            print(f"   File: {file_info['file_name']}")
            print(f"   Type: {file_info['file_type']}")
            print(f"   CSV Links: {file_info.get('csv_links', [])}")
            print()
            
    except Exception as e:
        print(f"❌ Error during file organization: {str(e)}")
        import traceback
        traceback.print_exc()


async def test_excel_processing():
    """Test Excel processing with mathematical operations"""
    print("\n🧪 Testing Excel processing...")
    
    # Initialize Excel processor
    excel_processor = ExcelProcessor(verbose=True)
    
    # Create a mock Excel file info
    mock_excel_file = FileInfo(
        file_id=1,
        file_name="test_excel.xlsx",
        blob_name="test_excel.xlsx",
        file_type=FileType.EXCEL,
        blob_url="https://mock.com/test_excel.xlsx",
        summary="Mock Excel file with financial data",
        content="Mock Excel content",
        chunks=None,
        csv_links=["https://mock.com/test_excel.csv"]  # Mock CSV link
    )
    
    # Create a mock planner decision for mathematical processing
    planner_decision = PlannerDecision(
        query_type=QueryType.ANALYSIS,
        whole_file_needed=True,
        excel_strategy=ProcessingStrategy.MATHEMATICAL,
        non_excel_strategy=ProcessingStrategy.RAG_BASED,
        sequential_processing=True,
        parallel_processing=False,
        reasoning="Mathematical analysis required for Excel data"
    )
    
    query = "What is the total sales amount in the spreadsheet?"
    
    try:
        results = await excel_processor.process_files([mock_excel_file], query, planner_decision)
        
        print(f"\n📊 Processing results: {len(results)}")
        for result in results:
            print(f"   File: {result['file_name']}")
            print(f"   Answer: {result['answer'][:200]}...")
            print(f"   Confidence: {result['confidence_score']}")
            print(f"   Error: {result.get('error', 'None')}")
            print()
            
    except Exception as e:
        print(f"❌ Error during Excel processing: {str(e)}")
        import traceback
        traceback.print_exc()


async def test_csv_path_resolution():
    """Test CSV path resolution logic"""
    print("\n🧪 Testing CSV path resolution...")
    
    excel_processor = ExcelProcessor(verbose=True)
    
    # Test cases
    test_cases = [
        {
            "name": "Excel file with CSV links",
            "file_info": FileInfo(
                file_id=1,
                file_name="test1.xlsx",
                blob_name="test1.xlsx",
                file_type=FileType.EXCEL,
                blob_url="https://mock.com/test1.xlsx",
                summary="Test file",
                content="",
                chunks=None,
                csv_links=["https://mock.com/test1.csv"]
            )
        },
        {
            "name": "Excel file without CSV links",
            "file_info": FileInfo(
                file_id=2,
                file_name="test2.xlsx",
                blob_name="test2.xlsx",
                file_type=FileType.EXCEL,
                blob_url="https://mock.com/test2.xlsx",
                summary="Test file",
                content="",
                chunks=None,
                csv_links=[]
            )
        },
        {
            "name": "CSV file",
            "file_info": FileInfo(
                file_id=3,
                file_name="test3.csv",
                blob_name="test3.csv",
                file_type=FileType.CSV,
                blob_url="https://mock.com/test3.csv",
                summary="Test file",
                content="",
                chunks=None,
                csv_links=None
            )
        }
    ]
    
    for test_case in test_cases:
        print(f"\n   Testing: {test_case['name']}")
        try:
            csv_path = await excel_processor._get_csv_path_for_file(test_case['file_info'])
            print(f"   Result: {csv_path}")
        except Exception as e:
            print(f"   Error: {str(e)}")


async def main():
    """Run all tests"""
    print("🚀 Starting Excel processing tests for Deep Search New...")
    
    await test_csv_links_population()
    await test_excel_processing()
    await test_csv_path_resolution()
    
    print("\n✅ All tests completed!")


if __name__ == "__main__":
    asyncio.run(main())
