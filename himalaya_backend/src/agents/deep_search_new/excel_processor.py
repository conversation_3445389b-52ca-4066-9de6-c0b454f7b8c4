"""
Excel Processing Pipeline

Processes Excel/CSV files using existing excel_main functionality with wrapper
for mathematical operations and summary handling.
"""

import logging
import time
from typing import List, Dict, Any, Optional

from .state import FileInfo, PlannerDecision, ProcessingResult, ProcessingStrategy, QueryType, FileType

try:
    from models.models import CSVFile
    from agents.csv.excel_langchain_rag import ExcelLangChainRAG
    DB_AVAILABLE = True
    EXCEL_RAG_AVAILABLE = True
    import_error = None
except ImportError as e:
    DB_AVAILABLE = False
    EXCEL_RAG_AVAILABLE = False
    CSVFile = None
    ExcelLangChainRAG = None
    import_error = str(e)

# Import basic Excel processor as fallback
try:
    from .basic_excel_processor import BasicExcelProcessor
    BASIC_EXCEL_AVAILABLE = True
except ImportError:
    BasicExcelProcessor = None
    BASIC_EXCEL_AVAILABLE = False

logger = logging.getLogger(__name__)


class ExcelProcessor:
    """
    Processor for Excel/CSV files that leverages existing Excel RAG functionality.
    
    Handles:
    - Summary questions using existing EDA summaries
    - Mathematical operations using CSV data
    - Complex analysis combining summaries with data operations
    """
    
    def __init__(self, verbose: bool = False):
        """
        Initialize the Excel processor.

        Args:
            verbose: Whether to enable verbose logging
        """
        self.verbose = verbose

        # Initialize Excel RAG agent
        if EXCEL_RAG_AVAILABLE:
            try:
                self.excel_rag = ExcelLangChainRAG(verbose=verbose)
                self.basic_excel = None
                if self.verbose:
                    print(f"✅ Excel RAG system initialized successfully")
            except Exception as e:
                self.excel_rag = None
                # Initialize basic Excel processor as fallback
                if BASIC_EXCEL_AVAILABLE:
                    self.basic_excel = BasicExcelProcessor(verbose=verbose)
                    if self.verbose:
                        print(f"⚠️ Excel RAG failed, using basic Excel processor: {str(e)}")
                else:
                    self.basic_excel = None
                    if self.verbose:
                        print(f"❌ Failed to initialize Excel RAG system: {str(e)}")
                logger.error(f"Failed to initialize Excel RAG system: {str(e)}")
        else:
            self.excel_rag = None
            # Initialize basic Excel processor as fallback
            if BASIC_EXCEL_AVAILABLE:
                self.basic_excel = BasicExcelProcessor(verbose=verbose)
                if self.verbose:
                    print(f"⚠️ Excel RAG not available, using basic Excel processor - import error: {import_error}")
            else:
                self.basic_excel = None
                if self.verbose:
                    print(f"❌ Excel RAG system not available - import error: {import_error}")
            logger.error(f"Excel RAG system not available - import error: {import_error}")
    
    async def process_files(self,
                          excel_files: List[FileInfo],
                          query: str,
                          planner_decision: PlannerDecision) -> List[ProcessingResult]:
        """
        Process all Excel/CSV files according to the planner's strategy.
        
        Args:
            excel_files: List of Excel/CSV files to process
            query: User query
            planner_decision: Processing strategy from planner
            
        Returns:
            List of processing results
        """
        if self.verbose:
            print(f"📊 EXCEL PROCESSOR: Processing {len(excel_files)} Excel/CSV files...")
            print(f"   Strategy: {planner_decision['excel_strategy']}")
            print(f"   Query Type: {planner_decision['query_type']}")
        
        results = []
        
        for file_info in excel_files:
            try:
                start_time = time.time()
                
                result = await self._process_single_file(
                    file_info, query, planner_decision
                )
                
                processing_time = time.time() - start_time
                result['processing_time'] = processing_time
                
                results.append(result)
                
                if self.verbose:
                    print(f"   ✅ Processed: {file_info['file_name']} ({processing_time:.2f}s)")
                    
            except Exception as e:
                error_msg = f"Error processing Excel file {file_info['file_name']}: {str(e)}"
                logger.error(error_msg)
                
                results.append(ProcessingResult(
                    file_id=file_info['file_id'],
                    file_name=file_info['file_name'],
                    answer=f"Error: {error_msg}",
                    references=[file_info['file_name']],
                    confidence_score=0.0,
                    processing_time=0.0,
                    error=error_msg
                ))
        
        if self.verbose:
            print(f"✅ EXCEL PROCESSOR: Completed processing {len(results)} files")
        
        return results
    
    async def _process_single_file(self,
                                 file_info: FileInfo,
                                 query: str,
                                 planner_decision: PlannerDecision) -> ProcessingResult:
        """Process a single Excel/CSV file."""
        strategy = planner_decision['excel_strategy']
        query_type = planner_decision['query_type']
        
        if strategy == ProcessingStrategy.SUMMARY_ONLY:
            return await self._process_summary_only(file_info, query)
        
        elif strategy == ProcessingStrategy.MATHEMATICAL:
            return await self._process_mathematical(file_info, query)
        
        elif strategy == ProcessingStrategy.FULL_CONTENT:
            return await self._process_full_content(file_info, query, query_type)
        
        else:  # RAG_BASED or fallback
            return await self._process_rag_based(file_info, query)
    
    async def _process_summary_only(self, file_info: FileInfo, query: str) -> ProcessingResult:
        """Process using only existing summaries."""
        try:
            # Get existing summary from file_info or database
            summary = file_info.get('summary', '')
            
            # Summary should come from the file_info populated by the summarization service
            # No need to query database here
            
            if not summary:
                summary = f"Excel/CSV file: {file_info['file_name']} (No summary available)"
            
            # For summary-only processing, return the existing summary
            # This is appropriate for quick overviews
            answer = f"Summary of {file_info['file_name']}:\n\n{summary}"
            
            return ProcessingResult(
                file_id=file_info['file_id'],
                file_name=file_info['file_name'],
                answer=answer,
                references=[file_info['file_name']],
                confidence_score=0.8,
                processing_time=0.0,
                error=None
            )
            
        except Exception as e:
            raise Exception(f"Summary-only processing failed: {str(e)}")
    
    async def _process_mathematical(self, file_info: FileInfo, query: str) -> ProcessingResult:
        """Process using mathematical operations on CSV data."""
        try:
            # Get CSV links for mathematical processing
            csv_links = file_info.get('csv_links', [])

            if not csv_links:
                # Single CSV file - use the file directly
                csv_path = await self._get_csv_path_for_file(file_info)
                if csv_path:
                    csv_links = [csv_path]

            if not csv_links:
                # Provide more detailed error information
                file_type = file_info.get('file_type')
                blob_url = file_info.get('blob_url')

                if self.verbose:
                    print(f"      No CSV data available for mathematical operations")
                    print(f"      File: {file_info['file_name']}")
                    print(f"      Type: {file_type}")
                    print(f"      Blob URL: {blob_url}")
                    print(f"      CSV Links: {csv_links}")

                # For Excel/CSV files, try using the blob URL directly as a fallback
                if file_type in [FileType.EXCEL, FileType.CSV] and blob_url:
                    if self.verbose:
                        print(f"      Attempting to use blob URL directly: {blob_url}")
                    csv_links = [blob_url]
                else:
                    error_msg = f"No CSV data available for mathematical operations on {file_info['file_name']}"
                    logger.error(error_msg)
                    raise Exception(error_msg)

            # Use the first CSV for mathematical operations
            # TODO: In future, could combine multiple sheets for complex operations
            csv_path = csv_links[0]

            if self.verbose:
                print(f"      Using CSV path for mathematical processing: {csv_path}")

            # Use Excel RAG for mathematical processing
            if self.excel_rag is not None:
                if self.verbose:
                    print(f"      🔄 Processing with Excel RAG...")

                result = self.excel_rag.process_csv_directly(
                    csv_path=csv_path,
                    query=query,
                    csv_id=f"file_{file_info['file_id']}"
                )
            elif self.basic_excel is not None:
                if self.verbose:
                    print(f"      🔄 Processing with Basic Excel processor...")

                result = self.basic_excel.process_csv_directly(
                    csv_path=csv_path,
                    query=query,
                    csv_id=f"file_{file_info['file_id']}"
                )
            else:
                # No Excel processing available
                error_details = []
                if not EXCEL_RAG_AVAILABLE:
                    error_details.append(f"Import error: {import_error}")
                else:
                    error_details.append("Initialization failed during startup")

                if not BASIC_EXCEL_AVAILABLE:
                    error_details.append("Basic Excel processor also not available")

                error_msg = f"Excel analysis not available - no Excel processing system initialized. {' '.join(error_details)}"

                if self.verbose:
                    print(f"      ❌ No Excel processing available: {error_msg}")

                answer = f"Excel analysis not available - no Excel processing system initialized. File: {file_info['file_name']}, Query: {query}"
                result = {
                    'answer': answer,
                    'status': 'error',
                    'confidence_score': 0.0
                }

            answer = result.get('answer', 'No answer generated')

            return ProcessingResult(
                file_id=file_info['file_id'],
                file_name=file_info['file_name'],
                answer=answer,
                references=[file_info['file_name']],
                confidence_score=result.get('confidence_score', 0.7),
                processing_time=0.0,
                error=None
            )

        except Exception as e:
            raise Exception(f"Mathematical processing failed: {str(e)}")
    
    async def _process_full_content(self, file_info: FileInfo, query: str, query_type: QueryType) -> ProcessingResult:
        """Process using full content analysis."""
        try:
            # For full content processing, combine summary with mathematical capabilities
            
            # First get summary
            summary_result = await self._process_summary_only(file_info, query)
            
            # If query needs mathematical analysis, also do mathematical processing
            if self._query_needs_math(query):
                try:
                    math_result = await self._process_mathematical(file_info, query)
                    
                    # Combine summary and mathematical results
                    combined_answer = f"{summary_result['answer']}\n\nDetailed Analysis:\n{math_result['answer']}"
                    
                    return ProcessingResult(
                        file_id=file_info['file_id'],
                        file_name=file_info['file_name'],
                        answer=combined_answer,
                        references=[file_info['file_name']],
                        confidence_score=max(summary_result['confidence_score'], math_result['confidence_score']),
                        processing_time=0.0,
                        error=None
                    )
                    
                except Exception:
                    # If mathematical processing fails, fall back to summary
                    return summary_result
            else:
                # For non-mathematical queries, summary is sufficient
                return summary_result
                
        except Exception as e:
            raise Exception(f"Full content processing failed: {str(e)}")
    
    async def _process_rag_based(self, file_info: FileInfo, query: str) -> ProcessingResult:
        """Process using RAG-based approach."""
        try:
            # For RAG-based processing, use Excel RAG if CSV data is available
            csv_links = file_info.get('csv_links', [])
            
            if csv_links:
                # Use mathematical processing as it includes RAG capabilities
                return await self._process_mathematical(file_info, query)
            else:
                # Fall back to summary-based processing
                return await self._process_summary_only(file_info, query)
                
        except Exception as e:
            raise Exception(f"RAG-based processing failed: {str(e)}")
    
    async def _get_csv_path_for_file(self, file_info: FileInfo) -> Optional[str]:
        """Get CSV path for a file - use csv_links from file_info if available."""
        try:
            if self.verbose:
                print(f"      Getting CSV path for file: {file_info['file_name']}")
                print(f"      File type: {file_info.get('file_type')}")
                print(f"      Blob URL: {file_info.get('blob_url')}")
                print(f"      CSV links: {file_info.get('csv_links', [])}")

            # Check if we have CSV links from the file organization
            csv_links = file_info.get('csv_links', [])
            if csv_links:
                if self.verbose:
                    print(f"      Using CSV link: {csv_links[0]}")
                return csv_links[0]  # Return the first CSV link

            # For single-file Excel/CSV, use the blob URL directly
            # The Excel RAG system can handle blob URLs for Excel and CSV files
            blob_url = file_info.get('blob_url')
            if blob_url:
                file_type = file_info.get('file_type')
                # Only return blob URL for Excel/CSV files
                if file_type and file_type in [FileType.EXCEL, FileType.CSV]:
                    if self.verbose:
                        print(f"      Using blob URL directly: {blob_url}")
                    return blob_url

            if self.verbose:
                print(f"      No CSV path found for file")
            return None

        except Exception as e:
            logger.error(f"Error getting CSV path: {str(e)}")
            return None
    
    def _query_needs_math(self, query: str) -> bool:
        """Determine if query needs mathematical operations."""
        math_keywords = [
            'calculate', 'sum', 'average', 'mean', 'total', 'count',
            'maximum', 'minimum', 'percentage', 'ratio', 'compare numbers',
            'statistics', 'analysis', 'trend', 'correlation'
        ]
        
        query_lower = query.lower()
        return any(keyword in query_lower for keyword in math_keywords)
