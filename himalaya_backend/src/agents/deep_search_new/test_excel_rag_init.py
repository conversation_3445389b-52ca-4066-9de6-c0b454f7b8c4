#!/usr/bin/env python3
"""
Test script to check Excel RAG initialization
"""

import sys
import os

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))


def test_langchain_experimental():
    """Test if langchain_experimental is available"""
    print("🧪 Testing langchain_experimental availability...")
    
    try:
        from langchain_experimental.agents import create_pandas_dataframe_agent
        from langchain.agents.agent_types import AgentType
        print("✅ langchain_experimental imported successfully")
        return True
    except ImportError as e:
        print(f"❌ langchain_experimental import failed: {e}")
        return False


def test_excel_rag_import():
    """Test Excel RAG import"""
    print("\n🧪 Testing Excel RAG import...")
    
    try:
        from agents.csv.excel_langchain_rag import ExcelLangChainRAG
        print("✅ ExcelLangChainRAG imported successfully")
        return True
    except ImportError as e:
        print(f"❌ ExcelLangChainRAG import failed: {e}")
        return False


def test_excel_rag_initialization():
    """Test Excel RAG initialization"""
    print("\n🧪 Testing Excel RAG initialization...")
    
    try:
        from agents.csv.excel_langchain_rag import ExcelLangChainRAG
        
        print("   Attempting to initialize ExcelLangChainRAG...")
        excel_rag = ExcelLangChainRAG(verbose=True)
        print("✅ ExcelLangChainRAG initialized successfully")
        
        # Test a simple method
        print("   Testing basic functionality...")
        # Don't actually process a file, just check if the object is working
        print(f"   Excel RAG object created: {type(excel_rag)}")
        return True
        
    except Exception as e:
        print(f"❌ ExcelLangChainRAG initialization failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_excel_processor_initialization():
    """Test Excel processor initialization"""
    print("\n🧪 Testing Excel processor initialization...")
    
    try:
        from agents.deep_search_new.excel_processor import ExcelProcessor
        
        print("   Attempting to initialize ExcelProcessor...")
        excel_processor = ExcelProcessor(verbose=True)
        print("✅ ExcelProcessor initialized successfully")
        
        # Check what was initialized
        if excel_processor.excel_rag is not None:
            print("   ✅ Excel RAG system is available")
        elif hasattr(excel_processor, 'basic_excel') and excel_processor.basic_excel is not None:
            print("   ⚠️ Using basic Excel processor (fallback)")
        else:
            print("   ❌ No Excel processing system available")
        
        return True
        
    except Exception as e:
        print(f"❌ ExcelProcessor initialization failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_config_settings():
    """Test configuration settings"""
    print("\n🧪 Testing configuration settings...")
    
    try:
        from config.settings import (
            AZURE_OPENAI_KEY, AZURE_OPENAI_API_VERSION, AZURE_OPENAI_ENDPOINT, 
            AZURE_OPENAI_DEPLOYMENT_NAME
        )
        
        print(f"   AZURE_OPENAI_ENDPOINT: {AZURE_OPENAI_ENDPOINT[:50] if AZURE_OPENAI_ENDPOINT else 'Not set'}...")
        print(f"   AZURE_OPENAI_DEPLOYMENT_NAME: {AZURE_OPENAI_DEPLOYMENT_NAME}")
        print(f"   AZURE_OPENAI_KEY: {'Set' if AZURE_OPENAI_KEY else 'Not set'}")
        print(f"   AZURE_OPENAI_API_VERSION: {AZURE_OPENAI_API_VERSION}")
        
        if AZURE_OPENAI_KEY and AZURE_OPENAI_ENDPOINT and AZURE_OPENAI_DEPLOYMENT_NAME:
            print("✅ Azure OpenAI configuration appears complete")
            return True
        else:
            print("❌ Azure OpenAI configuration incomplete")
            return False
            
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        return False


def main():
    """Run all tests"""
    print("🚀 Starting Excel RAG initialization tests...")
    
    # Test each component
    langchain_success = test_langchain_experimental()
    import_success = test_excel_rag_import()
    config_success = test_config_settings()
    excel_rag_success = test_excel_rag_initialization()
    excel_processor_success = test_excel_processor_initialization()
    
    print(f"\n✅ Test Results:")
    print(f"   langchain_experimental: {'✅ PASS' if langchain_success else '❌ FAIL'}")
    print(f"   Excel RAG Import: {'✅ PASS' if import_success else '❌ FAIL'}")
    print(f"   Configuration: {'✅ PASS' if config_success else '❌ FAIL'}")
    print(f"   Excel RAG Init: {'✅ PASS' if excel_rag_success else '❌ FAIL'}")
    print(f"   Excel Processor Init: {'✅ PASS' if excel_processor_success else '❌ FAIL'}")
    
    if all([langchain_success, import_success, config_success, excel_rag_success, excel_processor_success]):
        print(f"\n🎉 All tests passed! Excel RAG should be working.")
    else:
        print(f"\n⚠️ Some tests failed. Check the error messages above.")


if __name__ == "__main__":
    main()
