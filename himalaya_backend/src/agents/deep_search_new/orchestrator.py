"""
Deep Search Orchestrator

Main orchestrator that coordinates all components and implements the complete
workflow as specified in the requirements.
"""

import logging
import time
from typing import Dict, List, Any, Optional

from .agent import DeepSearchNewAgent
from .state import DeepSearchNewState

logger = logging.getLogger(__name__)


class DeepSearchOrchestrator:
    """
    Main orchestrator for the deep search new system.
    
    Provides a high-level interface for deep search operations and coordinates
    all the underlying components according to the specified workflow.
    """
    
    def __init__(self, verbose: bool = False):
        """
        Initialize the deep search orchestrator.
        
        Args:
            verbose: Whether to enable verbose logging
        """
        self.verbose = verbose
        self.agent = DeepSearchNewAgent(verbose=verbose)
    
    async def process_deep_search_query(self,
                                      query: str,
                                      selected_file_ids: List[int],
                                      conversation_history: Optional[str] = None,
                                      conversation_summary: Optional[str] = None,
                                      user_context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Process a deep search query with the complete workflow.
        
        This is the main entry point for deep search operations.
        
        Args:
            query: User query
            selected_file_ids: List of file IDs selected by user
            conversation_history: Previous conversation context
            conversation_summary: Summary of conversation
            user_context: Additional user context
            
        Returns:
            Complete deep search result
        """
        if self.verbose:
            print(f"\n🚀 DEEP SEARCH ORCHESTRATOR: Starting deep search")
            print(f"   Query: {query}")
            print(f"   Selected Files: {len(selected_file_ids)}")
            print(f"   Deep Search Mode: ENABLED")
        
        start_time = time.time()
        
        try:
            # Validate inputs
            if not query or not query.strip():
                raise ValueError("Query cannot be empty")
            
            if not selected_file_ids:
                raise ValueError("No files selected for deep search")
            
            # Extract blob names from user context if available
            blob_names = None
            if user_context and 'blob_names' in user_context:
                blob_names = user_context['blob_names']

            # Process the query using the deep search agent
            result = await self.agent.process_query(
                query=query,
                selected_file_ids=selected_file_ids,
                blob_names=blob_names,
                conversation_history=conversation_history,
                conversation_summary=conversation_summary,
                user_context=user_context  # Pass user_context for validation mode
            )
            
            # Enhance result with orchestrator metadata
            total_time = time.time() - start_time
            
            enhanced_result = {
                **result,
                "orchestrator_metadata": {
                    "total_processing_time": total_time,
                    "files_processed": len(selected_file_ids),
                    "deep_search_mode": True,
                    "workflow_version": "deep_search_new_v1.0",
                    "user_context": user_context or {}
                },
                "success": True,
                "status": "completed"
            }
            
            if self.verbose:
                print(f"✅ DEEP SEARCH ORCHESTRATOR: Completed in {total_time:.2f}s")
                print(f"   Answer length: {len(result.get('answer', ''))} characters")
                print(f"   References: {len(result.get('references', []))} files")
            
            return enhanced_result
            
        except Exception as e:
            error_msg = f"Deep search orchestration failed: {str(e)}"
            logger.error(error_msg)
            
            return {
                "answer": f"Deep search failed: {error_msg}",
                "references": [],
                "success": False,
                "status": "error",
                "error_message": error_msg,
                "processing_time": time.time() - start_time,
                "orchestrator_metadata": {
                    "total_processing_time": time.time() - start_time,
                    "files_processed": 0,
                    "deep_search_mode": True,
                    "workflow_version": "deep_search_new_v1.0",
                    "error": str(e)
                }
            }
    
    async def health_check(self) -> Dict[str, Any]:
        """
        Perform a health check of the deep search system.
        
        Returns:
            Health check results
        """
        if self.verbose:
            print("🔍 DEEP SEARCH ORCHESTRATOR: Performing health check...")
        
        health_status = {
            "status": "healthy",
            "components": {},
            "timestamp": time.time(),
            "version": "deep_search_new_v1.0"
        }
        
        try:
            # Check agent initialization
            if self.agent:
                health_status["components"]["agent"] = "healthy"
            else:
                health_status["components"]["agent"] = "unhealthy"
                health_status["status"] = "degraded"
            
            # Check individual services
            services_to_check = [
                ("file_content_service", self.agent.file_content_service),
                ("planner_agent", self.agent.planner_agent),
                ("summarization_service", self.agent.summarization_service),
                ("excel_processor", self.agent.excel_processor),
                ("non_excel_processor", self.agent.non_excel_processor),
                ("context_validator", self.agent.context_validator),
                ("synthesis_engine", self.agent.synthesis_engine)
            ]
            
            for service_name, service in services_to_check:
                if service:
                    health_status["components"][service_name] = "healthy"
                else:
                    health_status["components"][service_name] = "unhealthy"
                    health_status["status"] = "degraded"
            
            # Check LLM connectivity (basic check)
            try:
                if hasattr(self.agent.planner_agent, 'llm') and self.agent.planner_agent.llm:
                    health_status["components"]["llm_connectivity"] = "healthy"
                else:
                    health_status["components"]["llm_connectivity"] = "unknown"
            except Exception:
                health_status["components"]["llm_connectivity"] = "unhealthy"
                health_status["status"] = "degraded"
            
            if self.verbose:
                print(f"✅ DEEP SEARCH ORCHESTRATOR: Health check completed - {health_status['status']}")
            
            return health_status
            
        except Exception as e:
            logger.error(f"Health check failed: {str(e)}")
            return {
                "status": "unhealthy",
                "error": str(e),
                "timestamp": time.time(),
                "version": "deep_search_new_v1.0"
            }
    
    async def get_processing_capabilities(self) -> Dict[str, Any]:
        """
        Get information about the processing capabilities of the system.
        
        Returns:
            Capabilities information
        """
        return {
            "supported_file_types": {
                "excel": ["xlsx", "xls"],
                "csv": ["csv"],
                "pdf": ["pdf"],
                "word": ["docx", "doc"],
                "text": ["txt"],
                "other": ["various"]
            },
            "processing_strategies": {
                "excel_csv": [
                    "summary_only",
                    "mathematical",
                    "full_content",
                    "rag_based"
                ],
                "non_excel": [
                    "summary_only",
                    "full_content",
                    "rag_based"
                ]
            },
            "query_types": [
                "summary",
                "questions_all_files",
                "rag_on_files",
                "comparison",
                "analysis"
            ],
            "features": [
                "file_centric_processing",
                "context_only_validation",
                "adaptive_chunking",
                "mathematical_operations",
                "multi_file_synthesis",
                "reference_tracking"
            ],
            "limits": {
                "max_files": 25,
                "max_chunk_size_words": 40000,
                "chunk_overlap_words": 200,
                "max_query_length": 2000
            }
        }
    
    def get_workflow_info(self) -> Dict[str, Any]:
        """
        Get information about the deep search workflow.
        
        Returns:
            Workflow information
        """
        return {
            "workflow_steps": [
                {
                    "step": 1,
                    "name": "organize_files",
                    "description": "Organize files into Excel and non-Excel categories"
                },
                {
                    "step": 2,
                    "name": "generate_summaries",
                    "description": "Generate summaries for all files"
                },
                {
                    "step": 3,
                    "name": "plan_processing",
                    "description": "Determine processing strategy using planner agent"
                },
                {
                    "step": 4,
                    "name": "process_excel_files",
                    "description": "Process Excel/CSV files with appropriate strategy"
                },
                {
                    "step": 5,
                    "name": "process_non_excel_files",
                    "description": "Process non-Excel files with appropriate strategy"
                },
                {
                    "step": 6,
                    "name": "validate_context",
                    "description": "Validate answers against source context"
                },
                {
                    "step": 7,
                    "name": "synthesize_answer",
                    "description": "Synthesize final answer from all results"
                }
            ],
            "processing_principles": [
                "File-centric processing (not traditional RAG)",
                "Full content awareness for selected files",
                "Separate Excel/CSV and non-Excel pipelines",
                "Planner-driven query classification",
                "Context-only validation",
                "Unique reference tracking"
            ]
        }


# Convenience function for easy import and usage
async def create_deep_search_orchestrator(verbose: bool = False) -> DeepSearchOrchestrator:
    """
    Create and return a deep search orchestrator instance.
    
    Args:
        verbose: Whether to enable verbose logging
        
    Returns:
        Configured deep search orchestrator
    """
    return DeepSearchOrchestrator(verbose=verbose)


# Quick processing function for simple use cases
async def quick_deep_search(query: str,
                          selected_file_ids: List[int],
                          verbose: bool = False) -> Dict[str, Any]:
    """
    Quick deep search processing function.
    
    Args:
        query: User query
        selected_file_ids: List of file IDs to process
        verbose: Whether to enable verbose logging
        
    Returns:
        Deep search result
    """
    orchestrator = await create_deep_search_orchestrator(verbose=verbose)
    return await orchestrator.process_deep_search_query(query, selected_file_ids)
