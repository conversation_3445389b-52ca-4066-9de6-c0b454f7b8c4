"""
Deep Search New Module

A file-centric deep search system that processes selected files with full content awareness.
Handles Excel/CSV files separately from non-Excel files with appropriate processing strategies.

Key Features:
- File-centric processing (not traditional RAG)
- Separate Excel/CSV and non-Excel pipelines
- Planner-driven query classification
- Context-only validation
- Full content summarization and analysis
"""

from .agent import DeepSearchNewAgent
from .orchestrator import DeepSearchOrchestrator

__all__ = [
    'DeepSearchNewAgent',
    'DeepSearchOrchestrator'
]
