"""
Deep Search New Agent

Main agent class for the file-centric deep search system.
"""

import logging
import time
from typing import Dict, List, Any, Optional

from langgraph.graph import StateGraph, END
from langchain_core.messages import SystemMessage, HumanMessage

from .state import DeepSearchNewState, FileType, QueryType, ProcessingStrategy

logger = logging.getLogger(__name__)


class DeepSearchNewAgent:
    """
    Main agent for file-centric deep search processing.
    
    This agent coordinates the entire deep search workflow:
    1. File organization and content retrieval
    2. Query planning and strategy determination
    3. File-specific processing (Excel vs non-Excel)
    4. Context validation
    5. Answer synthesis
    """
    
    def __init__(self, verbose: bool = False):
        """
        Initialize the Deep Search New Agent.
        
        Args:
            verbose: Whether to enable verbose logging
        """
        self.verbose = verbose
        
        # Initialize services (lazy loading to avoid circular imports)
        self.file_content_service = None
        self.planner_agent = None
        self.summarization_service = None
        self.excel_processor = None
        self.non_excel_processor = None
        self.context_validator = None
        self.synthesis_engine = None
        
        # Create the processing graph
        self.graph = self._create_graph()

    def _initialize_services(self):
        """Initialize services with lazy loading."""
        if self.file_content_service is None:
            from .file_content_service import FileContentService
            self.file_content_service = FileContentService(verbose=self.verbose)

        if self.planner_agent is None:
            from .planner_agent import PlannerAgent
            self.planner_agent = PlannerAgent(verbose=self.verbose)

        if self.summarization_service is None:
            from .summarization_service import SummarizationService
            self.summarization_service = SummarizationService(verbose=self.verbose)

        if self.excel_processor is None:
            from .excel_processor import ExcelProcessor
            self.excel_processor = ExcelProcessor(verbose=self.verbose)

        if self.non_excel_processor is None:
            from .non_excel_processor import NonExcelProcessor
            self.non_excel_processor = NonExcelProcessor(verbose=self.verbose)

        if self.context_validator is None:
            from .context_validator import ContextValidator
            self.context_validator = ContextValidator(verbose=self.verbose)

        if self.synthesis_engine is None:
            from .synthesis_engine import SynthesisEngine
            self.synthesis_engine = SynthesisEngine(verbose=self.verbose)
    
    def _create_graph(self) -> StateGraph:
        """Create the LangGraph processing workflow."""
        graph = StateGraph(DeepSearchNewState)
        
        # Add nodes
        graph.add_node("organize_files", self.organize_files)
        graph.add_node("generate_summaries", self.generate_summaries)
        graph.add_node("plan_processing", self.plan_processing)
        graph.add_node("process_excel_files", self.process_excel_files)
        graph.add_node("process_non_excel_files", self.process_non_excel_files)
        graph.add_node("validate_context", self.validate_context)
        graph.add_node("synthesize_answer", self.synthesize_answer)
        
        # Add edges
        graph.add_edge("organize_files", "generate_summaries")
        graph.add_edge("generate_summaries", "plan_processing")
        graph.add_edge("plan_processing", "process_excel_files")
        graph.add_edge("process_excel_files", "process_non_excel_files")
        graph.add_edge("process_non_excel_files", "validate_context")
        graph.add_edge("validate_context", "synthesize_answer")
        graph.add_edge("synthesize_answer", END)
        
        # Set entry point
        graph.set_entry_point("organize_files")
        
        return graph.compile()
    
    async def process_query(self,
                          query: str,
                          selected_file_ids: Optional[List[int]] = None,
                          blob_names: Optional[List[str]] = None,
                          conversation_history: Optional[str] = None,
                          conversation_summary: Optional[str] = None,
                          user_context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Process a deep search query with selected files.

        Args:
            query: The user query
            selected_file_ids: List of file IDs to process (optional)
            blob_names: List of blob names to process (optional)
            conversation_history: Previous conversation context
            conversation_summary: Summary of conversation

        Returns:
            Processing result with answer and metadata
        """
        start_time = time.time()

        # Validate inputs
        if not selected_file_ids and not blob_names:
            raise ValueError("Either selected_file_ids or blob_names must be provided")

        # Initialize services
        self._initialize_services()

        # Determine which input method to use
        if blob_names:
            file_count = len(blob_names)
            input_type = "blob_names"
        else:
            file_count = len(selected_file_ids)
            input_type = "file_ids"

        if self.verbose:
            print(f"\n🚀 DEEP SEARCH NEW: Starting query processing")
            print(f"   Query: {query}")
            print(f"   Files: {file_count} selected ({input_type})")

        # Initialize state
        initial_state = DeepSearchNewState(
            query=query,
            selected_file_ids=selected_file_ids or [],
            blob_names=blob_names or [],
            conversation_history=conversation_history,
            conversation_summary=conversation_summary,
            user_context=user_context,  # Pass user_context for multi-agent coordination
            excel_files=[],
            non_excel_files=[],
            file_summaries={},
            planner_decision=None,
            excel_results=[],
            non_excel_results=[],
            validation_results=[],
            final_answer=None,
            all_references=[],
            processing_time=0.0,
            error_messages=[],
            debug_info={}
        )
        
        try:
            # Run the processing graph
            final_state = await self.graph.ainvoke(initial_state)
            
            # Calculate total processing time
            total_time = time.time() - start_time
            final_state["processing_time"] = total_time
            
            if self.verbose:
                print(f"✅ DEEP SEARCH NEW: Processing completed in {total_time:.2f}s")
            
            return {
                "answer": final_state.get("final_answer", "No answer generated"),
                "references": {
                    "deep_search_references": final_state.get("all_references", [])
                },
                "processing_time": total_time,
                "planner_decision": final_state.get("planner_decision"),
                "excel_results": final_state.get("excel_results", []),
                "non_excel_results": final_state.get("non_excel_results", []),
                "validation_results": final_state.get("validation_results", []),
                "error_messages": final_state.get("error_messages", []),
                "debug_info": final_state.get("debug_info", {})
            }
            
        except Exception as e:
            error_msg = f"Deep search processing failed: {str(e)}"
            logger.error(error_msg)
            
            return {
                "answer": f"Error: {error_msg}",
                "references": {
                    "deep_search_references": []
                },
                "processing_time": time.time() - start_time,
                "error_messages": [error_msg],
                "debug_info": {"error": str(e)}
            }
    
    async def organize_files(self, state: DeepSearchNewState) -> DeepSearchNewState:
        """Organize files into Excel and non-Excel categories."""
        if self.verbose:
            print("\n📁 DEEP SEARCH NEW: Organizing files...")

        try:
            # Determine which method to use based on available data
            if state["blob_names"]:
                # Use blob names
                excel_files, non_excel_files = await self.file_content_service.organize_files_from_blob_names(
                    state["blob_names"]
                )
            else:
                # Use file IDs
                excel_files, non_excel_files = await self.file_content_service.organize_files(
                    state["selected_file_ids"]
                )

            return {
                **state,
                "excel_files": excel_files,
                "non_excel_files": non_excel_files
            }

        except Exception as e:
            error_msg = f"File organization failed: {str(e)}"
            logger.error(error_msg)
            return {
                **state,
                "error_messages": state["error_messages"] + [error_msg]
            }
    
    async def generate_summaries(self, state: DeepSearchNewState) -> DeepSearchNewState:
        """Generate summaries for all files."""
        if self.verbose:
            print("\n📝 DEEP SEARCH NEW: Generating file summaries...")
        
        try:
            file_summaries = await self.summarization_service.generate_all_summaries(
                excel_files=state["excel_files"],
                non_excel_files=state["non_excel_files"]
            )

            # Update file_info objects with their summaries
            for file_info in state["excel_files"]:
                file_id = file_info['file_id']
                if file_id in file_summaries:
                    file_info['summary'] = file_summaries[file_id]

            for file_info in state["non_excel_files"]:
                file_id = file_info['file_id']
                if file_id in file_summaries:
                    file_info['summary'] = file_summaries[file_id]

            return {
                **state,
                "file_summaries": file_summaries
            }
            
        except Exception as e:
            error_msg = f"Summary generation failed: {str(e)}"
            logger.error(error_msg)
            return {
                **state,
                "error_messages": state["error_messages"] + [error_msg]
            }
    
    async def plan_processing(self, state: DeepSearchNewState) -> DeepSearchNewState:
        """Plan the processing strategy using the planner agent."""
        if self.verbose:
            print("\n🧠 DEEP SEARCH NEW: Planning processing strategy...")
        
        try:
            planner_decision = await self.planner_agent.plan_processing(
                query=state["query"],
                file_summaries=state["file_summaries"],
                conversation_history=state.get("conversation_history"),
                conversation_summary=state.get("conversation_summary"),
                excel_files=state["excel_files"],
                non_excel_files=state["non_excel_files"]
            )
            
            return {
                **state,
                "planner_decision": planner_decision
            }
            
        except Exception as e:
            error_msg = f"Processing planning failed: {str(e)}"
            logger.error(error_msg)
            return {
                **state,
                "error_messages": state["error_messages"] + [error_msg]
            }
    
    async def process_excel_files(self, state: DeepSearchNewState) -> DeepSearchNewState:
        """Process Excel/CSV files."""
        if self.verbose:
            print("\n📊 DEEP SEARCH NEW: Processing Excel/CSV files...")
        
        try:
            excel_results = await self.excel_processor.process_files(
                excel_files=state["excel_files"],
                query=state["query"],
                planner_decision=state["planner_decision"]
            )
            
            return {
                **state,
                "excel_results": excel_results
            }
            
        except Exception as e:
            error_msg = f"Excel processing failed: {str(e)}"
            logger.error(error_msg)
            return {
                **state,
                "error_messages": state["error_messages"] + [error_msg]
            }
    
    async def process_non_excel_files(self, state: DeepSearchNewState) -> DeepSearchNewState:
        """Process non-Excel files."""
        if self.verbose:
            print("\n📄 DEEP SEARCH NEW: Processing non-Excel files...")
        
        try:
            non_excel_results = await self.non_excel_processor.process_files(
                non_excel_files=state["non_excel_files"],
                query=state["query"],
                planner_decision=state["planner_decision"]
            )
            
            return {
                **state,
                "non_excel_results": non_excel_results
            }
            
        except Exception as e:
            error_msg = f"Non-Excel processing failed: {str(e)}"
            logger.error(error_msg)
            return {
                **state,
                "error_messages": state["error_messages"] + [error_msg]
            }
    
    async def validate_context(self, state: DeepSearchNewState) -> DeepSearchNewState:
        """Validate that answers are based only on provided context."""
        if self.verbose:
            print("\n✅ DEEP SEARCH NEW: Validating context adherence...")

        try:
            # Check if we're in multi-agent mode with lenient validation
            user_context = state.get("user_context", {})
            validation_mode = user_context.get("validation_mode", "strict")
            multi_agent_mode = user_context.get("multi_agent_mode", False)

            if multi_agent_mode and validation_mode == "lenient":
                if self.verbose:
                    print("✅ DEEP SEARCH NEW: Skipping strict validation (multi-agent lenient mode)")
                    print("   Reason: Content extraction for other agents - validation will be done at Universal level")

                # Create lenient validation results - mark all as valid for content extraction
                validation_results = []
                for result in state["excel_results"] + state["non_excel_results"]:
                    validation_results.append({
                        'is_valid': True,
                        'validated_answer': result['answer'],
                        'removed_content': [],
                        'confidence_score': 0.9
                    })
            else:
                # Use strict validation as normal
                validation_results = await self.context_validator.validate_answers(
                    excel_results=state["excel_results"],
                    non_excel_results=state["non_excel_results"],
                    file_summaries=state["file_summaries"]
                )

            return {
                **state,
                "validation_results": validation_results
            }
            
        except Exception as e:
            error_msg = f"Context validation failed: {str(e)}"
            logger.error(error_msg)
            return {
                **state,
                "error_messages": state["error_messages"] + [error_msg]
            }
    
    async def synthesize_answer(self, state: DeepSearchNewState) -> DeepSearchNewState:
        """Synthesize final answer from all processing results."""
        if self.verbose:
            print("\n🔄 DEEP SEARCH NEW: Synthesizing final answer...")
        
        try:
            final_answer, all_references = await self.synthesis_engine.synthesize(
                excel_results=state["excel_results"],
                non_excel_results=state["non_excel_results"],
                validation_results=state["validation_results"],
                query=state["query"]
            )
            
            return {
                **state,
                "final_answer": final_answer,
                "all_references": all_references
            }
            
        except Exception as e:
            error_msg = f"Answer synthesis failed: {str(e)}"
            logger.error(error_msg)
            return {
                **state,
                "error_messages": state["error_messages"] + [error_msg],
                "final_answer": f"Error synthesizing answer: {str(e)}"
            }
