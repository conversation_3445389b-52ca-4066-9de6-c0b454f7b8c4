"""
File Summarization Service

Creates summaries for each file - for Excel files uses existing EDA summaries,
for non-Excel files creates comprehensive summaries from full content.
"""

import logging
from typing import Dict, List, Any, Optional

from langchain_openai import AzureChatOpenAI
from langchain_core.messages import SystemMessage, HumanMessage
from sqlalchemy.orm import sessionmaker

from config.settings import (
    AZURE_OPENAI_KEY, AZURE_OPENAI_ENDPOINT, AZURE_OPENAI_API_VERSION,
    AZURE_OPENAI_DEPLOYMENT_NAME
)
from .state import FileInfo, FileType

#comeback : why this database thing here, this is a non existant thing.
try:
    from database.database import get_db_session
    from models.models import CSVFile
    from services.azure_search_service import AzureSearchService
    DB_AVAILABLE = True
except ImportError:
    DB_AVAILABLE = False
    get_db_session = None
    CSVFile = None
    AzureSearchService = None

logger = logging.getLogger(__name__)


class SummarizationService:
    """
    Service for generating comprehensive summaries of files.
    
    For Excel/CSV files: Uses existing EDA summaries from the vector store
    For non-Excel files: Generates summaries from full content
    """
    
    def __init__(self, verbose: bool = False):
        """
        Initialize the summarization service.
        
        Args:
            verbose: Whether to enable verbose logging
        """
        self.verbose = verbose
        
        # Initialize Azure OpenAI client
        self.llm = AzureChatOpenAI(
            azure_endpoint=AZURE_OPENAI_ENDPOINT,
            api_key=AZURE_OPENAI_KEY,
            api_version=AZURE_OPENAI_API_VERSION,
            deployment_name=AZURE_OPENAI_DEPLOYMENT_NAME,
            temperature=0.1,
            max_tokens=2000
        )
        
        self.search_service = AzureSearchService() if DB_AVAILABLE else None
    
    async def generate_all_summaries(self,
                                   excel_files: List[FileInfo],
                                   non_excel_files: List[FileInfo]) -> Dict[int, str]:
        """
        Generate summaries for all files.
        
        Args:
            excel_files: List of Excel/CSV files
            non_excel_files: List of non-Excel files
            
        Returns:
            Dictionary mapping file_id to summary
        """
        if self.verbose:
            print(f"📝 SUMMARIZATION SERVICE: Generating summaries...")
            print(f"   Excel files: {len(excel_files)}")
            print(f"   Non-Excel files: {len(non_excel_files)}")
        
        summaries = {}
        
        # Process Excel files
        for file_info in excel_files:
            try:
                summary = await self._generate_excel_summary(file_info)
                summaries[file_info['file_id']] = summary
                
                if self.verbose:
                    print(f"   ✅ Excel summary: {file_info['file_name']}")
                    
            except Exception as e:
                logger.error(f"Failed to generate Excel summary for {file_info['file_name']}: {str(e)}")
                summaries[file_info['file_id']] = f"Error generating summary: {str(e)}"
        
        # Process non-Excel files
        for file_info in non_excel_files:
            try:
                summary = await self._generate_non_excel_summary(file_info)
                summaries[file_info['file_id']] = summary
                
                if self.verbose:
                    print(f"   ✅ Non-Excel summary: {file_info['file_name']}")
                    
            except Exception as e:
                logger.error(f"Failed to generate non-Excel summary for {file_info['file_name']}: {str(e)}")
                summaries[file_info['file_id']] = f"Error generating summary: {str(e)}"
        
        if self.verbose:
            print(f"✅ SUMMARIZATION SERVICE: Generated {len(summaries)} summaries")
        
        return summaries
    
    async def _generate_excel_summary(self, file_info: FileInfo) -> str:
        """Generate summary for Excel/CSV files using Azure Search vector store."""
        try:
            # Try to get existing summary from vector store first
            summary = await self._get_excel_summary_from_vector_store(file_info)
            if summary:
                return summary

            # If no specific summary found, generate from available content
            if file_info.get('content'):
                return await self._generate_summary_from_content(
                    file_info['content'],
                    f"Excel/CSV file: {file_info['file_name']}"
                )

            # Last resort: return basic file info
            return f"Excel/CSV file: {file_info['file_name']} - Contains structured data and analysis"

        except Exception as e:
            logger.error(f"Error generating Excel summary: {str(e)}")
            return f"Excel/CSV file: {file_info['file_name']} (Error generating summary)"
    
    async def _get_excel_summary_from_vector_store(self, file_info: FileInfo) -> Optional[str]:
        """Try to get Excel summary from vector store."""
        try:
            if not self.search_service:
                return None

            # Search for content from this specific file using multiple approaches
            all_chunks = []

            # Try searching by parent_id first
            try:
                parent_id_results = self.search_service.search_client.search(
                    search_text="*",
                    filter=f"parent_id eq 'file_{file_info['file_id']}'",
                    select=["chunk", "chunk_id", "title"],
                    top=20  # Get more chunks to find summaries
                )
                all_chunks.extend(list(parent_id_results))
            except:
                pass

            # If no results by parent_id, try by blob name
            if not all_chunks:
                try:
                    blob_results = self.search_service.search_client.search(
                        search_text="*",
                        filter=f"metadata_storage_name eq '{file_info['blob_name']}'",
                        select=["chunk", "chunk_id", "title"],
                        top=20
                    )
                    all_chunks.extend(list(blob_results))
                except:
                    pass

            if not all_chunks:
                return None

            # Look for summary-like content or use first few chunks
            summary_chunks = []
            regular_chunks = []

            for result in all_chunks:
                chunk_content = result.get('chunk', '')
                if chunk_content:
                    # Check if this looks like a summary
                    if any(keyword in chunk_content.lower() for keyword in
                          ['summary', 'analysis', 'overview', 'key findings', 'conclusion', 'insights']):
                        summary_chunks.append(chunk_content)
                    else:
                        regular_chunks.append(chunk_content)

            # Use summary chunks if available, otherwise use first few regular chunks
            content_to_summarize = summary_chunks if summary_chunks else regular_chunks[:3]

            if content_to_summarize:
                combined_content = "\n".join(content_to_summarize)
                return await self._generate_summary_from_content(
                    combined_content,
                    f"Excel/CSV file: {file_info['file_name']}"
                )

            return None

        except Exception as e:
            logger.error(f"Error getting Excel summary from vector store: {str(e)}")
            return None
    
    async def _generate_non_excel_summary(self, file_info: FileInfo) -> str:
        """Generate summary for non-Excel files from full content."""
        try:
            content = file_info.get('content', '')

            if self.verbose:
                print(f"      Summarizing {file_info['file_name']}: {len(content)} characters")

            if not content:
                if self.verbose:
                    print(f"      ⚠️ No content available for {file_info['file_name']}")
                return f"Document: {file_info['file_name']} (No content available)"
            
            # Generate comprehensive summary from full content
            if self.verbose:
                print(f"      🤖 Generating AI summary for {file_info['file_name']}...")

            summary = await self._generate_summary_from_content(
                content,
                f"{file_info['file_type'].value.upper()} document: {file_info['file_name']}"
            )

            if self.verbose:
                print(f"      ✅ Generated summary ({len(summary)} characters)")

            return summary
            
        except Exception as e:
            logger.error(f"Error generating non-Excel summary: {str(e)}")
            return f"Document: {file_info['file_name']} (Error generating summary)"
    
    async def _generate_summary_from_content(self, content: str, file_description: str) -> str:
        """Generate summary from content using LLM."""
        try:
            # Truncate content if too long (keep first 8000 words for summary)
            words = content.split()
            if len(words) > 8000:
                content = ' '.join(words[:8000]) + "\n\n[Content truncated for summary generation]"
            
            system_prompt = """You are an expert document summarizer. Create comprehensive, informative summaries that capture:

1. Document type and purpose
2. Key topics and themes
3. Main findings or conclusions
4. Important data points or statistics
5. Structure and organization
6. Relevance for potential queries

Keep summaries concise but comprehensive (200-400 words). Focus on content that would be useful for answering user questions."""
            
            user_prompt = f"""Please create a comprehensive summary of this {file_description}:

CONTENT:
{content}

Provide a summary that captures the essential information and would help users understand what this document contains and how it might answer their questions."""
            
            messages = [
                SystemMessage(content=system_prompt),
                HumanMessage(content=user_prompt)
            ]
            
            response = await self.llm.ainvoke(messages)
            return response.content.strip()
            
        except Exception as e:
            logger.error(f"Error generating summary from content: {str(e)}")
            if self.verbose:
                print(f"      ❌ LLM summary generation failed: {str(e)}")
            return f"{file_description} (Error generating detailed summary: {str(e)})"
    
    async def generate_single_file_summary(self, file_info: FileInfo) -> str:
        """
        Generate summary for a single file.
        
        Args:
            file_info: File information
            
        Returns:
            File summary
        """
        if file_info['file_type'] in [FileType.EXCEL, FileType.CSV]:
            return await self._generate_excel_summary(file_info)
        else:
            return await self._generate_non_excel_summary(file_info)
