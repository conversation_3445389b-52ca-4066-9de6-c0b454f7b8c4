#!/usr/bin/env python3
"""
Test script to verify CSV links population in Deep Search New
"""

import asyncio
import sys
import os

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))

from agents.deep_search_new.file_content_service import FileContentService


async def test_csv_links_population():
    """Test CSV links population for the Amazon Excel file"""
    print("🧪 Testing CSV links population for Amazon Excel file...")
    
    # Initialize file content service
    file_service = FileContentService(verbose=True)
    
    # Test with the specific blob name that was failing
    test_blob_names = [
        "5a36fc68-d96a-464b-a0df-b45c199ab9a6_20250703_192910_Amazon_Customer_Behavior_Survey_1.xlsx"
    ]
    
    try:
        print(f"\n📁 Organizing files from blob names...")
        excel_files, non_excel_files = await file_service.organize_files_from_blob_names(test_blob_names)
        
        print(f"\n📊 Results:")
        print(f"   Excel files found: {len(excel_files)}")
        print(f"   Non-Excel files found: {len(non_excel_files)}")
        
        if excel_files:
            file_info = excel_files[0]
            print(f"\n📄 File Details:")
            print(f"   File ID: {file_info['file_id']}")
            print(f"   File Name: {file_info['file_name']}")
            print(f"   Blob Name: {file_info['blob_name']}")
            print(f"   File Type: {file_info['file_type']}")
            print(f"   Blob URL: {file_info['blob_url']}")
            print(f"   Content Length: {len(file_info.get('content', ''))}")
            print(f"   Chunks Count: {len(file_info.get('chunks', []))}")
            print(f"   CSV Links: {file_info.get('csv_links', [])}")
            
            # Check if chunks have metadata_storage_name
            chunks = file_info.get('chunks', [])
            if chunks:
                first_chunk = chunks[0]
                print(f"\n📄 First Chunk Details:")
                print(f"   Chunk ID: {first_chunk.get('chunk_id', 'N/A')}")
                print(f"   Content Preview: {first_chunk.get('content', '')[:100]}...")
                chunk_metadata = first_chunk.get('metadata', {})
                print(f"   Metadata Storage Name: {chunk_metadata.get('metadata_storage_name', 'N/A')}")
            
            # Test CSV links population
            csv_links = file_info.get('csv_links', [])
            if csv_links:
                print(f"\n✅ CSV Links populated successfully!")
                for i, link in enumerate(csv_links):
                    print(f"   Link {i+1}: {link}")
                return True
            else:
                print(f"\n❌ CSV Links not populated!")
                return False
        else:
            print(f"\n❌ No Excel files found!")
            return False
            
    except Exception as e:
        print(f"❌ Error during test: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


async def test_database_query():
    """Test database query directly"""
    print("\n🧪 Testing database query directly...")
    
    try:
        from models.models import File, CSVFile
        
        # Test the exact blob name from the logs
        blob_name = "5a36fc68-d96a-464b-a0df-b45c199ab9a6_20250703_192910_Amazon_Customer_Behavior_Survey_1.xlsx"
        
        print(f"   Looking for file with blob_name: {blob_name}")
        file_record = File.query.filter_by(blob_name=blob_name).first()
        
        if file_record:
            print(f"   ✅ Found file record:")
            print(f"      ID: {file_record.id}")
            print(f"      Name: {file_record.file_name}")
            print(f"      Blob Name: {file_record.blob_name}")
            print(f"      Blob URL: {file_record.blob_url}")
            return True
        else:
            print(f"   ❌ No file record found!")
            
            # Show some sample files
            print(f"   📄 Sample files in database:")
            sample_files = File.query.limit(10).all()
            for f in sample_files:
                print(f"      ID: {f.id}, Name: {f.file_name}")
                print(f"      Blob Name: {f.blob_name}")
                print()
            return False
            
    except Exception as e:
        print(f"❌ Error during database test: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """Run all tests"""
    print("🚀 Starting CSV links population tests...")
    
    # Test database query first
    db_success = await test_database_query()
    
    # Test CSV links population
    csv_success = await test_csv_links_population()
    
    print(f"\n✅ Test Results:")
    print(f"   Database Query: {'✅ PASS' if db_success else '❌ FAIL'}")
    print(f"   CSV Links Population: {'✅ PASS' if csv_success else '❌ FAIL'}")
    
    if db_success and csv_success:
        print(f"\n🎉 All tests passed! CSV links should now work.")
    else:
        print(f"\n⚠️ Some tests failed. Check the error messages above.")


if __name__ == "__main__":
    asyncio.run(main())
