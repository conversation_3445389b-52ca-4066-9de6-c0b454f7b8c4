"""
Planner Agent for Deep Search New

Analyzes queries, file summaries, and conversation context to determine
query type, processing strategy, and execution plan.
"""

import logging
from typing import Dict, List, Any, Optional

from langchain_openai import AzureChatOpenAI
from langchain_core.messages import SystemMessage, HumanMessage

from config.settings import (
    AZURE_OPENAI_KEY, AZURE_OPENAI_ENDPOINT, AZURE_OPENAI_API_VERSION,
    AZURE_OPENAI_DEPLOYMENT_NAME
)
from .state import PlannerDecision, QueryType, ProcessingStrategy, FileInfo

logger = logging.getLogger(__name__)


class PlannerAgent:
    """
    Planner agent that analyzes queries and determines processing strategies.
    
    This agent makes intelligent decisions about:
    - Query type classification
    - Whether full file content is needed
    - Processing strategy for Excel vs non-Excel files
    - Sequential vs parallel processing approach
    """
    
    def __init__(self, verbose: bool = False):
        """
        Initialize the planner agent.
        
        Args:
            verbose: Whether to enable verbose logging
        """
        self.verbose = verbose
        
        # Initialize Azure OpenAI client
        self.llm = AzureChatOpenAI(
            azure_endpoint=AZURE_OPENAI_ENDPOINT,
            api_key=AZURE_OPENAI_KEY,
            api_version=AZURE_OPENAI_API_VERSION,
            deployment_name=AZURE_OPENAI_DEPLOYMENT_NAME,
            temperature=0.1,
            max_tokens=2000
        )
        
        self.system_prompt = self._get_system_prompt()
    
    def _get_system_prompt(self) -> str:
        """Get the system prompt for the planner agent."""
        return """You are an intelligent query planning agent for a file-centric deep search system.

Your role is to analyze user queries and determine the optimal processing strategy for selected files.

QUERY TYPES:
1. SUMMARY - User wants summaries of files ("summarize", "overview", "what's in these files")
2. QUESTIONS_ALL_FILES - Questions that need information from all files ("compare", "analyze across", "find patterns")
3. RAG_ON_FILES - Specific questions that can be answered through targeted search within files
4. COMPARISON - Direct comparison between files or data points
5. ANALYSIS - Deep analytical questions requiring comprehensive understanding

PROCESSING STRATEGIES:
1. FULL_CONTENT - Use complete file content (for summaries, comprehensive analysis)
2. RAG_BASED - Use targeted retrieval within files (for specific questions)
3. MATHEMATICAL - Use mathematical operations on data (for Excel/CSV calculations)
4. SUMMARY_ONLY - Use only pre-generated summaries (for quick overviews)

EXCEL/CSV PROCESSING:
- For summary questions: Use existing EDA summaries
- For mathematical questions: Use CSV data with mathematical operations
- For data analysis: Combine summaries with targeted mathematical operations

NON-EXCEL PROCESSING:
- For summary questions: Use full content to generate comprehensive summaries
- For specific questions: Use RAG on full content or chunked content
- For analysis: Use full content with analytical processing

PROCESSING APPROACH:
- Sequential: Process files one by one (for complex analysis, comparisons)
- Parallel: Process files simultaneously (for independent summaries, simple questions)

Respond with a JSON object containing:
{
    "query_type": "SUMMARY|QUESTIONS_ALL_FILES|RAG_ON_FILES|COMPARISON|ANALYSIS",
    "whole_file_needed": true|false,
    "excel_strategy": "FULL_CONTENT|RAG_BASED|MATHEMATICAL|SUMMARY_ONLY",
    "non_excel_strategy": "FULL_CONTENT|RAG_BASED|MATHEMATICAL|SUMMARY_ONLY",
    "sequential_processing": true|false,
    "parallel_processing": true|false,
    "reasoning": "Detailed explanation of the decision"
}"""
    
    async def plan_processing(self,
                            query: str,
                            file_summaries: Dict[int, str],
                            conversation_history: Optional[str] = None,
                            conversation_summary: Optional[str] = None,
                            excel_files: List[FileInfo] = None,
                            non_excel_files: List[FileInfo] = None) -> PlannerDecision:
        """
        Plan the processing strategy for the given query and files.
        
        Args:
            query: User query to analyze
            file_summaries: Summaries of all files
            conversation_history: Previous conversation context
            conversation_summary: Summary of conversation
            excel_files: List of Excel/CSV files
            non_excel_files: List of non-Excel files
            
        Returns:
            Planning decision with processing strategy
        """
        if self.verbose:
            print(f"🧠 PLANNER AGENT: Analyzing query and planning strategy...")
            print(f"   Query: {query}")
            print(f"   Files: {len(excel_files or [])} Excel, {len(non_excel_files or [])} non-Excel")
        
        try:
            # Prepare context information
            context_info = self._prepare_context_info(
                file_summaries, conversation_history, conversation_summary,
                excel_files, non_excel_files
            )
            
            # Create the planning prompt
            planning_prompt = self._create_planning_prompt(query, context_info)
            
            # Get LLM response
            messages = [
                SystemMessage(content=self.system_prompt),
                HumanMessage(content=planning_prompt)
            ]
            
            response = await self.llm.ainvoke(messages)
            
            # Parse the response
            decision = self._parse_planning_response(response.content)
            
            if self.verbose:
                print(f"✅ PLANNER AGENT: Strategy determined")
                print(f"   Query Type: {decision['query_type']}")
                print(f"   Excel Strategy: {decision['excel_strategy']}")
                print(f"   Non-Excel Strategy: {decision['non_excel_strategy']}")
                print(f"   Processing: {'Sequential' if decision['sequential_processing'] else 'Parallel'}")
            
            return decision
            
        except Exception as e:
            logger.error(f"Planning failed: {str(e)}")
            # Return default strategy
            return self._get_default_strategy()
    
    def _prepare_context_info(self,
                            file_summaries: Dict[int, str],
                            conversation_history: Optional[str],
                            conversation_summary: Optional[str],
                            excel_files: List[FileInfo],
                            non_excel_files: List[FileInfo]) -> str:
        """Prepare context information for planning."""
        context_parts = []
        
        # File information
        if excel_files:
            context_parts.append(f"EXCEL/CSV FILES ({len(excel_files)}):")
            for file_info in excel_files:
                file_id = file_info['file_id']
                summary = file_summaries.get(file_id, "No summary available")
                context_parts.append(f"- {file_info['file_name']}: {summary[:200]}...")
        
        if non_excel_files:
            context_parts.append(f"\nNON-EXCEL FILES ({len(non_excel_files)}):")
            for file_info in non_excel_files:
                file_id = file_info['file_id']
                summary = file_summaries.get(file_id, "No summary available")
                context_parts.append(f"- {file_info['file_name']}: {summary[:200]}...")
        
        # Conversation context
        if conversation_summary:
            context_parts.append(f"\nCONVERSATION SUMMARY: {conversation_summary}")
        
        if conversation_history:
            context_parts.append(f"\nRECENT CONVERSATION: {conversation_history[-500:]}")
        
        return "\n".join(context_parts)
    
    def _create_planning_prompt(self, query: str, context_info: str) -> str:
        """Create the planning prompt for the LLM."""
        return f"""QUERY TO ANALYZE: {query}

AVAILABLE FILES AND CONTEXT:
{context_info}

Please analyze this query and determine the optimal processing strategy. Consider:

1. What type of query is this? (summary, specific questions, comparison, analysis)
2. Do we need full file content or can we work with summaries/targeted retrieval?
3. For Excel/CSV files: Do we need mathematical operations, summaries, or full content analysis?
4. For non-Excel files: Do we need full content analysis or targeted retrieval?
5. Should files be processed sequentially (for comparisons/analysis) or in parallel (for independent processing)?

Provide your analysis as a JSON object with the specified format."""
    
    def _parse_planning_response(self, response_content: str) -> PlannerDecision:
        """Parse the LLM response into a planning decision."""
        try:
            import json
            
            # Extract JSON from response
            start_idx = response_content.find('{')
            end_idx = response_content.rfind('}') + 1
            
            if start_idx == -1 or end_idx == 0:
                raise ValueError("No JSON found in response")
            
            json_str = response_content[start_idx:end_idx]
            parsed = json.loads(json_str)
            
            # Convert to enum values
            return PlannerDecision(
                query_type=QueryType(parsed['query_type'].lower()),
                whole_file_needed=parsed['whole_file_needed'],
                excel_strategy=ProcessingStrategy(parsed['excel_strategy'].lower()),
                non_excel_strategy=ProcessingStrategy(parsed['non_excel_strategy'].lower()),
                sequential_processing=parsed['sequential_processing'],
                parallel_processing=parsed['parallel_processing'],
                reasoning=parsed.get('reasoning', 'No reasoning provided')
            )
            
        except Exception as e:
            logger.error(f"Failed to parse planning response: {str(e)}")
            logger.error(f"Response content: {response_content}")
            return self._get_default_strategy()
    
    def _get_default_strategy(self) -> PlannerDecision:
        """Get default processing strategy as fallback."""
        return PlannerDecision(
            query_type=QueryType.RAG_ON_FILES,
            whole_file_needed=True,
            excel_strategy=ProcessingStrategy.SUMMARY_ONLY,
            non_excel_strategy=ProcessingStrategy.FULL_CONTENT,
            sequential_processing=True,
            parallel_processing=False,
            reasoning="Default strategy due to planning failure"
        )
