"""
Test suite for Deep Search New module
"""

import asyncio
import time
import logging
from typing import List, Dict, Any

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_orchestrator_initialization():
    """Test orchestrator initialization."""
    print("\n🧪 TEST: Orchestrator Initialization")
    
    try:
        from .orchestrator import DeepSearchOrchestrator
        
        orchestrator = DeepSearchOrchestrator(verbose=True)
        
        print("✅ Orchestrator initialized successfully")
        return True
        
    except Exception as e:
        print(f"❌ Orchestrator initialization failed: {str(e)}")
        return False


async def test_health_check():
    """Test system health check."""
    print("\n🧪 TEST: Health Check")
    
    try:
        from .orchestrator import DeepSearchOrchestrator
        
        orchestrator = DeepSearchOrchestrator(verbose=False)
        health_status = await orchestrator.health_check()
        
        print(f"Health Status: {health_status['status']}")
        print("Components:")
        for component, status in health_status.get('components', {}).items():
            print(f"  - {component}: {status}")
        
        if health_status['status'] in ['healthy', 'degraded']:
            print("✅ Health check passed")
            return True
        else:
            print("❌ Health check failed")
            return False
            
    except Exception as e:
        print(f"❌ Health check failed: {str(e)}")
        return False


async def test_capabilities():
    """Test capabilities reporting."""
    print("\n🧪 TEST: Capabilities")
    
    try:
        from .orchestrator import DeepSearchOrchestrator
        
        orchestrator = DeepSearchOrchestrator(verbose=False)
        capabilities = await orchestrator.get_processing_capabilities()
        
        print("Supported file types:")
        for category, types in capabilities.get('supported_file_types', {}).items():
            print(f"  - {category}: {types}")
        
        print("Processing strategies:")
        for category, strategies in capabilities.get('processing_strategies', {}).items():
            print(f"  - {category}: {strategies}")
        
        print("✅ Capabilities test passed")
        return True
        
    except Exception as e:
        print(f"❌ Capabilities test failed: {str(e)}")
        return False


async def test_workflow_info():
    """Test workflow information."""
    print("\n🧪 TEST: Workflow Info")
    
    try:
        from .orchestrator import DeepSearchOrchestrator
        
        orchestrator = DeepSearchOrchestrator(verbose=False)
        workflow_info = orchestrator.get_workflow_info()
        
        print("Workflow steps:")
        for step in workflow_info.get('workflow_steps', []):
            print(f"  {step['step']}. {step['name']}: {step['description']}")
        
        print("✅ Workflow info test passed")
        return True
        
    except Exception as e:
        print(f"❌ Workflow info test failed: {str(e)}")
        return False


async def test_file_content_service():
    """Test file content service."""
    print("\n🧪 TEST: File Content Service")
    
    try:
        from .file_content_service import FileContentService
        
        service = FileContentService(verbose=False)
        
        # Test file type determination
        test_files = [
            "test.xlsx",
            "data.csv", 
            "document.pdf",
            "report.docx",
            "notes.txt"
        ]
        
        for filename in test_files:
            file_type = service._determine_file_type(filename)
            print(f"  {filename} -> {file_type.value}")
        
        print("✅ File content service test passed")
        return True
        
    except Exception as e:
        print(f"❌ File content service test failed: {str(e)}")
        return False


async def test_planner_agent():
    """Test planner agent."""
    print("\n🧪 TEST: Planner Agent")
    
    try:
        from .planner_agent import PlannerAgent
        from .state import FileInfo, FileType
        
        planner = PlannerAgent(verbose=False)
        
        # Test with mock data
        mock_file_summaries = {
            1: "Financial report with quarterly data and revenue analysis",
            2: "Technical documentation about system architecture"
        }
        
        mock_excel_files = [FileInfo(
            file_id=1,
            file_name="q1_report.xlsx",
            blob_name="q1_report.xlsx",
            file_type=FileType.EXCEL,
            blob_url="https://example.com/q1_report.xlsx",
            summary=None,
            content=None,
            chunks=None,
            csv_links=None
        )]
        
        mock_non_excel_files = [FileInfo(
            file_id=2,
            file_name="architecture.pdf",
            blob_name="architecture.pdf",
            file_type=FileType.PDF,
            blob_url="https://example.com/architecture.pdf",
            summary=None,
            content=None,
            chunks=None,
            csv_links=None
        )]
        
        # Test different query types
        test_queries = [
            "Summarize all the documents",
            "What is the total revenue?",
            "Compare the financial performance",
            "Analyze the system architecture"
        ]
        
        for query in test_queries:
            try:
                decision = await planner.plan_processing(
                    query=query,
                    file_summaries=mock_file_summaries,
                    excel_files=mock_excel_files,
                    non_excel_files=mock_non_excel_files
                )
                print(f"  Query: '{query}' -> {decision['query_type'].value}")
            except Exception as e:
                print(f"  Query: '{query}' -> Error: {str(e)}")
        
        print("✅ Planner agent test passed")
        return True
        
    except Exception as e:
        print(f"❌ Planner agent test failed: {str(e)}")
        return False


async def test_mock_processing():
    """Test mock processing with fake data."""
    print("\n🧪 TEST: Mock Processing")
    
    try:
        from .orchestrator import quick_deep_search
        
        # This will fail with real file IDs, but we can test the error handling
        result = await quick_deep_search(
            query="Test query",
            selected_file_ids=[999999],  # Non-existent file ID
            verbose=False
        )
        
        print(f"Mock processing result: {result.get('status', 'unknown')}")
        
        if 'error' in result.get('answer', '').lower():
            print("✅ Mock processing test passed (error handling works)")
            return True
        else:
            print("❌ Mock processing test failed (no error handling)")
            return False
            
    except Exception as e:
        print(f"❌ Mock processing test failed: {str(e)}")
        return False


async def run_all_tests():
    """Run all tests."""
    print("🚀 DEEP SEARCH NEW: Running test suite...")
    
    tests = [
        test_orchestrator_initialization,
        test_health_check,
        test_capabilities,
        test_workflow_info,
        test_file_content_service,
        test_planner_agent,
        test_mock_processing
    ]
    
    results = []
    
    for test in tests:
        try:
            result = await test()
            results.append(result)
        except Exception as e:
            print(f"❌ Test {test.__name__} crashed: {str(e)}")
            results.append(False)
    
    # Summary
    passed = sum(results)
    total = len(results)
    
    print(f"\n📊 TEST SUMMARY:")
    print(f"   Passed: {passed}/{total}")
    print(f"   Success Rate: {passed/total*100:.1f}%")
    
    if passed == total:
        print("🎉 All tests passed!")
    elif passed > total * 0.7:
        print("✅ Most tests passed - system is functional")
    else:
        print("⚠️ Many tests failed - system needs attention")
    
    return passed == total


if __name__ == "__main__":
    asyncio.run(run_all_tests())
