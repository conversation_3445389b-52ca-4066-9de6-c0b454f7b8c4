"""
File Content Retrieval Service

Service to retrieve full file content by getting all chunks from database and vector store.
Handles Excel/CSV files separately from non-Excel files.
"""

import logging
from typing import List, Tuple, Dict, Any, Optional
from azure.search.documents import SearchClient
from azure.core.credentials import AzureKeyCredential
from .state import FileInfo, FileType
from config.settings import (
    AZURE_SEARCH_SERVICE_ENDPOINT,
    AZURE_SEARCH_ENHANCED_INDEX_NAME,
    AZURE_SEARCH_ADMIN_KEY
)

logger = logging.getLogger(__name__)


class FileContentService:
    """Service for retrieving full file content and organizing files by type."""
    
    def __init__(self, verbose: bool = False):
        """
        Initialize the file content service.

        Args:
            verbose: Whether to enable verbose logging
        """
        self.verbose = verbose
        # Initialize Azure Search client directly (like RAG agent)
        self.search_client = SearchClient(
            endpoint=AZURE_SEARCH_SERVICE_ENDPOINT,
            index_name=AZURE_SEARCH_ENHANCED_INDEX_NAME,
            credential=AzureKeyCredential(AZURE_SEARCH_ADMIN_KEY)
        )
    
    async def organize_files(self, file_ids: List[int]) -> Tuple[List[FileInfo], List[FileInfo]]:
        """
        Organize files by file IDs - deprecated, use organize_files_from_blob_names instead.

        Args:
            file_ids: List of file IDs to organize

        Returns:
            Tuple of (excel_files, non_excel_files)
        """
        if self.verbose:
            print(f"📁 FILE CONTENT SERVICE: organize_files with file_ids is deprecated, returning empty lists")

        # Return empty lists since we now use blob names
        return [], []

    async def organize_files_from_blob_names(self, blob_names: List[str]) -> Tuple[List[FileInfo], List[FileInfo]]:
        """
        Organize files from blob names into Excel and non-Excel categories.

        Args:
            blob_names: List of blob names to organize

        Returns:
            Tuple of (excel_files, non_excel_files)
        """
        if self.verbose:
            print(f"📁 FILE CONTENT SERVICE: Organizing {len(blob_names)} files from blob names...")

        excel_files = []
        non_excel_files = []

        # Process blob names - try database first if available, otherwise use blob names directly
        for blob_name in blob_names:
            try:
                file_id = None
                file_name = blob_name
                blob_url = f"https://aivcstorage01p2.blob.core.windows.net/uat-generalaisearch/{blob_name}"

                # Generate a pseudo file ID from blob name (no database needed)
                file_id = hash(blob_name) % 1000000  # Generate a pseudo file ID
                # Keep blob name as filename for now
                file_name = blob_name
                if self.verbose:
                    print(f"   Processing blob: {file_name} (Pseudo ID: {file_id})")

                # Determine file type
                file_type = self._determine_file_type(file_name)

                # Create base file info
                file_info = FileInfo(
                    file_id=file_id,
                    file_name=file_name,
                    blob_name=blob_name,
                    file_type=file_type,
                    blob_url=blob_url,
                    summary=None,
                    content=None,
                    chunks=None,
                    csv_links=None
                )

                if file_type in [FileType.EXCEL, FileType.CSV]:
                    # Handle Excel/CSV files - get content from Azure Search
                    await self._get_full_content_from_azure_search(file_info)
                    excel_files.append(file_info)

                    if self.verbose:
                        print(f"   📊 Excel/CSV: {file_info['file_name']}")
                else:
                    # Handle non-Excel files - get content from Azure Search
                    await self._get_full_content_from_azure_search(file_info)
                    non_excel_files.append(file_info)

                    if self.verbose:
                        print(f"   📄 Non-Excel: {file_info['file_name']}")

            except Exception as e:
                logger.error(f"Error processing blob {blob_name}: {str(e)}")
                continue

        if self.verbose:
            print(f"✅ FILE CONTENT SERVICE: Organized {len(excel_files)} Excel and {len(non_excel_files)} non-Excel files from blob names")

        return excel_files, non_excel_files
    
    def _determine_file_type(self, file_name: str) -> FileType:
        """Determine file type from file name."""
        extension = file_name.lower().split('.')[-1] if '.' in file_name else ''
        
        if extension in ['xlsx', 'xls']:
            return FileType.EXCEL
        elif extension == 'csv':
            return FileType.CSV
        elif extension == 'pdf':
            return FileType.PDF
        elif extension in ['docx', 'doc']:
            return FileType.DOCX
        elif extension == 'txt':
            return FileType.TXT
        else:
            return FileType.OTHER
    
    def _extract_blob_name(self, blob_url: str) -> str:
        """Extract blob name from blob URL."""
        return blob_url.split('/')[-1] if '/' in blob_url else blob_url

    def _extract_clean_filename(self, blob_name: str) -> str:
        """Extract clean filename from blob name by removing UUID and timestamp prefixes."""
        # Blob names typically follow pattern: uuid_timestamp_filename.ext
        # Example: bb8f1d8e-f681-4e9a-bd55-44f0ba876c9c_20250708_143514_OECD491.pdf
        parts = blob_name.split('_')
        if len(parts) >= 3:
            # Skip UUID (first part) and timestamp (second part), keep the rest
            clean_name = '_'.join(parts[2:])
            return clean_name
        else:
            # If pattern doesn't match, return original
            return blob_name
    
    async def _populate_excel_file_info(self, file_info: FileInfo) -> None:
        """Populate file info for Excel/CSV files using Azure Search."""
        try:
            # Get all content from Azure Search for this file
            await self._get_full_content_from_azure_search(file_info)

            # Populate CSV links for Excel/CSV files and update file name
            await self._populate_csv_links(file_info)

            if self.verbose:
                chunks_count = len(file_info.get('chunks', []))
                csv_links_count = len(file_info.get('csv_links', []))
                print(f"      Retrieved {chunks_count} chunks and {csv_links_count} CSV links for Excel file from Azure Search")

        except Exception as e:
            logger.error(f"Error populating Excel file info for {file_info['file_name']}: {str(e)}")
            # Set empty content as fallback
            file_info['content'] = ""
            file_info['chunks'] = []
            file_info['csv_links'] = []

    async def _populate_non_excel_file_info(self, file_info: FileInfo) -> None:
        """Populate file info for non-Excel files using Azure Search."""
        await self._get_full_content_from_azure_search(file_info)
    
    async def _get_full_content_from_azure_search(self, file_info: FileInfo) -> None:
        """Get full content by retrieving all chunks from Azure Search (like RAG agent)."""
        try:

            # Search for all chunks of this file in Azure Search
            results = []

            # Primary approach: search by blob name (most reliable for our use case)
            try:
                if self.verbose:
                    print(f"      🔍 Searching Azure Search for blob: '{file_info['blob_name']}'")
                    print(f"      🔍 Search filter: metadata_storage_name eq '{file_info['blob_name']}'")

                blob_results = self.search_client.search(
                    search_text="*",
                    filter=f"metadata_storage_name eq '{file_info['blob_name']}'",
                    select=["chunk_id", "chunk", "metadata_storage_name", "title"],
                    top=1000  # Get all chunks
                )
                results = list(blob_results)

                if self.verbose:
                    print(f"      📊 Found {len(results)} results by blob name")
                    if results:
                        # Show first result details
                        first_result = results[0]
                        print(f"      📄 First result metadata_storage_name: '{first_result.get('metadata_storage_name', 'N/A')}'")
                        print(f"      📄 First result chunk preview: '{first_result.get('chunk', '')[:100]}...'")
                    else:
                        print(f"      ⚠️ No results found for blob name: '{file_info['blob_name']}'")

                        # Try a broader search to see what's in the index
                        try:
                            print(f"      🔍 Trying broader search to see what's in the index...")
                            broad_results = self.search_client.search(
                                search_text="*",
                                select=["metadata_storage_name"],
                                top=10
                            )
                            broad_list = list(broad_results)
                            print(f"      📊 Found {len(broad_list)} total documents in index")
                            if broad_list:
                                print(f"      📄 Sample metadata_storage_names:")
                                for i, result in enumerate(broad_list[:5]):
                                    print(f"        {i+1}. '{result.get('metadata_storage_name', 'N/A')}'")
                        except Exception as broad_e:
                            print(f"      ❌ Broad search also failed: {str(broad_e)}")

            except Exception as e:
                if self.verbose:
                    print(f"      ❌ Blob name search failed: {str(e)}")
                logger.error(f"Azure Search blob name query failed: {str(e)}")

            # Fallback: try searching by parent_id if we have a real file ID
            if not results and isinstance(file_info['file_id'], int) and file_info['file_id'] > 1000000:
                # Only try parent_id for real file IDs (not our pseudo IDs)
                try:
                    if self.verbose:
                        print(f"      Trying parent_id search for file ID: {file_info['file_id']}")

                    parent_id_results = self.search_client.search(
                        search_text="*",
                        filter=f"parent_id eq 'file_{file_info['file_id']}'",
                        select=["chunk_id", "chunk", "metadata_storage_name", "title"],
                        top=1000  # Get all chunks
                    )
                    results = list(parent_id_results)

                    if self.verbose:
                        print(f"      Found {len(results)} results by parent_id")

                except Exception as e:
                    if self.verbose:
                        print(f"      ❌ Parent ID search failed: {str(e)}")

            # Process results
            chunks_data = []
            full_content = ""

            for result in results:
                content = result.get('chunk', '')
                if content:
                    full_content += content + "\n"
                    chunks_data.append({
                        "chunk_id": result.get('chunk_id', ''),
                        "content": content,
                        "metadata": {
                            "title": result.get('title', ''),
                            "metadata_storage_name": result.get('metadata_storage_name', '')
                        }
                    })

            file_info['content'] = full_content.strip()
            file_info['chunks'] = chunks_data

            if self.verbose:
                print(f"      Retrieved {len(chunks_data)} chunks from Azure Search")
                if chunks_data:
                    print(f"      Content length: {len(full_content)} characters")
                    print(f"      Content preview: {full_content[:200]}...")
                else:
                    print(f"      ⚠️ No chunks found for {file_info['file_name']}")
                    print(f"      Searched for blob_name: '{file_info['blob_name']}'")

        except Exception as e:
            logger.error(f"Error getting chunks from Azure Search for {file_info['file_name']}: {str(e)}")
            if self.verbose:
                print(f"      ❌ Azure Search error: {str(e)}")
            file_info['content'] = ""
            file_info['chunks'] = []
    

    
    async def get_file_content_by_id(self, file_id: int) -> Optional[str]:
        """
        Get full content for a single file by ID.
        
        Args:
            file_id: File ID to retrieve content for
            
        Returns:
            Full file content or None if not found
        """
        excel_files, non_excel_files = await self.organize_files([file_id])
        
        all_files = excel_files + non_excel_files
        if all_files:
            return all_files[0]['content']
        
        return None
    
    async def chunk_large_content(self, content: str, max_words: int = 40000, overlap_words: int = 200) -> List[str]:
        """
        Chunk large content into smaller pieces with overlap.
        
        Args:
            content: Content to chunk
            max_words: Maximum words per chunk
            overlap_words: Number of words to overlap between chunks
            
        Returns:
            List of content chunks
        """
        if not content:
            return []
        
        words = content.split()
        
        if len(words) <= max_words:
            return [content]
        
        chunks = []
        start_idx = 0
        
        while start_idx < len(words):
            end_idx = min(start_idx + max_words, len(words))
            chunk_words = words[start_idx:end_idx]
            chunks.append(' '.join(chunk_words))
            
            # Move start index, accounting for overlap
            if end_idx >= len(words):
                break
            start_idx = end_idx - overlap_words
        
        if self.verbose:
            print(f"      Chunked content into {len(chunks)} pieces")

        return chunks

    def _create_mock_files(self, file_ids: List[int]) -> Tuple[List[FileInfo], List[FileInfo]]:
        """Create mock files for testing when database is not available."""
        excel_files = []
        non_excel_files = []

        for file_id in file_ids:
            # Create mock file based on ID
            if file_id % 2 == 0:
                # Even IDs are Excel files
                file_info = FileInfo(
                    file_id=file_id,
                    file_name=f"mock_excel_{file_id}.xlsx",
                    blob_name=f"mock_excel_{file_id}.xlsx",
                    file_type=FileType.EXCEL,
                    blob_url=f"https://mock.com/mock_excel_{file_id}.xlsx",
                    summary=f"Mock Excel file {file_id} with financial data",
                    content="Mock Excel content with numerical data",
                    chunks=None,
                    csv_links=[f"https://mock.com/sheet1_{file_id}.csv"]
                )
                excel_files.append(file_info)
            else:
                # Odd IDs are PDF files
                file_info = FileInfo(
                    file_id=file_id,
                    file_name=f"mock_document_{file_id}.pdf",
                    blob_name=f"mock_document_{file_id}.pdf",
                    file_type=FileType.PDF,
                    blob_url=f"https://mock.com/mock_document_{file_id}.pdf",
                    summary=f"Mock PDF document {file_id} with text content",
                    content=f"Mock PDF content for document {file_id}. This is a sample document with various information.",
                    chunks=[{
                        "chunk_id": f"chunk_{file_id}_1",
                        "content": f"Mock content chunk for document {file_id}",
                        "metadata": {"page": 1}
                    }],
                    csv_links=None
                )
                non_excel_files.append(file_info)

        return excel_files, non_excel_files

    def _create_mock_files_from_blob_names(self, blob_names: List[str]) -> Tuple[List[FileInfo], List[FileInfo]]:
        """Create mock files from blob names for testing when database is not available."""
        excel_files = []
        non_excel_files = []

        for blob_name in blob_names:
            # Generate a pseudo file ID from blob name
            file_id = hash(blob_name) % 1000000

            # Determine file type from blob name
            file_type = self._determine_file_type(blob_name)

            if file_type in [FileType.EXCEL, FileType.CSV]:
                # Create mock Excel file
                file_info = FileInfo(
                    file_id=file_id,
                    file_name=blob_name,
                    blob_name=blob_name,
                    file_type=file_type,
                    blob_url=f"https://mock.com/{blob_name}",
                    summary=f"Mock Excel file from {blob_name} with financial data",
                    content="Mock Excel content with numerical data",
                    chunks=None,
                    csv_links=[f"https://mock.com/sheet1_{file_id}.csv"]
                )
                excel_files.append(file_info)
            else:
                # Create mock non-Excel file
                file_info = FileInfo(
                    file_id=file_id,
                    file_name=blob_name,
                    blob_name=blob_name,
                    file_type=file_type,
                    blob_url=f"https://mock.com/{blob_name}",
                    summary=f"Mock document from {blob_name} with text content",
                    content=f"Mock content for document {blob_name}. This is a sample document with various information.",
                    chunks=[{
                        "chunk_id": f"chunk_{file_id}_1",
                        "content": f"Mock content chunk for document {blob_name}",
                        "metadata": {"page": 1}
                    }],
                    csv_links=None
                )
                non_excel_files.append(file_info)

        return excel_files, non_excel_files

    def debug_file_resolution(self, storage_name: str) -> None:
        """Debug file resolution for a given storage name."""
        try:
            from models.models import File

            print(f"\n🔍 DEBUG: File resolution for storage_name: {storage_name}")

            # Try the same approach as RAG planner agent
            file_record = File.query.filter(File.blob_url.contains(storage_name)).first()

            if file_record:
                print(f"✅ Found file record:")
                print(f"   ID: {file_record.id}")
                print(f"   Name: {file_record.file_name}")
                print(f"   Blob Name: {file_record.blob_name}")
                print(f"   Blob URL: {file_record.blob_url}")
            else:
                print(f"❌ No file record found")
                print(f"   Trying alternative queries...")

                # Try exact blob_name match
                file_record2 = File.query.filter_by(blob_name=storage_name).first()
                if file_record2:
                    print(f"   ✅ Found by exact blob_name match")
                else:
                    print(f"   ❌ No exact blob_name match")

                # Show sample files
                print(f"   Sample files in database:")
                sample_files = File.query.limit(3).all()
                for f in sample_files:
                    print(f"     ID: {f.id}, Name: {f.file_name}")
                    print(f"     Blob URL: {f.blob_url}")
                    print()

        except Exception as e:
            print(f"❌ Error in debug_file_resolution: {str(e)}")
            import traceback
            traceback.print_exc()

    async def _populate_csv_links(self, file_info: FileInfo) -> None:
        """
        Populate CSV links for Excel/CSV files using the same logic as RAG planner agent.
        For PDF files, looks for generated CSV files.
        For native Excel/CSV files, uses the original blob URL.
        """
        try:
            # Import here to avoid circular imports
            from models.models import File, CSVFile

            # Extract metadata_storage_name from chunks (like RAG agent does)
            metadata_storage_name = None
            chunks = file_info.get('chunks', [])

            if chunks and len(chunks) > 0:
                # Get metadata_storage_name from the first chunk
                first_chunk = chunks[0]
                if isinstance(first_chunk, dict):
                    # Check if it's in the metadata sub-dict
                    chunk_metadata = first_chunk.get('metadata', {})
                    metadata_storage_name = chunk_metadata.get('metadata_storage_name')

                    # Fallback to direct access
                    if not metadata_storage_name:
                        metadata_storage_name = first_chunk.get('metadata_storage_name')
                else:
                    # Handle Azure Search result object
                    metadata_storage_name = getattr(first_chunk, 'metadata_storage_name', None)

            # Fallback to blob_name if metadata_storage_name not found
            if not metadata_storage_name:
                metadata_storage_name = file_info['blob_name']

            if self.verbose:
                print(f"      Looking for file record with metadata_storage_name: {metadata_storage_name}")

            # Use the same approach as RAG planner agent: find file where blob_url contains storage_name
            file_record = File.query.filter(File.blob_url.contains(metadata_storage_name)).first()

            if not file_record:
                if self.verbose:
                    print(f"      No file record found with blob_url containing: {metadata_storage_name}")
                    print(f"      Trying alternative approaches...")

                # Try exact blob_name match as fallback
                file_record = File.query.filter_by(blob_name=metadata_storage_name).first()

                if not file_record:
                    # Try with file_name
                    file_name = file_info['file_name']
                    if self.verbose:
                        print(f"      Trying file_name: {file_name}")
                    file_record = File.query.filter_by(file_name=file_name).first()

            if not file_record:
                if self.verbose:
                    print(f"      No file record found for metadata_storage_name: {metadata_storage_name}")
                    print(f"      Available files in database (sample):")
                    # Show some files for debugging
                    sample_files = File.query.limit(5).all()
                    for f in sample_files:
                        print(f"        ID: {f.id}, Name: {f.file_name}")
                        print(f"        Blob Name: {f.blob_name}")
                        print(f"        Blob URL: {f.blob_url}")
                        print()
                file_info['csv_links'] = []
                return

            if self.verbose:
                print(f"      ✅ Found file record:")
                print(f"         ID: {file_record.id}")
                print(f"         Name: {file_record.file_name}")
                print(f"         Blob Name: {file_record.blob_name}")
                print(f"         Blob URL: {file_record.blob_url}")

            # Update the file_name in file_info to use the clean name from database
            file_info['file_name'] = file_record.file_name
            if self.verbose:
                print(f"      ✅ Updated file_name to: {file_record.file_name}")

            # Use the same logic as RAG planner agent
            # Check if this is a PDF file (indicating it's a PDF table document)
            if file_record.file_name.lower().endswith('.pdf'):
                # This is a PDF table document - look for the generated CSV file
                if self.verbose:
                    print(f"      📄 This is a PDF file - looking for generated CSV...")

                csv_file = CSVFile.query.filter_by(original_file_id=file_record.id).first()

                if csv_file and csv_file.azure_url:
                    if self.verbose:
                        print(f"      ✅ Found generated CSV for PDF {file_record.file_name}: {csv_file.azure_url}")
                    file_info['csv_links'] = [csv_file.azure_url]
                else:
                    if self.verbose:
                        print(f"      ❌ No generated CSV found for PDF file {file_record.file_name}")
                    file_info['csv_links'] = []
            else:
                # This is a native CSV/Excel file - use the original blob URL
                if self.verbose:
                    print(f"      📊 This is a native Excel/CSV file - using original blob URL")
                    print(f"      ✅ CSV Link: {file_record.blob_url}")
                file_info['csv_links'] = [file_record.blob_url]

        except Exception as e:
            logger.error(f"Error populating CSV links for {file_info['file_name']}: {str(e)}")
            import traceback
            logger.error(f"Full traceback: {traceback.format_exc()}")
            file_info['csv_links'] = []
