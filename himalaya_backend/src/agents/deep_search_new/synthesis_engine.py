"""
Answer Synthesis Engine

Combines answers from all files, maintains unique references, and produces
final consolidated answer.
"""

import logging
from typing import List, Dict, Any, Tuple, Set

from langchain_openai import AzureChatOpenAI
from langchain_core.messages import SystemMessage, HumanMessage

from config.settings import (
    AZURE_OPENAI_KEY, AZURE_OPENAI_ENDPOINT, AZURE_OPENAI_API_VERSION,
    AZURE_OPENAI_DEPLOYMENT_NAME
)
from .state import ProcessingResult, ValidationResult

logger = logging.getLogger(__name__)


class SynthesisEngine:
    """
    Engine that synthesizes final answers from all processing results.
    
    Combines answers from Excel and non-Excel processing, maintains unique
    references, and produces a coherent final answer.
    """
    
    def __init__(self, verbose: bool = False):
        """
        Initialize the synthesis engine.
        
        Args:
            verbose: Whether to enable verbose logging
        """
        self.verbose = verbose
        
        # Initialize Azure OpenAI client
        self.llm = AzureChatOpenAI(
            azure_endpoint=AZURE_OPENAI_ENDPOINT,
            api_key=AZURE_OPENAI_KEY,
            api_version=AZURE_OPENAI_API_VERSION,
            deployment_name=AZURE_OPENAI_DEPLOYMENT_NAME,
            temperature=0.2,  # Slightly higher for creative synthesis
            max_tokens=4000
        )
    
    async def synthesize(self,
                        excel_results: List[ProcessingResult],
                        non_excel_results: List[ProcessingResult],
                        validation_results: List[ValidationResult],
                        query: str) -> Tuple[str, List[Dict[str, Any]]]:
        """
        Synthesize final answer from all processing results.
        
        Args:
            excel_results: Results from Excel processing
            non_excel_results: Results from non-Excel processing
            validation_results: Results from context validation
            query: Original user query
            
        Returns:
            Tuple of (final_answer, unique_references)
        """
        if self.verbose:
            print(f"🔄 SYNTHESIS ENGINE: Synthesizing final answer...")
            print(f"   Excel results: {len(excel_results)}")
            print(f"   Non-Excel results: {len(non_excel_results)}")
            print(f"   Validation results: {len(validation_results)}")
        
        try:
            # Combine all results
            all_results = excel_results + non_excel_results
            
            if not all_results:
                return "No results were generated from the selected files.", []
            
            # Apply validation results
            validated_results = self._apply_validation_results(all_results, validation_results)
            
            # Extract unique references
            unique_references = self._extract_unique_references(validated_results)
            
            # Check if we have any valid results
            valid_results = [r for r in validated_results if not r.get('error')]
            
            if not valid_results:
                error_summary = self._create_error_summary(validated_results)
                return error_summary, unique_references
            
            # Synthesize final answer
            final_answer = await self._synthesize_final_answer(valid_results, query)
            
            if self.verbose:
                print(f"✅ SYNTHESIS ENGINE: Final answer generated")
                print(f"   References: {len(unique_references)} unique files")
            
            return final_answer, unique_references
            
        except Exception as e:
            error_msg = f"Error synthesizing final answer: {str(e)}"
            logger.error(error_msg)
            return error_msg, []
    
    def _apply_validation_results(self,
                                all_results: List[ProcessingResult],
                                validation_results: List[ValidationResult]) -> List[ProcessingResult]:
        """Apply validation results to processing results."""
        if len(validation_results) != len(all_results):
            logger.warning("Mismatch between results and validation results count")
            return all_results
        
        validated_results = []
        
        for result, validation in zip(all_results, validation_results):
            if validation['is_valid']:
                # Use validated answer
                validated_result = result.copy()
                validated_result['answer'] = validation['validated_answer']
                validated_result['confidence_score'] = min(
                    result['confidence_score'],
                    validation['confidence_score']
                )
                validated_results.append(validated_result)
            else:
                # Handle invalid validation - check if it's due to no content
                if "No summary available" in validation['validated_answer'] or "No content available" in validation['validated_answer']:
                    # This is not a validation failure, just no content available
                    result_copy = result.copy()
                    result_copy['answer'] = validation['validated_answer']
                    validated_results.append(result_copy)
                else:
                    # Actual validation failure
                    invalid_result = result.copy()
                    invalid_result['error'] = f"Failed context validation: {validation['validated_answer']}"
                    validated_results.append(invalid_result)
        
        return validated_results
    
    def _extract_unique_references(self, results: List[ProcessingResult]) -> List[Dict[str, Any]]:
        """Extract unique file references from all results in proper format for chat_routes."""
        references_dict = {}

        for result in results:
            file_name = result['file_name']
            file_id = result.get('file_id', 'unknown')
            blob_name = result.get('blob_name', file_name)

            # Create structured reference object like other agents
            if file_name not in references_dict:
                references_dict[file_name] = {
                    'file_name': file_name,
                    'file_id': file_id,
                    'blob_name': blob_name,
                    'chunk_id': f'file_{file_id}',
                    'content': f'Document: {file_name}',
                    'confidence_score': 0.9,
                    'metadata': {
                        'source_type': 'document',
                        'file_name': file_name,
                        'file_id': file_id,
                        'blob_name': blob_name
                    }
                }

        return list(references_dict.values())
    
    def _create_error_summary(self, results: List[ProcessingResult]) -> str:
        """Create summary of errors when no valid results are available."""
        error_files = []
        
        for result in results:
            if result.get('error'):
                error_files.append(f"- {result['file_name']}: {result['error']}")
        
        if error_files:
            return f"Unable to process the selected files due to the following errors:\n\n" + "\n".join(error_files)
        else:
            return "No valid results could be generated from the selected files."
    
    async def _synthesize_final_answer(self, results: List[ProcessingResult], query: str) -> str:
        """Synthesize final answer from valid results."""
        try:
            if len(results) == 1:
                # Single result - return it directly with minor formatting
                return await self._format_single_result(results[0], query)
            else:
                # Multiple results - synthesize them
                return await self._synthesize_multiple_results(results, query)
                
        except Exception as e:
            logger.error(f"Error synthesizing final answer: {str(e)}")
            # Fallback: concatenate all answers
            return self._fallback_synthesis(results, query)
    
    async def _format_single_result(self, result: ProcessingResult, query: str) -> str:
        """Format a single result as the final answer."""
        try:
            system_prompt = """You are an expert at formatting and presenting information clearly.

Format the provided answer to be clear, well-structured, and directly responsive to the user's query.
Maintain all the factual content but improve readability and organization."""
            
            user_prompt = f"""User Query: {query}

Source File: {result['file_name']}

Answer to format:
{result['answer']}

Please format this answer to be clear and well-structured while maintaining all factual content."""
            
            messages = [
                SystemMessage(content=system_prompt),
                HumanMessage(content=user_prompt)
            ]
            
            response = await self.llm.ainvoke(messages)
            return response.content.strip()
            
        except Exception as e:
            logger.error(f"Error formatting single result: {str(e)}")
            return result['answer']
    
    async def _synthesize_multiple_results(self, results: List[ProcessingResult], query: str) -> str:
        """Synthesize multiple results into a coherent answer."""
        try:
            system_prompt = """You are an expert at synthesizing information from multiple sources.

Create a comprehensive, well-organized answer that:
1. Directly addresses the user's query
2. Integrates information from all sources
3. Eliminates redundancy while preserving important details
4. Organizes information logically
5. Maintains clear attribution to sources when relevant
6. Provides a coherent narrative flow

Do not add external information - only synthesize what's provided."""
            
            # Prepare source information
            source_info = []
            for i, result in enumerate(results, 1):
                source_info.append(f"Source {i} - {result['file_name']}:\n{result['answer']}")
            
            combined_sources = "\n\n" + "="*50 + "\n\n".join(source_info)
            
            user_prompt = f"""User Query: {query}

Information from {len(results)} sources:
{combined_sources}

Please synthesize this information into a comprehensive, well-organized answer that directly addresses the user's query."""
            
            messages = [
                SystemMessage(content=system_prompt),
                HumanMessage(content=user_prompt)
            ]
            
            response = await self.llm.ainvoke(messages)
            return response.content.strip()
            
        except Exception as e:
            logger.error(f"Error synthesizing multiple results: {str(e)}")
            return self._fallback_synthesis(results, query)
    
    def _fallback_synthesis(self, results: List[ProcessingResult], query: str) -> str:
        """Fallback synthesis when LLM synthesis fails."""
        if len(results) == 1:
            return results[0]['answer']
        
        # Simple concatenation with headers
        synthesized_parts = [f"Based on the analysis of {len(results)} files:\n"]
        
        for i, result in enumerate(results, 1):
            synthesized_parts.append(f"\n{i}. From {result['file_name']}:")
            synthesized_parts.append(result['answer'])
        
        return "\n".join(synthesized_parts)
    
    async def synthesize_partial_results(self,
                                       partial_results: List[ProcessingResult],
                                       query: str) -> str:
        """
        Synthesize partial results (useful for streaming or incremental processing).
        
        Args:
            partial_results: Partial processing results
            query: Original query
            
        Returns:
            Synthesized answer from partial results
        """
        if not partial_results:
            return "No results available yet."
        
        valid_results = [r for r in partial_results if not r.get('error')]
        
        if not valid_results:
            return "Processing in progress, no valid results yet."
        
        return await self._synthesize_final_answer(valid_results, query)
