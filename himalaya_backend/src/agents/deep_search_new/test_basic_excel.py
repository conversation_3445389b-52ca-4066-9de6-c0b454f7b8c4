#!/usr/bin/env python3
"""
Test script to verify basic Excel processor functionality
"""

import asyncio
import sys
import os
import pandas as pd
import tempfile

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))

from agents.deep_search_new.basic_excel_processor import BasicExcelProcessor
from agents.deep_search_new.excel_processor import ExcelProcessor
from agents.deep_search_new.state import FileInfo, FileType, PlannerDecision, QueryType, ProcessingStrategy


def create_test_csv():
    """Create a test CSV file with sample data"""
    data = {
        'Customer_ID': range(1, 101),
        'Add_to_Cart_Frequency': [2, 5, 1, 8, 3, 6, 4, 7, 2, 9] * 10,
        'Purchase_Frequency': [1, 3, 0, 5, 2, 4, 2, 4, 1, 6] * 10,
        'Browsing_Time_Minutes': [15, 45, 10, 60, 25, 50, 30, 55, 20, 70] * 10,
        'Age': [25, 35, 28, 42, 31, 38, 29, 45, 33, 40] * 10,
        'Satisfaction_Score': [4.2, 4.8, 3.5, 4.9, 4.1, 4.7, 4.3, 4.6, 4.0, 4.9] * 10
    }
    
    df = pd.DataFrame(data)
    
    # Create temporary file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as f:
        df.to_csv(f.name, index=False)
        return f.name


async def test_basic_excel_processor():
    """Test the basic Excel processor directly"""
    print("🧪 Testing Basic Excel Processor...")
    
    # Create test data
    csv_path = create_test_csv()
    
    try:
        processor = BasicExcelProcessor(verbose=True)
        
        # Test relationship query
        query = "What is the relation between add to cart frequency and purchase frequency?"
        result = processor.process_csv_directly(csv_path, query, "test_csv")
        
        print(f"\n📊 Basic Excel Processor Result:")
        print(f"   Status: {result.get('status')}")
        print(f"   Answer: {result.get('answer')[:200]}...")
        
        return result.get('status') == 'success'
        
    except Exception as e:
        print(f"❌ Error testing basic Excel processor: {str(e)}")
        return False
    finally:
        # Clean up
        try:
            os.unlink(csv_path)
        except:
            pass


async def test_excel_processor_with_fallback():
    """Test the Excel processor with fallback to basic processor"""
    print("\n🧪 Testing Excel Processor with Fallback...")
    
    # Create test data
    csv_path = create_test_csv()
    
    try:
        processor = ExcelProcessor(verbose=True)
        
        # Create mock file info
        file_info = FileInfo(
            file_id=1,
            file_name="test_amazon_data.csv",
            blob_name="test_amazon_data.csv",
            file_type=FileType.CSV,
            blob_url=csv_path,  # Use local file path
            summary="Test Amazon customer behavior data",
            content="Test content",
            chunks=None,
            csv_links=[csv_path]
        )
        
        # Create planner decision
        planner_decision = PlannerDecision(
            query_type=QueryType.ANALYSIS,
            whole_file_needed=True,
            excel_strategy=ProcessingStrategy.MATHEMATICAL,
            non_excel_strategy=ProcessingStrategy.RAG_BASED,
            sequential_processing=True,
            parallel_processing=False,
            reasoning="Test mathematical analysis"
        )
        
        query = "What is the relation between add to cart frequency and purchase frequency?"
        
        results = await processor.process_files([file_info], query, planner_decision)
        
        print(f"\n📊 Excel Processor Results:")
        for result in results:
            print(f"   File: {result['file_name']}")
            print(f"   Answer: {result['answer'][:200]}...")
            print(f"   Confidence: {result['confidence_score']}")
            print(f"   Error: {result.get('error', 'None')}")
        
        return len(results) > 0 and results[0].get('answer') != 'Excel analysis not available'
        
    except Exception as e:
        print(f"❌ Error testing Excel processor: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        # Clean up
        try:
            os.unlink(csv_path)
        except:
            pass


async def main():
    """Run all tests"""
    print("🚀 Starting Excel processor tests...")
    
    # Test basic Excel processor
    basic_success = await test_basic_excel_processor()
    
    # Test Excel processor with fallback
    fallback_success = await test_excel_processor_with_fallback()
    
    print(f"\n✅ Test Results:")
    print(f"   Basic Excel Processor: {'✅ PASS' if basic_success else '❌ FAIL'}")
    print(f"   Excel Processor Fallback: {'✅ PASS' if fallback_success else '❌ FAIL'}")
    
    if basic_success and fallback_success:
        print(f"\n🎉 All tests passed! Excel processing should now work.")
    else:
        print(f"\n⚠️ Some tests failed. Check the error messages above.")


if __name__ == "__main__":
    asyncio.run(main())
