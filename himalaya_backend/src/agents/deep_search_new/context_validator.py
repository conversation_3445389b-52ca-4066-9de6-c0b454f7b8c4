"""
Context Validator

Ensures answers are strictly based on provided context and removes any information
not present in the source files.
"""

import logging
from typing import List, Dict, Any, Optional

from langchain_openai import AzureChatOpenAI
from langchain_core.messages import SystemMessage, HumanMessage

from config.settings import (
    AZURE_OPENAI_KEY, AZURE_OPENAI_ENDPOINT, AZURE_OPENAI_API_VERSION,
    AZURE_OPENAI_DEPLOYMENT_NAME
)
from .state import ProcessingResult, ValidationResult

logger = logging.getLogger(__name__)


class ContextValidator:
    """
    Validator that ensures answers are strictly based on provided context.
    
    This validator acts as a "reverse RAG" system where it verifies that
    information in answers is present in the source context in some form.
    """
    
    def __init__(self, verbose: bool = False):
        """
        Initialize the context validator.
        
        Args:
            verbose: Whether to enable verbose logging
        """
        self.verbose = verbose
        
        # Initialize Azure OpenAI client with higher temperature for validation
        self.llm = AzureChatOpenAI(
            azure_endpoint=AZURE_OPENAI_ENDPOINT,
            api_key=AZURE_OPENAI_KEY,
            api_version=AZURE_OPENAI_API_VERSION,
            deployment_name=AZURE_OPENAI_DEPLOYMENT_NAME,
            temperature=0.0,  # Very low temperature for consistent validation
            max_tokens=3000
        )
    
    async def validate_answers(self,
                             excel_results: List[ProcessingResult],
                             non_excel_results: List[ProcessingResult],
                             file_summaries: Dict[int, str]) -> List[ValidationResult]:
        """
        Validate all answers to ensure they're based only on provided context.
        
        Args:
            excel_results: Results from Excel processing
            non_excel_results: Results from non-Excel processing
            file_summaries: File summaries for context validation
            
        Returns:
            List of validation results
        """
        if self.verbose:
            print(f"✅ CONTEXT VALIDATOR: Validating {len(excel_results + non_excel_results)} answers...")
        
        validation_results = []
        
        # Validate Excel results (skip validation as they use mathematical operations)
        for result in excel_results:
            # Excel results are generally trusted as they come from mathematical operations
            # on actual data, so we apply lighter validation
            validation_result = await self._validate_excel_result(result, file_summaries)
            validation_results.append(validation_result)
            
            if self.verbose:
                print(f"   📊 Excel validated: {result['file_name']}")
        
        # Validate non-Excel results (strict validation)
        for result in non_excel_results:
            validation_result = await self._validate_non_excel_result(result, file_summaries)
            validation_results.append(validation_result)
            
            if self.verbose:
                status = "✅" if validation_result['is_valid'] else "⚠️"
                print(f"   📄 {status} Non-Excel validated: {result['file_name']}")
        
        if self.verbose:
            valid_count = sum(1 for v in validation_results if v['is_valid'])
            print(f"✅ CONTEXT VALIDATOR: {valid_count}/{len(validation_results)} answers validated")
        
        return validation_results
    
    async def _validate_excel_result(self, result: ProcessingResult, file_summaries: Dict[int, str]) -> ValidationResult:
        """Validate Excel processing result (lighter validation)."""
        try:
            # For Excel results, we mainly check if the answer is reasonable
            # given the file summary, since mathematical operations are generally trustworthy
            
            file_summary = file_summaries.get(result['file_id'], '')
            
            if result.get('error'):
                # If there was an error, mark as invalid
                return ValidationResult(
                    is_valid=False,
                    validated_answer=f"Error in processing: {result['error']}",
                    removed_content=[],
                    confidence_score=0.0
                )
            
            # For Excel, we trust the mathematical operations but check for obvious issues
            answer = result['answer']
            
            # Simple validation: check if answer mentions the file or contains data-like content
            if (result['file_name'].lower() in answer.lower() or
                any(keyword in answer.lower() for keyword in ['data', 'analysis', 'summary', 'table', 'sheet'])):
                
                return ValidationResult(
                    is_valid=True,
                    validated_answer=answer,
                    removed_content=[],
                    confidence_score=0.9  # High confidence for Excel results
                )
            else:
                # If answer seems disconnected, apply stricter validation
                return await self._strict_validate_answer(answer, file_summary, result['file_name'])
                
        except Exception as e:
            logger.error(f"Error validating Excel result: {str(e)}")
            return ValidationResult(
                is_valid=False,
                validated_answer=f"Validation error: {str(e)}",
                removed_content=[],
                confidence_score=0.0
            )
    
    async def _validate_non_excel_result(self, result: ProcessingResult, file_summaries: Dict[int, str]) -> ValidationResult:
        """Validate non-Excel processing result (strict validation)."""
        try:
            if result.get('error'):
                return ValidationResult(
                    is_valid=False,
                    validated_answer=f"Error in processing: {result['error']}",
                    removed_content=[],
                    confidence_score=0.0
                )
            
            file_summary = file_summaries.get(result['file_id'], '')
            answer = result['answer']
            
            # Strict validation for non-Excel results
            return await self._strict_validate_answer(answer, file_summary, result['file_name'])
            
        except Exception as e:
            logger.error(f"Error validating non-Excel result: {str(e)}")
            return ValidationResult(
                is_valid=False,
                validated_answer=f"Validation error: {str(e)}",
                removed_content=[],
                confidence_score=0.0
            )
    
    async def _strict_validate_answer(self, answer: str, context: str, file_name: str) -> ValidationResult:
        """Perform strict validation of answer against context."""
        try:
            system_prompt = """You are a strict context validator. Your job is to ensure that answers are based ONLY on the provided context.

VALIDATION RULES:
1. Every factual claim in the answer must be verifiable from the context
2. Remove any information that cannot be found in the context
3. Remove any external knowledge or assumptions
4. Keep only information that can be directly traced to the context
5. If the answer contains mostly invalid information, mark it as invalid

RESPONSE FORMAT:
{
    "is_valid": true/false,
    "validated_answer": "cleaned answer with only context-based information",
    "removed_content": ["list of removed statements"],
    "confidence_score": 0.0-1.0,
    "reasoning": "explanation of validation decision"
}"""
            
            user_prompt = f"""CONTEXT (from file "{file_name}"):
{context}

ANSWER TO VALIDATE:
{answer}

Please validate this answer against the context. Remove any information not present in the context and provide the cleaned answer."""
            
            messages = [
                SystemMessage(content=system_prompt),
                HumanMessage(content=user_prompt)
            ]
            
            response = await self.llm.ainvoke(messages)
            
            # Parse the validation response
            validation_result = self._parse_validation_response(response.content, answer)
            
            return validation_result
            
        except Exception as e:
            logger.error(f"Error in strict validation: {str(e)}")
            # Fallback: mark as valid but with low confidence
            return ValidationResult(
                is_valid=True,
                validated_answer=answer,
                removed_content=[],
                confidence_score=0.3
            )
    
    def _parse_validation_response(self, response_content: str, original_answer: str) -> ValidationResult:
        """Parse the validation response from LLM."""
        try:
            import json
            
            # Extract JSON from response
            start_idx = response_content.find('{')
            end_idx = response_content.rfind('}') + 1
            
            if start_idx == -1 or end_idx == 0:
                raise ValueError("No JSON found in validation response")
            
            json_str = response_content[start_idx:end_idx]
            parsed = json.loads(json_str)
            
            # Validator should only validate, not generate content
            is_valid = parsed.get('is_valid', True)
            validated_answer = parsed.get('validated_answer', original_answer)

            # If original answer indicates no content available, mark as invalid
            if "No summary available" in original_answer or "No content available" in original_answer:
                is_valid = False
                validated_answer = original_answer  # Keep original message
                if self.verbose:
                    print(f"      ❌ No content available for validation")

            return ValidationResult(
                is_valid=is_valid,
                validated_answer=validated_answer,
                removed_content=parsed.get('removed_content', []),
                confidence_score=parsed.get('confidence_score', 0.5)
            )
            
        except Exception as e:
            logger.error(f"Failed to parse validation response: {str(e)}")
            logger.error(f"Response content: {response_content}")
            
            # Fallback: try to extract validated answer from text
            lines = response_content.split('\n')
            validated_answer = original_answer
            
            for line in lines:
                if 'validated_answer' in line.lower() or 'cleaned answer' in line.lower():
                    # Try to extract the answer
                    if ':' in line:
                        potential_answer = line.split(':', 1)[1].strip()
                        if len(potential_answer) > 20:  # Reasonable answer length
                            validated_answer = potential_answer
                            break
            
            return ValidationResult(
                is_valid=True,
                validated_answer=validated_answer,
                removed_content=[],
                confidence_score=0.5
            )
    
    async def validate_single_answer(self, answer: str, context: str, file_name: str) -> ValidationResult:
        """
        Validate a single answer against its context.
        
        Args:
            answer: Answer to validate
            context: Source context
            file_name: Name of source file
            
        Returns:
            Validation result
        """
        return await self._strict_validate_answer(answer, context, file_name)
