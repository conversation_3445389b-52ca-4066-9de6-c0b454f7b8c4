#!/usr/bin/env python3
"""
Test script to debug file resolution in Deep Search New
"""

import asyncio
import sys
import os

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))

from agents.deep_search_new.file_content_service import FileContentService


async def test_file_resolution():
    """Test file resolution for the Amazon Excel file"""
    print("🧪 Testing file resolution for Amazon Excel file...")
    
    # The exact blob name from the logs
    blob_name = "5a36fc68-d96a-464b-a0df-b45c199ab9a6_20250703_192910_Amazon_Customer_Behavior_Survey_1.xlsx"
    
    # Initialize file content service
    file_service = FileContentService(verbose=True)
    
    # Debug file resolution
    file_service.debug_file_resolution(blob_name)
    
    return True


async def test_rag_agent_approach():
    """Test the RAG agent approach directly"""
    print("\n🧪 Testing RAG agent approach directly...")
    
    try:
        from models.models import File, CSVFile
        
        # The exact storage name from the logs
        storage_name = "5a36fc68-d96a-464b-a0df-b45c199ab9a6_20250703_192910_Amazon_Customer_Behavior_Survey_1.xlsx"
        
        print(f"   Looking for file with blob_url containing: {storage_name}")
        
        # Use the exact same approach as RAG planner agent
        file_record = File.query.filter(File.blob_url.contains(storage_name)).first()
        
        if file_record:
            print(f"   ✅ Found file record:")
            print(f"      ID: {file_record.id}")
            print(f"      Name: {file_record.file_name}")
            print(f"      Blob Name: {file_record.blob_name}")
            print(f"      Blob URL: {file_record.blob_url}")
            
            # Check if this is a PDF file (indicating it's a PDF table document)
            if file_record.file_name.lower().endswith('.pdf'):
                print(f"   📄 This is a PDF file - looking for generated CSV...")
                csv_file = CSVFile.query.filter_by(original_file_id=file_record.id).first()
                
                if csv_file and csv_file.azure_url:
                    print(f"   ✅ Found generated CSV: {csv_file.azure_url}")
                    return csv_file.azure_url
                else:
                    print(f"   ❌ No generated CSV found")
                    return None
            else:
                print(f"   📊 This is a native Excel/CSV file - using original blob URL")
                return file_record.blob_url
        else:
            print(f"   ❌ No file record found!")
            return None
            
    except Exception as e:
        print(f"❌ Error in RAG agent approach test: {str(e)}")
        import traceback
        traceback.print_exc()
        return None


async def test_full_csv_links_population():
    """Test the full CSV links population process"""
    print("\n🧪 Testing full CSV links population process...")
    
    # Initialize file content service
    file_service = FileContentService(verbose=True)
    
    # Test with the specific blob name that was failing
    test_blob_names = [
        "5a36fc68-d96a-464b-a0df-b45c199ab9a6_20250703_192910_Amazon_Customer_Behavior_Survey_1.xlsx"
    ]
    
    try:
        print(f"\n📁 Organizing files from blob names...")
        excel_files, non_excel_files = await file_service.organize_files_from_blob_names(test_blob_names)
        
        print(f"\n📊 Results:")
        print(f"   Excel files found: {len(excel_files)}")
        print(f"   Non-Excel files found: {len(non_excel_files)}")
        
        if excel_files:
            file_info = excel_files[0]
            print(f"\n📄 File Details:")
            print(f"   File ID: {file_info['file_id']}")
            print(f"   File Name: {file_info['file_name']}")
            print(f"   Blob Name: {file_info['blob_name']}")
            print(f"   File Type: {file_info['file_type']}")
            print(f"   Blob URL: {file_info['blob_url']}")
            print(f"   Content Length: {len(file_info.get('content', ''))}")
            print(f"   Chunks Count: {len(file_info.get('chunks', []))}")
            print(f"   CSV Links: {file_info.get('csv_links', [])}")
            
            # Check if chunks have metadata_storage_name
            chunks = file_info.get('chunks', [])
            if chunks:
                first_chunk = chunks[0]
                print(f"\n📄 First Chunk Details:")
                print(f"   Chunk ID: {first_chunk.get('chunk_id', 'N/A')}")
                chunk_metadata = first_chunk.get('metadata', {})
                print(f"   Metadata Storage Name: {chunk_metadata.get('metadata_storage_name', 'N/A')}")
            
            # Test CSV links population
            csv_links = file_info.get('csv_links', [])
            if csv_links:
                print(f"\n✅ CSV Links populated successfully!")
                for i, link in enumerate(csv_links):
                    print(f"   Link {i+1}: {link}")
                return True
            else:
                print(f"\n❌ CSV Links not populated!")
                return False
        else:
            print(f"\n❌ No Excel files found!")
            return False
            
    except Exception as e:
        print(f"❌ Error during test: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """Run all tests"""
    print("🚀 Starting file resolution tests...")
    
    # Test file resolution
    await test_file_resolution()
    
    # Test RAG agent approach
    csv_url = await test_rag_agent_approach()
    
    # Test full CSV links population
    csv_success = await test_full_csv_links_population()
    
    print(f"\n✅ Test Results:")
    print(f"   RAG Agent Approach: {'✅ PASS' if csv_url else '❌ FAIL'}")
    if csv_url:
        print(f"      CSV URL: {csv_url}")
    print(f"   CSV Links Population: {'✅ PASS' if csv_success else '❌ FAIL'}")
    
    if csv_url and csv_success:
        print(f"\n🎉 All tests passed! File resolution should now work.")
    else:
        print(f"\n⚠️ Some tests failed. Check the error messages above.")


if __name__ == "__main__":
    asyncio.run(main())
