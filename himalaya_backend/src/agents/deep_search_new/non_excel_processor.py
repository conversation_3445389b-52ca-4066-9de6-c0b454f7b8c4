"""
Non-Excel Processing Pipeline

Handles full content chunking (40k words with 200 word overlap), RAG operations,
and answer generation for non-Excel files.
"""

import logging
import time
from typing import List, Dict, Any, Optional

from langchain_openai import AzureChatOpenAI
from langchain_core.messages import SystemMessage, HumanMessage

from config.settings import (
    AZURE_OPENAI_KEY, AZURE_OPENAI_ENDPOINT, AZURE_OPENAI_API_VERSION,
    AZURE_OPENAI_DEPLOYMENT_NAME
)
from .state import FileInfo, PlannerDecision, ProcessingResult, ProcessingStrategy, QueryType
from .file_content_service import FileContentService

try:
    from services.azure_search_service import AzureSearchService
    DB_AVAILABLE = True
except ImportError:
    AzureSearchService = None
    DB_AVAILABLE = False

logger = logging.getLogger(__name__)


class NonExcelProcessor:
    """
    Processor for non-Excel files (PDF, DOCX, TXT, etc.).
    
    Handles:
    - Full content analysis with chunking for large files
    - RAG-based targeted retrieval within files
    - Summary generation from full content
    - Answer synthesis from multiple content chunks
    """
    
    def __init__(self, verbose: bool = False):
        """
        Initialize the non-Excel processor.
        
        Args:
            verbose: Whether to enable verbose logging
        """
        self.verbose = verbose
        
        # Initialize services
        self.llm = AzureChatOpenAI(
            azure_endpoint=AZURE_OPENAI_ENDPOINT,
            api_key=AZURE_OPENAI_KEY,
            api_version=AZURE_OPENAI_API_VERSION,
            deployment_name=AZURE_OPENAI_DEPLOYMENT_NAME,
            temperature=0.1,
            max_tokens=3000
        )
        
        self.search_service = AzureSearchService() if DB_AVAILABLE else None
        self.file_content_service = FileContentService(verbose=verbose)
    
    async def process_files(self,
                          non_excel_files: List[FileInfo],
                          query: str,
                          planner_decision: PlannerDecision) -> List[ProcessingResult]:
        """
        Process all non-Excel files according to the planner's strategy.
        
        Args:
            non_excel_files: List of non-Excel files to process
            query: User query
            planner_decision: Processing strategy from planner
            
        Returns:
            List of processing results
        """
        if self.verbose:
            print(f"📄 NON-EXCEL PROCESSOR: Processing {len(non_excel_files)} non-Excel files...")
            print(f"   Strategy: {planner_decision['non_excel_strategy']}")
            print(f"   Query Type: {planner_decision['query_type']}")
        
        results = []
        
        for file_info in non_excel_files:
            try:
                start_time = time.time()
                
                result = await self._process_single_file(
                    file_info, query, planner_decision
                )
                
                processing_time = time.time() - start_time
                result['processing_time'] = processing_time
                
                results.append(result)
                
                if self.verbose:
                    print(f"   ✅ Processed: {file_info['file_name']} ({processing_time:.2f}s)")
                    
            except Exception as e:
                error_msg = f"Error processing non-Excel file {file_info['file_name']}: {str(e)}"
                logger.error(error_msg)
                
                results.append(ProcessingResult(
                    file_id=file_info['file_id'],
                    file_name=file_info['file_name'],
                    answer=f"Error: {error_msg}",
                    references=[file_info['file_name']],
                    confidence_score=0.0,
                    processing_time=0.0,
                    error=error_msg
                ))
        
        if self.verbose:
            print(f"✅ NON-EXCEL PROCESSOR: Completed processing {len(results)} files")
        
        return results
    
    async def _process_single_file(self,
                                 file_info: FileInfo,
                                 query: str,
                                 planner_decision: PlannerDecision) -> ProcessingResult:
        """Process a single non-Excel file."""
        strategy = planner_decision['non_excel_strategy']
        query_type = planner_decision['query_type']
        
        if strategy == ProcessingStrategy.SUMMARY_ONLY:
            return await self._process_summary_only(file_info, query)
        
        elif strategy == ProcessingStrategy.FULL_CONTENT:
            return await self._process_full_content(file_info, query, query_type)
        
        elif strategy == ProcessingStrategy.RAG_BASED:
            return await self._process_rag_based(file_info, query)
        
        else:  # Fallback to full content
            return await self._process_full_content(file_info, query, query_type)
    
    async def _process_summary_only(self, file_info: FileInfo, query: str) -> ProcessingResult:
        """Process using only existing summary."""
        try:
            summary = file_info.get('summary', '')
            
            if not summary:
                summary = f"Document: {file_info['file_name']} (No summary available)"
            
            # For summary-only processing, return the existing summary
            answer = f"Summary of {file_info['file_name']}:\n\n{summary}"
            
            return ProcessingResult(
                file_id=file_info['file_id'],
                file_name=file_info['file_name'],
                answer=answer,
                references=[file_info['file_name']],
                confidence_score=0.8,
                processing_time=0.0,
                error=None
            )
            
        except Exception as e:
            raise Exception(f"Summary-only processing failed: {str(e)}")
    
    async def _process_full_content(self, file_info: FileInfo, query: str, query_type: QueryType) -> ProcessingResult:
        """Process using full content with chunking for large files."""
        try:
            content = file_info.get('content', '')
            
            if not content:
                raise Exception("No content available for processing")
            
            # Check if content needs chunking (more than 40k words)
            words = content.split()
            
            if len(words) <= 40000:
                # Small file - process as single chunk
                answer = await self._process_content_chunk(content, query, file_info['file_name'])
                
                return ProcessingResult(
                    file_id=file_info['file_id'],
                    file_name=file_info['file_name'],
                    answer=answer,
                    references=[file_info['file_name']],
                    confidence_score=0.8,
                    processing_time=0.0,
                    error=None
                )
            else:
                # Large file - chunk and process
                return await self._process_large_file(file_info, query, content)
                
        except Exception as e:
            raise Exception(f"Full content processing failed: {str(e)}")
    
    async def _process_large_file(self, file_info: FileInfo, query: str, content: str) -> ProcessingResult:
        """Process large file by chunking and combining results."""
        try:
            # Chunk the content
            chunks = await self.file_content_service.chunk_large_content(
                content, max_words=40000, overlap_words=200
            )
            
            if self.verbose:
                print(f"      Processing {len(chunks)} chunks for large file")
            
            # Process each chunk
            chunk_answers = []
            for i, chunk in enumerate(chunks):
                try:
                    chunk_answer = await self._process_content_chunk(
                        chunk, query, f"{file_info['file_name']} (Part {i+1})"
                    )
                    chunk_answers.append(chunk_answer)
                    
                except Exception as e:
                    logger.warning(f"Failed to process chunk {i+1}: {str(e)}")
                    continue
            
            if not chunk_answers:
                raise Exception("No chunks could be processed successfully")
            
            # Combine chunk answers
            combined_answer = await self._combine_chunk_answers(
                chunk_answers, query, file_info['file_name']
            )
            
            return ProcessingResult(
                file_id=file_info['file_id'],
                file_name=file_info['file_name'],
                answer=combined_answer,
                references=[file_info['file_name']],
                confidence_score=0.7,  # Lower confidence for chunked processing
                processing_time=0.0,
                error=None
            )
            
        except Exception as e:
            raise Exception(f"Large file processing failed: {str(e)}")
    
    async def _process_rag_based(self, file_info: FileInfo, query: str) -> ProcessingResult:
        """Process using RAG-based approach within the file."""
        try:
            # Use Azure Search to find relevant chunks within this specific file
            results = self.search_service.search_client.search(
                search_text=query,
                filter=f"metadata_storage_name eq '{file_info['blob_name']}'",
                select=["chunk", "chunk_id"],
                top=10  # Get top 10 relevant chunks
            )
            
            relevant_chunks = []
            for result in results:
                chunk_content = result.get('chunk', '')
                if chunk_content:
                    relevant_chunks.append(chunk_content)
            
            if not relevant_chunks:
                # Fallback to full content if no relevant chunks found
                return await self._process_full_content(file_info, query, QueryType.RAG_ON_FILES)
            
            # Combine relevant chunks and process
            combined_content = "\n\n".join(relevant_chunks)
            answer = await self._process_content_chunk(
                combined_content, query, file_info['file_name']
            )
            
            return ProcessingResult(
                file_id=file_info['file_id'],
                file_name=file_info['file_name'],
                answer=answer,
                references=[file_info['file_name']],
                confidence_score=0.8,
                processing_time=0.0,
                error=None
            )
            
        except Exception as e:
            raise Exception(f"RAG-based processing failed: {str(e)}")
    
    async def _process_content_chunk(self, content: str, query: str, source_name: str) -> str:
        """Process a single content chunk to answer the query."""
        try:
            system_prompt = """You are an expert document analyst. Answer the user's query based ONLY on the provided content.

IMPORTANT GUIDELINES:
1. Use ONLY information present in the provided content
2. Do not add external knowledge or assumptions
3. If the content doesn't contain relevant information, say so clearly
4. Provide specific quotes or references when possible
5. Be comprehensive but concise
6. If asked for a summary, capture all key points from the content"""
            
            user_prompt = f"""Based on the following content from "{source_name}", please answer this query: {query}

CONTENT:
{content}

Please provide a comprehensive answer based only on the information in the content above."""
            
            messages = [
                SystemMessage(content=system_prompt),
                HumanMessage(content=user_prompt)
            ]
            
            response = await self.llm.ainvoke(messages)
            return response.content.strip()
            
        except Exception as e:
            logger.error(f"Error processing content chunk: {str(e)}")
            return f"Error processing content from {source_name}: {str(e)}"
    
    async def _combine_chunk_answers(self, chunk_answers: List[str], query: str, file_name: str) -> str:
        """Combine answers from multiple chunks into a coherent response."""
        try:
            system_prompt = """You are an expert at synthesizing information from multiple sources. 

Combine the provided answers into a single, coherent response that:
1. Eliminates redundancy
2. Organizes information logically
3. Maintains all important details
4. Provides a comprehensive answer to the original query

Do not add external information - only synthesize what's provided."""
            
            combined_content = "\n\n---\n\n".join([
                f"Answer from Part {i+1}:\n{answer}" 
                for i, answer in enumerate(chunk_answers)
            ])
            
            user_prompt = f"""Original Query: {query}
File: {file_name}

Please synthesize these answers from different parts of the document into a single, comprehensive response:

{combined_content}"""
            
            messages = [
                SystemMessage(content=system_prompt),
                HumanMessage(content=user_prompt)
            ]
            
            response = await self.llm.ainvoke(messages)
            return response.content.strip()
            
        except Exception as e:
            logger.error(f"Error combining chunk answers: {str(e)}")
            # Fallback: return all answers concatenated
            return f"Combined answers from {file_name}:\n\n" + "\n\n".join(chunk_answers)
