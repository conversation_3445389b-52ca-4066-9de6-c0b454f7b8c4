#!/usr/bin/env python3
"""
Test script to check import issues with Excel RAG system
"""

import sys
import os

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))

def test_basic_imports():
    """Test basic imports"""
    print("🧪 Testing basic imports...")
    
    try:
        import pandas as pd
        print("✅ pandas imported successfully")
    except ImportError as e:
        print(f"❌ pandas import failed: {e}")
    
    try:
        from langchain_openai import AzureChatOpenAI
        print("✅ langchain_openai imported successfully")
    except ImportError as e:
        print(f"❌ langchain_openai import failed: {e}")
    
    try:
        from langchain_experimental.agents import create_pandas_dataframe_agent
        from langchain.agents.agent_types import AgentType
        print("✅ langchain_experimental imported successfully")
    except ImportError as e:
        print(f"❌ langchain_experimental import failed: {e}")
        print("   This package may need to be installed: pip install langchain_experimental")

def test_models_import():
    """Test models import"""
    print("\n🧪 Testing models import...")
    
    try:
        from models.models import File, CSVFile
        print("✅ models.models imported successfully")
    except ImportError as e:
        print(f"❌ models.models import failed: {e}")

def test_excel_rag_import():
    """Test Excel RAG import"""
    print("\n🧪 Testing Excel RAG import...")
    
    try:
        from agents.csv.excel_langchain_rag import ExcelLangChainRAG
        print("✅ ExcelLangChainRAG imported successfully")
        
        # Try to initialize it
        try:
            excel_rag = ExcelLangChainRAG(verbose=True)
            print("✅ ExcelLangChainRAG initialized successfully")
        except Exception as e:
            print(f"❌ ExcelLangChainRAG initialization failed: {e}")
            
    except ImportError as e:
        print(f"❌ ExcelLangChainRAG import failed: {e}")

def test_config_import():
    """Test config import"""
    print("\n🧪 Testing config import...")
    
    try:
        from config.settings import (
            AZURE_OPENAI_KEY, AZURE_OPENAI_API_VERSION, AZURE_OPENAI_ENDPOINT, 
            AZURE_OPENAI_DEPLOYMENT_NAME
        )
        print("✅ config.settings imported successfully")
        print(f"   AZURE_OPENAI_ENDPOINT: {AZURE_OPENAI_ENDPOINT[:50] if AZURE_OPENAI_ENDPOINT else 'Not set'}...")
        print(f"   AZURE_OPENAI_DEPLOYMENT_NAME: {AZURE_OPENAI_DEPLOYMENT_NAME}")
    except ImportError as e:
        print(f"❌ config.settings import failed: {e}")

def main():
    """Run all tests"""
    print("🚀 Starting import tests...")
    
    test_basic_imports()
    test_models_import()
    test_config_import()
    test_excel_rag_import()
    
    print("\n✅ All import tests completed!")

if __name__ == "__main__":
    main()
