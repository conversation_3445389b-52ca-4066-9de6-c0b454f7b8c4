"""
Basic Excel Processor - Fallback when langchain_experimental is not available

This module provides basic Excel/CSV analysis capabilities using pandas
without requiring the langchain_experimental package.
"""

import logging
import pandas as pd
import numpy as np
from typing import Dict, Any, Optional
import tempfile
import requests
from urllib.parse import urlparse

logger = logging.getLogger(__name__)


class BasicExcelProcessor:
    """
    Basic Excel processor that provides simple analysis without LangChain agents.
    
    This is a fallback when langchain_experimental is not available.
    """
    
    def __init__(self, verbose: bool = False):
        """Initialize the basic Excel processor."""
        self.verbose = verbose
    
    def process_csv_directly(self, csv_path: str, query: str, csv_id: str = None) -> Dict[str, Any]:
        """
        Process a CSV file with basic analysis capabilities.
        
        Args:
            csv_path: Path to CSV file or blob URL
            query: User query
            csv_id: Identifier for the CSV
            
        Returns:
            Processing result
        """
        try:
            # Load the data
            df = self._load_csv_data(csv_path)
            if df is None:
                return {
                    'csv_id': csv_id,
                    'csv_path': csv_path,
                    'answer': f'Failed to load data from {csv_path}',
                    'status': 'error'
                }
            
            if df.empty:
                return {
                    'csv_id': csv_id,
                    'csv_path': csv_path,
                    'answer': 'The dataset appears to be empty',
                    'status': 'error'
                }
            
            # Analyze the query and provide basic insights
            answer = self._analyze_query(df, query)
            
            return {
                'csv_id': csv_id,
                'csv_path': csv_path,
                'answer': answer,
                'status': 'success',
                'data_shape': df.shape
            }
            
        except Exception as e:
            logger.error(f"Error in basic Excel processing: {str(e)}")
            return {
                'csv_id': csv_id,
                'csv_path': csv_path,
                'answer': f'Error processing file: {str(e)}',
                'status': 'error'
            }
    
    def _load_csv_data(self, csv_path: str) -> Optional[pd.DataFrame]:
        """Load CSV data from file path or URL."""
        try:
            if self.verbose:
                print(f"      Loading data from: {csv_path}")
            
            # Check if it's a URL
            if csv_path.startswith(('http://', 'https://')):
                # Download the file to a temporary location
                response = requests.get(csv_path)
                response.raise_for_status()
                
                with tempfile.NamedTemporaryFile(delete=False, suffix='.xlsx') as tmp_file:
                    tmp_file.write(response.content)
                    tmp_path = tmp_file.name
                
                # Try to read as Excel first, then CSV
                try:
                    df = pd.read_excel(tmp_path)
                except:
                    df = pd.read_csv(tmp_path)
            else:
                # Local file
                if csv_path.endswith(('.xlsx', '.xls')):
                    df = pd.read_excel(csv_path)
                else:
                    df = pd.read_csv(csv_path)
            
            if self.verbose:
                print(f"      Loaded DataFrame with shape: {df.shape}")
                print(f"      Columns: {list(df.columns)}")
            
            return df
            
        except Exception as e:
            logger.error(f"Error loading CSV data: {str(e)}")
            return None
    
    def _analyze_query(self, df: pd.DataFrame, query: str) -> str:
        """Provide basic analysis based on the query."""
        query_lower = query.lower()
        
        # Get basic dataset information
        basic_info = self._get_basic_info(df)
        
        # Look for specific analysis requests
        if any(word in query_lower for word in ['relation', 'relationship', 'correlation', 'between']):
            return self._analyze_relationships(df, query, basic_info)
        elif any(word in query_lower for word in ['sum', 'total', 'count', 'average', 'mean']):
            return self._analyze_aggregations(df, query, basic_info)
        elif any(word in query_lower for word in ['trend', 'pattern', 'over time']):
            return self._analyze_trends(df, query, basic_info)
        else:
            return self._provide_general_analysis(df, query, basic_info)
    
    def _get_basic_info(self, df: pd.DataFrame) -> str:
        """Get basic information about the dataset."""
        info_parts = [
            f"Dataset contains {df.shape[0]} rows and {df.shape[1]} columns.",
            f"Columns: {', '.join(df.columns.tolist())}"
        ]
        
        # Add sample data
        if not df.empty:
            info_parts.append(f"\nSample data (first 3 rows):")
            info_parts.append(df.head(3).to_string())
        
        return "\n".join(info_parts)
    
    def _analyze_relationships(self, df: pd.DataFrame, query: str, basic_info: str) -> str:
        """Analyze relationships between variables."""
        try:
            # Look for column names mentioned in the query
            mentioned_columns = []
            for col in df.columns:
                if col.lower() in query.lower():
                    mentioned_columns.append(col)
            
            if len(mentioned_columns) >= 2:
                # Calculate correlation between mentioned columns
                numeric_cols = [col for col in mentioned_columns if pd.api.types.is_numeric_dtype(df[col])]
                
                if len(numeric_cols) >= 2:
                    correlation = df[numeric_cols].corr()
                    
                    result = f"Analysis of relationships in the dataset:\n\n"
                    result += f"Correlation matrix for {', '.join(numeric_cols)}:\n"
                    result += correlation.to_string()
                    result += f"\n\n{basic_info}"
                    return result
            
            # Fallback: general correlation analysis
            numeric_columns = df.select_dtypes(include=[np.number]).columns
            if len(numeric_columns) >= 2:
                correlation = df[numeric_columns].corr()
                
                result = f"Relationship analysis:\n\n"
                result += f"Correlation matrix for numeric columns:\n"
                result += correlation.to_string()
                result += f"\n\n{basic_info}"
                return result
            else:
                return f"Limited relationship analysis available - insufficient numeric columns.\n\n{basic_info}"
                
        except Exception as e:
            return f"Error analyzing relationships: {str(e)}\n\n{basic_info}"
    
    def _analyze_aggregations(self, df: pd.DataFrame, query: str, basic_info: str) -> str:
        """Analyze aggregations like sum, count, average."""
        try:
            numeric_columns = df.select_dtypes(include=[np.number]).columns
            
            if len(numeric_columns) == 0:
                return f"No numeric columns available for aggregation analysis.\n\n{basic_info}"
            
            result = f"Aggregation analysis:\n\n"
            
            for col in numeric_columns:
                result += f"{col}:\n"
                result += f"  Sum: {df[col].sum():.2f}\n"
                result += f"  Average: {df[col].mean():.2f}\n"
                result += f"  Count: {df[col].count()}\n"
                result += f"  Min: {df[col].min():.2f}\n"
                result += f"  Max: {df[col].max():.2f}\n\n"
            
            result += basic_info
            return result
            
        except Exception as e:
            return f"Error analyzing aggregations: {str(e)}\n\n{basic_info}"
    
    def _analyze_trends(self, df: pd.DataFrame, query: str, basic_info: str) -> str:
        """Analyze trends in the data."""
        try:
            # Look for date columns
            date_columns = df.select_dtypes(include=['datetime64']).columns
            
            if len(date_columns) == 0:
                # Try to find columns that might contain dates
                potential_date_cols = [col for col in df.columns if 'date' in col.lower() or 'time' in col.lower()]
                if potential_date_cols:
                    result = f"Potential date columns found: {', '.join(potential_date_cols)}\n"
                    result += "Consider converting these to datetime format for trend analysis.\n\n"
                else:
                    result = "No date/time columns found for trend analysis.\n\n"
            else:
                result = f"Date columns available for trend analysis: {', '.join(date_columns)}\n\n"
            
            result += basic_info
            return result
            
        except Exception as e:
            return f"Error analyzing trends: {str(e)}\n\n{basic_info}"
    
    def _provide_general_analysis(self, df: pd.DataFrame, query: str, basic_info: str) -> str:
        """Provide general analysis of the dataset."""
        try:
            result = f"General dataset analysis:\n\n"
            
            # Data types
            result += f"Data types:\n"
            for col, dtype in df.dtypes.items():
                result += f"  {col}: {dtype}\n"
            
            result += f"\n{basic_info}"
            
            # Add query-specific note
            result += f"\n\nNote: For the specific query '{query}', consider using the full Excel RAG system with langchain_experimental installed for more detailed analysis."
            
            return result
            
        except Exception as e:
            return f"Error in general analysis: {str(e)}\n\n{basic_info}"
