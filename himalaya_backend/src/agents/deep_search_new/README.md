# Deep Search New Module

A file-centric deep search system that processes selected files with full content awareness. This module implements a fundamentally different approach from traditional RAG by focusing on complete file content analysis rather than similarity-based retrieval.

## Key Features

- **File-Centric Processing**: Works with full content of selected files rather than traditional RAG
- **Separate Processing Pipelines**: Excel/CSV files and non-Excel files are handled with specialized strategies
- **Planner-Driven Architecture**: Intelligent query classification and processing strategy determination
- **Context-Only Validation**: Strict adherence to provided context without external knowledge
- **Adaptive Chunking**: Handles large files with intelligent chunking (40k words with 200-word overlap)
- **Mathematical Operations**: Leverages existing Excel RAG for data analysis and calculations
- **Reference Tracking**: Maintains unique file references throughout the processing pipeline

## Architecture

### Core Components

1. **DeepSearchOrchestrator**: Main entry point and workflow coordinator
2. **DeepSearchNewAgent**: Core agent implementing the LangGraph workflow
3. **FileContentService**: Retrieves and organizes full file content
4. **PlannerAgent**: Analyzes queries and determines processing strategies
5. **SummarizationService**: Generates comprehensive file summaries
6. **ExcelProcessor**: Handles Excel/CSV files with mathematical operations
7. **NonExcelProcessor**: Processes PDF, DOCX, TXT files with full content analysis
8. **ContextValidator**: Ensures answers are strictly based on provided context
9. **SynthesisEngine**: Combines results from all files into coherent answers

### Processing Workflow

```
1. organize_files        → Separate Excel and non-Excel files
2. generate_summaries    → Create summaries for all files
3. plan_processing       → Determine optimal processing strategy
4. process_excel_files   → Handle Excel/CSV with math operations
5. process_non_excel_files → Handle other files with full content
6. validate_context      → Ensure context-only answers
7. synthesize_answer     → Combine all results
```

## Usage

### Basic Usage

```python
from agents.deep_search_new import DeepSearchOrchestrator

# Create orchestrator
orchestrator = DeepSearchOrchestrator(verbose=True)

# Process query with selected files
result = await orchestrator.process_deep_search_query(
    query="Summarize all the financial reports and analyze trends",
    selected_file_ids=[1, 2, 3, 4, 5],
    conversation_history="Previous discussion about Q1 results",
    conversation_summary="User interested in quarterly analysis"
)

print(result["answer"])
print(f"References: {result['references']}")
```

### Quick Processing

```python
from agents.deep_search_new.orchestrator import quick_deep_search

result = await quick_deep_search(
    query="What are the key findings in these documents?",
    selected_file_ids=[10, 11, 12],
    verbose=True
)
```

### Health Check

```python
orchestrator = DeepSearchOrchestrator()
health = await orchestrator.health_check()
print(f"System status: {health['status']}")
```

## Processing Strategies

### Excel/CSV Files

- **SUMMARY_ONLY**: Uses existing EDA summaries for quick overviews
- **MATHEMATICAL**: Performs calculations and data analysis using CSV data
- **FULL_CONTENT**: Combines summaries with mathematical operations
- **RAG_BASED**: Targeted retrieval within Excel data

### Non-Excel Files

- **SUMMARY_ONLY**: Uses generated summaries for quick responses
- **FULL_CONTENT**: Analyzes complete file content with chunking for large files
- **RAG_BASED**: Targeted search within file content

## Query Types

- **SUMMARY**: Document summarization requests
- **QUESTIONS_ALL_FILES**: Questions requiring information from all files
- **RAG_ON_FILES**: Specific questions answerable through targeted search
- **COMPARISON**: Direct comparisons between files or data
- **ANALYSIS**: Deep analytical questions requiring comprehensive understanding

## File Type Support

- **Excel**: .xlsx, .xls (with mathematical operations)
- **CSV**: .csv (with mathematical operations)
- **PDF**: .pdf (full content analysis)
- **Word**: .docx, .doc (full content analysis)
- **Text**: .txt (full content analysis)

## Configuration

### Limits

- Maximum files: 25 per query
- Maximum chunk size: 40,000 words
- Chunk overlap: 200 words
- Maximum query length: 2,000 characters

### Processing Modes

- **Sequential**: Files processed one by one (for comparisons/analysis)
- **Parallel**: Files processed simultaneously (for independent summaries)

## Testing

Run the test suite:

```bash
cd himalaya_backend/src/agents/deep_search_new
python test_deep_search_new.py
```

## Integration

### With Existing RAG System

The deep search new module is designed to work alongside the existing RAG system:

- Traditional RAG: For similarity-based retrieval across large document collections
- Deep Search New: For comprehensive analysis of specifically selected files

### API Integration

```python
# In your API endpoint
from agents.deep_search_new import DeepSearchOrchestrator

async def deep_search_endpoint(request):
    if request.deep_search_enabled and request.selected_file_ids:
        orchestrator = DeepSearchOrchestrator()
        return await orchestrator.process_deep_search_query(
            query=request.query,
            selected_file_ids=request.selected_file_ids,
            conversation_history=request.conversation_history
        )
    else:
        # Fall back to traditional RAG
        return await traditional_rag_processing(request)
```

## Performance Considerations

- **Large Files**: Automatically chunked with overlap for efficient processing
- **Multiple Files**: Intelligent parallel/sequential processing based on query type
- **Memory Usage**: Optimized content loading and processing
- **Response Time**: Typically 10-30 seconds for 5-10 files

## Error Handling

The system includes comprehensive error handling:

- File access errors
- Content extraction failures
- LLM processing errors
- Validation failures
- Synthesis errors

All errors are logged and gracefully handled with fallback strategies.

## Monitoring

Use the health check and capabilities endpoints for monitoring:

```python
# Health monitoring
health = await orchestrator.health_check()
capabilities = await orchestrator.get_processing_capabilities()
workflow_info = orchestrator.get_workflow_info()
```

## Future Enhancements

- Streaming responses for real-time processing updates
- Advanced mathematical operations for complex Excel analysis
- Multi-language support for international documents
- Enhanced visualization for data-heavy results
- Integration with external data sources
