"""
State definitions for Deep Search New module
"""

from typing import Dict, List, Any, Optional, TypedDict
from enum import Enum


class QueryType(Enum):
    """Types of queries the system can handle"""
    SUMMARY = "summary"
    QUESTIONS_ALL_FILES = "questions_all_files"
    RAG_ON_FILES = "rag_on_files"
    COMPARISON = "comparison"
    ANALYSIS = "analysis"


class ProcessingStrategy(Enum):
    """Processing strategies for different file types"""
    FULL_CONTENT = "full_content"
    RAG_BASED = "rag_based"
    MATHEMATICAL = "mathematical"
    SUMMARY_ONLY = "summary_only"


class FileType(Enum):
    """File type classifications"""
    EXCEL = "excel"
    CSV = "csv"
    PDF = "pdf"
    DOCX = "docx"
    TXT = "txt"
    OTHER = "other"


class FileInfo(TypedDict):
    """Information about a file"""
    file_id: int
    file_name: str
    blob_name: str
    file_type: FileType
    blob_url: str
    summary: Optional[str]
    content: Optional[str]
    chunks: Optional[List[Dict[str, Any]]]
    csv_links: Optional[List[str]]  # For Excel files with multiple sheets


class PlannerDecision(TypedDict):
    """Decision made by the planner agent"""
    query_type: QueryType
    whole_file_needed: bool
    excel_strategy: ProcessingStrategy
    non_excel_strategy: ProcessingStrategy
    sequential_processing: bool
    parallel_processing: bool
    reasoning: str


class ProcessingResult(TypedDict):
    """Result from processing a file"""
    file_id: int
    file_name: str
    answer: str
    references: List[str]
    confidence_score: float
    processing_time: float
    error: Optional[str]


class ValidationResult(TypedDict):
    """Result from context validation"""
    is_valid: bool
    validated_answer: str
    removed_content: List[str]
    confidence_score: float


class DeepSearchNewState(TypedDict):
    """Main state for the deep search new system"""
    # Input
    query: str
    selected_file_ids: List[int]
    blob_names: List[str]  # Added blob names support
    conversation_history: Optional[str]
    conversation_summary: Optional[str]
    user_context: Optional[Dict[str, Any]]  # For multi-agent coordination
    
    # File organization
    excel_files: List[FileInfo]
    non_excel_files: List[FileInfo]
    file_summaries: Dict[int, str]
    
    # Planning
    planner_decision: Optional[PlannerDecision]
    
    # Processing results
    excel_results: List[ProcessingResult]
    non_excel_results: List[ProcessingResult]
    
    # Validation and synthesis
    validation_results: List[ValidationResult]
    final_answer: Optional[str]
    all_references: List[str]
    
    # Metadata
    processing_time: float
    error_messages: List[str]
    debug_info: Dict[str, Any]
