aiohappyeyeballs==2.6.1
aiohttp==3.11.18
aiosignal==1.3.2
annotated-types==0.7.0
anyio==4.6.2.post1
APScheduler==3.11.0
attrs==25.3.0
azure-cognitiveservices-speech==1.41.1
azure-cognitiveservices-vision-customvision==3.1.0
azure-common==1.1.28
azure-core==1.31.0
azure-search-documents==11.5.1
azure-ai-documentintelligence>=1.0.0
azure-ai-formrecognizer>=3.3.0
azure-identity>=1.13.0
azure-storage-blob==12.23.1
beautifulsoup4==4.12.3
blinker==1.8.2
certifi==2024.8.30
cffi==1.17.1
charset-normalizer==3.4.0
click==8.1.7
colorama==0.4.6
cryptography==37.0.4
dataclasses-json==0.6.7
distro==1.9.0
docx2pdf==0.1.8
docx2txt==0.8
ffmpeg-python==0.2.0
fire==0.7.0
Flask==3.0.3
Flask-Cors==5.0.0
Flask-SQLAlchemy==3.1.1
fonttools==4.54.1
frozenlist==1.6.0
future==1.0.0
greenlet==3.2.1
h11==0.14.0
httpcore==1.0.6
httpx==0.27.2
httpx-sse==0.4.0
idna==3.10
isodate==0.7.2
itsdangerous==2.2.0
Jinja2==3.1.4
jiter==0.6.1
jsonpatch==1.33
jsonpointer==3.0.0
langchain==0.3.24
langchain-community==0.3.22
langchain-core==0.3.55
langchain-openai==0.3.14
langchain-text-splitters==0.3.8
langchain-experimental>=0.3.0
langgraph==0.3.31
langgraph-checkpoint==2.0.24
langgraph-prebuilt==0.1.8
langgraph-sdk==0.1.63
langsmith==0.3.33
lxml==5.3.0
chromadb>=0.4.22
langchain-chroma>=0.0.2
markdown-it-py==3.0.0
markdown_pdf==1.3
markdownify==0.13.1
matplotlib
MarkupSafe==3.0.2
marshmallow==3.26.1
mdurl==0.1.2
# msal==1.16.0
msal>=1.30.0
msrest==0.7.1
multidict==6.4.3
mypy_extensions==1.1.0
numpy==2.0.2
oauthlib==3.2.2
Office365-REST-Python-Client==2.4.1
openai==1.75.0
opencv-python-headless==*********
orjson==3.10.16
ormsgpack==1.9.1
packaging==24.2
pdf2docx==0.5.8
# pdfminer.six==20220524
pdfminer.six>=20221105
pdfplumber>=0.10.3
pillow>=10.1.0
propcache==0.3.1
psycopg2-binary==2.9.10
pycparser==2.22
pydantic==2.9.2
pydantic-settings==2.9.1
pydantic_core==2.23.4
PyJWT==2.10.1
PyMuPDF==1.24.2
PyMuPDFb==1.24.1
pypandoc==1.14
PyPDF2==3.0.1
pypdf>=4.0.1
pypdfium2==4.30.0
pdf2image>=1.16.3
python-docx==1.1.2
python-dotenv==1.0.1
python-multipart>=0.0.6
python-pptx==0.6.21
pytz==2025.2
# pywin32==310
PyYAML==6.0.2
regex==2024.11.6
requests==2.32.3
requests-oauthlib==2.0.0
requests-toolbelt==1.0.0
six==1.16.0
sniffio==1.3.1
soupsieve==2.6
SQLAlchemy==2.0.40
alembic>=1.13.1
tenacity==9.1.2
termcolor==2.5.0
tiktoken==0.9.0
tqdm==4.66.5
pytest>=7.4.3
typing-inspect==0.9.0
typing-inspection==0.4.0
typing_extensions==4.12.2
tzdata==2025.2
tzlocal==5.3.1
urllib3==2.2.3
Wand==0.6.13
Werkzeug==3.0.4
XlsxWriter==3.2.0
xxhash==3.5.0
yarl==1.20.0
zstandard==0.23.0

# NEW DEPENDENCIES FOR ENHANCED DOCUMENT PROCESSING
# Document processing libraries
openpyxl==3.1.2
pytesseract==0.3.10
tabula-py==2.9.0

# Image processing for OCR
Pillow>=10.0.0

# Additional text processing
spacy==3.7.2
nltk==3.8.1

# Enhanced PDF processing (already have pdfplumber, adding more options)
# pymupdf==1.23.26

# Excel and CSV processing enhancements
pandas>=2.1.4,<3.0.0
xlrd==2.0.1
tabulate>=0.9.0

# LangChain pandas agent dependencies
matplotlib>=3.5.0
seaborn>=0.11.0
plotly>=5.0.0
scipy>=1.7.0
scikit-learn>=1.0.0

# Text extraction and processing
# textract==1.6.5
python-magic==0.4.27

# Document format detection
filetype==1.2.0

# yt reqmnts
youtube-transcript-api>=1.0.3
pytube>=15.0.0
