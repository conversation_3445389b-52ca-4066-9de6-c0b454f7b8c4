from flask import Blueprint, redirect, url_for, session, request, render_template, make_response, jsonify
from msal import ConfidentialClientApplication
from models.models import db, User
from config.settings import MS_CONFIG, JWT_SECRET_KEY, JWT_EXPIRATION_HOURS
from datetime import datetime, timedelta
import jwt
from flask_cors import CORS

auth_bp = Blueprint('auth', __name__)

# Add this right after creating the Blueprint
CORS(auth_bp, supports_credentials=True)

msal_client = ConfidentialClientApplication(
    MS_CONFIG['client_id'],
    authority=MS_CONFIG['auth_uri'],
    client_credential=MS_CONFIG['client_secret']
)

def generate_jwt_token(user):
    expiration = datetime.utcnow() + timedelta(hours=JWT_EXPIRATION_HOURS)
    return jwt.encode({
        'user_id': user.id,
        'email': user.email,
        'is_admin': user.is_admin,
        'exp': expiration
    }, JWT_SECRET_KEY, algorithm='HS256')

@auth_bp.route('/login')
def login():
    # Clear any existing session
    session.clear()
    
    auth_url = msal_client.get_authorization_request_url(
        scopes=MS_CONFIG['scope'],
        redirect_uri=MS_CONFIG['redirect_uri'],
        prompt='login'  # Force login prompt
    )
    return redirect(auth_url)

@auth_bp.route('/callback', methods=['GET'])
def callback():
    try:
        token_response = msal_client.acquire_token_by_authorization_code(
            request.args['code'],
            scopes=MS_CONFIG['scope'],
            redirect_uri=MS_CONFIG['redirect_uri']
        )
        
        if 'error' in token_response:
            return jsonify({'error': token_response['error']}), 400
            
        user_info = {
            'ms_user_id': token_response['id_token_claims']['oid'],
            'user_name': token_response['id_token_claims']['name'],
            'email': token_response['id_token_claims']['preferred_username']
        }
        
        user = User.query.filter_by(email=user_info['email']).first()
        
        if not user:
            user = User(
                user_name=user_info['user_name'],
                email=user_info['email'],
                ms_user_id=user_info['ms_user_id']
            )
            db.session.add(user)
            db.session.commit()
        
        # Generate JWT token
        access_token = generate_jwt_token(user)
        
        # Create response with user data and token
        response = jsonify({
            'success': True,
            'user': {
                'id': user.id,
                'name': user.user_name,
                'email': user.email,
                'is_admin': user.is_admin,
            },
            'access_token': access_token
        })
        print(response)
        return response
        
    except Exception as e:
        print(f"Login error: {str(e)}")
        return jsonify({'error': str(e)}), 400

@auth_bp.route('/logout')
def logout():
    return jsonify({
        'success': True,
        'message': 'Successfully logged out'
    })