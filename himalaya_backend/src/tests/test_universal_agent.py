"""
Test script for the Universal Agent.

This script tests the Universal Agent's ability to combine multiple specialized agents.
"""

import os
import sys
import json

# Add the parent directory to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from agents.universal import create_universal_agent

def test_universal_agent():
    """Test the Universal Agent's ability to combine multiple specialized agents."""
    print("\n=== Testing Universal Agent ===")
    
    # Create the universal agent
    universal_agent = create_universal_agent(verbose=True)
    
    # Test cases
    test_cases = [
        {
            "name": "General knowledge query",
            "query": "What is the capital of France?",
            "expected_agents": ["gpt"]
        },
        {
            "name": "Current event query",
            "query": "What are the latest developments in AI technology?",
            "expected_agents": ["web_search", "gpt"]
        },
        {
            "name": "Complex query requiring multiple agents",
            "query": "Compare the financial performance of Apple and Microsoft in the last quarter and explain the key factors affecting their stock prices.",
            "expected_agents": ["web_search", "gpt"]
        }
    ]
    
    # Run tests
    for test_case in test_cases:
        print(f"\n--- Testing: {test_case['name']} ---")
        print(f"Query: {test_case['query']}")
        
        try:
            # Process the query
            result = universal_agent.process_query(test_case["query"])
            
            # Print the result
            print(f"Answer: {result['answer'][:100]}...")
            print(f"Agent type: {result['agent_type']}")
            
            # Check which agents were used
            execution_plan = result.get("execution_plan", {}).get("execution_plan", {})
            used_agents = []
            if execution_plan.get("use_rag", False):
                used_agents.append("rag")
            if execution_plan.get("use_web_search", False):
                used_agents.append("web_search")
            if execution_plan.get("use_gpt", False):
                used_agents.append("gpt")
                
            print(f"Used agents: {used_agents}")
            
            # Check if the expected agents were used
            expected_agents = test_case["expected_agents"]
            all_expected_used = all(agent in used_agents for agent in expected_agents)
            if all_expected_used:
                print("✅ All expected agents were used")
            else:
                print("❌ Not all expected agents were used")
                print(f"Expected: {expected_agents}")
                print(f"Actual: {used_agents}")
                
        except Exception as e:
            print(f"❌ Error processing query: {e}")
    
    print("\n=== Universal Agent Testing Complete ===")

if __name__ == "__main__":
    test_universal_agent() 