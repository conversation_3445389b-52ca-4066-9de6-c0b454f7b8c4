"""
Test script for API agent flags integration with planner agent.
"""

import os
import sys
import json

# Add the parent directory to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from agents.planner.agent import create_planner_agent, AgentType

def test_api_agent_flags_integration():
    """Test that the API agent flags are correctly passed to the planner agent and respected."""
    print("\n=== Testing API Agent Flags Integration ===")
    
    # Create the planner agent
    planner_agent = create_planner_agent(verbose=True)
    
    # Simulate API payload with different agent flags
    test_cases = [
        {
            "name": "Work flag true",
            "query": "Tell me about Hindustan Unilever Limited",
            "web_search": False,
            "work": True,
            "gpt": False,
            "expected_agent_type": AgentType.RAG
        },
        {
            "name": "GPT flag true",
            "query": "What is the capital of France?",
            "web_search": False,
            "work": False,
            "gpt": True,
            "expected_agent_type": AgentType.GPT
        },
        {
            "name": "Web search flag true",
            "query": "What are the latest developments in AI?",
            "web_search": True,
            "work": False,
            "gpt": False,
            "expected_agent_type": AgentType.WEB_SEARCH
        },
        {
            "name": "Multiple flags true (web_search takes precedence)",
            "query": "What is quantum computing?",
            "web_search": True,
            "work": True,
            "gpt": True,
            "expected_agent_type": AgentType.WEB_SEARCH
        },
        {
            "name": "Conversational query overrides work flag",
            "query": "Hello, how are you doing today?",
            "web_search": False,
            "work": True,
            "gpt": False,
            "expected_agent_type": AgentType.CONVERSATION
        }
    ]
    
    for test_case in test_cases:
        print(f"\n--- Test Case: {test_case['name']} ---")
        
        # Determine the API agent type based on the flags
        api_agent_type = None
        if test_case["web_search"]:
            api_agent_type = "web_search"
        elif test_case["work"]:
            api_agent_type = "rag"
        elif test_case["gpt"]:
            api_agent_type = "gpt"
        
        # Directly invoke the planner graph
        result = planner_agent.graph.invoke({
            "messages": [],
            "query": test_case["query"],
            "conversation_history": None,
            "conversation_summary": "",
            "web_search": test_case["web_search"],
            "agent_type": AgentType.UNKNOWN,
            "api_agent_type": api_agent_type,
            "analysis": {},
            "qa_observations": None,
            "improvement_instructions": None,
            "require_table": False,
            "require_chart": False,
            "chart_type": None
        })
        
        actual_agent_type = result["agent_type"]
        print(f"Query: {test_case['query']}")
        print(f"Agent flags: web_search={test_case['web_search']}, work={test_case['work']}, gpt={test_case['gpt']}")
        print(f"Expected agent type: {test_case['expected_agent_type']}")
        print(f"Actual agent type: {actual_agent_type}")
        
        if actual_agent_type == test_case["expected_agent_type"]:
            print("✅ PASS: Agent type matches expected")
        else:
            print("❌ FAIL: Agent type does not match expected")
    
    print("\nTests completed!")

if __name__ == "__main__":
    # Run the tests
    test_api_agent_flags_integration() 