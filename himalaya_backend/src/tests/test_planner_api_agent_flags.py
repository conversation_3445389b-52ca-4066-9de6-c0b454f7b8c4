"""
Test script for planner agent respecting API agent flags.
"""

import os
import sys
import json

# Add the parent directory to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from agents.planner.agent import create_planner_agent, process_query, AgentType

def test_planner_api_agent_flags():
    """Test the planner agent's ability to respect API-provided agent flags."""
    print("\n=== Testing Planner Agent API Agent Flags Handling ===")
    
    # Create the planner agent
    planner_agent = create_planner_agent(verbose=True)
    
    # Test case 1: work=True for a factual query
    print("\nTest Case 1: work=True for factual query")
    result = process_query(
        planner_agent=planner_agent,
        query="Tell me about Hindustan Unilever Limited",
        web_search=False,
        work=True,
        gpt=False
    )
    print(f"Result agent_type: {result['agent_type']}")
    print(f"Standalone query: {result['standalone_query']}")
    
    # Test case 2: work=True but query is conversational
    print("\nTest Case 2: work=True but query is conversational")
    result = process_query(
        planner_agent=planner_agent,
        query="Hello, how are you doing today?",
        web_search=False,
        work=True,
        gpt=False
    )
    print(f"Result agent_type: {result['agent_type']}")
    print(f"Standalone query: {result['standalone_query']}")
    
    # Test case 3: gpt=True for a general knowledge query
    print("\nTest Case 3: gpt=True for general knowledge query")
    result = process_query(
        planner_agent=planner_agent,
        query="What is the capital of France?",
        web_search=False,
        work=False,
        gpt=True
    )
    print(f"Result agent_type: {result['agent_type']}")
    print(f"Standalone query: {result['standalone_query']}")
    
    # Test case 4: work=True but web_search is also True
    print("\nTest Case 4: work=True but web_search is also True")
    result = process_query(
        planner_agent=planner_agent,
        query="What are the latest developments in AI?",
        web_search=True,
        work=True,
        gpt=False
    )
    print(f"Result agent_type: {result['agent_type']}")
    print(f"Standalone query: {result['standalone_query']}")
    
    # Test case 5: Multiple agent flags set to True (web_search takes precedence)
    print("\nTest Case 5: Multiple agent flags set to True (web_search takes precedence)")
    result = process_query(
        planner_agent=planner_agent,
        query="What is quantum computing?",
        web_search=True,
        work=True,
        gpt=True
    )
    print(f"Result agent_type: {result['agent_type']}")
    print(f"Standalone query: {result['standalone_query']}")
    
    # Test case 6: Follow-up query with conversation history
    print("\nTest Case 6: Follow-up query with conversation history")
    result = process_query(
        planner_agent=planner_agent,
        query="What was their revenue?",
        web_search=False,
        work=True,
        gpt=False,
        conversation_history="User: Tell me about Hindustan Unilever Limited\nAssistant: Hindustan Unilever Limited (HUL) is India's largest fast-moving consumer goods company with a heritage of over 85 years in India.",
        conversation_summary="The conversation is about Hindustan Unilever Limited (HUL), which is India's largest fast-moving consumer goods company."
    )
    print(f"Result agent_type: {result['agent_type']}")
    print(f"Standalone query: {result['standalone_query']}")

if __name__ == "__main__":
    # Run the tests
    test_planner_api_agent_flags()
    
    print("\nTests completed!") 