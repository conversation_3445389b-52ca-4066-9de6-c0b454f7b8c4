"""
Test script for the GPT agent.
"""

import os
import sys
import time

# Add the parent directory to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from agents.gpt import create_gpt_agent
from agents.planner import create_planner_agent, AgentType

def test_gpt_agent():
    """Test the GPT agent directly."""
    print("Testing GPT agent directly...")
    
    # Create the GPT agent
    gpt_agent = create_gpt_agent(verbose=True)
    
    # Define a test query
    query = "What is the capital of France?"
    
    # Process the query
    start_time = time.time()
    result = gpt_agent.process_query(query)
    end_time = time.time()
    
    # Print the result
    print(f"Query: {query}")
    print(f"Answer: {result['answer']}")
    print(f"Processing time: {end_time - start_time:.2f} seconds")
    
    return result

def test_planner_with_gpt():
    """Test the planner agent with a query that should be routed to the GPT agent."""
    print("\nTesting planner agent with GPT routing...")
    
    # Create the planner agent
    planner = create_planner_agent(verbose=True)
    
    # Define a test query that should be routed to GPT
    query = "Explain the concept of quantum computing"
    
    # Process the query
    start_time = time.time()
    result = planner.graph.invoke({
        "messages": [],
        "query": query,
        "conversation_history": None,
        "conversation_summary": "",
        "web_search": False,
        "agent_type": AgentType.UNKNOWN,
        "analysis": {},
        "require_table": False,
        "require_chart": False,
        "chart_type": None
    })
    end_time = time.time()
    
    # Print the result
    print(f"Query: {query}")
    print(f"Agent type: {result['agent_type']}")
    print(f"Standalone query: {result['query']}")
    print(f"Processing time: {end_time - start_time:.2f} seconds")
    
    return result

if __name__ == "__main__":
    # Test the GPT agent directly
    gpt_result = test_gpt_agent()
    
    # Test the planner with GPT routing
    planner_result = test_planner_with_gpt()
    
    print("\nTests completed!") 