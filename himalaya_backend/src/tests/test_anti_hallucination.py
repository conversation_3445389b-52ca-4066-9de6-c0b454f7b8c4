"""
Test script for anti-hallucination measures in the Himalaya system.
"""

import os
import sys
import json

# Add the parent directory to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from agents.qa import create_qa_agent
from agents.rag.rag_planner_agent import RAGPlannerAgent

def test_qa_hallucination_detection():
    """Test the QA agent's ability to detect hallucinations."""
    print("\n=== Testing QA Agent Hallucination Detection ===")
    
    # Create the QA agent
    qa_agent = create_qa_agent(verbose=True)
    
    # Test case 1: Answer contains information not in references
    print("\nTest Case 1: Answer with hallucinated content")
    
    # Create a test state with hallucinated content
    test_state = {
        "query": "Tell me about TVS Motors",
        "conversation_history": None,
        "conversation_summary": None,
        "last_qa_pair": {},
        "answer": "TVS Motors is an Indian multinational motorcycle manufacturer headquartered in Chennai, India. It is the third largest motorcycle company in India with a revenue of over $2.9 billion. The company manufactures the popular Apache series of motorcycles and has a strong presence in both domestic and international markets.",
        "references": {
            "ref_1": "Hindustan Unilever Limited (HUL) is India's largest fast-moving consumer goods company with a heritage of over 85 years in India. The company's products include foods, beverages, cleaning agents, personal care products, and water purifiers.",
            "ref_2": "HUL's annual revenue for 2024-25 was ₹58,154 crores with a net profit of ₹9,485 crores."
        },
        "improvement_count": 0
    }
    
    # Run the QA agent
    result = qa_agent.graph.invoke(test_state)
    
    # Print the results
    print(f"Source adherence score: {result['evaluation']['evaluation'].get('source_adherence', 'N/A')}/5")
    print(f"Hallucinations detected: {result['evaluation'].get('hallucinations_detected', [])}")
    print(f"Needs improvement: {result['needs_improvement']}")
    if result['improvement_instructions']:
        print(f"Improvement instructions: {result['improvement_instructions']}")
    
    # Test case 2: Answer with no hallucinations
    print("\nTest Case 2: Answer with no hallucinations")
    
    # Create a test state with no hallucinations
    test_state = {
        "query": "Tell me about Hindustan Unilever Limited",
        "conversation_history": None,
        "conversation_summary": None,
        "last_qa_pair": {},
        "answer": "Hindustan Unilever Limited (HUL) is India's largest fast-moving consumer goods company with a heritage of over 85 years in India. The company's products include foods, beverages, cleaning agents, personal care products, and water purifiers. HUL's annual revenue for 2024-25 was ₹58,154 crores with a net profit of ₹9,485 crores.",
        "references": {
            "ref_1": "Hindustan Unilever Limited (HUL) is India's largest fast-moving consumer goods company with a heritage of over 85 years in India. The company's products include foods, beverages, cleaning agents, personal care products, and water purifiers.",
            "ref_2": "HUL's annual revenue for 2024-25 was ₹58,154 crores with a net profit of ₹9,485 crores."
        },
        "improvement_count": 0
    }
    
    # Run the QA agent
    result = qa_agent.graph.invoke(test_state)
    
    # Print the results
    print(f"Source adherence score: {result['evaluation']['evaluation'].get('source_adherence', 'N/A')}/5")
    print(f"Hallucinations detected: {result['evaluation'].get('hallucinations_detected', [])}")
    print(f"Needs improvement: {result['needs_improvement']}")
    
def test_rag_planner_topic_mismatch():
    """Test the RAG Planner's ability to detect topic mismatches."""
    print("\n=== Testing RAG Planner Topic Mismatch Detection ===")
    
    # Create the RAG Planner agent
    rag_planner = RAGPlannerAgent(verbose=True)
    
    # Test the entity extraction function
    print("\nTesting entity extraction:")
    test_texts = [
        "TVS Motors is a motorcycle manufacturer",
        "Hindustan Unilever Limited (HUL) is a consumer goods company",
        "The company reported strong financial performance"
    ]
    
    for text in test_texts:
        entities = rag_planner._extract_key_entities(text)
        print(f"Text: {text}")
        print(f"Extracted entities: {entities}")
    
    # Test topic mismatch detection
    print("\nTesting topic mismatch detection:")
    
    # Create a test context with a topic mismatch
    query = "Tell me about TVS Motors and their motorcycle lineup"
    context = "Hindustan Unilever Limited (HUL) is India's largest fast-moving consumer goods company with a heritage of over 85 years in India."
    
    query_entities = rag_planner._extract_key_entities(query.lower())
    context_entities = rag_planner._extract_key_entities(context.lower())
    
    print(f"Query entities: {query_entities}")
    print(f"Context entities: {context_entities}")
    
    query_only_entities = query_entities - context_entities
    print(f"Query-only entities: {query_only_entities}")
    
    if query_only_entities:
        print("✅ Topic mismatch detected correctly")
    else:
        print("❌ Failed to detect topic mismatch")

if __name__ == "__main__":
    # Run the tests
    test_qa_hallucination_detection()
    test_rag_planner_topic_mismatch()
    
    print("\nTests completed!") 