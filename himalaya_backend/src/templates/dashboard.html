<!DOCTYPE html>
<html>
<head>
    <title>Dashboard - HiGPT</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
        }
        .header {
            background-color: #0078d4;
            color: white;
            padding: 1rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .content {
            padding: 20px;
        }
        .user-info {
            margin-bottom: 20px;
            padding: 15px;
            background-color: #f5f5f5;
            border-radius: 4px;
        }
        .admin-panel {
            margin-top: 20px;
            padding: 15px;
            background-color: #e6f3ff;
            border-radius: 4px;
        }
        .logout-button {
            color: white;
            text-decoration: none;
            padding: 8px 16px;
            border-radius: 4px;
            background-color: rgba(255,255,255,0.1);
        }
        .logout-button:hover {
            background-color: rgba(255,255,255,0.2);
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Welcome, {{ user.get('name', 'User') }}!</h1>
        <a href="{{ url_for('auth.logout') }}" class="logout-button">Logout</a>
    </div>
    
    <div class="content">
        <div class="user-info">
            <h2>User Information</h2>
            <p><strong>Email:</strong> {{ user.get('email') }}</p>
            <p><strong>Login Time:</strong> {{ user.get('logged_in_at') }}</p>
        </div>

        {% if is_admin %}
        <div class="admin-panel">
            <h2>Admin Panel</h2>
            <p>You have administrative privileges.</p>
            <ul>
                <li><a href="/api/users">Manage Users</a></li>
                <li><a href="/api/departments">Manage Departments</a></li>
            </ul>
        </div>
        {% endif %}
    </div>
</body>
</html> 