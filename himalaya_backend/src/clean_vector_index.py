#!/usr/bin/env python3
"""
Vector Index Cleanup Script for Himalaya Backend

This script provides various options to clean the vector-1742884738327 index.
Use with caution as this will delete search data.

Usage:
    python clean_vector_index.py --help
    python clean_vector_index.py --delete-all --confirm
    python clean_vector_index.py --delete-by-file-ids 123,456,789
    python clean_vector_index.py --delete-older-than 2024-01-01
    python clean_vector_index.py --count-documents
    python clean_vector_index.py --count-documents --include-database                                                           
"""

import os
import sys
import argparse
import logging
from datetime import datetime, timezone
from typing import List, Optional
import traceback

# Add the src directory to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from azure.search.documents import SearchClient
from azure.core.credentials import AzureKeyCredential
from azure.core.exceptions import ResourceNotFoundError

from config.settings import (
    AZURE_SEARCH_SERVICE_ENDPOINT,
    AZURE_SEARCH_ADMIN_KEY,
    AZURE_SEARCH_ENHANCED_INDEX_NAME,
    FLASK_CONFIG
)

# Import database models for agentic table cleanup
from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from models.models import (
    db, ExcelFile, CSVFile, CSVEmbedding, 
    DocumentChunk, DocumentTable, FileProcessingMetadata
)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class VectorIndexCleaner:
    """
    A utility class to clean the vector search index and related database tables.
    """
    
    def __init__(self, include_database: bool = False):
        """Initialize the vector index cleaner."""
        try:
            self.index_name = AZURE_SEARCH_ENHANCED_INDEX_NAME
            credential = AzureKeyCredential(AZURE_SEARCH_ADMIN_KEY)
            self.search_client = SearchClient(
                endpoint=AZURE_SEARCH_SERVICE_ENDPOINT,
                index_name=self.index_name,
                credential=credential
            )
            logger.info(f"✅ Connected to Azure Search index: {self.index_name}")
            
            # Initialize database connection if requested
            self.include_database = include_database
            self.db_session = None
            if include_database:
                self._init_database()
                
        except Exception as e:
            logger.error(f"❌ Failed to connect to Azure Search: {str(e)}")
            raise
    
    def _init_database(self):
        """Initialize database connection for agentic table cleanup."""
        try:
            app = Flask(__name__)
            app.config.update(FLASK_CONFIG)
            
            db.init_app(app)
            self.app_context = app.app_context()
            self.app_context.push()
            
            logger.info("✅ Connected to database for agentic table cleanup")
        except Exception as e:
            logger.error(f"❌ Failed to connect to database: {str(e)}")
            self.include_database = False
    
    def count_documents(self) -> int:
        """
        Count total documents in the index.
        
        Returns:
            Number of documents
        """
        try:
            results = self.search_client.search(
                search_text="*",
                include_total_count=True,
                top=0  # We only want the count
            )
            count = results.get_count()
            logger.info(f"📊 Total documents in index '{self.index_name}': {count}")
            return count
        except Exception as e:
            logger.error(f"❌ Error counting documents: {str(e)}")
            return 0
    
    def count_database_records(self) -> dict:
        """
        Count records in agentic database tables.
        
        Returns:
            Dictionary with counts for each table
        """
        if not self.include_database:
            return {}
        
        try:
            counts = {
                'excel_files': ExcelFile.query.count(),
                'csv_files': CSVFile.query.count(),
                'csv_embeddings': CSVEmbedding.query.count(),
                'document_chunks': DocumentChunk.query.count(),
                'document_tables': DocumentTable.query.count(),
                'file_processing_metadata': FileProcessingMetadata.query.count()
            }
            
            logger.info("📊 Database table counts:")
            for table, count in counts.items():
                logger.info(f"   {table}: {count}")
            
            return counts
        except Exception as e:
            logger.error(f"❌ Error counting database records: {str(e)}")
            return {}
    
    def get_document_sample(self, count: int = 5) -> List[dict]:
        """
        Get a sample of documents from the index.
        
        Args:
            count: Number of sample documents to retrieve
            
        Returns:
            List of sample documents
        """
        try:
            results = self.search_client.search(
                search_text="*",
                top=count,
                select=['chunk_id', 'parent_id', 'title', 'metadata_storage_name']
            )
            
            samples = []
            for result in results:
                samples.append({
                    'chunk_id': result.get('chunk_id'),
                    'parent_id': result.get('parent_id'),
                    'title': result.get('title', '')[:100] + '...' if len(result.get('title', '')) > 100 else result.get('title', ''),
                    'metadata_storage_name': result.get('metadata_storage_name')
                })
            
            logger.info(f"📋 Retrieved {len(samples)} sample documents")
            return samples
            
        except Exception as e:
            logger.error(f"❌ Error retrieving sample documents: {str(e)}")
            return []
    
    def delete_all_documents(self, confirm: bool = False) -> bool:
        """
        Delete ALL documents from the index and optionally database tables.
        
        Args:
            confirm: Must be True to actually perform deletion
            
        Returns:
            True if successful
        """
        if not confirm:
            logger.warning("⚠️  Deletion not confirmed. Use --confirm flag to actually delete.")
            return False
        
        try:
            logger.warning(f"🚨 DELETING ALL DOCUMENTS from index '{self.index_name}'")
            
            # Delete from vector index
            success = self._delete_all_from_index()
            
            # Delete from database tables if requested
            if self.include_database:
                logger.warning("🚨 DELETING ALL AGENTIC DATABASE RECORDS")
                db_success = self._delete_all_from_database()
                success = success and db_success
            
            return success
            
        except Exception as e:
            logger.error(f"❌ Error deleting all documents: {str(e)}")
            logger.error(traceback.format_exc())
            return False
    
    def _delete_all_from_index(self) -> bool:
        """Delete all documents from the vector index."""
        try:
            # Get all document IDs in batches
            all_doc_ids = []
            skip = 0
            batch_size = 1000
            
            while True:
                results = self.search_client.search(
                    search_text="*",
                    select=['chunk_id'],
                    top=batch_size,
                    skip=skip
                )
                
                batch_ids = [result['chunk_id'] for result in results]
                if not batch_ids:
                    break
                
                all_doc_ids.extend(batch_ids)
                skip += batch_size
                
                logger.info(f"📥 Collected {len(all_doc_ids)} document IDs...")
            
            if not all_doc_ids:
                logger.info("✅ No documents found to delete from index")
                return True
            
            # Delete in batches (Azure Search has batch size limits)
            deleted_count = 0
            batch_size = 1000  # Azure Search batch limit
            
            for i in range(0, len(all_doc_ids), batch_size):
                batch = all_doc_ids[i:i + batch_size]
                delete_docs = [{'chunk_id': doc_id, '@search.action': 'delete'} for doc_id in batch]
                
                try:
                    result = self.search_client.upload_documents(delete_docs)
                    successful_deletes = sum(1 for r in result if r.succeeded)
                    deleted_count += successful_deletes
                    
                    logger.info(f"🗑️  Deleted batch {i//batch_size + 1}: {successful_deletes}/{len(batch)} documents from index")
                    
                except Exception as batch_error:
                    logger.error(f"❌ Error deleting batch {i//batch_size + 1}: {str(batch_error)}")
            
            logger.info(f"✅ Total documents deleted from index: {deleted_count}/{len(all_doc_ids)}")
            return deleted_count == len(all_doc_ids)
            
        except Exception as e:
            logger.error(f"❌ Error deleting from index: {str(e)}")
            return False
    
    def _delete_all_from_database(self) -> bool:
        """Delete all records from agentic database tables."""
        try:
            deleted_counts = {}
            
            # Delete in proper order (respecting foreign key constraints)
            tables_to_clean = [
                (CSVEmbedding, 'csv_embeddings'),
                (CSVFile, 'csv_files'),
                (ExcelFile, 'excel_files'),
                (DocumentChunk, 'document_chunks'),
                (DocumentTable, 'document_tables'),
                (FileProcessingMetadata, 'file_processing_metadata')
            ]
            
            for model_class, table_name in tables_to_clean:
                try:
                    count_before = model_class.query.count()
                    if count_before > 0:
                        deleted = model_class.query.delete()
                        db.session.commit()
                        deleted_counts[table_name] = deleted
                        logger.info(f"🗑️  Deleted {deleted} records from {table_name}")
                    else:
                        logger.info(f"ℹ️  No records to delete from {table_name}")
                        deleted_counts[table_name] = 0
                except Exception as table_error:
                    logger.error(f"❌ Error deleting from {table_name}: {str(table_error)}")
                    db.session.rollback()
                    return False
            
            total_deleted = sum(deleted_counts.values())
            logger.info(f"✅ Total database records deleted: {total_deleted}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Error deleting from database: {str(e)}")
            db.session.rollback()
            return False
    
    def delete_by_file_ids(self, file_ids: List[int]) -> bool:
        """
        Delete documents for specific file IDs from both index and database.
        
        Args:
            file_ids: List of file IDs to delete
            
        Returns:
            True if successful
        """
        try:
            total_deleted = 0
            
            for file_id in file_ids:
                logger.info(f"🗑️  Deleting documents for file ID: {file_id}")
                
                # Delete from vector index
                index_success = self._delete_file_from_index(file_id)
                
                # Delete from database if requested
                if self.include_database:
                    db_success = self._delete_file_from_database(file_id)
                    if not (index_success and db_success):
                        logger.warning(f"⚠️  Partial deletion for file {file_id}")
                else:
                    if not index_success:
                        logger.warning(f"⚠️  Failed to delete file {file_id} from index")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Error deleting by file IDs: {str(e)}")
            return False
    
    def _delete_file_from_index(self, file_id: int) -> bool:
        """Delete a specific file's documents from the vector index."""
        try:
            # Search for documents with this parent_id
            results = self.search_client.search(
                search_text="*",
                filter=f"parent_id eq 'file_{file_id}'",
                select=['chunk_id'],
                top=1000  # Adjust if you have files with more chunks
            )
            
            doc_ids = [result['chunk_id'] for result in results]
            
            if doc_ids:
                delete_docs = [{'chunk_id': doc_id, '@search.action': 'delete'} for doc_id in doc_ids]
                result = self.search_client.upload_documents(delete_docs)
                
                successful_deletes = sum(1 for r in result if r.succeeded)
                logger.info(f"✅ Deleted {successful_deletes}/{len(doc_ids)} documents from index for file {file_id}")
                return successful_deletes == len(doc_ids)
            else:
                logger.info(f"ℹ️  No documents found in index for file {file_id}")
                return True
                
        except Exception as e:
            logger.error(f"❌ Error deleting file {file_id} from index: {str(e)}")
            return False
    
    def _delete_file_from_database(self, file_id: int) -> bool:
        """Delete a specific file's records from agentic database tables."""
        try:
            deleted_counts = {}
            
            # Delete in proper order
            try:
                # CSV embeddings first
                csv_embeddings_deleted = CSVEmbedding.query.join(CSVFile).filter(CSVFile.original_file_id == file_id).delete(synchronize_session=False)
                deleted_counts['csv_embeddings'] = csv_embeddings_deleted
                
                # CSV files
                csv_files_deleted = CSVFile.query.filter(CSVFile.original_file_id == file_id).delete()
                deleted_counts['csv_files'] = csv_files_deleted
                
                # Excel files
                excel_files_deleted = ExcelFile.query.filter(ExcelFile.original_file_id == file_id).delete()
                deleted_counts['excel_files'] = excel_files_deleted
                
                # Document chunks
                chunks_deleted = DocumentChunk.query.filter(DocumentChunk.file_id == file_id).delete()
                deleted_counts['document_chunks'] = chunks_deleted
                
                # Document tables
                tables_deleted = DocumentTable.query.filter(DocumentTable.file_id == file_id).delete()
                deleted_counts['document_tables'] = tables_deleted
                
                # File processing metadata
                metadata_deleted = FileProcessingMetadata.query.filter(FileProcessingMetadata.file_id == file_id).delete()
                deleted_counts['file_processing_metadata'] = metadata_deleted
                
                db.session.commit()
                
                total_deleted = sum(deleted_counts.values())
                if total_deleted > 0:
                    logger.info(f"✅ Deleted {total_deleted} database records for file {file_id}")
                    for table, count in deleted_counts.items():
                        if count > 0:
                            logger.info(f"   {table}: {count}")
                else:
                    logger.info(f"ℹ️  No database records found for file {file_id}")
                
                return True
                
            except Exception as db_error:
                logger.error(f"❌ Database error for file {file_id}: {str(db_error)}")
                db.session.rollback()
                return False
                
        except Exception as e:
            logger.error(f"❌ Error deleting file {file_id} from database: {str(e)}")
            return False
    
    def delete_by_metadata_pattern(self, pattern: str) -> bool:
        """
        Delete documents matching a metadata pattern.
        
        Args:
            pattern: Pattern to match in metadata_storage_name
            
        Returns:
            True if successful
        """
        try:
            logger.info(f"🔍 Searching for documents with metadata pattern: {pattern}")
            
            # Use search.ismatch for pattern matching
            filter_expression = f"search.ismatch('{pattern}', 'metadata_storage_name')"
            
            results = self.search_client.search(
                search_text="*",
                filter=filter_expression,
                select=['chunk_id', 'metadata_storage_name'],
                top=1000
            )
            
            doc_ids = []
            for result in results:
                doc_ids.append(result['chunk_id'])
                logger.info(f"📄 Found: {result.get('metadata_storage_name')}")
            
            if doc_ids:
                delete_docs = [{'chunk_id': doc_id, '@search.action': 'delete'} for doc_id in doc_ids]
                result = self.search_client.upload_documents(delete_docs)
                
                successful_deletes = sum(1 for r in result if r.succeeded)
                logger.info(f"✅ Deleted {successful_deletes}/{len(doc_ids)} documents matching pattern")
                return successful_deletes == len(doc_ids)
            else:
                logger.info(f"ℹ️  No documents found matching pattern: {pattern}")
                return True
                
        except Exception as e:
            logger.error(f"❌ Error deleting by pattern: {str(e)}")
            return False
    
    def cleanup(self):
        """Cleanup resources."""
        if hasattr(self, 'app_context'):
            self.app_context.pop()

def main():
    """Main function to handle command line arguments."""
    parser = argparse.ArgumentParser(
        description='Clean Vector Index - Himalaya Backend',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Count documents (index only)
  python clean_vector_index.py --count-documents
  
  # Count documents and database records
  python clean_vector_index.py --count-documents --include-database
  
  # Show sample documents
  python clean_vector_index.py --show-sample
  
  # Delete specific files (index + database)
  python clean_vector_index.py --delete-by-file-ids 123,456,789 --include-database
  
  # Delete all documents (DANGEROUS!)
  python clean_vector_index.py --delete-all --confirm --include-database
  
  # Delete by file name pattern
  python clean_vector_index.py --delete-by-pattern "test_document"
        """
    )
    
    parser.add_argument('--count-documents', action='store_true',
                        help='Count total documents in the index')
    
    parser.add_argument('--show-sample', action='store_true',
                        help='Show sample documents from the index')
    
    parser.add_argument('--delete-all', action='store_true',
                        help='Delete ALL documents from the index (requires --confirm)')
    
    parser.add_argument('--delete-by-file-ids', type=str,
                        help='Delete documents for specific file IDs (comma-separated)')
    
    parser.add_argument('--delete-by-pattern', type=str,
                        help='Delete documents matching metadata pattern')
    
    parser.add_argument('--include-database', action='store_true',
                        help='Also clean agentic database tables (csv_files, excel_files, etc.)')
    
    parser.add_argument('--confirm', action='store_true',
                        help='Confirm destructive operations')
    
    args = parser.parse_args()
    
    if not any([args.count_documents, args.show_sample, args.delete_all, 
                args.delete_by_file_ids, args.delete_by_pattern]):
        parser.print_help()
        return
    
    cleaner = None
    try:
        cleaner = VectorIndexCleaner(include_database=args.include_database)
        
        if args.count_documents:
            count = cleaner.count_documents()
            print(f"\n📊 Index '{cleaner.index_name}' contains {count} documents")
            
            if args.include_database:
                db_counts = cleaner.count_database_records()
                if db_counts:
                    print(f"\n📊 Database table counts:")
                    for table, count in db_counts.items():
                        print(f"   {table}: {count}")
        
        if args.show_sample:
            samples = cleaner.get_document_sample(10)
            print(f"\n📋 Sample documents from index '{cleaner.index_name}':")
            for i, doc in enumerate(samples, 1):
                print(f"{i:2d}. ID: {doc['chunk_id']}")
                print(f"    Parent: {doc['parent_id']}")
                print(f"    Title: {doc['title']}")
                print(f"    File: {doc['metadata_storage_name']}")
                print()
        
        if args.delete_by_file_ids:
            file_ids = [int(fid.strip()) for fid in args.delete_by_file_ids.split(',')]
            action = "index and database" if args.include_database else "index only"
            print(f"\n🗑️  Deleting documents for file IDs ({action}): {file_ids}")
            success = cleaner.delete_by_file_ids(file_ids)
            if success:
                print("✅ Deletion completed successfully")
            else:
                print("❌ Deletion failed")
        
        if args.delete_by_pattern:
            print(f"\n🔍 Deleting documents matching pattern: {args.delete_by_pattern}")
            success = cleaner.delete_by_metadata_pattern(args.delete_by_pattern)
            if success:
                print("✅ Pattern deletion completed successfully")
            else:
                print("❌ Pattern deletion failed")
        
        if args.delete_all:
            if not args.confirm:
                print("\n⚠️  WARNING: This will delete ALL documents from the index!")
                if args.include_database:
                    print("⚠️  WARNING: This will also delete ALL agentic database records!")
                print("Use --confirm flag to proceed with deletion.")
                return
            
            action = "index and database" if args.include_database else "index only"
            print(f"\n🚨 DANGER: Deleting ALL documents from {action}")
            print("This action cannot be undone!")
            
            confirm_input = input("Type 'DELETE ALL' to confirm: ")
            if confirm_input == 'DELETE ALL':
                success = cleaner.delete_all_documents(confirm=True)
                if success:
                    print("✅ All documents deleted successfully")
                else:
                    print("❌ Deletion failed")
            else:
                print("❌ Deletion cancelled")
    
    except Exception as e:
        logger.error(f"❌ Script failed: {str(e)}")
        logger.error(traceback.format_exc())
        sys.exit(1)
    finally:
        if cleaner:
            cleaner.cleanup()

if __name__ == "__main__":
    main() 