-- Migration: Create SQL Chat Sessions and Update Query History
-- Date: 2024-01-25
-- Description: Create separate SQL chat session management and update query history to use proper foreign keys

-- Create SQL Chat Sessions table
CREATE TABLE sql_chat_sessions (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    database_id INTEGER NOT NULL REFERENCES database_connections(id) ON DELETE CASCADE,
    session_name VARCHAR(255),
    session_description TEXT,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    is_active BOOLEAN DEFAULT TRUE,
    
    -- Session metadata
    total_queries INTEGER DEFAULT 0,
    successful_queries INTEGER DEFAULT 0,
    last_activity_at TIMESTAMP DEFAULT NOW()
);

-- Add indexes for SQL chat sessions
CREATE INDEX idx_sql_chat_sessions_user_db ON sql_chat_sessions(user_id, database_id);
CREATE INDEX idx_sql_chat_sessions_user_active ON sql_chat_sessions(user_id, is_active);
CREATE INDEX idx_sql_chat_sessions_last_activity ON sql_chat_sessions(last_activity_at);

-- Add new columns to query_history table
ALTER TABLE query_history 
ADD COLUMN sql_session_id INTEGER REFERENCES sql_chat_sessions(id) ON DELETE SET NULL,
ADD COLUMN query_intent VARCHAR(50),
ADD COLUMN requires_visualization BOOLEAN DEFAULT FALSE,
ADD COLUMN chart_type VARCHAR(50),
ADD COLUMN processing_time_ms INTEGER;

-- Add indexes for new query_history columns
CREATE INDEX idx_query_history_sql_session ON query_history(sql_session_id);
CREATE INDEX idx_query_history_intent ON query_history(query_intent);

-- Create a function to update session activity
CREATE OR REPLACE FUNCTION update_sql_session_activity()
RETURNS TRIGGER AS $$
BEGIN
    -- Update session statistics when a new query is added
    IF TG_OP = 'INSERT' AND NEW.sql_session_id IS NOT NULL THEN
        UPDATE sql_chat_sessions 
        SET 
            total_queries = total_queries + 1,
            successful_queries = successful_queries + CASE WHEN NEW.was_successful THEN 1 ELSE 0 END,
            last_activity_at = NOW(),
            updated_at = NOW()
        WHERE id = NEW.sql_session_id;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically update session activity
CREATE TRIGGER trigger_update_sql_session_activity
    AFTER INSERT ON query_history
    FOR EACH ROW
    EXECUTE FUNCTION update_sql_session_activity();

-- Create a function to clean up old inactive sessions
CREATE OR REPLACE FUNCTION cleanup_old_sql_sessions(days_old INTEGER DEFAULT 30)
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    -- Delete queries from old inactive sessions
    DELETE FROM query_history 
    WHERE sql_session_id IN (
        SELECT id FROM sql_chat_sessions 
        WHERE last_activity_at < NOW() - INTERVAL '1 day' * days_old 
        AND is_active = FALSE
    );
    
    -- Delete old inactive sessions
    DELETE FROM sql_chat_sessions 
    WHERE last_activity_at < NOW() - INTERVAL '1 day' * days_old 
    AND is_active = FALSE;
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Create a view for session statistics
CREATE OR REPLACE VIEW sql_session_stats AS
SELECT 
    s.id,
    s.session_name,
    s.user_id,
    s.database_id,
    d.name as database_name,
    u.email as user_email,
    s.total_queries,
    s.successful_queries,
    CASE 
        WHEN s.total_queries > 0 THEN 
            ROUND((s.successful_queries::DECIMAL / s.total_queries) * 100, 2)
        ELSE 0 
    END as success_rate,
    s.created_at,
    s.last_activity_at,
    s.is_active,
    -- Recent activity indicators
    CASE 
        WHEN s.last_activity_at > NOW() - INTERVAL '1 hour' THEN 'active'
        WHEN s.last_activity_at > NOW() - INTERVAL '1 day' THEN 'recent'
        WHEN s.last_activity_at > NOW() - INTERVAL '7 days' THEN 'week'
        ELSE 'old'
    END as activity_status
FROM sql_chat_sessions s
JOIN database_connections d ON s.database_id = d.id
JOIN users u ON s.user_id = u.id;

-- Add comments for documentation
COMMENT ON TABLE sql_chat_sessions IS 'SQL chat sessions separate from document chat sessions';
COMMENT ON COLUMN sql_chat_sessions.session_name IS 'User-friendly name for the SQL chat session';
COMMENT ON COLUMN sql_chat_sessions.session_description IS 'Optional description of the session purpose';
COMMENT ON COLUMN sql_chat_sessions.total_queries IS 'Total number of queries executed in this session';
COMMENT ON COLUMN sql_chat_sessions.successful_queries IS 'Number of successful queries in this session';
COMMENT ON COLUMN sql_chat_sessions.last_activity_at IS 'Timestamp of last query in this session';

COMMENT ON COLUMN query_history.sql_session_id IS 'Foreign key to sql_chat_sessions table';
COMMENT ON COLUMN query_history.query_intent IS 'Intent of the query: retrieve, count, aggregate, etc.';
COMMENT ON COLUMN query_history.requires_visualization IS 'Whether the query results should be visualized';
COMMENT ON COLUMN query_history.chart_type IS 'Type of chart for visualization: bar, line, pie, etc.';
COMMENT ON COLUMN query_history.processing_time_ms IS 'Total processing time including planning and visualization';

-- Sample data for testing (optional - remove in production)
-- INSERT INTO sql_chat_sessions (user_id, database_id, session_name, session_description)
-- VALUES 
--     (1, 1, 'Sales Analysis Session', 'Analyzing Q4 sales data'),
--     (1, 1, 'Customer Insights', 'Understanding customer behavior patterns');

-- Grant permissions (adjust as needed for your setup)
-- GRANT SELECT, INSERT, UPDATE, DELETE ON sql_chat_sessions TO your_app_user;
-- GRANT SELECT ON sql_session_stats TO your_app_user;
-- GRANT EXECUTE ON FUNCTION cleanup_old_sql_sessions TO your_app_user;
