-- Create the user_navigation table
CREATE TABLE user_navigation (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,
    vertical_id INTEGER NOT NULL,
    department_id INTEGER NOT NULL,
    created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITHOUT TIME ZONE DEFAULT NOW(),
    
    -- Foreign key constraints
    CONSTRAINT fk_user_navigation_user_id FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    CONSTRAINT fk_user_navigation_vertical_id FOREIGN KEY (vertical_id) REFERENCES verticals(id) ON DELETE CASCADE,
    CONSTRAINT fk_user_navigation_department_id FOREIGN KEY (department_id) REFERENCES departments(id) ON DELETE CASCADE,
    
    -- Unique constraint to prevent duplicate combinations
    CONSTRAINT uq_user_navigation_combination UNIQUE (user_id, vertical_id, department_id)
);

-- Create indexes for better query performance
CREATE INDEX idx_user_navigation_user_id ON user_navigation(user_id);
CREATE INDEX idx_user_navigation_vertical_id ON user_navigation(vertical_id);
CREATE INDEX idx_user_navigation_department_id ON user_navigation(department_id);
CREATE INDEX idx_user_navigation_user_vertical ON user_navigation(user_id, vertical_id);

-- Create trigger function to automatically update the updated_at column
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger to automatically update the updated_at column
CREATE TRIGGER update_user_navigation_updated_at 
    BEFORE UPDATE ON user_navigation 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();