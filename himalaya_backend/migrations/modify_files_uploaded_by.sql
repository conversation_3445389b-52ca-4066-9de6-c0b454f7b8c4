-- First add archived_uploader_name if not exists
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'files' AND column_name = 'archived_uploader_name'
    ) THEN
        ALTER TABLE files ADD COLUMN archived_uploader_name VARCHAR(100);
    END IF;
END $$;

-- Update the uploaded_by constraint to allow NULL
ALTER TABLE files 
ALTER COLUMN uploaded_by DROP NOT NULL;

-- Add index on uploaded_by for better performance
CREATE INDEX IF NOT EXISTS idx_files_uploaded_by ON files(uploaded_by); 