-- Migration: Add vertical_id and department_id to chat_sessions table
-- Date: 2024-01-24
-- Description: Add vertical_id and department_id columns to chat_sessions table with foreign key constraints

-- Add the new columns
ALTER TABLE chat_sessions 
ADD COLUMN vertical_id INTEGER,
ADD COLUMN department_id INTEGER;

-- Add foreign key constraints
ALTER TABLE chat_sessions 
ADD CONSTRAINT fk_chat_sessions_vertical_id 
FOREIGN KEY (vertical_id) REFERENCES verticals(id) ON DELETE SET NULL;

ALTER TABLE chat_sessions 
ADD CONSTRAINT fk_chat_sessions_department_id 
FOREIGN KEY (department_id) REFERENCES departments(id) ON DELETE SET NULL;

-- Add indexes for better query performance
CREATE INDEX idx_chat_sessions_vertical_id ON chat_sessions(vertical_id);
CREATE INDEX idx_chat_sessions_department_id ON chat_sessions(department_id);
CREATE INDEX idx_chat_sessions_user_vertical ON chat_sessions(user_id, vertical_id);
CREATE INDEX idx_chat_sessions_user_department ON chat_sessions(user_id, department_id); 