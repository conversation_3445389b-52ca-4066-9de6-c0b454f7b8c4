"""create presentations table

Revision ID: create_presentations_table
Revises: <previous_revision_id>
Create Date: 2025-01-03 10:54:26.388975

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic
revision = 'create_presentations_table'
down_revision = '<previous_revision_id>'  # Replace with your last migration's revision ID
branch_labels = None
depends_on = None

def upgrade():
    op.create_table('presentations',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('theme_id', sa.Integer(), nullable=False),
        sa.Column('ppt_url', sa.Text(), nullable=False),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['theme_id'], ['themes.id'], ),
        sa.PrimaryKeyConstraint('id')
    )

def downgrade():
    op.drop_table('presentations') 