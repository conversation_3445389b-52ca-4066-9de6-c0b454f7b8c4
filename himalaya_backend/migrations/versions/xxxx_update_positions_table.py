"""update positions table

Revision ID: xxxx
Revises: previous_revision
Create Date: 2024-xx-xx

"""
from alembic import op
import sqlalchemy as sa

def upgrade():
    # Add level column
    op.add_column('positions', sa.Column('level', sa.Integer(), nullable=True))
    
    # Create temporary mapping for existing positions
    position_levels = {
        'Director': 20,
        'Senior Manager': 15,
        'Manager': 10,
        'Assistant Manager': 8,
        'Senior Executive': 6,
        'Executive': 4,
        'Associate': 2
    }
    
    # Update existing records
    for name, level in position_levels.items():
        op.execute(f"UPDATE positions SET level = {level} WHERE name = '{name}'")
    
    # Make level column not nullable and unique
    op.alter_column('positions', 'level',
                    existing_type=sa.Integer(),
                    nullable=False)
    op.create_unique_constraint('uq_positions_level', 'positions', ['level'])

def downgrade():
    op.drop_constraint('uq_positions_level', 'positions', type_='unique')
    op.drop_column('positions', 'level') 