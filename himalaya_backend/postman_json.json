{"info": {"name": "User and Master Management APIs", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Master APIs", "item": [{"name": "Get Verticals", "request": {"method": "GET", "url": "{{base_url}}/api/verticals", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}]}}, {"name": "Create Vertical", "request": {"method": "POST", "url": "{{base_url}}/api/verticals", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": {"name": "New Vertical Name"}}}}, {"name": "Update Vertical", "request": {"method": "PUT", "url": "{{base_url}}/api/verticals/{{vertical_id}}", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": {"name": "Updated Vertical Name"}}}}, {"name": "Delete Vertical", "request": {"method": "DELETE", "url": "{{base_url}}/api/verticals/{{vertical_id}}", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}]}}, {"name": "Get Departments by Vertical", "request": {"method": "GET", "url": "{{base_url}}/api/departments?vertical_id={{vertical_id}}", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}]}}, {"name": "Create Department", "request": {"method": "POST", "url": "{{base_url}}/api/departments", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": {"name": "New Department Name", "vertical_id": 1}}}}, {"name": "Update Department", "request": {"method": "PUT", "url": "{{base_url}}/api/departments/{{department_id}}", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": {"name": "Updated Department Name", "vertical_id": 1}}}}, {"name": "Delete Department", "request": {"method": "DELETE", "url": "{{base_url}}/api/departments/{{department_id}}", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}]}}]}, {"name": "User APIs", "item": [{"name": "Get Users", "request": {"method": "GET", "url": "{{base_url}}/api/users", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "query": [{"key": "page", "value": "1"}, {"key": "per_page", "value": "10"}]}}, {"name": "Create User", "request": {"method": "POST", "url": "{{base_url}}/api/users", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": {"user_name": "<PERSON>", "email": "<EMAIL>", "position_id": 1, "scopes": [1, 2], "vertical_ids": [1, 2], "departments": [{"vertical_id": 1, "department_ids": [1, 2]}, {"vertical_id": 2, "department_ids": [3, 4]}]}}}}, {"name": "Update User", "request": {"method": "PUT", "url": "{{base_url}}/api/users/{{user_id}}", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": {"user_name": "Updated Name", "position_id": 2, "scopes": [1, 2, 3], "vertical_ids": [1, 3], "departments": [{"vertical_id": 1, "department_ids": [1, 5]}, {"vertical_id": 3, "department_ids": [6, 7]}]}}}}, {"name": "Delete User", "request": {"method": "DELETE", "url": "{{base_url}}/api/users/{{user_id}}", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}]}}, {"name": "Get User Master Data", "request": {"method": "GET", "url": "{{base_url}}/api/users/{{user_id}}/master-data", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}]}}, {"name": "Get User Verticals", "request": {"method": "GET", "url": "{{base_url}}/api/users/{{user_id}}/verticals", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}]}}, {"name": "Update User Verticals", "request": {"method": "POST", "url": "{{base_url}}/api/users/{{user_id}}/verticals", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": {"vertical_ids": [1, 2, 3]}}}}, {"name": "Get User Departments", "request": {"method": "GET", "url": "{{base_url}}/api/users/{{user_id}}/departments", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "query": [{"key": "vertical_id", "value": "1"}]}}, {"name": "Update User Departments", "request": {"method": "POST", "url": "{{base_url}}/api/users/{{user_id}}/departments", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": {"vertical_id": 1, "department_ids": [1, 2, 3]}}}}]}], "variable": [{"key": "base_url", "value": "http://localhost:5001"}, {"key": "token", "value": "your-jwt-token-here"}]}