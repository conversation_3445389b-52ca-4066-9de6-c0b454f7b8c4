-- Create tables for master data
CREATE TABLE departments (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE positions (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE verticals (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE scopes (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE languages (
    id SERIAL PRIMARY KEY,
    name VARCHA<PERSON>(50) NOT NULL UNIQUE,
    code VARCHAR(10) NOT NULL UNIQUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create users table
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    user_name VARCHAR(100) NOT NULL,
    email VARCHAR(120) NOT NULL UNIQUE,
    ms_user_id VARCHAR(100) UNIQUE,
    department_id INTEGER REFERENCES departments(id),
    position_id INTEGER REFERENCES positions(id),
    vertical_id INTEGER REFERENCES verticals(id),
    scopes INTEGER[],
    is_admin BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create file-related tables
CREATE TABLE file_formats (
    id SERIAL PRIMARY KEY,
    name VARCHAR(50) NOT NULL UNIQUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE file_types (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE files (
    id SERIAL PRIMARY KEY,
    file_name VARCHAR(255) NOT NULL,
    file_format_id INTEGER REFERENCES file_formats(id),
    file_type_id INTEGER REFERENCES file_types(id),
    department_id INTEGER REFERENCES departments(id),
    vertical_id INTEGER REFERENCES verticals(id),
    position_id INTEGER REFERENCES positions(id),
    blob_url TEXT NOT NULL,
    uploaded_by INTEGER REFERENCES users(id) NOT NULL,
    upload_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create chat-related tables
CREATE TABLE chat_sessions (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) NOT NULL,
    session_name VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE
);

CREATE TABLE chat_messages (
    id SERIAL PRIMARY KEY,
    session_id INTEGER REFERENCES chat_sessions(id) NOT NULL,
    question TEXT NOT NULL,
    answer TEXT NOT NULL,
    sources JSONB,
    token_usage JSONB,
    search_metadata JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create FGD-related tables
CREATE TABLE themes (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    objective TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    interview_guide_language_id INTEGER REFERENCES languages(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE theme_owners (
    theme_id INTEGER REFERENCES themes(id),
    user_id INTEGER REFERENCES users(id),
    PRIMARY KEY (theme_id, user_id)
);

CREATE TABLE theme_languages (
    theme_id INTEGER REFERENCES themes(id),
    language_id INTEGER REFERENCES languages(id),
    PRIMARY KEY (theme_id, language_id)
);

CREATE TABLE theme_discussion_guides (
    id SERIAL PRIMARY KEY,
    theme_id INTEGER REFERENCES themes(id) NOT NULL,
    language_id INTEGER REFERENCES languages(id) NOT NULL,
    guide_url TEXT NOT NULL,
    is_base_language BOOLEAN DEFAULT FALSE
);

CREATE TABLE fgds (
    id SERIAL PRIMARY KEY,
    theme_id INTEGER REFERENCES themes(id) NOT NULL,
    language_id INTEGER REFERENCES languages(id) NOT NULL,
    group_size INTEGER NOT NULL,
    conductor_name VARCHAR(255) NOT NULL,
    discussion_date DATE NOT NULL,
    country VARCHAR(100),
    status VARCHAR(50) DEFAULT 'Pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE participants (
    id SERIAL PRIMARY KEY,
    fgd_id INTEGER REFERENCES fgds(id) NOT NULL,
    gender VARCHAR(10),
    age INTEGER,
    nationality VARCHAR(100),
    marital_status VARCHAR(50),
    has_children BOOLEAN
);

CREATE TABLE videos (
    id SERIAL PRIMARY KEY,
    fgd_id INTEGER REFERENCES fgds(id) NOT NULL,
    blob_url TEXT NOT NULL,
    audio_blob_url TEXT,
    transcription_blob_url TEXT,
    transcription_json_url TEXT,
    transcription_text TEXT,
    transcription_metadata JSONB,
    processing_status VARCHAR(50) DEFAULT 'pending',
    error_message TEXT,
    uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    processed_at TIMESTAMP,
    file_name VARCHAR(255),
    vector_blob_url TEXT
);

CREATE TABLE presentations (
    id SERIAL PRIMARY KEY,
    theme_id INTEGER REFERENCES themes(id) NOT NULL,
    ppt_url TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE fgd_chat_sessions (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id) NOT NULL,
    session_name VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE
);

CREATE TABLE fgd_chat_messages (
    id SERIAL PRIMARY KEY,
    session_id INTEGER REFERENCES fgd_chat_sessions(id) NOT NULL,
    question TEXT NOT NULL,
    answer TEXT NOT NULL,
    selected_video_ids INTEGER[],
    token_usage JSONB,
    search_metadata JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_ms_user_id ON users(ms_user_id);
CREATE INDEX idx_files_uploaded_by ON files(uploaded_by);
CREATE INDEX idx_files_upload_time ON files(upload_time);
CREATE INDEX idx_chat_messages_session_id ON chat_messages(session_id);
CREATE INDEX idx_chat_messages_created_at ON chat_messages(created_at);
CREATE INDEX idx_fgds_theme_id ON fgds(theme_id);
CREATE INDEX idx_fgds_language_id ON fgds(language_id);
CREATE INDEX idx_videos_fgd_id ON videos(fgd_id);
CREATE INDEX idx_videos_processing_status ON videos(processing_status);
CREATE INDEX idx_theme_discussion_guides_theme_id ON theme_discussion_guides(theme_id);

-- Insert master data
-- Languages
INSERT INTO languages (name, code) VALUES
('English', 'en'),
('Spanish', 'es'),
('French', 'fr'),
('German', 'de'),
('Italian', 'it'),
('Portuguese', 'pt'),
('Chinese', 'zh'),
('Japanese', 'ja'),
('Korean', 'ko'),
('Arabic', 'ar');

-- Scopes
INSERT INTO scopes (name) VALUES
('Chat Access'),           -- id: 1
('Upload Files'),          -- id: 2
('Create Theme'),          -- id: 3
('View Files'),           -- id: 4
('Create FGD'),           -- id: 5
('Process Videos'),       -- id: 6
('Generate Reports'),     -- id: 7
('Remove Files'),         -- id: 8
('FGD Chat Access');      -- id: 9

-- File Formats
INSERT INTO file_formats (name) VALUES
('PDF'),
('DOCX'),
('XLSX'),
('PPTX'),
('CSV'),
('TXT');

-- File Types
INSERT INTO file_types (name) VALUES
('Report'),
('Presentation'),
('Data Sheet'),
('Documentation'),
('Template'),
('Other');

-- Create trigger for updated_at timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply the trigger to all tables with updated_at
CREATE TRIGGER update_user_updated_at
    BEFORE UPDATE ON users
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_department_updated_at
    BEFORE UPDATE ON departments
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Add similar triggers for other tables with updated_at column 

-- Add triggers for remaining tables with updated_at column
CREATE TRIGGER update_position_updated_at
    BEFORE UPDATE ON positions
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_vertical_updated_at
    BEFORE UPDATE ON verticals
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_scope_updated_at
    BEFORE UPDATE ON scopes
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_language_updated_at
    BEFORE UPDATE ON languages
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_file_format_updated_at
    BEFORE UPDATE ON file_formats
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_file_type_updated_at
    BEFORE UPDATE ON file_types
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_chat_session_updated_at
    BEFORE UPDATE ON chat_sessions
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_theme_updated_at
    BEFORE UPDATE ON themes
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_fgd_updated_at
    BEFORE UPDATE ON fgds
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_fgd_chat_session_updated_at
    BEFORE UPDATE ON fgd_chat_sessions
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Add cascade delete rules to theme-related tables
ALTER TABLE theme_owners
    DROP CONSTRAINT theme_owners_theme_id_fkey,
    ADD CONSTRAINT theme_owners_theme_id_fkey 
    FOREIGN KEY (theme_id) 
    REFERENCES themes(id) 
    ON DELETE CASCADE;

ALTER TABLE theme_languages
    DROP CONSTRAINT theme_languages_theme_id_fkey,
    ADD CONSTRAINT theme_languages_theme_id_fkey 
    FOREIGN KEY (theme_id) 
    REFERENCES themes(id) 
    ON DELETE CASCADE;

ALTER TABLE theme_discussion_guides
    DROP CONSTRAINT theme_discussion_guides_theme_id_fkey,
    ADD CONSTRAINT theme_discussion_guides_theme_id_fkey 
    FOREIGN KEY (theme_id) 
    REFERENCES themes(id) 
    ON DELETE CASCADE;

-- Add additional useful indexes
CREATE INDEX idx_files_department_id ON files(department_id);
CREATE INDEX idx_files_vertical_id ON files(vertical_id);
CREATE INDEX idx_files_position_id ON files(position_id);
CREATE INDEX idx_users_department_id ON users(department_id);
CREATE INDEX idx_users_vertical_id ON users(vertical_id);
CREATE INDEX idx_users_position_id ON users(position_id);
CREATE INDEX idx_themes_is_active ON themes(is_active);
CREATE INDEX idx_chat_sessions_user_id ON chat_sessions(user_id);
CREATE INDEX idx_fgd_chat_sessions_user_id ON fgd_chat_sessions(user_id);

-- Insert sample departments
INSERT INTO departments (name) VALUES
('Sales'),
('Marketing'),
('Research'),
('Operations'),
('Human Resources'),
('Finance');

-- Insert sample positions
INSERT INTO positions (name) VALUES
('Manager'),
('Senior Analyst'),
('Analyst'),
('Associate'),
('Coordinator'),
('Director');

-- Insert sample verticals
INSERT INTO verticals (name) VALUES
('Retail'),
('Healthcare'),
('Technology'),
('Financial Services'),
('Education'),
('Manufacturing');

-- Create a default admin user (you should change the email to match your admin's email)
INSERT INTO users (
    user_name,
    email,
    is_admin,
    scopes
) VALUES (
    'Prasanna',
    '<EMAIL>',
    TRUE,
    ARRAY[1,2,3,4,5,6,7,8,9]  -- All scopes
);