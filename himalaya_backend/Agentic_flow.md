﻿# Himalaya Agentic Flow Documentation

## Overview

The Himalaya system implements a sophisticated agentic architecture that intelligently routes user queries through different processing pipelines based on content type and user intent. This document provides a detailed walkthrough of the agentic flow, including all functions, agents, and processing nodes.

## Entry Point and Core Architecture

### Main Entry Point
**Function**: `add_message()` in `src/api/chat_routes.py:310`

The agentic flow begins when a user sends a chat message to the `/chat/sessions/<session_id>/messages` endpoint.

#### Initial Processing Steps:
1. **Session Validation**: Validates chat session and user permissions
2. **Request Parsing**: Extracts query, web_search flag, deep_search flag, selected files
3. **File Access Control**: Applies user-based filtering (verticals, departments, permissions)
4. **Blob Name Extraction**: Converts file records to blob names for vector search
5. **Question Rephrasing**: Calls `rephrase_question()` for context-aware query optimization

### Question Rephrasing Process
**Function**: `rephrase_question()` in `src/utils/openai_utils.py:253`

```python
def rephrase_question(question, conversation_history=None)
```

**Process**:
1. Analyzes question intent and context relevance
2. Uses conversation history only if topically relevant
3. Preserves original intent while making query self-contained
4. Returns JSON with original and rephrased questions
5. Handles fallback to original question on errors

### Context Management
**Location**: `src/api/chat_routes.py:580-610`

**Conversation History Building**:
1. Retrieves last 5 relevant messages from database
2. Filters messages by relevance score and recency
3. Formats as structured conversation history
4. Creates conversation summary for context

## Core Agentic Processing Pipeline

### Main Agentic Handler
**Function**: `process_agentic_chat()` in `src/api/agentic_chat_handler.py:155`

```python
def process_agentic_chat(
    session_id: int,
    data: Dict[str, Any], 
    message_metadata: Dict[str, Any],
    relevant_previous_messages: List[ChatMessage],
    conversation_history: str,
    blob_names: List[str]
) -> Dict[str, Any]
```

**Flow**:
1. **User Preference Respect**: Checks for explicit file selection + web search disabled
2. **Planner Agent Invocation**: Routes through planner for autonomous decision making
3. **Agent Routing**: Directs to appropriate specialized agent
4. **QA Evaluation**: All responses go through quality assurance

### Planner Agent
**Class**: `PlannerAgent` in `src/agents/planner/agent.py:48`

**State**: `PlannerState` - Contains query, conversation context, agent type, analysis

**Key Nodes**:
1. **`analyze_query()`** - Analyzes intent and determines routing
2. **`determine_agent()`** - Finalizes agent selection based on flags

**Agent Types**:
- `WEB_SEARCH` - Web search queries
- `RAG` - Document-based queries  
- `CSV` - Structured data analysis
- `CONVERSATION` - General chat
- `SQL` - Database queries

## Four Processing Flows

## 1. PDF/Image/Document Files Flow

### Agent Routing
**Entry**: User selects PDF/image/doc files, web_search=False

### Process Flow

#### Step 1: RAG Agent (Vector Search)
**Handler**: `handle_rag_agent()` in `src/api/agentic_chat_handler.py:398`
**Agent**: `RAGAgent` in `src/agents/rag/rag_agent.py:57`

**Key Nodes**:
1. **`vector_search()`**: 
   - Creates embeddings using Azure OpenAI
   - Performs vector search on Azure AI Search
   - Applies score threshold filtering
   - Returns relevant document chunks

2. **`classify_documents()`**:
   - Separates CSV vs text documents
   - Prepares documents for specialized processing

#### Step 2: RAG Planner Agent (Document Processing)
**Agent**: `RAGPlannerAgent` in `src/agents/rag/rag_planner_agent.py:72`

**State**: `RAGPlannerState` - Contains classified documents and processing status

**Key Nodes**:

1. **`analyze_documents()`**:
   - Classifies documents by type (PDF/CSV/text)
   - Determines processing requirements
   - Analyzes intent for CSV vs document processing

2. **`process_pdf()`** (if PDF tables detected):
   - **Agent**: `PDFTableProcessor` in `src/agents/pdf/pdf_table_processor.py:25`
   - Handles table extraction from PDFs
   - Processes structured data within PDFs
   - Routes to CSV processing for tabular content

3. **`process_text()`** (for document content):
   - Generates comprehensive text-based responses
   - Uses document chunks for context
   - Handles narrative and explanatory content

4. **`generate_final_answer()`**:
   - Combines all processing results
   - Creates unified response with proper citations
   - Manages source attribution

#### Step 3: QA Evaluation
**Function**: `evaluate_answer_with_qa()` in `src/api/agentic_chat_handler.py:25`
**Agent**: `QAAgent` in `src/agents/qa/agent.py:36`

**QA State**: Contains evaluation metrics and improvement instructions

**Key Nodes**:
1. **`evaluate_answer()`**:
   - Scores completeness, relevance, clarity (1-5 scale)
   - Identifies missing information
   - Determines if improvement needed

2. **`determine_improvement()`**:
   - Decides if answer needs refinement
   - Provides specific improvement instructions
   - Manages improvement loop limits (max 3)

**Final Agent**: `agentic_rag_planner_qa` - Produces final answer

---

## 2. Excel/CSV Files Flow

### Agent Routing
**Entry**: User selects Excel/CSV files OR system detects tabular data intent

### Process Flow

#### Step 1: RAG Agent (Same as PDF flow)
**Process**: Identical vector search and document classification

#### Step 2: RAG Planner Agent (CSV Focus)

**Key Nodes**:

1. **`analyze_documents()`**:
   - **`_analyze_csv_intent()`**: Uses LLM to determine if query requires structured data analysis
   - Prioritizes CSV processing for data-focused queries

2. **`process_csv()`**:
   - **CSV Agent**: `ExcelLangChainRAG` in `src/agents/csv/excel_langchain_rag.py:43`
   - **Key Function**: `process_csv_directly()`
   
   **CSV Processing Pipeline**:
   - **Data Loading**: `load_csv_data()` - Handles blob URLs and local files
   - **Enhanced Metadata**: `_enhance_dataframe_metadata()` - Analyzes column types, detects dates
   - **Pandas Agent**: Uses `create_pandas_dataframe_agent()` for data analysis
   
   **Pandas Agent Capabilities**:
   - Intelligent table generation based on query intent
   - Automatic correlation analysis for relationship queries
   - Pivot tables for segmentation queries
   - Statistical analysis for overview queries
   - Outlier detection for anomaly queries
   - Business insights generation

3. **`evaluate_csv_result()`**:
   - **QA Agent**: `RAGQAAgent` in `src/agents/rag/rag_qa_agent.py:47`
   - Evaluates CSV analysis quality
   - Determines if additional CSV files needed

#### Step 3: Multiple CSV Processing (if needed)
**Process**: Iteratively processes additional CSV files until query satisfied

#### Step 4: Final QA Evaluation
**Same QA process as PDF flow**

**Final Agent**: `agentic_rag_planner_qa` with CSV analysis

---

## 3. Web Search Flow

### Agent Routing
**Entry**: User enables web_search=True OR planner determines web search needed

### Process Flow

#### Step 1: Web Search Agent
**Handler**: `handle_web_search_agent()` in `src/api/agentic_chat_handler.py:284`
**Agent**: `WebSearchAgent` in `src/agents/web/agent.py:50`

**State**: `WebSearchState` - Contains search results and scraped content

**Key Nodes**:

1. **`check_previous_content()`**:
   - Checks for previously scraped content from same session
   - Avoids redundant web scraping

2. **`search()`**:
   - Performs Bing web search
   - Extracts relevant URLs
   - Handles different content types (web pages, PDFs, YouTube)

3. **`scrape_content()`**:
   - **Multi-format Support**:
     - Web pages: Uses BeautifulSoup with multiple fallbacks
     - PDFs: Uses LangChain PyPDFLoader
     - YouTube videos: Extracts transcripts
   - **Timeout Handling**: 30-second timeout with graceful fallback
   - **Content Storage**: Saves scraped content to blob storage

4. **`generate_response()`**:
   - Uses scraped content as context
   - Generates comprehensive web-based responses
   - Maintains source URL references

#### Step 2: QA Evaluation Loop
**Process**: Iterative improvement up to 3 loops

**Improvement Loop**:
1. Web agent generates initial response
2. QA agent evaluates response quality
3. If improvement needed, web agent refines response
4. Continues until quality threshold met or max loops reached

**Final Agent**: `agentic_web_search_qa` - Produces final web-enhanced answer

---

## 4. Deep Search Flow

### Agent Routing
**Entry**: User enables deep_search=True (also called deep_think)

### Process Flow

#### Step 1: Enhanced RAG Agent (Deep Search Mode)
**Same RAG Agent but with enhanced parameters**

**Deep Search Configuration**:
- **Budget**: 60 initial results → 12 final results (vs 15 → 8 standard)
- **Per-file Strategy**: Uses `PerFileRerankingService`
- **Guaranteed Coverage**: Ensures all selected files represented

#### Step 2: Per-File Reranking Service
**Service**: `PerFileRerankingService` in `src/services/per_file_reranking_service.py:39`

**Key Functions**:

1. **`perform_per_file_search()`**:
   - Allocates search budget across files
   - Ensures balanced file representation
   - Applies intelligent reranking

2. **`_calculate_file_allocations()`**:
   - Distributes search budget fairly
   - Considers file count and query requirements

3. **`_search_and_rerank_single_file()`**:
   - Searches individual files with allocated budget
   - Reranks results within each file
   - Maintains file-specific context

#### Step 3: Enhanced RAG Planner Processing
**Same RAG Planner flow but with:**
- Higher quality document chunks
- Better file coverage
- More comprehensive analysis
- Enhanced context preservation

#### Step 4: Deep QA Evaluation
**Enhanced QA process with:**
- Higher evaluation standards
- More thorough completeness checking
- Enhanced improvement instructions

**Final Agent**: `agentic_rag_planner_qa` with deep search enhancement

---

## Quality Assurance Architecture

### QA Agent System
**All processing flows** include QA evaluation as final step

#### QA State Management
```python
class QAState(TypedDict):
    query: str
    conversation_history: Optional[str]
    answer: str
    references: Dict[str, str]
    evaluation: Dict[str, Any]
    needs_improvement: bool
    improvement_count: int
    improvement_instructions: Optional[str]
```

#### Evaluation Metrics
1. **Completeness** (1-5): Does answer fully address query?
2. **Relevance** (1-5): Is content relevant to user's intent?
3. **Clarity** (1-5): Is answer clear and well-structured?

#### Improvement Loop Process
1. **Initial Response**: Agent generates first answer
2. **QA Evaluation**: Scores response on three metrics
3. **Improvement Decision**: Determines if refinement needed
4. **Iterative Refinement**: Up to 3 improvement cycles
5. **Final Approval**: QA approves final response

### QA Integration Points
- **Web Search**: After each web search attempt
- **RAG Processing**: After document analysis
- **CSV Analysis**: After data processing
- **Final Response**: Before returning to user

---

## Context and State Management

### Conversation Context
**Management**: Distributed across all agents

#### Context Types
1. **Conversation History**: Last 5 relevant messages
2. **Conversation Summary**: AI-generated summary of session
3. **User Context**: File selections, preferences, constraints
4. **Processing Context**: Agent metadata, improvement instructions

#### Context Propagation
- **Planner → Specialized Agents**: Query context and intent
- **RAG → CSV/PDF Agents**: Document context and conversation history  
- **QA → All Agents**: Evaluation feedback and improvement instructions
- **Final Response**: Comprehensive context preservation

### State Transitions
Each agent maintains typed state with proper transitions:

1. **PlannerState** → **AgentType** decision
2. **RAGState** → **Document classification**
3. **RAGPlannerState** → **Specialized processing**
4. **QAState** → **Quality evaluation**
5. **Final Response** → **User delivery**

---

## Agent Coordination and Handoffs

### Multi-Agent Orchestration
**Coordinator**: RAG Planner Agent acts as primary orchestrator

#### Agent Handoff Pattern
1. **Document Classification**: RAG → RAG Planner
2. **Specialized Processing**: RAG Planner → CSV/PDF Agents
3. **Quality Assurance**: All Agents → QA Agent  
4. **Iterative Refinement**: QA → Original Agent (if needed)

#### Metadata Propagation
- **Processing Time**: Tracked across all agents
- **Token Usage**: Accumulated from all LLM calls
- **Source Attribution**: Maintained through all transformations
- **Agent Genealogy**: Tracks which agents contributed to final answer

### Error Handling and Fallbacks
**Graceful Degradation**: Each agent has fallback mechanisms

#### Fallback Hierarchy
1. **Primary Agent Failure** → **Fallback Processing**
2. **CSV Processing Error** → **Document Processing**
3. **Web Search Timeout** → **Cached Content**
4. **QA Evaluation Error** → **Default Approval**

---

## Performance and Monitoring

### Logging Architecture
**Multi-level Logging**: Performance, agentic, and error logging

#### Performance Tracking
- **Agent Execution Time**: Individual agent performance
- **Vector Search Performance**: Search quality and speed
- **Token Usage**: LLM cost optimization
- **User Experience**: End-to-end response time

#### Agentic Logging
- **Agent Decisions**: Planner routing decisions
- **Multi-Agent Coordination**: Agent handoff tracking
- **Improvement Loops**: QA evaluation cycles
- **Context Management**: Conversation state tracking

### Configuration Parameters

#### Vector Search Configuration
- **Standard Search**: 15 → 8 results
- **Deep Search**: 60 → 12 results
- **Score Threshold**: 0.5 (standard), 0.45 (deep)
- **Conversation History**: 5 messages max

#### QA Evaluation Limits
- **Max Improvement Loops**: 3 per flow
- **Evaluation Timeout**: 30 seconds
- **Quality Threshold**: 4/5 overall score

---

## Final Answer Producers

Each flow has a specific final agent type that produces the ultimate response:

### Agent Type Mapping
1. **PDF/Document Flow**: `agentic_rag_planner_qa`
2. **Excel/CSV Flow**: `agentic_rag_planner_qa` (with CSV analysis)
3. **Web Search Flow**: `agentic_web_search_qa`
4. **Deep Search Flow**: `agentic_rag_planner_qa` (with deep search enhancement)
5. **Conversation Flow**: `agentic_conversation_qa`

### Response Structure
All final agents return standardized response format:
```python
{
    'answer': str,
    'sources': List[Dict],
    'agent_type': str,
    'processing_time': float,
    'agents_used': List[str],
    'token_usage': Dict,
    'qa_evaluation': Dict
}
```

---

## Summary

The Himalaya agentic system provides a sophisticated multi-agent architecture that:

1. **Intelligently Routes** queries based on content type and user intent
2. **Maintains Context** across conversation sessions and agent handoffs
3. **Ensures Quality** through comprehensive QA evaluation loops
4. **Optimizes Performance** with configurable search budgets and timeouts
5. **Provides Transparency** through detailed logging and metadata tracking

This architecture ensures optimal response quality while maintaining system performance and user experience across all content types and search modalities.
