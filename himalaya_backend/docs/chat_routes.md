##### Imports

```
# Flask-related imports
from flask import Blueprint, request, jsonify, send_file
# Blueprint: Used to organize routes/endpoints in a modular way
# request: Handles incoming HTTP requests and their data
# jsonify: Converts Python dictionaries/lists to JSON responses
# send_file: Helps send files to the client (used for file downloads)

from flask_cors import CORS
# CORS (Cross-Origin Resource Sharing): Allows your API to be accessed from different domains/origins
# Important for web security and when your frontend is on a different domain than your backend

# Database models
from models.models import db, ChatSession, ChatMessage, User, File, Position, Department
# db: The database instance
# Other imports are database models representing different tables in your database
# (ChatSession, ChatMessage, etc. are like templates for database records)

# Custom utility functions and decorators
from utils.decorators import login_required, require_scope
from utils.openai_utils import get_ai_response, rephrase_question
from utils.blob_utils import generate_sas_url
# login_required: Decorator to ensure user is logged in
# require_scope: Decorator to check user permissions
# get_ai_response, rephrase_question: Functions for AI processing
# generate_sas_url: Creates secure URLs for accessing files

from datetime import datetime
# For handling dates and times, used for timestamps

# Azure-related imports
from azure.search.documents import SearchClient
from azure.core.credentials import AzureKeyCredential
from openai import AzureOpenAI
# These are for interacting with Azure services:
# - Azure Cognitive Search (for searching through documents)
# - Azure OpenAI (for AI capabilities)

import logging
# For logging errors and debug information

# Configuration settings
from config.settings import (
    AZURE_SEARCH_SERVICE_ENDPOINT, 
    AZURE_SEARCH_INDEX_NAME, 
    AZURE_SEARCH_ADMIN_KEY,
    AZURE_OPENAI_KEY, 
    AZURE_OPENAI_ENDPOINT, 
    AZURE_OPENAI_API_VERSION,
    AZURE_OPENAI_DEPLOYMENT_NAME, 
    AZURE_OPENAI_EMBEDDING_DEPLOYMENT
)
# These are configuration variables for connecting to Azure services

# File handling imports
from io import BytesIO, StringIO
import csv
# BytesIO, StringIO: For handling file-like objects in memory
# csv: For working with CSV files (used in export functionality)
```
These imports can be grouped into several categories:
1. Web Framework Tools (Flask-related):
    - Essential for building the web API
    - Handle requests, responses, and routing
2. Database Tools:
    - Models for interacting with your database
    - Define the structure of your data
3. Azure Services:
    - Connect to Azure's AI and search services
    - Enable AI chat and document search features
4. Utility Functions:
    - Custom helpers for authentication
    - File handling
    - AI processing
5. Standard Library Imports:
    - datetime: For timestamp management
    - logging: For error tracking
    - csv: For file exports
    - io: For file-like object handling
This is a fairly complex application that:
- Handles web requests
- Interacts with a database
- Uses AI services
- Manages file operations
- Implements security features
- Provides export capabilities

Each import brings in specific functionality needed for these different aspects of the application. It's normal to have many imports in a production application as it usually requires multiple libraries and tools working together.

##### 1. Logger Setup

```
 logger = logging.getLogger(__name__)
```
- This creates a logger object that helps track what's happening in your application
- Think of it like a digital diary that records errors and important events
- __name__ is a special Python variable that contains the current module's name

##### 2. Blueprint Creation:

```
chat_bp = Blueprint('chat', __name__)
```
- A Blueprint is like a template for organizing related routes (URLs) in your application
- Think of it as grouping all chat-related features together
- 'chat' is the name of this blueprint
- This helps keep your code organized when you have many different features

##### 3. CORS Setup:

```
CORS(chat_bp, supports_credentials=True)
```
- CORS (Cross-Origin Resource Sharing) is a security feature
- It controls which websites can access your API
- supports_credentials=True allows the API to handle login sessions
- This is important when your frontend (website) is on a different domain than your backend (API)

##### 4. Azure OpenAI Client Setup:

```
openai_client = AzureOpenAI(
    api_key=AZURE_OPENAI_KEY,
    api_version=AZURE_OPENAI_API_VERSION,
    azure_endpoint=AZURE_OPENAI_ENDPOINT
)
```
- Creates a connection to Azure's OpenAI service
- Like setting up a phone line to talk to Azure's AI
- Requires three pieces of information:
    - api_key: Your secret password to use the service
    - api_version: Which version of the service you're using
    - azure_endpoint: Where to find the service (like an address)

##### 5. Azure Client Function

```
def get_search_client():
    try:
        credential = AzureKeyCredential(AZURE_SEARCH_ADMIN_KEY)
        return SearchClient(
            endpoint=AZURE_SEARCH_SERVICE_ENDPOINT,
            index_name=AZURE_SEARCH_INDEX_NAME,
            credential=credential
        )
    except Exception as e:
        raise Exception(f"Failed to connect to Azure AI Search: {str(e)}")
```
- This function creates a connection to Azure's Search service
- It's wrapped in a try/except block to handle errors gracefully
- Components:
    - credential: Your authentication token (like an ID card)
    - endpoint: Where to find the search service
    - index_name: Which collection of documents to search
- If something goes wrong, it raises an error with a helpful message

##### 6. Scope Constant:

```
SCOPE_CHAT = "chat"
```
- This is a permission level identifier
- Used to control who can access chat features
- The number 1 is likely used in permission checks
- Think of it like an access level card in a building

This code sets up the basic infrastructure for:
- Logging (tracking what happens)
- Routing (organizing URLs)
- Security (controlling access)
- External Services (connecting to Azure)

It's like setting up the foundation and utilities before building a house. These components are essential for the rest of the chat functionality to work properly.

##### API Route - 1 : Get all chat sessions ('/chat/sessions', methods=['GET'])

![alt text](image-1.png)

This function is typically used when displaying a list of chat conversations in a user interface, allowing users to browse through their chat history efficiently.

```
# This decorator defines a new route/URL endpoint for GET requests to '/chat/sessions'
@chat_bp.route('/chat/sessions', methods=['GET'])

# These decorators ensure:
# 1. The user is logged in
# 2. The user has permission to access chat features
@login_required
@require_scope(SCOPE_CHAT)

# Function to get all chat sessions for the current user
def get_sessions():
    try:
        # Get query parameters from the URL with default values
        # Example URL: /chat/sessions?page=2&per_page=20
        # If not provided, page defaults to 1 and per_page to 10
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 10, type=int)
        
        # Query the database for chat sessions:
        # 1. filter_by(): Only get sessions for the current user
        # 2. order_by(): Sort by update time, newest first
        # 3. paginate(): Split results into pages
        sessions = ChatSession.query.filter_by(
            user_id=request.user.id
        ).order_by(ChatSession.updated_at.desc()).paginate(
            page=page,          # Which page to show
            per_page=per_page,  # How many items per page
            error_out=False     # Don't raise error for invalid pages
        )

        # Return a JSON response with status code 200 (OK)
        return jsonify({
            # List of sessions for the current page
            # This is a list comprehension that creates a dictionary for each session
            'items': [{
                'id': session.id,                              # Unique session ID
                'session_name': session.session_name,          # Name of the session
                'created_at': session.created_at.isoformat(),  # Creation time in ISO format
                'updated_at': session.updated_at.isoformat(),  # Last update time
                'is_active': session.is_active,                # Whether session is active
                'message_count': len(session.messages)         # Number of messages in session
            } for session in sessions.items],

            # Pagination information to help with navigation
            'pagination': {
                'total_items': sessions.total,    # Total number of sessions
                'total_pages': sessions.pages,    # Total number of pages
                'current_page': sessions.page,    # Current page number
                'per_page': per_page,            # Items per page
                'has_next': sessions.has_next,   # Whether there's a next page
                'has_prev': sessions.has_prev    # Whether there's a previous page
            }
        }), 200  # 200 is the HTTP status code for success

    # If any error occurs during the process
    except Exception as e:
        # Return error message with status code 500 (Server Error)
        return jsonify({'error': str(e)}), 500
```
Key Features:
- Pagination: Instead of returning all sessions at once, it returns them in smaller chunks (pages)
- Security: Only shows sessions belonging to the logged-in user
- Sorting: Shows newest sessions first
- Error Handling: Safely handles any problems that might occur
- Information Rich: Provides detailed information about each session and the pagination status



##### API Route - 2 : Create a new chat session ('/chat/sessions', methods=['POST'])

![alt text](image.png)

This function is typically called when a user wants to start a new chat conversation in the application. It creates a new session in the database and returns the details of that session to the client.

```
# This line creates a new route/endpoint at '/chat/sessions' that only accepts POST requests
@chat_bp.route('/chat/sessions', methods=['POST'])

# These are decorators that:
# - @login_required: ensures the user is logged in before they can access this function
# - @require_scope(SCOPE_CHAT): checks if the user has permission to use chat features
@login_required
@require_scope(SCOPE_CHAT)

# This defines the function that will handle the request
def create_session():
    try:
        # Get the JSON data sent with the request
        data = request.json
        
        # Get the session_name from the data, or create a default name if none provided
        # Default format example: "Chat 2024-03-20 14:30"
        session_name = data.get('session_name', f"Chat {datetime.now().strftime('%Y-%m-%d %H:%M')}")

        # Create a new ChatSession object with:
        # - user_id: the ID of the currently logged-in user
        # - session_name: either the provided name or the default one
        session = ChatSession(
            user_id=request.user.id,
            session_name=session_name
        )
        
        # Add the new session to the database
        db.session.add(session)
        # Save the changes to the database
        db.session.commit()

        # Return a success response (status code 201 means "Created")
        # Including the session details in JSON format
        return jsonify({
            'id': session.id,
            'session_name': session.session_name,
            'created_at': session.created_at.isoformat()
        }), 201

    # If anything goes wrong:
    except Exception as e:
        # Undo any database changes that weren't committed
        db.session.rollback()
        # Return an error response (status code 500 means "Server Error")
        return jsonify({'error': str(e)}), 500
```
Key Concepts to understand:
- @login_required: ensures the user is logged in before they can access this function
- @require_scope(SCOPE_CHAT): checks if the user has permission to use chat features
- request.user.id: the ID of the currently logged-in user
- request.json: the JSON data sent with the request
- db.session.add(session): adds the new session to the database
- db.session.commit(): saves the changes to the database
- jsonify: converts the response to JSON format
- 201: HTTP status code for "Created"
- 500: HTTP status code for "Server Error"

##### API Route - 3 : Get messages for a specific chat session ('/chat/sessions/<int:session_id>/messages', methods=['GET'])

![alt text](image-2.png) --->
![alt text](image-5.png)
This function is like a librarian that helps you get messages from a chat conversation.
```
# This route handles GET requests to fetch messages for a specific chat session
# <int:session_id> in the URL means it expects a number (session ID) in the URL
@chat_bp.route('/chat/sessions/<int:session_id>/messages', methods=['GET'])
# Ensures user is logged in before accessing this function
@login_required
# Checks if user has permission to use chat features
@require_scope(SCOPE_CHAT)
def get_session_messages(session_id):
    try:
        # Get pagination parameters from URL query string
        # Default to page 1 if not specified
        # Example URL: /chat/sessions/123/messages?page=2&per_page=30
        page = request.args.get('page', 1, type=int)
        # Number of messages per page (default 20)
        per_page = request.args.get('per_page', 20, type=int)
        
        # Find the chat session in database
        # Ensures the session exists and belongs to the current user
        # first_or_404() returns 404 error if not found
        session = ChatSession.query.filter_by(
            id=session_id, 
            user_id=request.user.id
        ).first_or_404()

        # Get all messages for this session with pagination
        # order_by() sorts messages by creation time
        messages = ChatMessage.query.filter_by(
            session_id=session_id
        ).order_by(ChatMessage.created_at).paginate(
            page=page,
            per_page=per_page,
            error_out=False  # Don't raise error for empty pages
        )

        # List to store processed messages
        processed_messages = []
        
        # Process each message in the current page
        for msg in messages.items:
            # List to store processed sources for this message
            processed_sources = []
            
            # If message has sources (referenced documents/files)
            if msg.sources:
                # Process each source
                for source in msg.sources:
                    # Create a copy of the source to avoid modifying original
                    source_copy = source.copy()
                    # Check if source has file information
                    if isinstance(source_copy, dict) and 'file_info' in source_copy:
                        file_info = source_copy['file_info']
                        # If file info contains a blob URL
                        if isinstance(file_info, dict) and 'blob_url' in file_info:
                            try:
                                # Generate new secure access URL (SAS = Shared Access Signature)
                                file_info['blob_url'] = generate_sas_url(file_info['blob_url'])
                            except Exception as e:
                                # Log error if URL generation fails
                                logger.error(f"Error generating SAS URL: {str(e)}")
                    # Add processed source to list
                    processed_sources.append(source_copy)

            # Create a dictionary with message information
            processed_messages.append({
                'id': msg.id,
                'question': msg.question,
                'answer': msg.answer,
                'sources': processed_sources,  # Include processed sources
                'created_at': msg.created_at.isoformat(),  # Convert datetime to string
                # Get file IDs if they exist in metadata
                'file_ids': msg.search_metadata.get('selected_file_ids') if msg.search_metadata else None
            })

        # Return successful response (status code 200)
        return jsonify({
            'items': processed_messages,  # List of processed messages
            'pagination': {  # Pagination information
                'total_items': messages.total,  # Total number of messages
                'total_pages': messages.pages,  # Total number of pages
                'current_page': messages.page,  # Current page number
                'per_page': per_page,          # Items per page
                'has_next': messages.has_next, # Whether there's a next page
                'has_prev': messages.has_prev  # Whether there's a previous page
            }
        }), 200

    # Handle any errors that occur
    except Exception as e:
        # Log the error
        logger.error(f"Error in get_session_messages: {str(e)}")
        # Return error response (status code 500)
        return jsonify({'error': str(e)}), 500
```

- Purpose: It retrieves messages from a specific chat session, showing them page by page (like a book)
- Security:
    - Checks if the user is logged in
    - Verifies they have permission to view chat messages
    - Ensures they can only see their own chat sessions
- Pagination:
    - Instead of returning all messages at once, it returns them in smaller chunks
    - This is like having a book with multiple pages instead of one very long scroll
    - You can specify which page you want and how many messages per page
- Processing:
    - For each message, it:
        - Gets the question and answer
        - Processes any attached files or sources
        - Updates secure access links for files
        - Formats dates into readable strings
- Response:
    - Returns a neat package containing:
        - The messages for the requested page
        - Information about total pages and navigation
        - Any error messages if something goes wrong

##### API Route - 4 : Update session name ('/chat/sessions/<int:session_id>', methods=['PUT'])

![alt text](image-3.png)

Think of this function like updating the name of a folder
```
# This route handles PUT requests to update a chat session's name
# PUT is typically used for updating existing resources
# <int:session_id> means it expects a number in the URL (e.g., /chat/sessions/123)
@chat_bp.route('/chat/sessions/<int:session_id>', methods=['PUT'])

# Security decorators:
# Ensures the user is logged in before they can use this function
@login_required
# Checks if the user has permission to use chat features
@require_scope(SCOPE_CHAT)

def update_session(session_id):
    try:
        # Get the JSON data sent with the request
        # Example request body: {"session_name": "My New Chat Name"}
        data = request.json
        
        # Check if data exists and contains a session_name
        # If not, return an error (400 = Bad Request)
        if not data or 'session_name' not in data:
            return jsonify({
                'error': 'No session name provided'
            }), 400

        # Find the chat session in the database that matches:
        # 1. The provided session_id
        # 2. Belongs to the current user (user_id)
        # first_or_404() will return a 404 error if not found
        session = ChatSession.query.filter_by(
            id=session_id, 
            user_id=request.user.id
        ).first_or_404()

        # Update the session's name with the new name
        session.session_name = data['session_name']
        
        # Update the timestamp to show when this change was made
        session.updated_at = datetime.utcnow()
        
        # Save the changes to the database
        db.session.commit()

        # Return a success response (200 = OK) with the updated session info
        return jsonify({
            'id': session.id,
            'session_name': session.session_name,
            # Convert datetime to string format for JSON
            'updated_at': session.updated_at.isoformat()
        }), 200

    # If anything goes wrong during the process:
    except Exception as e:
        # Undo any database changes that weren't committed
        db.session.rollback()
        
        # Return an error response (500 = Server Error)
        return jsonify({
            'error': str(e)
        }), 500
```

- Purpose:
    - It allows users to rename their chat sessions
    - Like renaming a folder or conversation thread
- Security Checks:
    - Makes sure the user is logged in
    - Verifies they have permission to use chat features
    - Ensures users can only modify their own chat sessions
- Process:
    - Receives a new name from the user
    - Finds the correct chat session
    - Updates the name
    - Records when the change was made
    - Saves everything to the database
- Error Handling:
    - Checks if a new name was provided
    - Handles cases where the session doesn't exist
    - Deals with any other errors that might occur
    - Undoes any partial changes if something goes wrong
- Response:
    - If successful: Returns the updated session information
    - If failed: Returns an error message explaining what went wrong
- Real-world Example:
    - Imagine you have a chat app where you can rename your conversations. This function handles the process when you click "Rename" and type in a new name. It's like when you rename a file in your computer, but with added security to make sure only the right person can make changes.

##### API Route - 5 : Delete a chat session ('/chat/sessions/<int:session_id>', methods=['DELETE'])

![alt text](image-6.png)

Think of this function like deleting a folder with all its contents.
```
# This route handles DELETE requests to remove a chat session and all its messages
# DELETE is the HTTP method used when we want to remove a resource
# <int:session_id> in the URL means it expects a number (e.g., /chat/sessions/123)
@chat_bp.route('/chat/sessions/<int:session_id>', methods=['DELETE'])

# Security decorators:
# Makes sure the user is logged in before they can delete anything
@login_required
# Verifies the user has permission to use chat features
@require_scope(SCOPE_CHAT)

def delete_session(session_id):
    try:
        # Find the chat session in the database that:
        # 1. Matches the provided session_id
        # 2. Belongs to the current user (user_id)
        # first_or_404() will return a 404 error if not found
        session = ChatSession.query.filter_by(
            id=session_id, 
            user_id=request.user.id
        ).first_or_404()

        # First, delete all messages that belong to this session
        # This is like deleting all the contents of a folder before deleting the folder
        ChatMessage.query.filter_by(session_id=session_id).delete()
        
        # Now delete the session itself
        # This is like deleting the empty folder
        db.session.delete(session)
        
        # Save all the deletion changes to the database
        # Nothing is actually deleted until we commit
        db.session.commit()

        # Return a success response (200 = OK)
        # Confirm the deletion was successful and which session was deleted
        return jsonify({
            'message': 'Chat session deleted successfully',
            'id': session_id
        }), 200

    # If anything goes wrong during the deletion process:
    except Exception as e:
        # Undo any changes that weren't committed
        # This ensures we don't have partial deletions
        db.session.rollback()
        
        # Log the error for administrators to review later
        logger.error(f"Error deleting chat session: {str(e)}")
        
        # Return an error response (500 = Server Error)
        return jsonify({
            'error': str(e)
        }), 500
```
- Purpose:
    - Completely removes a chat session and all its messages
    - Like deleting a conversation history and all its messages
- Security Checks:
    - Ensures the user is logged in
    - Verifies they have permission to use chat features
    - Makes sure users can only delete their own chat sessions
- Deletion Process:
    - First, finds the chat session to make sure it exists
    - Deletes all messages in the session (like deleting files in a folder)
    - Deletes the session itself (like deleting the empty folder)
    - Saves all these changes to make them permanent
- Safety Features:
    - If anything goes wrong, all deletion steps are undone (rollback)
    - Logs errors so administrators can investigate problems
    - Only allows users to delete their own sessions
- Response:
    - If successful: Returns a message confirming the deletion and the session ID
    - If failed: Returns an error message explaining what went wrong
- Real-world Example:
    - Imagine you have a chat app where you can delete a conversation. This function handles the process when you click "Delete" on a conversation. It's like when you delete a file in your computer, but with added security to make sure only the right person can delete their own conversations.

##### API Route - 6 : Export chat history ('/chat/sessions/<int:session_id>/export', methods=['GET'])

![alt text](image-7.png)

Think of this function like an export button that saves your chat history to a file.
```
# This route handles GET requests to export chat history to a file
# <int:session_id> means it expects a session number in the URL (e.g., /chat/sessions/123/export)
@chat_bp.route('/chat/sessions/<int:session_id>/export', methods=['GET'])

# Security decorators:
# Ensures the user is logged in
@login_required
# Verifies the user has permission to use chat features
@require_scope(SCOPE_CHAT)

def export_chat_history(session_id):
    try:
        # Find the chat session and verify it belongs to the current user
        # Returns 404 error if not found
        session = ChatSession.query.filter_by(
            id=session_id, 
            user_id=request.user.id
        ).first_or_404()

        # Get all messages from this session
        # order_by() sorts them by creation time (oldest to newest)
        messages = ChatMessage.query.filter_by(
            session_id=session_id
        ).order_by(ChatMessage.created_at).all()

        # Check what format the user wants (from URL parameter, e.g., ?format=csv)
        # Default to CSV if not specified
        export_format = request.args.get('format', 'csv').lower()

        # Handle CSV export format
        if export_format == 'csv':
            # Create a temporary storage space in memory for our CSV data
            output = StringIO()
            # Create a CSV writer object to help format the data
            writer = csv.writer(output)

            # Write the column headers to the CSV
            writer.writerow(['Timestamp', 'Question', 'Answer', 'Sources'])

            # Process each message and write to CSV
            for message in messages:
                # Initialize empty string for sources
                sources_str = ''
                
                # If the message has sources (referenced documents)
                if message.sources:
                    # Create a set to store unique file names
                    source_files = set()
                    
                    # Process each source
                    for source in message.sources:
                        # Check if source has file information
                        if isinstance(source, dict) and 'file_info' in source:
                            # Get file info dictionary
                            file_info = source.get('file_info', {})
                            # Get file name or use 'Unknown File' if not found
                            file_name = file_info.get('file_name', 'Unknown File')
                            # Add to set of unique files
                            source_files.add(file_name)
                    
                    # Join all file names with semicolons
                    sources_str = '; '.join(source_files)

                # Write one row to the CSV for this message
                writer.writerow([
                    # Format timestamp as readable string
                    message.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                    message.question,    # The user's question
                    message.answer,      # The AI's answer
                    sources_str         # List of source documents
                ])

            # Get the complete CSV data as a string
            output_str = output.getvalue()
            # Convert to bytes with UTF-8 encoding (works better with Excel)
            bytes_output = BytesIO(output_str.encode('utf-8-sig'))
            
            # Create a filename with current timestamp
            # Example: chat_history_MyChat_20240320_143022.csv
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"chat_history_{session.session_name}_{timestamp}.csv"
            
            # Send the file to the user for download
            return send_file(
                bytes_output,           # The file data
                mimetype='text/csv',    # Tell browser it's a CSV file
                as_attachment=True,     # Force download rather than display
                download_name=filename  # Suggested filename for saving
            )

        # If user requests a format other than CSV
        else:
            return jsonify({
                'error': 'Unsupported export format. Currently only CSV is supported.'
            }), 400  # 400 = Bad Request

    # If anything goes wrong during the export
    except Exception as e:
        # Log the error for administrators
        logger.error(f"Error exporting chat history: {str(e)}")
        # Return error message to user
        return jsonify({'error': str(e)}), 500  # 500 = Server Error
```

- Purpose:
    - Allows users to download their chat history as a CSV file
    - Like saving a conversation history to read later
- Security:
    - Checks if user is logged in
    - Verifies they have permission
    - Ensures they can only export their own chats
- Process:
    - Finds the requested chat session
    - Gets all messages in order
    - Creates a CSV file with:
        - Timestamp for each message
        - The question asked
        - The answer received
        - Any source documents referenced   
- File Creation:
    - Creates a nicely formatted CSV file
    - Includes headers for each column
    - Names the file with the chat name and current time
    - Makes it easy to open in Excel or similar programs
- Error Handling:
    - Handles unsupported file formats
    - Logs any errors that occur
    - Provides clear error messages to users
- Real-world Example:
    - Imagine you have a chat app where you can export your conversation history. This function handles the process when you click "Export" on a conversation. It's like when you save a file in your computer, but with added security to make sure only the right person can export their own conversations.


##### API Route - 7 : Add a new message to a chat session ('/chat/sessions/<int:session_id>/messages', methods=['POST'])

![alt text](image-8.png)

Part 1 - Initial Setup and File Filtering:
```
# This route handles POST requests to add new messages to a chat session
@chat_bp.route('/chat/sessions/<int:session_id>/messages', methods=['POST'])
@login_required  # Ensure user is logged in
@require_scope(SCOPE_CHAT)  # Check user has chat permissions
def add_message(session_id):
    try:
        # Find the chat session and verify it belongs to the current user
        session = ChatSession.query.filter_by(
            id=session_id, 
            user_id=request.user.id
        ).first_or_404()

        # Get the data sent with the request
        data = request.json
        if not data or 'question' not in data:
            return jsonify({'error': 'No question provided'}), 400

        # Start building the database query for files
        query = File.query

        # Connect the File table with Position table
        query = query.join(Position, File.position_id == Position.id)

        # Get the current user's position level for access control
        user = User.query.get(request.user.id)
        if user.position_id:
            user_position = Position.query.get(user.position_id)
            if user_position:
                # Only show files appropriate for user's position level
                query = query.filter(Position.level <= user_position.level)
```
Part 2 - Query Type Handling:

```
        # Handle different types of searches based on query_type
        query_type = data.get('query_type', 'all')  # Default to 'all' if not specified
        selected_file_ids = data.get('selected_file_ids', [])

        if query_type == 'all':
            # Get all files user has access to based on their verticals and departments
            user_vertical_ids = [v.id for v in user.verticals]
            user_department_ids = [d.id for d in user.departments]
            
            # Filter files by user's permissions
            query = query.join(Department).filter(
                db.and_(
                    File.vertical_id.in_(user_vertical_ids),
                    File.department_id.in_(user_department_ids),
                    Department.vertical_id.in_(user_vertical_ids)
                )
            )
            
        elif query_type == 'filter':
            # Handle specific filters for vertical/department/position/dates
            if 'vertical_id' in data:
                query = query.filter(File.vertical_id == data['vertical_id'])
                
                if 'department_id' in data:
                    # Verify department belongs to selected vertical
                    department = Department.query.filter_by(
                        id=data['department_id'],
                        vertical_id=data['vertical_id']
                    ).first()
                    
                    if not department:
                        return jsonify({
                            'error': 'Selected department does not belong to the specified vertical'
                        }), 400
                    
                    query = query.filter(File.department_id == data['department_id'])
            
            # Handle position-based filtering
            if 'position_id' in data:
                selected_position = Position.query.get(data['position_id'])
                if selected_position:
                    query = query.filter(Position.level <= selected_position.level)
                               
            # Handle date range filtering
            if 'start_date' in data:
                query = query.filter(File.upload_time >= datetime.fromisoformat(data['start_date']))
            if 'end_date' in data:
                query = query.filter(File.upload_time <= datetime.fromisoformat(data['end_date']))
```

Part 3 - AI Processing and Response:
```
        # Get previous chat messages for context
        previous_messages = ChatMessage.query.filter_by(
            session_id=session_id
        ).order_by(ChatMessage.created_at.desc()).limit(5).all()

        # Format conversation history
        conversation_history = "\n\n".join([
            f"Previous Question: {msg.question}\nPrevious Answer: {msg.answer}"
            for msg in reversed(previous_messages)
        ])

        # Rephrase the question using AI to improve search results
        question_data = rephrase_question(
            data['question'],
            conversation_history if previous_messages else None
        )

        # Create an AI embedding (vector) of the question for searching
        response = openai_client.embeddings.create(
            input=question_data['rephrased_question'],
            model=AZURE_OPENAI_EMBEDDING_DEPLOYMENT
        )
        vector = response.data[0].embedding

        # Search for relevant documents using the vector
        search_client = get_search_client()
        vector_query = {
            "vector": vector,
            "fields": "text_vector",
            "k": 3,
            "kind": "vector"
        }
```

Part 4 - Saving and Response:

```
        # Save the message to the database
        message = ChatMessage(
            session_id=session_id,
            question=data['question'],
            answer=ai_response['answer'],
            sources=ai_response['sources'],
            token_usage=ai_response.get('token_usage'),
            search_metadata={
                **ai_response.get('metadata', {}),
                'question_rephrasing': {
                    'original_question': question_data['original_question'],
                    'rephrased_question': question_data['rephrased_question'],
                    'token_usage': question_data['token_usage']
                },
                'selected_file_ids': selected_file_ids
            }
        )
        db.session.add(message)
        
        # Update the session's last activity time
        session.updated_at = datetime.utcnow()
        db.session.commit()

        # Return the response to the user
        return jsonify({
            'id': message.id,
            'question': message.question,
            'answer': message.answer,
            'sources': response_sources,
            'created_at': message.created_at.isoformat(),
            'metadata': message.search_metadata
        }), 201

    except Exception as e:
        # If anything goes wrong, undo database changes and return error
        db.session.rollback()
        print(f"Error in add_message: {str(e)}")
        return jsonify({'error': str(e)}), 500
```
This is a complex function that handles AI-powered chat interactions. Here's the process:

- User Access Check:
    - Verifies the user is logged in
    - Checks their permissions
    - Finds their chat session
- File Access Control:
    - Determines which files the user can access
    - Filters based on user's position, department, and vertical
    - Handles different types of searches (all, filtered, or selected files)
- AI Processing:
    - Gets previous chat context
    - Rephrases the question for better results
    - Creates an AI vector of the question
    - Searches for relevant documents
    - Gets AI response based on found documents
- Saving and Response:
    - Saves the new message to the database
    - Updates the session's last activity time
    - Returns the AI's response with relevant sources

Think of it like a smart librarian who:
- Checks your ID card
- Understands what documents you can access
- Rephrases your question to be clearer
- Searches through allowed documents
- Provides an answer with references
- Keeps a record of the conversation

This is a sophisticated system that combines security, document management, and AI to provide intelligent responses while respecting user permissions and access controls.