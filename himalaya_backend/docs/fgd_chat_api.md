# FGD Chat API Documentation

## Overview
The FGD Chat API provides endpoints for managing chat sessions and messages related to Focus Group Discussions (FGDs). These endpoints allow users to create chat sessions, ask questions about FGD transcripts, and receive AI-powered responses.

## Base URL

## Endpoints

### 1. Get Chat Sessions
Retrieves a paginated list of chat sessions for the authenticated user.

**Endpoint:** `GET /fgd-chat/sessions`

**Query Parameters:**
- `page` (optional): Page number (default: 1)
- `per_page` (optional): Items per page (default: 10)

**Response:**