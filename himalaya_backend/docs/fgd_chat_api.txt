FGD Chat API Documentation
=========================

Overview
--------
The FGD Chat API provides endpoints for managing chat sessions and messages related to Focus Group Discussions (FGDs). These endpoints allow users to create chat sessions, ask questions about FGD transcripts, and receive AI-powered responses.

Base URL
--------
/api

Authentication
-------------
All endpoints require authentication using a valid JWT token in the Authorization header:
Authorization: Bearer <token>

Endpoints
---------

1. Get Chat Sessions
-------------------
Retrieves a paginated list of chat sessions for the authenticated user.

Endpoint: GET /fgd-chat/sessions

Query Parameters:
- page (optional): Page number (default: 1)
- per_page (optional): Items per page (default: 10)

Response:
{
    "items": [
        {
            "id": 1,
            "session_name": "FGD Chat 2024-01-20 15:30",
            "created_at": "2024-01-20T15:30:00Z",
            "updated_at": "2024-01-20T15:45:00Z",
            "is_active": true,
            "message_count": 5
        }
    ],
    "pagination": {
        "total_items": 20,
        "total_pages": 2,
        "current_page": 1,
        "per_page": 10,
        "has_next": true,
        "has_prev": false
    }
}

2. Create Chat Session
---------------------
Creates a new chat session.

Endpoint: POST /fgd-chat/sessions

Request Body:
{
    "session_name": "My FGD Chat Session"  // Optional
}

Response:
{
    "id": 1,
    "session_name": "My FGD Chat Session",
    "created_at": "2024-01-20T15:30:00Z"
}

3. Update Chat Session
---------------------
Updates an existing chat session's name.

Endpoint: PUT /fgd-chat/sessions/{session_id}

Request Body:
{
    "session_name": "Updated Session Name"
}

Response:
{
    "id": 1,
    "session_name": "Updated Session Name",
    "updated_at": "2024-01-20T16:00:00Z"
}

4. Get Session Messages
----------------------
Retrieves messages from a specific chat session.

Endpoint: GET /fgd-chat/sessions/{session_id}/messages

Query Parameters:
- page (optional): Page number (default: 1)
- per_page (optional): Items per page (default: 20)

Response:
{
    "items": [
        {
            "id": 1,
            "question": "What were the main concerns raised by participants?",
            "answer": "Based on the transcripts...",
            "selected_video_ids": [1, 2, 3],
            "created_at": "2024-01-20T15:35:00Z",
            "metadata": {
                "question_rephrasing": {},
                "video_contexts": []
            }
        }
    ],
    "pagination": {
        "total_items": 25,
        "total_pages": 2,
        "current_page": 1,
        "per_page": 20,
        "has_next": true,
        "has_prev": false
    }
}

5. Add Message
-------------
Adds a new message to a chat session and gets an AI-generated response.

Endpoint: POST /fgd-chat/sessions/{session_id}/messages

Request Body:
{
    "question": "What were the key insights about product usage?",
    "video_ids": [1, 2, 3]  // IDs of FGD videos to analyze
}

Response:
{
    "id": 1,
    "question": "What were the key insights about product usage?",
    "answer": "Based on the FGD transcripts...",
    "selected_video_ids": [1, 2, 3],
    "created_at": "2024-01-20T15:35:00Z",
    "metadata": {
        "question_rephrasing": {
            "original_question": "What were the key insights about product usage?",
            "rephrased_question": "Based on the FGD discussions, what were the main findings regarding how participants use the product?",
            "token_usage": {}
        },
        "video_contexts": [
            "FGD conducted on 2024-01-15 in USA with 8 participants"
        ]
    }
}

6. Get Eligible Videos
---------------------
Retrieves a list of videos eligible for FGD chat analysis.

Endpoint: GET /themes/{theme_id}/presentation_eligible

Response:
[
    {
        "video_id": 1,
        "blob_url": "https://storage.azure.com/video1.mp4",
        "uploaded_at": "2024-01-15T10:00:00Z",
        "processed_at": "2024-01-15T10:15:00Z",
        "original_file_name": "FGD_Session_1.mp4",
        "fgd": {
            "id": 1,
            "language_id": 1,
            "group_size": 8,
            "conductor_name": "John Doe",
            "discussion_date": "2024-01-15",
            "country": "USA",
            "status": "completed"
        }
    }
]

Error Responses
--------------

400 <USER> <GROUP>
{
    "error": "Missing required fields"
}

401 Unauthorized
{
    "error": "Invalid or missing authentication token"
}

404 Not Found
{
    "error": "Session not found"
}

500 Internal Server Error
{
    "error": "Internal server error message"
}

Notes
-----

General:
- All timestamps are returned in ISO 8601 format
- The API uses pagination for list endpoints to manage large datasets
- All endpoints require authentication
- Responses are in JSON format

Chat Features:
- Messages are processed using Azure OpenAI for intelligent responses
- Video transcripts are used as context for AI responses
- Question rephrasing is applied based on conversation history
- Multiple video transcripts can be analyzed simultaneously

Data Management:
- Sessions and messages are user-specific
- Video selection is validated against user permissions
- Metadata includes both question processing and video context information
- Token usage tracking is available for AI interactions

Best Practices:
1. Always check pagination parameters for list endpoints
2. Include relevant video IDs when asking questions
3. Store session IDs for continuous conversations
4. Handle error responses appropriately
5. Monitor token usage through metadata

Rate Limiting:
- Standard rate limiting applies to all endpoints
- AI processing endpoints may have additional limitations

Security:
- All endpoints require valid JWT authentication
- User-specific data isolation is enforced
- Cross-origin resource sharing (CORS) is configured
- Session ownership is validated for all operations 