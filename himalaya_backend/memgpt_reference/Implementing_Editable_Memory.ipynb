{"cells": [{"cell_type": "markdown", "id": "cac06555-9ce8-4f01-bbef-3f8407f4b54d", "metadata": {}, "source": ["# Lab 1: Implementing self-editing memory from scratch\n", "\n", "There are a variety of ways to enable long-term memory in LLM agents, such as RAG and recursive summarization. The MemGPT paper first introduced the notion of *self-editing memory*. Essentially, offload memory management to the LLM. After all, the LLM is the most \"intelligent\" part of our programs, so why not have the LLM figure out memory instead of hard coding some solution?\n", "\n", "In this section, we'll walk through how to use OpenAI's tool calling to implement some simple memory management tools."]}, {"cell_type": "markdown", "id": "9e37f2c02f7efc14", "metadata": {}, "source": ["## Preparation\n", "\n", "<div style=\"background-color:#fff6ff; padding:13px; border-width:3px; border-color:#efe6ef; border-style:solid; border-radius:6px\">\n", "<p> 💻 &nbsp; <b>Access <code>requirements.txt</code> and <code>helper.py</code> files:</b> 1) click on the <em>\"File\"</em> option on the top menu of the notebook and then 2) click on <em>\"Open\"</em>.\n", "\n", "<p> ⬇ &nbsp; <b>Download Notebooks:</b> 1) click on the <em>\"File\"</em> option on the top menu of the notebook and then 2) click on <em>\"Download as\"</em> and select <em>\"Notebook (.ipynb)\"</em>.</p>\n", "\n", "<p> 📒 &nbsp; For more help, please see the <em>\"Appendix – Tips, Help, and Download\"</em> Lesson.</p>\n", "</div>"]}, {"cell_type": "markdown", "id": "a94b5a19cc0264e8", "metadata": {}, "source": ["<p style=\"background-color:#f7fff8; padding:15px; border-width:3px; border-color:#e0f0e0; border-style:solid; border-radius:6px\"> 🚨\n", "&nbsp; <b>Different Run Results:</b> The output generated by AI models can vary with each execution due to their dynamic, probabilistic nature. Your results may differ from those shown in the video.</p>"]}, {"cell_type": "markdown", "id": "aad3a8cc-d17a-4da1-b621-ecc93c9e2106", "metadata": {}, "source": ["## Section 0: Setup OpenAI"]}, {"cell_type": "code", "execution_count": null, "id": "3aeaf75d-acb0-4c10-89d2-a35973f46c62", "metadata": {"height": 46}, "outputs": [], "source": ["from helper import get_openai_api_key\n", "openai_api_key = get_openai_api_key()"]}, {"cell_type": "code", "execution_count": null, "id": "7ccd43f2-164b-4d25-8465-894a3bb54c4b", "metadata": {"height": 114}, "outputs": [], "source": ["from openai import OpenAI\n", "import os\n", "\n", "client = OpenAI(\n", "    api_key=openai_api_key\n", ")"]}, {"cell_type": "markdown", "id": "65bf0dc2-d1ac-4d4c-8674-f3156eeb611d", "metadata": {}, "source": ["## Section 1: Breaking down the LLM context window\n", "LLM's have a *context window* (the set of tokens that go into the model) that is limited in size. That means we need to be smart about what we place into the context. Usually the context window for an agent is strucuted in a certain way. Depending on the agent framework, the structure will vary, but usually the context window contains: \n", "* A *system prompt* instructing the agent's behavior\n", "* A *conservation history* of previous conversations\n", "\n", "Because context windows are limited, only some of the conversation history can be included. Some frameworks will also place a recursive summary in the context, or retrieve relevant messages from an external database and also place them into the context. In MemGPT, we also reserver additional sections of the context for: \n", "* A *recursive summary* of all previous conversations\n", "* A *core memory* section that is read-writeable by the agent"]}, {"cell_type": "markdown", "id": "fe092474-6b91-4124-884d-484fc28b58e7", "metadata": {}, "source": ["### A simple agent's context window\n", "In the code below, you can see how we can define an agent with a system prompt. The system prompt will be included in every chat completions request as the first message, while later messages will change over time as the user and assistant exchange messages. "]}, {"cell_type": "code", "execution_count": null, "id": "db55c7ad-2af7-407e-b842-5c90f1d07e0c", "metadata": {"height": 29}, "outputs": [], "source": ["model = \"gpt-4o-mini\""]}, {"cell_type": "code", "execution_count": null, "id": "62dcf31d-6f45-40f5-8373-61981f03da62", "metadata": {"height": 29}, "outputs": [], "source": ["system_prompt = \"You are a chatbot.\""]}, {"cell_type": "code", "execution_count": null, "id": "dd580173-3eae-496e-9111-43cf0496432b", "metadata": {"height": 199}, "outputs": [], "source": ["# Make the completion request with the tool usage\n", "chat_completion = client.chat.completions.create(\n", "    model=model,\n", "    messages=[\n", "        # system prompt: always included in the context window \n", "        {\"role\": \"system\", \"content\": system_prompt}, \n", "        # chat history (evolves over time)\n", "        {\"role\": \"user\", \"content\": \"What is my name?\"}, \n", "    ]\n", ")\n", "chat_completion.choices[0].message.content"]}, {"cell_type": "markdown", "id": "d9e6ae74-5a84-441d-8a7d-a43078566916", "metadata": {}, "source": ["### Adding memory to the context \n", "Similar to how we always start a chat completitions request with a system prompt, we can also prefix the list of messages with a prompt containing important memories. Lets see how we can add a memory section to the context window, and have the agent use that memory to respond to user messages.  "]}, {"cell_type": "code", "execution_count": null, "id": "56acd769-8051-4daf-bfdc-5a60b256249f", "metadata": {"height": 80}, "outputs": [], "source": ["agent_memory = {\"human\": \"Name: <PERSON>\"}\n", "system_prompt = \"You are a chatbot. \" \\\n", "+ \"You have a section of your context called [MEMORY] \" \\\n", "+ \"that contains information relevant to your conversation\""]}, {"cell_type": "code", "execution_count": null, "id": "2bfb311f-8df3-4679-90b1-cfd61b091747", "metadata": {"height": 233}, "outputs": [], "source": ["import json\n", "\n", "\n", "chat_completion = client.chat.completions.create(\n", "    model=model,\n", "    messages=[\n", "        # system prompt \n", "        {\"role\": \"system\", \"content\": system_prompt + \"[MEMORY]\\n\" + json.dumps(agent_memory)},\n", "        # chat history \n", "        {\"role\": \"user\", \"content\": \"What is my name?\"},\n", "    ],\n", ")\n", "chat_completion.choices[0].message.content"]}, {"cell_type": "markdown", "id": "7ed76cd1-767f-43f2-b700-3a3ae5dd4b2e", "metadata": {}, "source": ["In the next section, we'll show you can to make this memory read-writeable by the agent. "]}, {"cell_type": "markdown", "id": "8ec227fc-55ea-4bc2-87b9-0bc385aa5ae3", "metadata": {}, "source": ["## Section 2: Modifing the memory with tools "]}, {"cell_type": "markdown", "id": "fbdc9b6e-8bd5-4c42-970e-473da4adb2f2", "metadata": {}, "source": ["We first need to define a memory save tool. In order to allow the agent to save new memory, we implement a simple tool that appends to a section of the memory dictionary which we will pass to the agent.  "]}, {"cell_type": "markdown", "id": "4182a134-65d2-423b-9c4b-731f55eca5aa", "metadata": {}, "source": ["### Defining a memory editing tool \n", "Instead of directly providing the name in the agent's memory, we'll instead start with a blank memory object and provide a function that can edit the memory object. "]}, {"cell_type": "code", "execution_count": null, "id": "135fcf3e-59c4-4da3-b86b-dbffb21aa343", "metadata": {"height": 97}, "outputs": [], "source": ["agent_memory = {\"human\": \"\", \"agent\": \"\"}\n", "\n", "def core_memory_save(section: str, memory: str): \n", "    agent_memory[section] += '\\n' \n", "    agent_memory[section] += memory "]}, {"cell_type": "markdown", "id": "0c57944f-8360-4567-933c-0716b1f09818", "metadata": {}, "source": ["To inform our agent of this tool and how to use it, we need to create a tool schema that OpenAI can process. This includes a description of how to use the tool, and the parameters the agent must generate to input into the tool. "]}, {"cell_type": "code", "execution_count": null, "id": "c3aff35a-6a2d-4b1b-879d-038112bef2b1", "metadata": {"height": 659}, "outputs": [], "source": ["\n", "# tool description \n", "core_memory_save_description = \"Save important information about you,\" \\\n", "+ \"the agent or the human you are chatting with.\"\n", "\n", "# arguments into the tool (generated by the LLM)\n", "# defines what the agent must generate to input into the tool \n", "core_memory_save_properties = \\\n", "{\n", "    # arg 1: section of memory to edit\n", "    \"section\": {\n", "        \"type\": \"string\",\n", "        \"enum\": [\"human\", \"agent\"],\n", "        \"description\": \"Must be either 'human' \" \\\n", "        + \"(to save information about the human) or 'agent'\" \\\n", "        + \"(to save information about yourself)\",            \n", "    },\n", "    # arg 2: memory to save\n", "    \"memory\": {\n", "        \"type\": \"string\",\n", "        \"description\": \"Memory to save in the section\",\n", "    },\n", "}\n", "\n", "# tool schema (passed to OpenAI)\n", "core_memory_save_metadata = \\\n", "    {\n", "        \"type\": \"function\",\n", "        \"function\": {\n", "            \"name\": \"core_memory_save\",\n", "            \"description\": core_memory_save_description,\n", "            \"parameters\": {\n", "                \"type\": \"object\",\n", "                \"properties\": core_memory_save_properties,\n", "                \"required\": [\"section\", \"memory\"],\n", "            },\n", "        }\n", "    }"]}, {"cell_type": "markdown", "id": "134d7313-ccc0-46b8-ba96-b267ae29be05", "metadata": {}, "source": ["Now, we can pass the tool call into the agent! "]}, {"cell_type": "code", "execution_count": null, "id": "e6e8049d-3f94-4f05-99e7-3e9d05a1d57c", "metadata": {"height": 386}, "outputs": [], "source": ["\n", "\n", "agent_memory = {\"human\": \"\"}\n", "system_prompt = \"You are a chatbot. \" \\\n", "+ \"You have a section of your context called [MEMORY] \" \\\n", "+ \"that contains information relevant to your conversation\"\n", "\n", "chat_completion = client.chat.completions.create(\n", "    model=model,\n", "    messages=[\n", "        # system prompt \n", "        {\"role\": \"system\", \"content\": system_prompt}, \n", "        # memory \n", "        {\"role\": \"system\", \"content\": \"[MEMORY]\\n\" + json.dumps(agent_memory)},\n", "        # chat history \n", "        {\"role\": \"user\", \"content\": \"My name is <PERSON>\"},\n", "    ],\n", "    # tool schemas \n", "    tools=[core_memory_save_metadata]\n", ")\n", "response = chat_completion.choices[0]\n", "response"]}, {"cell_type": "markdown", "id": "98f0488f-9864-4280-b3d6-8376c9473b7f", "metadata": {}, "source": ["### Executing the tool \n", "Unfortunately, OpenAI isn't going to actually execute the tool, so we have to do this ourselves. Lets take the arguments specified in the tool call response we just got to run the tool. "]}, {"cell_type": "code", "execution_count": null, "id": "bb4b3621-df4b-4fff-962b-2f7e4f6027fc", "metadata": {"height": 46}, "outputs": [], "source": ["arguments = json.loads(response.message.tool_calls[0].function.arguments)\n", "arguments"]}, {"cell_type": "code", "execution_count": null, "id": "a1175d64-4352-475f-bbe5-76c5bc637fae", "metadata": {"height": 46}, "outputs": [], "source": ["# run the function with the specified arguments \n", "core_memory_save(**arguments)"]}, {"cell_type": "markdown", "id": "18d0e5b7-e308-4c12-aaad-72afa590124e", "metadata": {}, "source": ["Now, we can view the memory object that has been updated: "]}, {"cell_type": "code", "execution_count": null, "id": "9ae048d8-3a86-4599-a25a-487c11b1877f", "metadata": {"height": 29}, "outputs": [], "source": ["agent_memory"]}, {"cell_type": "markdown", "id": "83561faa-fc91-45f5-975e-ea4eb0f9b138", "metadata": {}, "source": ["### Running the next agent step \n", "Now, we can see how the agent responds differently as the memory has been updated. "]}, {"cell_type": "code", "execution_count": null, "id": "d01492be-0b2b-4eb9-99b3-c5ee5e2c9a0c", "metadata": {"height": 250}, "outputs": [], "source": ["chat_completion = client.chat.completions.create(\n", "    model=model,\n", "    messages=[\n", "        # system prompt \n", "        {\"role\": \"system\", \"content\": system_prompt}, \n", "        # memory \n", "        {\"role\": \"system\", \"content\": \"[MEMORY]\\n\" + json.dumps(agent_memory)},\n", "        # chat history \n", "        {\"role\": \"user\", \"content\": \"what is my name\"},\n", "    ],\n", "    tools=[core_memory_save_metadata]\n", ")\n", "response = chat_completion.choices[0]\n", "response.message"]}, {"cell_type": "markdown", "id": "7bdb0bbd-5042-498d-ae98-70836b4633be", "metadata": {}, "source": ["Congrats! You've now implemented the a memory editing function. Can you think of how you could add support for other data structures?"]}, {"cell_type": "markdown", "id": "12cc2425-47d4-495f-a29c-b536fc760d97", "metadata": {}, "source": ["## Implementing an agentic loop\n", "In our current implementation, the agent can only take one step at a time: it can either edit memory, or respond to the user. However, ideally we want our agent to support *multi-step reasoning*, so it can combine multiple actions together. For example, if we tell the agent our name is \"<PERSON>\", we want the agent to both edit its memory and also return back a message to us. \n", "\n", "We can implement multi-step reasoning by calling the `client.chat.completions.create(...)` in a loop, and allowing the agent to choose whether to continue its reasoning steps or to break out of the loop. For simplicity, we will assume that an agent response that is *not* a tool call breaks out of the reasoning loop yields control back to the user.\n"]}, {"cell_type": "code", "execution_count": null, "id": "a7d59344-edfe-48b3-b3e1-abae5e1da65f", "metadata": {"height": 29}, "outputs": [], "source": ["agent_memory = {\"human\": \"\"}"]}, {"cell_type": "code", "execution_count": null, "id": "4fe6e658-e6dc-4578-99d9-0e21692ce542", "metadata": {"height": 114}, "outputs": [], "source": ["system_prompt_os = system_prompt \\\n", "+ \"\\n. You must either call a tool (core_memory_save) or\" \\\n", "+ \"write a response to the user. \" \\\n", "+ \"Do not take the same actions multiple times!\" \\\n", "+ \"When you learn new information, make sure to always\" \\\n", "+ \"call the core_memory_save tool.\" "]}, {"cell_type": "markdown", "id": "185c30aa-6b52-4aa8-9c71-d0009f3f0466", "metadata": {}, "source": ["We implement a simple step function for the agent. The function responds to the user message but allows the agent to take multiple actions in sequence, and returns to the user when a message (that does not call a tool) is sent. "]}, {"cell_type": "code", "execution_count": null, "id": "d29a6788-6417-4bd9-9c4b-61f78520ae88", "metadata": {"height": 897}, "outputs": [], "source": ["def agent_step(user_message, chat_history = []): \n", "\n", "    # prefix messages with system prompt and memory\n", "    messages = [\n", "        # system prompt \n", "        {\"role\": \"system\", \"content\": system_prompt_os}, \n", "        # memory\n", "        {\n", "            \"role\": \"system\", \n", "            \"content\": \"[MEMORY]\\n\" + json.dumps(agent_memory)\n", "        },\n", "    ] \n", "\n", "    # append the chat history \n", "    messages += chat_history\n", "    \n", "\n", "    # append the most recent message\n", "    messages.append({\"role\": \"user\", \"content\": user_message})\n", "    \n", "    # agentic loop \n", "    while True: \n", "        chat_completion = client.chat.completions.create(\n", "            model=model,\n", "            messages=messages,\n", "            tools=[core_memory_save_metadata]\n", "        )\n", "        response = chat_completion.choices[0]\n", "\n", "        # update the messages with the agent's response \n", "        messages.append(response.message)\n", "\n", "        # if NOT calling a tool (responding to the user), return \n", "        if not response.message.tool_calls: \n", "            messages.append({\n", "                \"role\": \"assistant\", \n", "                \"content\": response.message.content\n", "            })\n", "            return response.message.content\n", "\n", "        # if calling a tool, execute the tool\n", "        if response.message.tool_calls: \n", "            print(\"TOOL CALL:\", response.message.tool_calls[0].function)\n", "\n", "            # add the tool call response to the message history \n", "            messages.append({\"role\": \"tool\", \"tool_call_id\": response.message.tool_calls[0].id, \"name\": \"core_memory_save\", \"content\": f\"Updated memory: {json.dumps(agent_memory)}\"})\n", "\n", "            # parse the arguments from the LLM function call\n", "            arguments = json.loads(response.message.tool_calls[0].function.arguments)\n", "\n", "            # run the function with the specified arguments\n", "            core_memory_save(**arguments)"]}, {"cell_type": "markdown", "id": "0a31ddca-4bb6-4082-851c-4e991c240d4f", "metadata": {}, "source": ["Now, we can observe the agent take multiple actions when we sent it a message: "]}, {"cell_type": "code", "execution_count": null, "id": "49ababa2-3fb3-46e8-85e6-ab1a2fb34d0c", "metadata": {"height": 29}, "outputs": [], "source": ["agent_step(\"my name is bob.\")"]}, {"cell_type": "markdown", "id": "181d8954-0b70-4617-a591-251db0099128", "metadata": {}, "source": ["Now, the agent is able to both edit its memory *and* generate a response to the user that uses the updated memory in a single step. \n", "\n", "Although in this example, we only support a single tools and responding to the user, this same structure can be used to implement more complex reasoning loops that combine many tools. In MemGPT, all actions (even a response to the user) is a tool, where some tools (such as sending a message) break the agent reasoning loop, while other tools (such as searching the archival memory store or editing memory) do not. \n", "\n", "Congrats! You've now implemented an agent with self-editing memory and multi-step reasoning :)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 5}