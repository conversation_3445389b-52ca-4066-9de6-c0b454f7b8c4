{"cells": [{"cell_type": "markdown", "id": "4afc3580d148e5df", "metadata": {}, "source": ["# Lab 6: Multi-agent Orchestration"]}, {"cell_type": "markdown", "id": "3731e48e33af6ff0", "metadata": {}, "source": ["## Preparation\n", "\n", "<div style=\"background-color:#fff6ff; padding:13px; border-width:3px; border-color:#efe6ef; border-style:solid; border-radius:6px\">\n", "<p> 💻 &nbsp; <b>Access <code>requirements.txt</code> and <code>helper.py</code> files:</b> 1) click on the <em>\"File\"</em> option on the top menu of the notebook and then 2) click on <em>\"Open\"</em>.\n", "\n", "<p> ⬇ &nbsp; <b>Download Notebooks:</b> 1) click on the <em>\"File\"</em> option on the top menu of the notebook and then 2) click on <em>\"Download as\"</em> and select <em>\"Notebook (.ipynb)\"</em>.</p>\n", "\n", "<p> 📒 &nbsp; For more help, please see the <em>\"Appendix – Tips, Help, and Download\"</em> Lesson.</p>\n", "</div>"]}, {"cell_type": "markdown", "id": "225cd13d43cb4d07", "metadata": {}, "source": ["<p style=\"background-color:#f7fff8; padding:15px; border-width:3px; border-color:#e0f0e0; border-style:solid; border-radius:6px\"> 🚨\n", "&nbsp; <b>Different Run Results:</b> The output generated by AI models can vary with each execution due to their dynamic, probabilistic nature. Your results may differ from those shown in the video.</p>"]}, {"cell_type": "markdown", "id": "1fc645c4cb6f9c1c", "metadata": {}, "source": ["## Section 0: Setup a Letta client"]}, {"cell_type": "code", "execution_count": null, "id": "a7a039f8-ea25-4d4e-9903-ba146ffe33bb", "metadata": {"height": 64}, "outputs": [], "source": ["from letta_client import Letta\n", "\n", "client = Letta(base_url=\"http://localhost:8283\")"]}, {"cell_type": "code", "execution_count": null, "id": "07d24220-706f-4ccf-b9f4-89a402756231", "metadata": {"height": 353}, "outputs": [], "source": ["def print_message(message):\n", "    if message.message_type == \"reasoning_message\":\n", "        print(\"🧠 Reasoning: \" + message.reasoning)\n", "    elif message.message_type == \"assistant_message\":\n", "        print(\"🤖 Agent: \" + message.content)\n", "    elif message.message_type == \"tool_call_message\":\n", "        print(\"🔧 Tool Call: \" + message.tool_call.name +  \\\n", "              \"\\n\" + message.tool_call.arguments)\n", "    elif message.message_type == \"tool_return_message\":\n", "        print(\"🔧 Tool Return: \" + message.tool_return)\n", "    elif message.message_type == \"user_message\":\n", "        print(\"👤 User Message: \" + message.content)\n", "    elif message.message_type == \"system_message\":\n", "        print(\" System Message: \" + message.content)\n", "    elif message.message_type == \"usage_statistics\":\n", "        # for streaming specifically, we send the final\n", "        # chunk that contains the usage statistics\n", "        print(f\"Usage: [{message}]\")\n", "        return\n", "    print(\"-----------------------------------------------------\")"]}, {"cell_type": "markdown", "id": "d416863a-42a1-4049-922c-e10ff1afe4dc", "metadata": {}, "source": ["## Section 1: Shared Memory Block"]}, {"cell_type": "markdown", "id": "b1afe5c8de3d7b6d", "metadata": {}, "source": ["### Creating a shared memory block"]}, {"cell_type": "code", "execution_count": null, "id": "0705a088-7af0-4518-916b-b90407a97ff3", "metadata": {"height": 166}, "outputs": [], "source": ["company_description = \"The company is called AgentOS \" \\\n", "+ \"and is building AI tools to make it easier to create \" \\\n", "+ \"and deploy LLM agents.\"\n", "\n", "company_block = client.blocks.create(\n", "    value=company_description,\n", "    label=\"company\",\n", "    limit=10000 # character limit\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "802f9b54-f808-4899-a503-ef022b3e9f46", "metadata": {"height": 30}, "outputs": [], "source": ["company_block"]}, {"cell_type": "markdown", "id": "f43782d3-4001-454a-b04b-4d313b196fc9", "metadata": {}, "source": ["## Section 2: Orchestrating Multiple Agents"]}, {"cell_type": "markdown", "id": "c7410dc7204beef3", "metadata": {}, "source": ["### Creating tools for the outreach agent"]}, {"cell_type": "code", "execution_count": null, "id": "fccf3104-9aa6-42a3-a68c-81aea1a41302", "metadata": {"height": 166}, "outputs": [], "source": ["def draft_candidate_email(content: str):\n", "    \"\"\"\n", "    Draft an email to reach out to a candidate.\n", "\n", "    Args:\n", "        content (str): Content of the email\n", "    \"\"\"\n", "    return f\"Here is a draft email: {content}\"\n", "draft_email_tool = client.tools.upsert_from_function(func=draft_candidate_email)"]}, {"cell_type": "markdown", "id": "4e79fa2c736493db", "metadata": {}, "source": ["### Creating the outreach agent"]}, {"cell_type": "code", "execution_count": null, "id": "621a0bff-84d6-4ddf-96b8-84a5d015fab6", "metadata": {"height": 285}, "outputs": [], "source": ["outreach_persona = (\n", "    \"You are responsible for drafting emails \"\n", "    \"on behalf of a company with the draft_candidate_email tool. \"\n", "    \"Candidates to email will be messaged to you. \"\n", ")\n", "\n", "outreach_agent = client.agents.create(\n", "    name=\"outreach_agent\",\n", "    memory_blocks=[\n", "        {\"label\": \"persona\", \"value\": outreach_persona}\n", "    ],\n", "    model=\"openai/gpt-4o-mini-2024-07-18\",\n", "    embedding=\"openai/text-embedding-ada-002\",\n", "    tools=[draft_email_tool.name],\n", "    block_ids=[company_block.id]\n", ")"]}, {"cell_type": "markdown", "id": "7c39188ce3d016a9", "metadata": {}, "source": ["### Creating tools for the evaluation agent"]}, {"cell_type": "code", "execution_count": null, "id": "085b4bc6-df65-45a2-85d1-c711e9301e12", "metadata": {"height": 200}, "outputs": [], "source": ["def reject(candidate_name: str): \n", "    \"\"\" \n", "    Reject a candidate. \n", "\n", "    Args: \n", "        candidate_name (str): The name of the candidate\n", "    \"\"\"\n", "    return\n", "\n", "\n", "reject_tool = client.tools.upsert_from_function(func=reject)"]}, {"cell_type": "markdown", "id": "d168755eabd8abe2", "metadata": {}, "source": ["### Creating a persona for the evaluation agent"]}, {"cell_type": "code", "execution_count": null, "id": "1d7c600f-3c48-4278-8832-be135f80e452", "metadata": {"height": 166}, "outputs": [], "source": ["skills = \"Front-end (React, Typescript) or software engineering skills\"\n", "\n", "eval_persona = (\n", "    f\"You are responsible for evaluating candidates. \"\n", "    f\"Ideal candidates have skills: {skills}. \"\n", "    \"Reject bad candidates with your reject tool. \"\n", "    f\"Send strong candidates to agent ID {outreach_agent.id}. \"\n", "    \"You must either reject or send candidates to the other agent. \"\n", ")"]}, {"cell_type": "markdown", "id": "65ce81d5d856374e", "metadata": {}, "source": ["### Creating the evaluation agent"]}, {"cell_type": "code", "execution_count": null, "id": "8268b212-605c-40e9-bdcf-ddf9a3a442a3", "metadata": {"height": 319}, "outputs": [], "source": ["eval_agent = client.agents.create(\n", "    name=\"eval_agent\",\n", "    memory_blocks=[\n", "        {\"label\": \"persona\", \"value\": eval_persona}\n", "    ],\n", "    model=\"openai/gpt-4o-mini-2024-07-18\",\n", "    embedding=\"openai/text-embedding-ada-002\",\n", "    tool_ids=[reject_tool.id],\n", "    tools=['send_message_to_agent_and_wait_for_reply'],\n", "    include_base_tools=False,\n", "    block_ids=[company_block.id],\n", "    tool_rules = [\n", "        {\n", "            \"type\": \"exit_loop\",\n", "            \"tool_name\": \"send_message_to_agent_and_wait_for_reply\"\n", "        }\n", "    ]\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "4f974f56-52f2-4edd-b77f-5ea2d6ad8c4c", "metadata": {"height": 30}, "outputs": [], "source": ["[tool.name for tool in eval_agent.tools]"]}, {"cell_type": "markdown", "id": "63d89421a63830b6", "metadata": {}, "source": ["### Sending resume data to agents"]}, {"cell_type": "code", "execution_count": null, "id": "28a1b392-62c9-4666-a037-132dfc15c20c", "metadata": {"height": 30}, "outputs": [], "source": ["resume = open(\"resumes/tony_stark.txt\", \"r\").read()"]}, {"cell_type": "code", "execution_count": null, "id": "3affdafc-fbb2-4115-a4b0-1e681e1431f3", "metadata": {"height": 200}, "outputs": [], "source": ["response = client.agents.messages.create_stream(\n", "    agent_id=eval_agent.id,\n", "    messages=[\n", "        {\n", "            \"role\": \"user\",\n", "            \"content\": f\"Evaluate: {resume}\"\n", "        }\n", "    ]\n", ")\n", "for message in response:\n", "    print_message(message)"]}, {"cell_type": "markdown", "id": "1e83381acdc4ced7", "metadata": {}, "source": ["### Viewing outreach agent messages"]}, {"cell_type": "code", "execution_count": null, "id": "5e0d5964-8efe-45a0-b266-4da2ae79d2f3", "metadata": {"height": 64}, "outputs": [], "source": ["# print messages for `outreach_agent`\n", "for message in client.agents.messages.list(agent_id=outreach_agent.id)[1:]: \n", "    print_message(message)"]}, {"cell_type": "markdown", "id": "5f5f132e-5477-493b-a04d-dbb2e82feb8d", "metadata": {}, "source": ["## Section 3: Shared Memory"]}, {"cell_type": "markdown", "id": "9c8a554803567c34", "metadata": {}, "source": ["### Updating information to shared memory blocks"]}, {"cell_type": "code", "execution_count": null, "id": "e3fc3f22-449f-4646-85af-91e6479bf45c", "metadata": {"height": 200}, "outputs": [], "source": ["response = client.agents.messages.create_stream(\n", "    agent_id=outreach_agent.id,\n", "    messages=[\n", "        {\n", "            \"role\": \"user\",\n", "            \"content\": \"The company has rebranded to Letta\"\n", "        }\n", "    ]\n", ")\n", "for message in response:\n", "    print_message(message)"]}, {"cell_type": "code", "execution_count": null, "id": "39799e40-8fd6-4dca-b104-54cedd7d3051", "metadata": {"height": 81}, "outputs": [], "source": ["client.agents.blocks.retrieve(\n", "    agent_id=eval_agent.id, \n", "    block_label=\"company\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "01842c97-0786-45fd-90c1-2509224d742b", "metadata": {"height": 81}, "outputs": [], "source": ["client.agents.blocks.retrieve(\n", "    agent_id=outreach_agent.id, \n", "    block_label=\"company\"\n", ")"]}, {"cell_type": "markdown", "id": "656f50fb-46c9-4d4c-a97d-c75963736f7b", "metadata": {}, "source": ["## Section 4: Multi-agent groups"]}, {"cell_type": "code", "execution_count": null, "id": "436d144c-4bed-492e-86c3-c31a7ac459d3", "metadata": {"height": 300}, "outputs": [], "source": ["def print_message_multiagent(message):  \n", "    if message.message_type == \"reasoning_message\": \n", "        print(f\"🧠 Reasoning ({message.name}): \" + message.reasoning) \n", "    elif message.message_type == \"assistant_message\": \n", "        print(f\"🤖 Agent ({message.name}): \" + message.content) \n", "    elif message.message_type == \"tool_call_message\": \n", "        print(f\"🔧 Tool Call ({message.name}): \" + message.tool_call.name + \"\\n\" + message.tool_call.arguments)\n", "    elif message.message_type == \"tool_return_message\": \n", "        print(f\"🔧 Tool Return ({message.name}): \" + message.tool_return)\n", "    elif message.message_type == \"user_message\": \n", "        print(\"👤 User Message: \" + message.content)\n", "    elif message.message_type == \"usage_statistics\": \n", "        # for streaming specifically, we send the final chunk that contains the usage statistics \n", "        print(f\"Usage: [{message}]\")\n", "        return \n", "    print(\"-----------------------------------------------------\")"]}, {"cell_type": "markdown", "id": "aae6a9c4de52f64", "metadata": {}, "source": ["### Recreating the outreach and evaluation agents"]}, {"cell_type": "code", "execution_count": null, "id": "ad8828be-820a-46d1-a784-183278c86eaf", "metadata": {"height": 404}, "outputs": [], "source": ["# create the outreach agent \n", "outreach_agent = client.agents.create(\n", "    name=\"outreach_agent\",\n", "    memory_blocks=[\n", "        { \"label\": \"persona\", \"value\": outreach_persona}\n", "    ],\n", "    model=\"openai/gpt-4o-mini-2024-07-18\",\n", "    embedding=\"openai/text-embedding-ada-002\",\n", "    tool_ids=[draft_email_tool.id], \n", "    block_ids=[company_block.id]\n", ")\n", "\n", "# create the evaluation agent \n", "eval_agent = client.agents.create(\n", "    name=\"eval_agent\",\n", "    memory_blocks=[\n", "        { \"label\": \"persona\", \"value\": eval_persona}\n", "    ],\n", "    model=\"openai/gpt-4o-mini-2024-07-18\",\n", "    embedding=\"openai/text-embedding-ada-002\",\n", "    tool_ids=[reject_tool.id],\n", "    block_ids=[company_block.id]\n", ")"]}, {"cell_type": "markdown", "id": "21d93e3c8807d7c3", "metadata": {}, "source": ["### Creating a round-robin agent group"]}, {"cell_type": "code", "execution_count": null, "id": "b7596508-25f9-4ac3-aa79-e78f83036962", "metadata": {"height": 132}, "outputs": [], "source": ["\"\"\"\n", "Round-Robin Group\n", "\"\"\"\n", "round_robin_group = client.groups.create(\n", "    description=\"This team is responsible for recruiting candidates.\",\n", "    agent_ids=[eval_agent.id, outreach_agent.id],\n", ")"]}, {"cell_type": "markdown", "id": "16653b59cf1b601f", "metadata": {}, "source": ["### Messaging an agent group"]}, {"cell_type": "code", "execution_count": null, "id": "e0e76277-9277-4db1-b0cb-843622bd331d", "metadata": {"height": 30}, "outputs": [], "source": ["resume = open(\"resumes/spongebob_squarepants.txt\", \"r\").read()"]}, {"cell_type": "code", "execution_count": null, "id": "b4fb5de7-631b-45e1-9c2e-e52b1a133b08", "metadata": {"height": 115}, "outputs": [], "source": ["response_stream = client.groups.messages.create_stream(\n", "    group_id=round_robin_group.id,\n", "    messages=[\n", "       {\"role\": \"user\", \"content\": f\"Evaluate: {resume}\"}\n", "    ]\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "f8bf8bd2-0fa1-48c2-b1d9-1bd711f38472", "metadata": {"height": 47}, "outputs": [], "source": ["for message in response_stream: \n", "    print_message_multiagent(message)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 5}